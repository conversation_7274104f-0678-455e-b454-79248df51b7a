nohup: ignoring input
yarn run v1.22.22
$ next dev --port 4000
  ▲ Next.js 13.5.8
  - Local:        http://localhost:4000

 ✓ Ready in 3.8s
 ✓ Compiled /src/middleware in 286ms (54 modules)
 ○ Compiling /login/page ...
 ✓ Compiled /login/page in 9.2s (690 modules)
 ✓ Compiled in 1069ms (293 modules)
 ○ Compiling /icon.ico/route ...
 ✓ Compiled /icon.ico/route in 3.8s (697 modules)
 ○ Compiling /admin/roles/page ...
 ⨯ ./src/components/ui/header/AdminSidebar.tsx:7:0
Module not found: Can't resolve 'react-responsive'
[0m [90m  5 | [39m[36mimport[39m { [33mDisclosure[39m[33m,[39m [33mTransition[39m } [36mfrom[39m [32m'@headlessui/react'[39m[33m;[39m [90m// Import Transition[39m[0m
[0m [90m  6 | [39m[36mimport[39m { [33mChevronUpIcon[39m } [36mfrom[39m [32m'@heroicons/react/20/solid'[39m[33m;[39m[0m
[0m[31m[1m>[22m[39m[90m  7 | [39m[36mimport[39m { useMediaQuery } [36mfrom[39m [32m"react-responsive"[39m[33m;[39m[0m
[0m [90m  8 | [39m[36mimport[39m {[0m
[0m [90m  9 | [39m  [33mBars3Icon[39m[33m,[39m[0m
[0m [90m 10 | [39m  [33mUserGroupIcon[39m[33m,[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/app/admin/layout.tsx
 ⨯ ./src/components/ui/header/AdminSidebar.tsx:7:0
Module not found: Can't resolve 'react-responsive'
[0m [90m  5 | [39m[36mimport[39m { [33mDisclosure[39m[33m,[39m [33mTransition[39m } [36mfrom[39m [32m'@headlessui/react'[39m[33m;[39m [90m// Import Transition[39m[0m
[0m [90m  6 | [39m[36mimport[39m { [33mChevronUpIcon[39m } [36mfrom[39m [32m'@heroicons/react/20/solid'[39m[33m;[39m[0m
[0m[31m[1m>[22m[39m[90m  7 | [39m[36mimport[39m { useMediaQuery } [36mfrom[39m [32m"react-responsive"[39m[33m;[39m[0m
[0m [90m  8 | [39m[36mimport[39m {[0m
[0m [90m  9 | [39m  [33mBars3Icon[39m[33m,[39m[0m
[0m [90m 10 | [39m  [33mUserGroupIcon[39m[33m,[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/app/admin/layout.tsx
 ⨯ ./src/components/ui/header/AdminSidebar.tsx:7:0
Module not found: Can't resolve 'react-responsive'
[0m [90m  5 | [39m[36mimport[39m { [33mDisclosure[39m[33m,[39m [33mTransition[39m } [36mfrom[39m [32m'@headlessui/react'[39m[33m;[39m [90m// Import Transition[39m[0m
[0m [90m  6 | [39m[36mimport[39m { [33mChevronUpIcon[39m } [36mfrom[39m [32m'@heroicons/react/20/solid'[39m[33m;[39m[0m
[0m[31m[1m>[22m[39m[90m  7 | [39m[36mimport[39m { useMediaQuery } [36mfrom[39m [32m"react-responsive"[39m[33m;[39m[0m
[0m [90m  8 | [39m[36mimport[39m {[0m
[0m [90m  9 | [39m  [33mBars3Icon[39m[33m,[39m[0m
[0m [90m 10 | [39m  [33mUserGroupIcon[39m[33m,[39m[0m

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./src/app/admin/layout.tsx
 ✓ Compiled /_error in 1477ms (1325 modules)
 ✓ Compiled /not-found in 329ms (1330 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled /admin/roles/page in 887ms (929 modules)
isDesktop false
 ⨯ src/app/admin/roles/page.tsx (32:22) @ sessionStorage
 ⨯ ReferenceError: sessionStorage is not defined
    at ModulePage (./src/app/admin/roles/page.tsx:36:25)
[0m [90m 30 | [39m  [36mconst[39m [isModalOpen[33m,[39m setIsModalOpen] [33m=[39m useState([36mfalse[39m)[33m;[39m[0m
[0m [90m 31 | [39m  [36mconst[39m [selectedJobIds[33m,[39m setSelectedJobIds] [33m=[39m useState[33m<[39m[33mnumber[39m[][33m>[39m([])[33m;[39m  [90m// Track selected job IDs[39m[0m
[0m[31m[1m>[22m[39m[90m 32 | [39m  [36mconst[39m storedFiles [33m=[39m sessionStorage[33m.[39mgetItem([32m"uploadedFiles"[39m)[33m;[39m[0m
[0m [90m    | [39m                     [31m[1m^[22m[39m[0m
[0m [90m 33 | [39m  [36mconst[39m [[33mUploadedDataVisual[39m[33m,[39m setUploadedDataVisual] [33m=[39m useState([36mfalse[39m)[0m
[0m [90m 34 | [39m  [36mconst[39m [values[33m,[39m setValues] [33m=[39m useState[33m<[39m[33mPagination[39m[33m>[39m({[0m
[0m [90m 35 | [39m    limit[33m:[39m [35m0[39m[33m,[39m[0m
 ✓ Compiled /admin/leaderboard/page in 1196ms (1839 modules)
 ✓ Compiled /admin/page in 1119ms (1855 modules)
 ✓ Compiled /dashboard/roles/page in 2.8s (1878 modules)
 ✓ Compiled /dashboard/page in 813ms (1918 modules)
isDesktop false
 ⨯ src/app/admin/roles/page.tsx (32:22) @ sessionStorage
 ⨯ ReferenceError: sessionStorage is not defined
    at ModulePage (./src/app/admin/roles/page.tsx:36:25)
[0m [90m 30 | [39m  [36mconst[39m [isModalOpen[33m,[39m setIsModalOpen] [33m=[39m useState([36mfalse[39m)[33m;[39m[0m
[0m [90m 31 | [39m  [36mconst[39m [selectedJobIds[33m,[39m setSelectedJobIds] [33m=[39m useState[33m<[39m[33mnumber[39m[][33m>[39m([])[33m;[39m  [90m// Track selected job IDs[39m[0m
[0m[31m[1m>[22m[39m[90m 32 | [39m  [36mconst[39m storedFiles [33m=[39m sessionStorage[33m.[39mgetItem([32m"uploadedFiles"[39m)[33m;[39m[0m
[0m [90m    | [39m                     [31m[1m^[22m[39m[0m
[0m [90m 33 | [39m  [36mconst[39m [[33mUploadedDataVisual[39m[33m,[39m setUploadedDataVisual] [33m=[39m useState([36mfalse[39m)[0m
[0m [90m 34 | [39m  [36mconst[39m [values[33m,[39m setValues] [33m=[39m useState[33m<[39m[33mPagination[39m[33m>[39m({[0m
[0m [90m 35 | [39m    limit[33m:[39m [35m0[39m[33m,[39m[0m
 ✓ Compiled /dashboard/roles/skill_level/page in 955ms (1924 modules)
 ✓ Compiled /dashboard/roles/assessment/page in 606ms (1930 modules)
 ✓ Compiled /dashboard/result/page in 647ms (1940 modules)
 ✓ Compiled /dashboard/[quizid]/page in 763ms (1936 modules)
 ✓ Compiled /quiz/page in 2.2s (2009 modules)
 ✓ Compiled /dashboard/roles/assessment/results/page in 729ms (1988 modules)
 ✓ Compiled /dashboard/skills/learning_plan/page in 2.6s (1997 modules)
 ✓ Compiled /login/page in 1220ms (997 modules)
 ✓ Compiled in 3.8s (2007 modules)
 ✓ Compiled /page in 2.5s (1999 modules)
 ✓ Compiled /admin/roles/skill_level/page in 2.1s (1967 modules)
 ✓ Compiled /admin/leaderboard/page in 282ms (967 modules)
 ✓ Compiled /dashboard/roles/page in 736ms (969 modules)
 ✓ Compiled /dashboard/roles/skill_level/page in 304ms (958 modules)
 ✓ Compiled /dashboard/roles/assessment/page in 399ms (962 modules)
 ✓ Compiled /dashboard/[quizid]/page in 727ms (964 modules)
 ✓ Compiled /quiz/page in 764ms (993 modules)
 ✓ Compiled /dashboard/roles/assessment/results/page in 1195ms (987 modules)
 ✓ Compiled /dashboard/skills/learning_plan/page in 765ms (985 modules)
isDesktop false
 ✓ Compiled /icon.ico/route in 958ms (1011 modules)
isDesktop false
 ✓ Compiled in 2s (1999 modules)
isDesktop false
 ✓ Compiled /icon.ico/route in 364ms (1011 modules)
isDesktop false
isDesktop false
isDesktop false
 ○ Compiling /admin/usergroup/page ...
 ✓ Compiled /admin/usergroup/page in 5.3s (2083 modules)
isDesktop false
 ✓ Compiled in 1465ms (1062 modules)
 ✓ Compiled in 788ms (1062 modules)
 ✓ Compiled in 857ms (1062 modules)
 ✓ Compiled in 1260ms (1062 modules)
 ✓ Compiled in 711ms (1062 modules)
 ✓ Compiled in 706ms (1062 modules)
 ✓ Compiled in 995ms (1062 modules)
 ✓ Compiled in 896ms (1062 modules)
 ✓ Compiled in 738ms (1062 modules)
 ✓ Compiled in 2.1s (2087 modules)
isDesktop false
 ✓ Compiled /icon.ico/route in 474ms (1055 modules)
 ✓ Compiled /login/page in 1170ms (1061 modules)
 ✓ Compiled /dashboard/roles/page in 1899ms (1064 modules)
isDesktop false
isDesktop false
isDesktop false
 ✓ Compiled /page in 841ms (1002 modules)
 ✓ Compiled /icon.ico/route in 285ms (1032 modules)
isDesktop false
 ✓ Compiled /dashboard/roles/skill_level/page in 863ms (1002 modules)
 ✓ Compiled /dashboard/roles/assessment/page in 1439ms (1008 modules)
 ✓ Compiled /dashboard/roles/assessment/results/page in 280ms (1012 modules)
isDesktop false
isDesktop false
 ✓ Compiled /icon.ico/route in 303ms (987 modules)
 ✓ Compiled /page in 854ms (955 modules)
 ✓ Compiled /icon.ico/route in 269ms (985 modules)
 ✓ Compiled /login/page in 289ms (980 modules)
 ✓ Compiled /not-found in 2.9s (2013 modules)
 ✓ Compiled /icon.ico/route in 247ms (979 modules)
 ✓ Compiled /dashboard/roles/page in 235ms (990 modules)
 ✓ Compiled /dashboard/roles/skill_level/page in 282ms (994 modules)
 ✓ Compiled /dashboard/result/page in 1295ms (1003 modules)
 ✓ Compiled /dashboard/result/[groupId]/page in 1527ms (2077 modules)
 ✓ Compiled /dashboard/[quizid]/page in 825ms (999 modules)
 ✓ Compiled /dashboard/skills/learning_plan/page in 761ms (1001 modules)
isDesktop false
isDesktop false
isDesktop false
 ○ Compiling /contentpanel/page ...
 ✓ Compiled /contentpanel/page in 10.4s (2313 modules)
activeItem:-  
contentPanelData22 undefined
contentIdasa 0 0
SADFdsvs {
  user_id: undefined,
  group_id: 1,
  module_id: 1,
  content_id: 0,
  completed: true,
  page_number: 0,
  video_progress: NaN,
  total_progress: '2025-03-12T04:07:15.606Z'
}
 ✓ Compiled /dashboard/group/page in 2.6s (2238 modules)
 ✓ Compiled /dashboard/assesments/page in 898ms (2251 modules)
 ✓ Compiled /icon.ico/route in 352ms (1119 modules)
 ✓ Compiled /dashboard/result/page in 302ms (1115 modules)
 ⨯ src/app/dashboard/[quizid]/page.tsx (11:20) @ parse
 ⨯ SyntaxError: Unexpected token u in JSON at position 0
    at JSON.parse (<anonymous>)
    at eval (./src/app/dashboard/[quizid]/page.tsx:20:21)
    at (ssr)/./src/app/dashboard/[quizid]/page.tsx (/home/<USER>/codedir/lms-frontend-WB/lms-frontend/.next/server/app/dashboard/[quizid]/page.js:313:1)
    at __webpack_require__ (/home/<USER>/codedir/lms-frontend-WB/lms-frontend/.next/server/webpack-runtime.js:33:43)
    at JSON.parse (<anonymous>)
[0m [90m  9 | [39m[0m
[0m [90m 10 | [39m[90m// Parse the JSON string into a JavaScript object[39m[0m
[0m[31m[1m>[22m[39m[90m 11 | [39m[36mvar[39m userData [33m=[39m [33mJSON[39m[33m.[39mparse([33mJSON[39m[33m.[39mstringify(user))[0m
[0m [90m    | [39m                   [31m[1m^[22m[39m[0m
[0m [90m 12 | [39m[0m
[0m [90m 13 | [39m[90m// Access the user_id property[39m[0m
[0m [90m 14 | [39m[36mvar[39m userId [33m=[39m userData[33m.[39muser_id[33m;[39m[0m
 ⨯ src/app/dashboard/[quizid]/page.tsx (11:20) @ parse
 ⨯ SyntaxError: Unexpected token u in JSON at position 0
    at JSON.parse (<anonymous>)
    at eval (./src/app/dashboard/[quizid]/page.tsx:20:21)
    at (ssr)/./src/app/dashboard/[quizid]/page.tsx (/home/<USER>/codedir/lms-frontend-WB/lms-frontend/.next/server/app/dashboard/[quizid]/page.js:313:1)
    at __webpack_require__ (/home/<USER>/codedir/lms-frontend-WB/lms-frontend/.next/server/webpack-runtime.js:33:43)
    at JSON.parse (<anonymous>)
[0m [90m  9 | [39m[0m
[0m [90m 10 | [39m[90m// Parse the JSON string into a JavaScript object[39m[0m
[0m[31m[1m>[22m[39m[90m 11 | [39m[36mvar[39m userData [33m=[39m [33mJSON[39m[33m.[39mparse([33mJSON[39m[33m.[39mstringify(user))[0m
[0m [90m    | [39m                   [31m[1m^[22m[39m[0m
[0m [90m 12 | [39m[0m
[0m [90m 13 | [39m[90m// Access the user_id property[39m[0m
[0m [90m 14 | [39m[36mvar[39m userId [33m=[39m userData[33m.[39muser_id[33m;[39m[0m
 ✓ Compiled /dashboard/roles/skill_level/page in 405ms (1101 modules)
 ✓ Compiled /dashboard/roles/assessment/page in 1413ms (1107 modules)
 ✓ Compiled /dashboard/skills/learning_plan/page in 872ms (1000 modules)
isDesktop false
 ✓ Compiled /login/page in 2.4s (965 modules)
 ✓ Compiled (971 modules)
 ✓ Compiled /dashboard/skills/page in 2s (2139 modules)
 ✓ Compiled /icon.ico/route in 357ms (1005 modules)
 ✓ Compiled /admin/leaderboard/page in 932ms (995 modules)
 ✓ Compiled /page in 912ms (954 modules)
 ✓ Compiled /dashboard/roles/skill_level/page in 939ms (954 modules)
 ✓ Compiled /dashboard/roles/assessment/page in 419ms (958 modules)
 ✓ Compiled /dashboard/[quizid]/page in 1320ms (958 modules)
 ✓ Compiled /quiz/page in 918ms (985 modules)
 ✓ Compiled /dashboard/roles/assessment/results/page in 1345ms (975 modules)
 ✓ Compiled /dashboard/skills/learning_plan/page in 943ms (985 modules)
 ✓ Compiled in 7.3s (2159 modules)
isDesktop false
 ⨯ src/api/user.localStorage.ts (35:20) @ localStorage
 ⨯ ReferenceError: localStorage is not defined
    at getContentLocalStorageData (./src/api/user.localStorage.ts:39:23)
    at Sidebar (./src/components/ui/header/Sidebar.tsx:108:104)
[0m [90m 33 | [39m[0m
[0m [90m 34 | [39m[36mexport[39m [36mfunction[39m getContentLocalStorageData() {[0m
[0m[31m[1m>[22m[39m[90m 35 | [39m  [36mconst[39m contentId [33m=[39m localStorage[33m.[39mgetItem([32m'content_id'[39m)[33m;[39m[0m
[0m [90m    | [39m                   [31m[1m^[22m[39m[0m
[0m [90m 36 | [39m  [36mconst[39m moduleId [33m=[39m localStorage[33m.[39mgetItem([32m'module_id'[39m)[33m;[39m[0m
[0m [90m 37 | [39m  [36mconst[39m groupId [33m=[39m localStorage[33m.[39mgetItem([32m'group_id'[39m)[33m;[39m[0m
[0m [90m 38 | [39m  [0m
 ✓ Compiled /login/page in 1697ms (1012 modules)
 ✓ Compiled (1018 modules)
 ✓ Compiled /page in 989ms (982 modules)
 ✓ Compiled /dashboard/roles/page in 1890ms (986 modules)
 ✓ Compiled in 3.2s (2125 modules)
 ○ Compiling /admin/roles/page ...
 ✓ Compiled /admin/roles/page in 5.1s (966 modules)
 ✓ Compiled /dashboard/roles/skill_level/page in 916ms (966 modules)
 ✓ Compiled /icon.ico/route in 1427ms (981 modules)
 ✓ Compiled /not-found in 2.8s (2119 modules)
 ✓ Compiled /icon.ico/route in 847ms (981 modules)
 ✓ Compiled /not-found in 1588ms (2149 modules)
 ✓ Compiled in 858s (2180 modules)
 ✓ Compiled in 8.7s (2180 modules)
 ✓ Compiled in 57.3s (2180 modules)
 ✓ Compiled in 4.4s (2180 modules)
