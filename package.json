{"name": "take-test", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "jest", "test:watch": "jest --watch", "lint": "next lint"}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-python": "^6.1.7", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.5", "@cyntler/react-doc-viewer": "^1.17.0", "@formfacade/embed-react": "^1.1.2", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "4", "@testing-library/user-event": "^14.5.1", "@types/node": "20.4.6", "@types/react": "^18.3.2", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.14", "axios": "^1.6.0", "canvas": "^3.0.1", "eslint": "8.46.0", "eslint-config-next": "13.4.12", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "flowbite-datepicker": "^1.2.6", "framer-motion": "^10.15.0", "install": "^0.13.0", "jest-worker": "^29.7.0", "js-cookie": "^3.0.5", "next": "^13.4.12", "next-auth": "^4.24.7", "pdfobject": "^2.3.1", "postcss": "8.4.27", "pptx2html": "^0.3.4", "primereact": "^10.6.5", "react": "^18.3.1", "react-datepicker": "^6.9.0", "react-datetime-picker": "^5.6.0", "react-datetimepicker": "^2.0.0", "react-dom": "^18.3.1", "react-google-slides": "^4.0.0", "react-hook-form": "^7.49.3", "react-icons": "^4.11.0", "react-player": "^2.16.0", "react-query": "^3.39.3", "react-responsive": "^10.0.1", "react-router-dom": "^6.22.3", "react-tailwindcss-datepicker": "^1.6.6", "react-timer-hook": "^3.0.7", "react-toastify": "^11.0.5", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "typescript": "5.1.6", "video.js": "^8.17.3", "videojs-contrib-eme": "^5.5.1", "videojs-mobile-ui": "^1.1.1", "videojs-seek-buttons": "^4.0.3", "videojs-sprite-thumbnails": "^2.2.1", "xlsx": "^0.18.5", "zustand": "^4.5.4"}, "devDependencies": {"@babel/core": "^7.24.9", "@babel/preset-env": "^7.24.8", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^14.3.1", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.1", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.6", "@types/react-datepicker": "^6.2.0", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "react-query": "^3.39.3", "ts-jest": "^29.1.5", "ts-node": "^10.9.2"}}