{
  "compilerOptions": {
    "target": "es6",
    "types": [
      "jest"
    ],
    "lib": [
      "dom",
      "dom.iterable",
      "esnext",
      "es2015"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "commonjs",
    "moduleResolution": "node",
    "baseUrl": "./",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": [
        "./src/*"
      ]
      // "@/components/*": ["components/*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "src/utils/generateImage.js",
    "src/api/admin/group/getUsersInGroup.tsx",
    "src/__tests__/*",
    "src/__tests__/dashboard/module/moduleIdPage.js",
    "src/__tests__/dashboard/content/contentpage.js",
    "tailwind.config.js"
  ],
  "exclude": [
    "node_modules"
  ]
}
