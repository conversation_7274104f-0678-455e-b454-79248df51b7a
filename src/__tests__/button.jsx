import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import '@testing-library/jest-dom'; // for the matchers like `toBeInTheDocument`
import Button from "../components/ui/Button"; // Adjust the import path as necessary

describe("Button Component", () => {
  test("renders with a given button name", () => {
    render(<Button btnName="Click Me" />);
    expect(screen.getByText("Click Me")).toBeInTheDocument();
  });

  test("calls onClickFunction when clicked", () => {
    const handleClick = jest.fn();
    render(<Button btnName="Click Me" onClickFunction={handleClick} />);
    
    fireEvent.click(screen.getByText("Click Me"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test("does not call onClickFunction when not clicked", () => {
    const handleClick = jest.fn();
    render(<Button btnName="Click Me" onClickFunction={handleClick} />);
    expect(handleClick).not.toHaveBeenCalled();
  });

  test("is clickable", () => {
    render(<Button btnName="Click Me" />);
    const button = screen.getByText("Click Me");
    expect(button).toBeEnabled();
  });
});