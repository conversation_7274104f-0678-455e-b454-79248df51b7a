import { render, screen, fireEvent } from "@testing-library/react";
import '@testing-library/jest-dom'; // Correctly import jest-dom

import LoginPage from "../../app/login/page";
import React from 'react';

// Mock the router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn().mockReturnValue({
    push: jest.fn(),
  }),
}));

// Mock the signIn and saveUser functions
jest.mock('@/api/user/userSignIn', () => ({
  signIn: jest.fn(),
}));

jest.mock('@/api/user.localStorage', () => ({
  saveUser: jest.fn(),
}));

describe("LoginPage", () => {
  it("test the Img of login page", () => {
    render(<LoginPage />);
    const imgAlt = screen.getByAltText("Skilling.ai");
    expect(imgAlt).toBeInTheDocument();
  });

  it("test the heading of login page", () => {
    render(<LoginPage />);
    const loginHeadings = screen.getByText(/Sign in to your account/i);
    expect(loginHeadings).toBeInTheDocument();
  });

  it("test the email input of login page", () => {
    render(<LoginPage />);
    const emailInput = screen.getByPlaceholderText("Enter Email");
    expect(emailInput).toBeInTheDocument();
  });

  it("test the password input of login page", () => {
    render(<LoginPage />);
    const passwordInput = screen.getByPlaceholderText("Enter password");
    expect(passwordInput).toBeInTheDocument();
  });


  it("test the button of login page", () => {
    render(<LoginPage />);
    const buttontitle = screen.getByTitle("login btn");
    expect(buttontitle).toBeInTheDocument();
  });

  it("test if the sign in button is clickable", () => {
    render(<LoginPage />);
    const signInButton = screen.getByTitle("login btn");

    // Check if the button is in the document
    expect(signInButton).toBeInTheDocument();

    // Mock the signIn function to return a resolved promise
    const signInMock = require('@/api/user/userSignIn').signIn;
    signInMock.mockResolvedValue({ roles: [{ ROLE: 'USER' }] });

    // Simulate a click event on the button
    fireEvent.click(signInButton);

    // You can further assert the signIn function was called
    expect(signInMock).toHaveBeenCalled();
  });

});


