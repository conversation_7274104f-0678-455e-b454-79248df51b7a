// RootLayout.test.tsx

import React from 'react';
import { render, screen } from '@testing-library/react';
import RootLayout from '../app/layout';


jest.mock('@/utils/provider', () => {
  const Provider: React.FC<{ children: React.ReactNode }> = ({ children }) => <div>{children}</div>;
  Provider.displayName = 'Provider'; // Add a display name
  return Provider;
});


describe('RootLayout Component', () => {
  test('renders children', () => {
    render(<RootLayout><div>Child Component</div></RootLayout>);

    // Check if children are rendered
    const childElement = screen.getByText('Child Component');
    expect(childElement).toBeInTheDocument();
  });


});
