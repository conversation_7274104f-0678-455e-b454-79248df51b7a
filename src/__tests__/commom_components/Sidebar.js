import "@testing-library/jest-dom";
import { render, screen } from "@testing-library/react";
import React from "react";
import Sidebar from "../../components/Sidebar";

describe("Sidebar Component", () => {
  // test("Heading testing with props", () => {
  //   render(<Sidebar />);
  //   // const h2 = screen.getByText(textName)
  //   // const h2 = screen.getByRole("heading");
  //   const  titleH2 = screen.getByTitle("title1")

  //   // expect(titleH2).toBeInTheDocument()

  //   // expect(titleH2).toHaveAttribute("title", "title1");
  //   expect(titleH2).toBeInTheDocument()
  // });

  

  test("H2 testing with props", () => {
    render(<Sidebar />);

    const menuItems = ["Program","Assesments", "Results", "User Attempts","Leader Board", "Certificate"];

    menuItems.forEach((menu) => {
      expect(screen.getByText(menu)).toBeInTheDocument();
    });
  });
});
