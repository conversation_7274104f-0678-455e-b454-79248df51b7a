import { render, screen } from "@testing-library/react";
import Button from "../../components/Button";
import "@testing-library/jest-dom";
import React from "react";
import userEvent from "@testing-library/user-event";

describe("Button Component", () => {
  test("Rendering Button Component Test", () => {
    render(<Button />);
    const btn = screen.getByRole("button");
    expect(btn).toBeInTheDocument();
  });

  test("Testing onClick Function when clicked", async () => {
    const clickTest = jest.fn();
    userEvent.setup();
    render(<Button onClickFunction={clickTest} />);
    const btn = screen.getByRole("button");
    await userEvent.click(btn);
    expect(clickTest).toBeCalled();
  });
});

