import "@testing-library/jest-dom"
import { render, screen } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import React from "react"
import SubmitModal from "../../components/SubmitModal"


describe("Submit Modal Component", () => {
    test('testing Button with onclick function', async() => {
        const btnClick = jest.fn()
        userEvent.setup()
        render(<SubmitModal onClose={btnClick}/>)
        const btn = screen.getByRole("button")
        await userEvent.click(btn)
        expect(btnClick).toBeCalled()
    })


    test('heading testing with props', () => {
        const name = "modalName"
        render(<SubmitModal modalName={name}/> )
        const h1 = screen.getByText(name)
        expect(h1).toBeInTheDocument()
    })
})