import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import AssessmentResult from "../../components/AssessmentResult";

// Mock the dependencies
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock('@/api/user.localStorage', () => ({
  getUser: () => ({ name: 'Test User' }),
}));

describe('AssessmentResult', () => {
  const assessments = [
    {
      assessment_id: '1',
      assessment_name: 'Test Assessment 1',
      start_date: '2023-06-01T12:00:00Z',
      end_date: '2023-06-01T13:00:00Z',
      user_id: 'user1',
    },
    {
      assessment_id: '2',
      assessment_name: 'Test Assessment 2',
      start_date: '2023-07-01T14:00:00Z',
      end_date: '2023-07-01T15:00:00Z',
      user_id: 'user2',
    },
  ];

  it('displays loading state', () => {
    render(<AssessmentResult assessments={[]} loadingState={true} />);
    expect(screen.getByText(/Loading/i)).toBeInTheDocument();
  });

  it('displays assessments when loaded', () => {
    render(<AssessmentResult assessments={assessments} loadingState={false} />);
    expect(screen.getByText('Test Assessment 1')).toBeInTheDocument();
    expect(screen.getByText('Test Assessment 2')).toBeInTheDocument();

  });


  it('displays no assessments message when there are no assessments', () => {
    render(<AssessmentResult assessments={[]} loadingState={false} />);
    expect(screen.getByText(/Currently, there are no asessments result for you/i)).toBeInTheDocument();
  });

  it('displays correct date and time for assessments', () => {
    render(<AssessmentResult assessments={assessments} loadingState={false} />);

    // Custom matcher function for dates
    const matchDate = (content, element) => {
      const normalizedText = element.textContent.replace(/\s/g, '');
      return normalizedText.includes('6/1/2023') || normalizedText.includes('1/6/2023');
    };

    // Custom matcher function for times
    const matchTime = (content, element) => {
      const normalizedText = element.textContent.replace(/\s/g, '');
      return normalizedText.includes('12:00:00PM') || normalizedText.includes('5:30:00pm');
    };

    expect(screen.queryAllByText(matchDate).length).toBeGreaterThan(0);
    expect(screen.queryAllByText(matchTime).length).toBeGreaterThan(0);

    // Check for the second assessment
    const matchDate2 = (content, element) => {
      const normalizedText = element.textContent.replace(/\s/g, '');
      return normalizedText.includes('7/1/2023') || normalizedText.includes('1/7/2023');
    };

    const matchTime2 = (content, element) => {
      const normalizedText = element.textContent.replace(/\s/g, '');
      return normalizedText.includes('2:00:00PM') || normalizedText.includes('7:30:00pm');
    };

    expect(screen.queryAllByText(matchDate2).length).toBeGreaterThan(0);
    expect(screen.queryAllByText(matchTime2).length).toBeGreaterThan(0);
  });
});