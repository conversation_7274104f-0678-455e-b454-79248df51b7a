import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import React from "react";
import BulkQuesModal from "../../components/BulkQuesModal";
import userEvent from "@testing-library/user-event";
userEvent;

describe("BulkQuesModal Component", () => {
  test("Button Testing", () => {
    render(<BulkQuesModal />);
    let btn = screen.getByRole("button");
    expect(btn).toBeInTheDocument();
  });

  test("Heading one Testing", () => {
    render(<BulkQuesModal />);
    let head1 = screen.getByRole("heading");
    expect(head1).toBeInTheDocument();
  });

  test("Heading one props testing", () => {
    const name = "modalName";
    render(<BulkQuesModal modalName={name} />);
    const h1 = screen.getByText(name);
    expect(h1).toBeInTheDocument();
  });

  test("Paragraph Testing", () => {
    render(<BulkQuesModal />);
    let para = screen.getByText("The undefined is submit.");
    expect(para).toBeInTheDocument();
  });

  test("Testing onClick Function when clicked", async () => {
    const clickTest = jest.fn();
    userEvent.setup();
    render(<BulkQuesModal onClose={clickTest} />);
    const btn = screen.getByRole("button");
    await userEvent.click(btn);
    expect(clickTest).toBeCalled();
  });
});
