import "@testing-library/jest-dom"
import { render, screen } from "@testing-library/react"
import React from "react"
// import ProfileModel from "../../components/ProfileModel"
import ProfileModal from "../../components/ProfileModel"
import userEvent from "@testing-library/user-event"



describe("Profile Modal Component", () => {

    test('H2 testing', () => {
        render(<ProfileModal/>)
        const h2 = screen.getByText("Change Password")
        expect(h2).toBeInTheDocument()
    })

    test('label testing', () => {
        render(<ProfileModal/>)
        const label1 = screen.getByLabelText("Old Password")
        const label2 = screen.getByLabelText("New Password")
        const label3 = screen.getByLabelText("Confirm Password")

        expect(label1).toBeInTheDocument()
        expect(label2).toBeInTheDocument()
        expect(label3).toBeInTheDocument()
    })


    test('Div onClick test', async() => {
        const divTest = jest.fn()
        userEvent.setup()
        render(<ProfileModal onClose={divTest} />)
        const div = screen.getByRole("div")
        await userEvent.click(div)
        expect(divTest).toBeCalled()
    })


    test('Button onClick Test', async() => {
        const btnTest = jest.fn()
        userEvent.setup()
        render(<ProfileModal handleSubmit={btnTest} /> )
        const btn = screen.getByRole("button")

        
        expect(btn).toBeInTheDocument()
        await userEvent.click(btn)
        // expect(btnTest).toHaveBeenCalledTimes(1)
    })
})