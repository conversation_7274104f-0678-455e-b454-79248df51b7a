import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SearchDropdown from '../../components/SearchDropdown';

const mockSetQuestionType = jest.fn();
const mockSetDifficultyLevel = jest.fn();
const mockSetTopic = jest.fn();
const mockSetQuestionSource = jest.fn();

const options = [
  { label: 'Option 1', content: ['Option 1-1', 'Option 1-2'] },
  { label: 'Option 2', content: ['Option 2-1', 'Option 2-2'] },
];

describe('SearchDropdown', () => {
  test('renders the dropdown button', () => {
    render(
      <SearchDropdown
        options={options}
        setQuestionType={mockSetQuestionType}
        setDifficultyLevel={mockSetDifficultyLevel}
        setTopic={mockSetTopic}
        setQuestionSource={mockSetQuestionSource}
      />
    );

    const button = screen.getByText('Filter');
    expect(button).toBeInTheDocument();
  });

  test('opens and closes the dropdown menu', () => {
    render(
      <SearchDropdown
        options={options}
        setQuestionType={mockSetQuestionType}
        setDifficultyLevel={mockSetDifficultyLevel}
        setTopic={mockSetTopic}
        setQuestionSource={mockSetQuestionSource}
      />
    );

    const button = screen.getByText('Filter');
    fireEvent.click(button);
    expect(screen.getByRole('menu')).toBeInTheDocument();

    fireEvent.click(button);
    expect(screen.queryByRole('menu')).not.toBeInTheDocument();
  });

  test('opens and closes option submenus', () => {
    render(
      <SearchDropdown
        options={options}
        setQuestionType={mockSetQuestionType}
        setDifficultyLevel={mockSetDifficultyLevel}
        setTopic={mockSetTopic}
        setQuestionSource={mockSetQuestionSource}
      />
    );

    const button = screen.getByText('Filter');
    fireEvent.click(button);

    const option1Button = screen.getByText('Option 1');
    fireEvent.click(option1Button);
    expect(screen.getByText('Option 1-1')).toBeInTheDocument();

    fireEvent.click(option1Button);
    expect(screen.queryByText('Option 1-1')).not.toBeInTheDocument();
  });

  test('selects an option', () => {
    render(
      <SearchDropdown
        options={options}
        setQuestionType={mockSetQuestionType}
        setDifficultyLevel={mockSetDifficultyLevel}
        setTopic={mockSetTopic}
        setQuestionSource={mockSetQuestionSource}
      />
    );

    const button = screen.getByText('Filter');
    fireEvent.click(button);

    const option1Button = screen.getByText('Option 1');
    fireEvent.click(option1Button);

    const option1Radio1 = screen.getByLabelText('Option 1-1');
    fireEvent.click(option1Radio1);

    expect(option1Radio1).toBeChecked();
  });

  test('clicking outside closes the dropdown', () => {
    render(
      <SearchDropdown
        options={options}
        setQuestionType={mockSetQuestionType}
        setDifficultyLevel={mockSetDifficultyLevel}
        setTopic={mockSetTopic}
        setQuestionSource={mockSetQuestionSource}
      />
    );

    const button = screen.getByText('Filter');
    fireEvent.click(button);

    fireEvent.mouseDown(document);
    expect(screen.queryByRole('menu')).not.toBeInTheDocument();
  });
});






