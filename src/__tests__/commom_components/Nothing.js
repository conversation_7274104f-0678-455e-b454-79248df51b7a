import { render, screen } from "@testing-library/react"
import "@testing-library/jest-dom"
import React from "react"
import Nothing from "../../components/Nothing"
import userEvent from "@testing-library/user-event"




describe("Nothing Component", () => {


    test('testing heading element with props', () => {
        const title = "Title"
        render(<Nothing title={title}/>)
        const h1 = screen.getByText(title)
        expect(h1).toBeInTheDocument()
    })


    test('Button testing onClick function', async() => {
        const clickTest = jest.fn()
        userEvent.setup()
        render(<Nothing btnName="btn1"  onClickFunctionForBtn={clickTest}/>)
        const btn = screen.getByText("btn1")
        await userEvent.click(btn)
        expect(clickTest).toBeCalled()

    })


    test('paragraph testing with props', () => {
        const para = "paragraph"
        render(<Nothing para={para}/>)
        const p1 = screen.getByText(para)
        expect(p1).toBeInTheDocument()
    })
})