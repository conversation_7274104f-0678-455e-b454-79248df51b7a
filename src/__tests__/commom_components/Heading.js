import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import Heading from "../../components/Heading";
import React from "react";

describe("Heading Component", ()=> {


  test("Heading Testing", () => {
    render(<Heading />);
    const h2 = screen.getByRole("heading");
    expect(h2).toBeInTheDocument();
  });


  test('Heading props testing', () => {
    const name = "Heading"
    render(<Heading pgHeading={name}/>)
    const h2 = screen.getByText(name)
    expect(h2).toBeInTheDocument()
  })
})
