import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'; // Use this import
import Header from '../../components/Header';
import { useRouter } from 'next/navigation';

// Mock useRouter
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false, // disable retries to speed up tests
    },
  },
});

describe('Header Component', () => {
  beforeEach(() => {
    useRouter.mockImplementation(() => ({
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
    }));
  });

  it('opens and closes the Assessments dropdown', () => {
    // Render the Header component wrapped in QueryClientProvider
    const queryClient = createTestQueryClient();
    render(
      <QueryClientProvider client={queryClient}>
        <Header />
      </QueryClientProvider>
    );

    // Click on the Assessments dropdown button
    const dropdownButton = screen.getByText('Assessments');
    fireEvent.click(dropdownButton);

    // Verify that the dropdown content is visible after the click
    expect(screen.getByText('Assign Assessment')).toBeInTheDocument();
    expect(screen.getByText('Assessment List')).toBeInTheDocument();

    // Click on the Assessments dropdown button again to close the dropdown
    fireEvent.click(dropdownButton);

    // Verify that the dropdown content is not visible after the second click
    expect(screen.queryByText('Assign Assessment')).not.toBeInTheDocument();
    expect(screen.queryByText('Assessment List')).not.toBeInTheDocument();
  });
});
