import { render, screen } from "@testing-library/react";
import Page from "../../../app/dashboard/leaderboard/myprogress/overall/page"; // Update with the correct path
import "@testing-library/jest-dom";
import { useGetAllUserGroup } from "@/hook/dashboard/leaderborad/useGetAllUserGroup";
import { useGetOverall } from "@/hook/dashboard/leaderborad/useGetOverall";

// Mock hooks
jest.mock("@/hook/dashboard/leaderborad/useGetAllUserGroup", () => ({
  useGetAllUserGroup: jest.fn(),
}));

jest.mock("@/hook/dashboard/leaderborad/useGetOverall", () => ({
  useGetOverall: jest.fn(),
}));

describe("Page Component", () => {
  const mockUseGetAllUserGroup = useGetAllUserGroup;
  const mockUseGetOverall = useGetOverall;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders loading state correctly", () => {
    mockUseGetAllUserGroup.mockReturnValue({ data: [], isLoading: true, isError: false });
    mockUseGetOverall.mockReturnValue({ data: [], isLoading: true, isError: false });

    render(<Page />);

    // Check if loading text is displayed
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("renders error state correctly", () => {
    mockUseGetAllUserGroup.mockReturnValue({ data: [], isLoading: false, isError: true });
    mockUseGetOverall.mockReturnValue({ data: [], isLoading: false, isError: true });

    render(<Page />);

    // Check if error text is displayed
    expect(screen.getByText("Error fetching modules")).toBeInTheDocument();
  });

  it("renders no data state correctly", () => {
    mockUseGetAllUserGroup.mockReturnValue({ data: [], isLoading: false, isError: false });
    mockUseGetOverall.mockReturnValue({ data: {}, isLoading: false, isError: false });

    render(<Page />);

    // Check if "No Data" component is displayed
    expect(screen.getByText("No Data")).toBeInTheDocument();
    expect(screen.getByText("There are currently no data to display. Please check back later.")).toBeInTheDocument();
  });

  it("renders data correctly", () => {
    mockUseGetAllUserGroup.mockReturnValue({ 
      data: [{ group_id: 1, group_name: "Group A" }, { group_id: 2, group_name: "Group B" }], 
      isLoading: false, 
      isError: false 
    });
    mockUseGetOverall.mockReturnValue({ 
      data: { '1': 'Complete', '2': 'Incomplete' }, 
      isLoading: false, 
      isError: false 
    });

    render(<Page />);

    // Check if group names and statuses are displayed
    expect(screen.getByText("Group A")).toBeInTheDocument();
    expect(screen.getByText("Status: Complete")).toBeInTheDocument();
    expect(screen.getByText("Group B")).toBeInTheDocument();
    expect(screen.getByText("Status: Incomplete")).toBeInTheDocument();
  });

  it("renders unknown group name for unmatched group ID", () => {
    mockUseGetAllUserGroup.mockReturnValue({ 
      data: [{ group_id: 1, group_name: "Group A" }], 
      isLoading: false, 
      isError: false 
    });
    mockUseGetOverall.mockReturnValue({ 
      data: { '2': 'Complete' }, 
      isLoading: false, 
      isError: false 
    });

    render(<Page />);

    // Check if "Unknown Group" is displayed for unmatched group ID
    expect(screen.getByText("Unknown Group")).toBeInTheDocument();
    expect(screen.getByText("Status: Complete")).toBeInTheDocument();
  });
});
