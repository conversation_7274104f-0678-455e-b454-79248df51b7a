import { render, screen } from "@testing-library/react";
import Page from "../../../app/dashboard/leaderboard/page"; 
import "@testing-library/jest-dom";
import { ArrowTrendingUpIcon, Bars4Icon } from "@heroicons/react/20/solid";
import Link from "next/link";
import Heading from "@/components/ui/Heading";

jest.mock("@heroicons/react/20/solid", () => ({
  ArrowTrendingUpIcon: jest.fn(() => <div>ArrowTrendingUpIcon</div>),
  Bars4Icon: jest.fn(() => <div>Bars4Icon</div>),
}));

jest.mock("next/link", () => ({
  __esModule: true,
  default: ({ children, href }) => <a href={href}>{children}</a>,
}));

jest.mock("@/components/ui/Heading", () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

describe("Page Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the main layout correctly", () => {
    render(<Page />);

    // Check if the main layout is rendered
    expect(screen.getByRole("main")).toBeInTheDocument();
  });

  it("renders the Heading component with correct text", () => {
    render(<Page />);

    // Check if the Heading component is rendered with correct text
    expect(screen.getByText("LeaderBoard")).toBeInTheDocument();
  });

  it("renders the ArrowTrendingUpIcon and My Progress link", () => {
    render(<Page />);

    // Check if the ArrowTrendingUpIcon is rendered
    expect(screen.getByText("ArrowTrendingUpIcon")).toBeInTheDocument();

    // Check if the My Progress link is rendered
    expect(screen.getByText("My Progress")).toBeInTheDocument();
    expect(screen.getByRole("link", { name: "My Progress" })).toHaveAttribute("href", "/dashboard/leaderboard/myprogress");
  });

  it("renders the Bars4Icon and My Cohort Progress link", () => {
    render(<Page />);

    // Check if the Bars4Icon is rendered
    expect(screen.getByText("Bars4Icon")).toBeInTheDocument();

    // Check if the My Cohort Progress link is rendered
    expect(screen.getByText("My Cohort Progress")).toBeInTheDocument();
    expect(screen.getByRole("link", { name: "My Cohort Progress" })).toHaveAttribute("href", "/dashboard/group");
  });
});
