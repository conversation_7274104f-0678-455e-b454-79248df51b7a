import { render, screen } from "@testing-library/react";
import Page from "../../../app/dashboard/leaderboard/myprogress/page"; // Update with the correct path
import "@testing-library/jest-dom";
import { Squares2X2Icon, CommandLineIcon, CubeTransparentIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import Heading from "@/components/ui/Heading";

jest.mock("@heroicons/react/20/solid", () => ({
  Squares2X2Icon: jest.fn(() => <div>Squares2X2Icon</div>),
  CommandLineIcon: jest.fn(() => <div>CommandLineIcon</div>),
  CubeTransparentIcon: jest.fn(() => <div>CubeTransparentIcon</div>),
}));

jest.mock("next/link", () => ({
  __esModule: true,
  default: ({ children, href }) => <a href={href}>{children}</a>,
}));

jest.mock("@/components/ui/Heading", () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

describe("Page Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the main layout correctly", () => {
    render(<Page />);

    // Check if the main layout is rendered
    expect(screen.getByRole("main")).toBeInTheDocument();
  });

  it("renders the Heading component with correct text", () => {
    render(<Page />);

    // Check if the Heading component is rendered with correct text
    expect(screen.getByText("My Progress")).toBeInTheDocument();
  });

  it("renders the CubeTransparentIcon and Overall Competition link", () => {
    render(<Page />);

    // Check if the CubeTransparentIcon is rendered
    expect(screen.getByText("CubeTransparentIcon")).toBeInTheDocument();

    // Check if the Overall Competition link is rendered
    expect(screen.getByText("Overall Competition")).toBeInTheDocument();
    expect(screen.getByRole("link", { name: "Overall Competition" })).toHaveAttribute("href", "/dashboard/leaderboard/myprogress/overall");
  });

  it("renders the CommandLineIcon and Retest Due link", () => {
    render(<Page />);

    // Check if the CommandLineIcon is rendered
    expect(screen.getByText("CommandLineIcon")).toBeInTheDocument();

    // Check if the Retest Due link is rendered
    expect(screen.getByText("Retest Due")).toBeInTheDocument();
    expect(screen.getByRole("link", { name: "Retest Due" })).toHaveAttribute("href", "/dashboard/assesments");
  });

  it("renders the Squares2X2Icon and Scores By Module link", () => {
    render(<Page />);

    // Check if the Squares2X2Icon is rendered
    expect(screen.getByText("Squares2X2Icon")).toBeInTheDocument();

    // Check if the Scores By Module link is rendered
    expect(screen.getByText("Scores By Module")).toBeInTheDocument();
    expect(screen.getByRole("link", { name: "Scores By Module" })).toHaveAttribute("href", "/dashboard");
  });
});
