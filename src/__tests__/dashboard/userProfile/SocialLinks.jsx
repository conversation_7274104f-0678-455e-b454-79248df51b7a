import SocialLinks from '@/components/dashboard/userProfile/SocialLinks';
import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom'; // For additional matchers


describe('SocialLinks Component', () => {
  test('renders social links correctly', () => {
    const { getByText, getByLabelText } = render(<SocialLinks />);

    // Check if the component renders correctly
    expect(getByText('Social Links:')).toBeInTheDocument();
    expect(getByText('You can also connect your social profiles with us to stay updated anywhere and everywhere.')).toBeInTheDocument();
    expect(getByText('Back Home')).toBeInTheDocument(); // Assuming this is the button text

    // Check if input fields are present
    expect(getByLabelText('Github')).toBeInTheDocument();
    expect(getByLabelText('LinkedIn')).toBeInTheDocument();
    expect(getByLabelText('Twitter')).toBeInTheDocument();
    expect(getByLabelText('Naukri.com')).toBeInTheDocument();
    
  });

  test('clicking Back Home button navigates to the correct link', () => {
    const { getByText } = render(<SocialLinks />);

    fireEvent.click(getByText('Back Home'));
    // Add your assertions for navigation, for example:
    // expect(window.location.pathname).toBe('/dashboard/assesments');
  });

  test('clicking Cancel button performs the correct action', () => {
    const { getByText } = render(<SocialLinks />);
    const cancelButton = getByText('Cancel');

    fireEvent.click(cancelButton);
    // Add your assertions for cancel action, for example:
    // expect(cancelAction).toHaveBeenCalled();
  });

  test('clicking Save Changes button performs the correct action', () => {
    const { getByText } = render(<SocialLinks />);
    const saveButton = getByText('Save Changes');

    fireEvent.click(saveButton);
    // Add your assertions for save action, for example:
    // expect(saveAction).toHaveBeenCalled();
  });

  // Add more tests as needed...
});
