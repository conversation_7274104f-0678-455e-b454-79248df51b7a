import { render, screen, fireEvent } from "@testing-library/react";
import '@testing-library/jest-dom';
import Notifications from "@/components/dashboard/userProfile/Notifications";

// Mock the Button component
// Mock the Button component
jest.mock('@/components/Button', () => {
  const Button = ({ btnName }) => <button>{btnName}</button>;
  Button.displayName = 'Button'; // Add displayName
  return Button;
});

// Mock the next/link component
jest.mock('next/link', () => {
  const Link = ({ children, href }) => <a href={href}>{children}</a>;
  Link.displayName = 'Link'; // Add displayName
  return Link;
});


describe('Notifications Component', () => {
  test('renders correctly', () => {
    render(<Notifications />);

    expect(screen.getByText('Notifications:')).toBeInTheDocument();
    expect(screen.getByText("We'll always let you know about important changes, but you pick what else you want to hear about.")).toBeInTheDocument();
    expect(screen.getByText('Back Home')).toBeInTheDocument();

    // Check for email notification options
    expect(screen.getByLabelText('Comments')).toBeInTheDocument();
    expect(screen.getByLabelText('Evaluation')).toBeInTheDocument();
    expect(screen.getByLabelText('New Assignment')).toBeInTheDocument();

    // Check for updates notification options
    expect(screen.getByLabelText('News and Announcements')).toBeInTheDocument();
    expect(screen.getByLabelText('Weekly Modules/Courses updates')).toBeInTheDocument();
    expect(screen.getByLabelText('Weekly assignment evaluation')).toBeInTheDocument();

    // Check for push notifications options
    expect(screen.getByLabelText('Everything')).toBeInTheDocument();
    expect(screen.getByLabelText('Same as email')).toBeInTheDocument();
    expect(screen.getByLabelText('No push notifications')).toBeInTheDocument();

    // Check for buttons
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Submit Profile')).toBeInTheDocument();
  });

  test('navigation to dashboard assessments works', () => {
    render(<Notifications />);
    const backButton = screen.getByText('Back Home').closest('a');
    expect(backButton).toHaveAttribute('href', '/dashboard/assesments');
  });

  test('checkbox interactions work', () => {
    render(<Notifications />);

    const commentsCheckbox = screen.getByLabelText('Comments');
    const evaluationCheckbox = screen.getByLabelText('Evaluation');

    fireEvent.click(commentsCheckbox);
    expect(commentsCheckbox).toBeChecked();

    fireEvent.click(evaluationCheckbox);
    expect(evaluationCheckbox).toBeChecked();
  });

  test('radio button interactions work', () => {
    render(<Notifications />);

    const everythingRadio = screen.getByLabelText('Everything');
    const sameAsEmailRadio = screen.getByLabelText('Same as email');
    const noPushRadio = screen.getByLabelText('No push notifications');

    fireEvent.click(everythingRadio);
    expect(everythingRadio).toBeChecked();

    fireEvent.click(sameAsEmailRadio);
    expect(sameAsEmailRadio).toBeChecked();

    fireEvent.click(noPushRadio);
    expect(noPushRadio).toBeChecked();
  });

  test('submit button interaction', () => {
    render(<Notifications />);
    const submitButton = screen.getByText('Submit Profile');
    fireEvent.click(submitButton);

    // Since there's no actual form submission, we just test if the button is clickable
    expect(submitButton).toBeInTheDocument();
  });

  test('cancel button interaction', () => {
    render(<Notifications />);
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    // Since there's no actual cancel functionality, we just test if the button is clickable
    expect(cancelButton).toBeInTheDocument();
  });
});
