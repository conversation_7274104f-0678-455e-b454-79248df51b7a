import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import PersonalInfo from '@/components/dashboard/userProfile/PersonalInfo'; // Adjust the path as per your project structure
import '@testing-library/jest-dom';
import { act } from 'react-dom/test-utils';
import userEvent from '@testing-library/user-event';

describe('PersonalInfo Component', () => {
  beforeEach(() => {
    // Mock fetch API
    global.fetch = jest.fn(() =>
      Promise.resolve({
        json: () => Promise.resolve([
          { name: 'United States', code: 'US' },
          { name: 'Canada', code: 'CA' },
        ]),
      })
    );
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('renders PersonalInfo component', async () => {
    await act(async () => {
      render(<PersonalInfo />);
    });

    expect(screen.getByText('Personal Information:')).toBeInTheDocument();
    expect(screen.getByLabelText('Bio:')).toBeInTheDocument();
    expect(screen.getByLabelText('Date Of Birth:')).toBeInTheDocument();
    expect(screen.getByLabelText('Phone:')).toBeInTheDocument();
    expect(screen.getByLabelText('Gender:')).toBeInTheDocument();
    expect(screen.getByLabelText('Country:')).toBeInTheDocument();
    expect(screen.getByLabelText('Permanent Address:')).toBeInTheDocument();
    expect(screen.getByLabelText('City:')).toBeInTheDocument();
    expect(screen.getByLabelText('State / Province:')).toBeInTheDocument();
    expect(screen.getByLabelText('ZIP / Postal code:')).toBeInTheDocument();
  });

  test('handles input changes', async () => {
    await act(async () => {
      render(<PersonalInfo />);
    });

    const phoneInput = screen.getByLabelText('Phone:');
    fireEvent.change(phoneInput, { target: { value: '1234567890' } });
    expect(phoneInput.value).toBe('1234567890');

    const bioTextarea = screen.getByLabelText('Bio:');
    fireEvent.change(bioTextarea, { target: { value: 'Test bio' } });
    expect(bioTextarea.value).toBe('Test bio');
  });

  test('handles date picker change', async () => {
    render(<PersonalInfo />);

    // Find the date input field
    const dateInput = screen.getByLabelText('Date Of Birth:');

    // Simulate interacting with the date picker
    userEvent.click(dateInput); // Open the date picker
    userEvent.type(dateInput, '{arrowdown}{arrowright}{enter}'); // Select the date (01/01/2000)

    // Ensure the date input field has the correct value
    const expectedDate = '01/01/2000';
    // expect(dateInput).toHaveValue(expectedDate);
  });

  test('handles country selection', async () => {
    await act(async () => {
      render(<PersonalInfo />);
    });

    await waitFor(() => screen.getByText('United States'));

    const countrySelect = screen.getByLabelText('Country:');
    fireEvent.change(countrySelect, { target: { value: 'US' } });
    expect(countrySelect.value).toBe('US');
  });

  test('handles gender selection', async () => {
    await act(async () => {
      render(<PersonalInfo />);
    });

    const genderSelect = screen.getByLabelText('Gender:');
    fireEvent.change(genderSelect, { target: { value: 'Male' } });
    expect(genderSelect.value).toBe('Male');
  });

  test('handles address input changes', async () => {
    await act(async () => {
      render(<PersonalInfo />);
    });

    const streetInput = screen.getByLabelText('Permanent Address:');
    fireEvent.change(streetInput, { target: { value: '123 Main St' } });
    expect(streetInput.value).toBe('123 Main St');

    const cityInput = screen.getByLabelText('City:');
    fireEvent.change(cityInput, { target: { value: 'Anytown' } });
    expect(cityInput.value).toBe('Anytown');

    const regionInput = screen.getByLabelText('State / Province:');
    fireEvent.change(regionInput, { target: { value: 'Anystate' } });
    expect(regionInput.value).toBe('Anystate');

    const postalCodeInput = screen.getByLabelText('ZIP / Postal code:');
    fireEvent.change(postalCodeInput, { target: { value: '12345' } });
    expect(postalCodeInput.value).toBe('12345');
  });

  test('handles form submission', async () => {
    await act(async () => {
      render(<PersonalInfo />);
    });

    const submitButton = screen.getByText('Save Changes');
    fireEvent.click(submitButton);

    // Implement your form submission logic here
    // For example, check if the form data is valid or submitted correctly
  });

  test('handles cancel button click', async () => {
    await act(async () => {
      render(<PersonalInfo />);
    });

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    // Implement your cancel button logic here
    // For example, check if the form resets or redirects correctly
  });
});
