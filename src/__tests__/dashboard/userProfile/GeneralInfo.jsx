import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import GeneralInfo from '@/components/dashboard/userProfile/GeneralInfo';
import { useRouter } from 'next/router';

// Mock the Button component and next/link with displayName
jest.mock('@/components/Button', () => {
  const Button = ({ btnName }) => <button>{btnName}</button>;
  Button.displayName = 'Button'; // Add displayName for the mock
  return Button;
});

jest.mock('next/link', () => {
  const Link = ({ children, href }) => <a href={href}>{children}</a>;
  Link.displayName = 'Link'; // Add displayName for the mock
  return Link;
});

// Mock the next/router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

describe('GeneralInfo Component', () => {
  beforeEach(() => {
    useRouter.mockReturnValue({ pathname: '/dashboard/assesments' });
  });

  test('renders correctly', () => {
    render(<GeneralInfo />);

    // Check if the component renders the expected elements
    expect(screen.getByText('User Profile Info')).toBeInTheDocument();
    expect(screen.getByText('Back Home')).toBeInTheDocument();
    expect(screen.getByText('Upload Photo')).toBeInTheDocument();
    expect(screen.getByLabelText('Username:')).toBeInTheDocument();
    expect(screen.getByLabelText('Email address:')).toBeInTheDocument();
    expect(screen.getByLabelText('First Name:')).toBeInTheDocument();
    expect(screen.getByLabelText('Last Name:')).toBeInTheDocument();
    expect(screen.getByLabelText('Job Profile:')).toBeInTheDocument();
    expect(screen.getByLabelText('Company Name:')).toBeInTheDocument();
    expect(screen.getByLabelText('Work Experience (in yrs):')).toBeInTheDocument();
    expect(screen.getByText('Submit Resume:')).toBeInTheDocument();
    expect(screen.getByText('Save Changes')).toBeInTheDocument();
  });

  test('validates email correctly', async () => {
    render(<GeneralInfo />);

    const emailInput = screen.getByLabelText('Email address:');
    
    // Simulate entering an invalid email
    fireEvent.change(emailInput, { target: { value: 'invalidemail' } });
    fireEvent.blur(emailInput);

    await waitFor(() => {
      // Ensure error message appears for invalid email
      expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
    });

    // Simulate entering a valid email
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.blur(emailInput);

    await waitFor(() => {
      // Ensure error message disappears for valid email
      expect(screen.queryByText('Please enter a valid email address.')).not.toBeInTheDocument();
    });
  });

  test('handles file upload', async () => {
    render(<GeneralInfo />);

    const fileInput = screen.getByLabelText('Upload Photo');
    const file = new File(['photo'], 'photo.png', { type: 'image/png' });

    // Simulate file selection
    fireEvent.change(fileInput, { target: { files: [file] } });

    // Here, you can add any assertions related to file upload, if needed.
    // For example, you can check for a function call or state change.
    // await waitFor(() => {
    // });
  });

  test('submit button exists', () => {
    render(<GeneralInfo />);

    const submitButton = screen.getByText('Save Changes');
    expect(submitButton).toBeInTheDocument();
  });

  test('cancel button exists', () => {
    render(<GeneralInfo />);

    const cancelButton = screen.getByText('Cancel');
    expect(cancelButton).toBeInTheDocument();
  });
});
