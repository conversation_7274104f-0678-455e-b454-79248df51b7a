import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import EduQualification from "@/components/dashboard/userProfile/EduQualification";
import React from 'react';
import '@testing-library/jest-dom';

// Mocking global fetch
global.fetch = jest.fn().mockResolvedValue({
    json: jest.fn().mockResolvedValue({}),
  });

describe('EduQualification component', () => {
    it('renders without crashing', () => {
      render(<EduQualification />);
    });
  
    it('fetches courses data on mount', async () => {
      render(<EduQualification />);
  
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/courses.json');
      });
    });
  
    it('updates selected course on dropdown change', () => {
      const { getByTestId } = render(<EduQualification />);
      const dropdown = getByTestId('courseDropdown');
  
      fireEvent.change(dropdown, { target: { value: 'selectedValue' } });
  
      setTimeout(() => {
        expect(dropdown.value).toBe('selectedValue');
      }, 0);
    });
  
    it('updates university name input value on change', () => {
      const { getByLabelText } = render(<EduQualification />);
      const universityInput = getByLabelText('University Name:');
  
      fireEvent.change(universityInput, { target: { value: 'Test University' } });
  
      expect(universityInput.value).toBe('Test University');
    });
  
    it('updates college name input value on change', () => {
      const { getByLabelText } = render(<EduQualification />);
      const collegeInput = getByLabelText('College Name:');
  
      fireEvent.change(collegeInput, { target: { value: 'Test College' } });
  
      expect(collegeInput.value).toBe('Test College');
    });
  
    it('updates specialisation input value on change', () => {
      const { getByLabelText } = render(<EduQualification />);
      const specialisationInput = getByLabelText('Specialisation:');
  
      fireEvent.change(specialisationInput, { target: { value: 'Test Specialisation' } });
  
      expect(specialisationInput.value).toBe('Test Specialisation');
    });
  
    it('updates year input value on change', () => {
      const { getByLabelText } = render(<EduQualification />);
      const yearInput = getByLabelText('Year of Passing:');
  
      fireEvent.change(yearInput, { target: { value: '2022' } });
  
      expect(yearInput.value).toBe('2022');
    });
  
    it('updates CGPA or percentage input value on change', () => {
      const { getByLabelText } = render(<EduQualification />);
      const cgpaInput = getByLabelText('CGPA or Percentage:');
  
      fireEvent.change(cgpaInput, { target: { value: '9.5' } });
  
      expect(cgpaInput.value).toBe('9.5');
    });
  
    it('triggers cancel action when cancel button is clicked', () => {
      const mockCancel = jest.fn();
      const { getByText } = render(<EduQualification onCancel={mockCancel} />);
      const cancelButton = getByText('Cancel');
  
      fireEvent.click(cancelButton);
  
    });
  
    it('triggers save action when save button is clicked', () => {
      const mockSave = jest.fn();
      const { getByText } = render(<EduQualification onSave={mockSave} />);
      const saveButton = getByText('Save Changes');
  
      fireEvent.click(saveButton);
    
    });
  });
