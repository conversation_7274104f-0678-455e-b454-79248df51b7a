import React, { useEffect } from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import Error from '@/app/dashboard/error';


// Mocking useEffect
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useEffect: jest.fn(),
}));

describe('Error Component', () => {
  const error = new Error('Test error');
  const resetMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the error message', () => {
    render(<Error error={error} reset={resetMock} />);

    expect(screen.getByText('Oops! Something went wrong. Please')).toBeInTheDocument();
  });

  it('logs the error to the console', () => {
    console.error = jest.fn(); // Mock console.error
    render(<Error error={error} reset={resetMock} />);
  
    let useEffectTriggered = false;
    useEffect.mockImplementation(() => {
      useEffectTriggered = true;
    });
    setTimeout(() => {
      expect(useEffectTriggered).toBe(true);
      expect(console.error).toHaveBeenCalledWith(error);
    }, 0);
  });

  it('calls the reset function when "Try again" button is clicked', () => {
    render(<Error error={error} reset={resetMock} />);

    const button = screen.getByText('Try again');
    fireEvent.click(button);

    expect(resetMock).toHaveBeenCalled();
  });

  it('has the correct classes for styling', () => {
    const { container } = render(<Error error={error} reset={resetMock} />);

    expect(container.firstChild).toHaveClass('w-full h-full bg-primary flex items-center justify-center');
    expect(screen.getByText('Oops! Something went wrong. Please').parentElement).toHaveClass('mx-auto bg-white p-8');
    expect(screen.getByText('Try again')).toHaveClass('w-44 mt-4 mx-auto justify-center rounded-md border-2 border-textSecondary bg-white px-3 py-1.5 text-sm font-semibold leading-6 text-textSecondary shadow-sm hover:bg-hoverColor hover:text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75');
  });
});
