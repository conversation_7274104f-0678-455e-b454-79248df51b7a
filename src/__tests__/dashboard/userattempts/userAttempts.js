import Page from '@/app/dashboard/userattempts/page';
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { getUser } from '@/api/user.localStorage'; // Import the actual implementation
import { useGetUserAssessments } from '@/hook/assessments/useGetUserAssessments';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock the useGetUserAssessments hook
jest.mock('@/hook/assessments/useGetUserAssessments');
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

jest.mock('@/api/user.localStorage', () => ({
  getUser: jest.fn(),
}));

const mockUser = { user_id: 3 };
const mockAssessments = [
  { assessment_id: 1 },
  { assessment_id: 2 },
  { assessment_id: 3 },
];

const queryClient = new QueryClient();

const renderWithProviders = (ui) => {
  return render(
    <QueryClientProvider client={queryClient}>
      {ui}
    </QueryClientProvider>
  );
};

describe('Page Component', () => {
  beforeEach(() => {
    getUser.mockResolvedValue(mockUser);
    useGetUserAssessments.mockReturnValue({ data: mockAssessments });
  });

  afterEach(() => {
    jest.clearAllMocks();
    queryClient.clear();
  });

  test('renders the heading', async () => {
    renderWithProviders(<Page />);

    // Wait for the user to be set
    await waitFor(() => expect(getUser).toHaveBeenCalled());

    // Check if the heading is rendered
    expect(screen.getByText('User Attempts For An Assessment')).toBeInTheDocument();
  });

  test('renders the assessments', async () => {
    renderWithProviders(<Page />);

    // Wait for the user to be set
    await waitFor(() => expect(getUser).toHaveBeenCalled());

    // Check if the assessments are rendered
    await waitFor(() => {
      mockAssessments.forEach(async (assessment) => {
        const assessmentElement = await screen.findByText(`assessment_id is ${assessment.assessment_id}`);
        expect(assessmentElement).toBeInTheDocument();
      });
    });
  });
});
