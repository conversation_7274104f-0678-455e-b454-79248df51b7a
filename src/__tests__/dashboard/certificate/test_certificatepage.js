import { render, screen } from "@testing-library/react";
import Page from "../../../app/dashboard/certificate/page"; // Update with the correct path
import "@testing-library/jest-dom";
import { ArrowTrendingUpIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import Heading from "@/components/ui/Heading";

jest.mock("@heroicons/react/24/outline", () => ({
  ArrowTrendingUpIcon: jest.fn(() => <div>ArrowTrendingUpIcon</div>),
}));

jest.mock("next/image", () => ({
  __esModule: true,
  default: ({ src, width, height, alt }) => <img src={src} width={width} height={height} alt={alt} />,
}));

jest.mock("@/components/ui/Heading", () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

describe("Page Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the main layout correctly", () => {
    render(<Page />);

    // Check if the main layout is rendered
    expect(screen.getByRole("main")).toBeInTheDocument();
  });

  it("renders the Heading component with correct text", () => {
    render(<Page />);

    // Check if the Heading component is rendered with correct text
    expect(screen.getByText("Certificate")).toBeInTheDocument();
  });

  it("renders the Image component with correct attributes", () => {
    render(<Page />);

    // Check if the Image component is rendered with correct attributes
    const img = screen.getByAltText("Progress Icon");
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute("src", "/certificate-svgrepo-com.svg");
    expect(img).toHaveAttribute("width", "160");
    expect(img).toHaveAttribute("height", "160");
  });

  it("renders the certificate title and link", () => {
    render(<Page />);

    // Check if the certificate title is rendered
    expect(screen.getByText("An Introduction to Generative AI")).toBeInTheDocument();

    // Check if the View Certificate link is rendered
    const link = screen.getByRole("link", { name: "View Certificate" });
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute("href", "/John Doe_Generative AI Fundamentals_certificate.pdf");
    expect(link).toHaveAttribute("target", "_blank");
    expect(link).toHaveAttribute("rel", "noopener noreferrer");
  });
});
