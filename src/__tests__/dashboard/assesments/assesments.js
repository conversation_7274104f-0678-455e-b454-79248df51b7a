import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Dashboard from '@/app/dashboard/assesments/page';
import { useGetUserAssessments } from '@/hook/assessments/useGetUserAssessments';
import { getUser } from '@/api/user.localStorage';

// Mock the imported modules
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/hook/assessments/useGetUserAssessments', () => ({
  useGetUserAssessments: jest.fn(),
}));

jest.mock('@/api/user.localStorage', () => ({
  getUser: jest.fn(),
}));

// Mocking the AssessmentTimeListing component
jest.mock('@/components/dashboard/assessment/AssessmentTimeListing', () => ({
  __esModule: true,
  default: jest.fn(() => <div>Mocked AssessmentTimeListing</div>),
}));

describe('Dashboard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the Dashboard component', () => {
    getUser.mockReturnValue(JSON.stringify({ user_id: 3 }));
    useGetUserAssessments.mockReturnValue({ data: [] });

    render(<Dashboard />);
    expect(screen.getAllByText('Mocked AssessmentTimeListing').length).toBe(3);
  });

  it('handles the Next button click', async () => {
    getUser.mockReturnValue(JSON.stringify({ user_id: 3 }));
    useGetUserAssessments.mockReturnValue({ data: new Array(20).fill({ end_date: new Date(), start_date: new Date() }) });

    render(<Dashboard />);

    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(screen.getAllByText('Mocked AssessmentTimeListing').length).toBe(3);
    });

    expect(screen.getByText('Next')).toHaveClass('bg-textColor');
    expect(screen.getByText('Previous')).toHaveClass('bg-textColor');
  });

  it('handles the Previous button click', async () => {
    getUser.mockReturnValue(JSON.stringify({ user_id: 3 }));
    useGetUserAssessments.mockReturnValue({ data: new Array(20).fill({ end_date: new Date(), start_date: new Date() }) });

    render(<Dashboard />);

    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);
    const previousButton = screen.getByText('Previous');
    fireEvent.click(previousButton);

    await waitFor(() => {
      expect(screen.getAllByText('Mocked AssessmentTimeListing').length).toBe(3);
    });

    expect(screen.getByText('Next')).toHaveClass('bg-textColor');
    expect(screen.getByText('Previous')).toHaveClass('bg-blue-500');
  });

  it('disables the Next button when assessments length is less than the limit', async () => {
    getUser.mockReturnValue(JSON.stringify({ user_id: 3 }));
    useGetUserAssessments.mockReturnValue({ data: new Array(5).fill({ end_date: new Date(), start_date: new Date() }) });

    render(<Dashboard />);

    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(screen.getAllByText('Mocked AssessmentTimeListing').length).toBe(3);
    });

    expect(screen.getByText('Next')).toHaveClass('bg-blue-500');
  });
});
