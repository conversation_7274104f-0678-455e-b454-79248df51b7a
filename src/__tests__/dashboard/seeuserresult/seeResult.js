import Page from '@/app/dashboard/seeuserresult/page';
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useSearchParams } from 'next/navigation';
import { useGetAllAttemptsForUser } from '@/hook/assessment_attempts/useGetAllAttemptsForUser';

jest.mock('next/navigation');
jest.mock('@/hook/assessment_attempts/useGetAllAttemptsForUser');

describe('Page Component', () => {
  beforeEach(() => {
    useSearchParams.mockReturnValue({
      get: jest.fn((key) => {
        if (key === 'assessment_id') return 'mocked_assessment_id';
        if (key === 'user_id') return 'mocked_user_id';
        return null;
      }),
    });

    useGetAllAttemptsForUser.mockReturnValue({
      data: null, // Mock no data for simplicity, adjust as needed
    });
  });

  it('renders Page component correctly with no data message', async () => {
    render(
      <QueryClientProvider client={new QueryClient()}>
        <Page />
      </QueryClientProvider>
    );

    // Wait for any asynchronous operations to complete
    await waitFor(() => {});

    // Assert that the component renders no data message
    expect(screen.getByText('Currently, there are no assessment results for you.')).toBeInTheDocument();
  });
});
