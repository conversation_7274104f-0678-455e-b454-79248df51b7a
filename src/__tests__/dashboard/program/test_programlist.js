import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useGetAllProgram } from '@/hook/dashboard/program/useGetAllProgram';
import ModulePage from '../../../app/dashboard/group/page';
import { useRouter } from 'next/navigation';

// Mock the useGetAllProgram hook
jest.mock('@/hook/dashboard/program/useGetAllProgram', () => ({
  useGetAllProgram: jest.fn(),
}));

// Mock the next/router to handle navigation
const push = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push }),
}));

describe('ModulePage Button Tests', () => {
  // Mock data
  const mockProgramData = [
    { group_id: '1', group_name: 'Program 1' },
    { group_id: '2', group_name: 'Program 2' },
  ];

  beforeEach(() => {
    // Reset mock implementations before each test
    useGetAllProgram.mockReturnValue({
      data: mockProgramData,
      isLoading: false,
      isError: false,
    });

    // Reset push mock before each test
    push.mockClear();
  });

  test('renders the "Let\'s Start" button for each program', () => {
    render(<ModulePage />);

    // Check if buttons are rendered
    const buttons = screen.getAllByText("Let's Start");
    expect(buttons.length).toBe(mockProgramData.length);

    // Verify that each button contains the correct text
    buttons.forEach((button) => {
      expect(button).toBeInTheDocument();
    });
  });

  test('navigates correctly on button click', () => {
    render(<ModulePage />);

    // Simulate a click on the first button
    const firstButton = screen.getAllByText("Let's Start")[0];
    fireEvent.click(firstButton);

   
  });
});
