// __tests__/FAQGroupId.test.js
import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import FAQGroupId from '../../../app/dashboard/group/[groupId]/page';
import { useParams } from 'next/navigation';
import { useGetAllProgram } from '@/hook/dashboard/program/useGetAllProgram';
import { useGetAllModulesForUser } from '@/hook/dashboard/program/useGetAllModulesForUser';

jest.mock('next/navigation', () => ({
  useParams: jest.fn(),
}));

jest.mock('@/hook/dashboard/program/useGetAllProgram');
jest.mock('@/hook/dashboard/program/useGetAllModulesForUser');

describe('FAQGroupId Component', () => {
  const mockProgramData = [
    {
      group_id: 1,
      group_faq: 'FAQ for Group 1',
    },
  ];

  const mockModulesData = [
    {
      sequence: 1,
      module_name: 'Module 1',
      module_headline: 'Headline 1',
      created_by: 'Author 1',
      module_description: 'Description 1',
      module_id: 1,
    },
  ];

  beforeEach(() => {
    useParams.mockReturnValue({ groupId: '1' });
  });

  it('renders loading state', () => {
    useGetAllProgram.mockReturnValue({ data: null, isLoading: true, isError: false });
    useGetAllModulesForUser.mockReturnValue({ data: null, isLoading: true, isError: false });

    render(<FAQGroupId />);

    expect(screen.getByText(/Loading.../i)).toBeInTheDocument();
  });

  it('renders error state', () => {
    useGetAllProgram.mockReturnValue({ data: null, isLoading: false, isError: true });
    useGetAllModulesForUser.mockReturnValue({ data: null, isLoading: false, isError: true });

    render(<FAQGroupId />);

    expect(screen.getByText(/Error fetching data/i)).toBeInTheDocument();
  });

  it('renders FAQ modal correctly and closes it', async () => {
    useGetAllProgram.mockReturnValue({ data: mockProgramData, isLoading: false, isError: false });
    useGetAllModulesForUser.mockReturnValue({ data: mockModulesData, isLoading: false, isError: false });

    render(<FAQGroupId />);

    await waitFor(() => expect(screen.getByText(/FAQ for Group 1/i)).toBeInTheDocument());

    // Check if modal is displayed
    expect(screen.getByText(/FAQ for Group 1/i)).toBeInTheDocument();
    
    // Click to close modal
    fireEvent.click(screen.getByText(/Close/i));
    
    // Check if modal is closed
    expect(screen.queryByText(/FAQ for Group 1/i)).not.toBeInTheDocument();
  });

  it('renders module list correctly', async () => {
    useGetAllProgram.mockReturnValue({ data: mockProgramData, isLoading: false, isError: false });
    useGetAllModulesForUser.mockReturnValue({ data: mockModulesData, isLoading: false, isError: false });

    render(<FAQGroupId />);

    await waitFor(() => expect(screen.getByText(/Modules List/i)).toBeInTheDocument());

    // Check if the module data is rendered correctly
    expect(screen.getByText('Module 1')).toBeInTheDocument();
    expect(screen.getByText('Headline 1')).toBeInTheDocument();
    expect(screen.getByText('Description 1')).toBeInTheDocument();
    expect(screen.getByText('By- Author 1')).toBeInTheDocument();
  });


});
