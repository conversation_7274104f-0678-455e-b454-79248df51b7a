// __tests__/ModuleId.test.js
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ModuleId from '../../../app/dashboard/group/module/[moduleId]/page'; 
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import ModuleContentTable from '@/components/dashboard/module/ModuleContentTable';
import ModuleAssessmentTable from '@/components/dashboard/module/ModuleAssessmentTable';
import Heading from '@/components/ui/Heading';

jest.mock('next/navigation', () => ({
  useParams: jest.fn(),
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock('@/components/dashboard/module/ModuleContentTable');
jest.mock('@/components/dashboard/module/ModuleAssessmentTable');
jest.mock('@/components/ui/Heading');

describe('ModuleId Component', () => {
  beforeEach(() => {
    useParams.mockReturnValue({ moduleId: '1' });
    useRouter.mockReturnValue({ back: jest.fn() });
    useSearchParams.mockReturnValue(new URLSearchParams('group_id=5'));
    ModuleContentTable.mockImplementation(({ moduleIDProp, groupIdProp }) => (
      <div data-testid="ModuleContentTable">
        ModuleContentTable - moduleID: {moduleIDProp}, groupId: {groupIdProp}
      </div>
    ));
    ModuleAssessmentTable.mockImplementation(({ moduleIDProp }) => (
      <div data-testid="ModuleAssessmentTable">
        ModuleAssessmentTable - moduleID: {moduleIDProp}
      </div>
    ));
    Heading.mockImplementation(({ pgHeading }) => <h1>{pgHeading}</h1>);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with correct props and elements', () => {
    render(<ModuleId />);

    // Check if Heading is rendered with correct text
    expect(screen.getByText('Modules')).toBeInTheDocument();

    // Check if ModuleContentTable and ModuleAssessmentTable are rendered with correct props
    expect(screen.getByTestId('ModuleContentTable')).toHaveTextContent('ModuleContentTable - moduleID: 1, groupId: 5');
    expect(screen.getByTestId('ModuleAssessmentTable')).toHaveTextContent('ModuleAssessmentTable - moduleID: 1');

    // Check if Back button is rendered
    expect(screen.getByText('Back')).toBeInTheDocument();
  });

  it('navigates back on Back button click', () => {
    const mockRouter = useRouter();
    render(<ModuleId />);

    // Click the back button
    fireEvent.click(screen.getByText('Back'));

    // Check if router.back() was called
    expect(mockRouter.back).toHaveBeenCalled();
  });

  it('should display module and group ids correctly', () => {
    render(<ModuleId />);

    // Verify that the moduleId and groupId are displayed correctly
    expect(screen.getByTestId('ModuleContentTable')).toHaveTextContent('moduleID: 1, groupId: 5');
    expect(screen.getByTestId('ModuleAssessmentTable')).toHaveTextContent('moduleID: 1');
  });
});
