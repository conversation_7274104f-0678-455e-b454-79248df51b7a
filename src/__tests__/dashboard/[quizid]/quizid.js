import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Quiz from '@/app/dashboard/[quizid]/page';


// Mock useRouter hook
jest.mock('next/router', () => ({
  useRouter: () => ({
    route: '/dashboard/[quizid]',
    pathname: '/dashboard/[quizid]',
    query: { quizid: '1' },
    asPath: '/dashboard/1',
    push: jest.fn(),
  }),
}));


// Mock getUser function
jest.mock('@/api/user.localStorage', () => ({
  getUser: jest.fn(() => ({
    user_id: 1, // Provide a mock user object with user_id
  })),
}));

// Mock useGetUserAssessments hook
jest.mock('@/hook/assessments/useGetUserAssessments', () => ({
  useGetUserAssessments: jest.fn(() => ({
    data: [
      {
        assessment_id: 1,
        assessment_name: 'Sample Assessment',
        instructions: '<p>Sample instructions</p>',
        total_time_allowed: 3600, // in seconds
        user_assessment_id: 123,
      },
    ],
  })),
}));

describe('Quiz component', () => {
  it('renders Quiz component correctly', () => {
    render(<Quiz params={{ quizid: 1 }} />);

    // Check if exam details are rendered
    expect(screen.getByText('Exam Details: Sample Assessment')).toBeInTheDocument();

    // // Check if instructions are rendered
    // expect(screen.getByText('Instructions:')).toBeInTheDocument();

    // Check if label is rendered
    expect(screen.getByText(/I have understood/)).toBeInTheDocument();

    // Check if button is rendered
    expect(screen.getByText('Start Quiz')).toBeInTheDocument();
  });

  it('handles checkbox click correctly', () => {
    render(<Quiz params={{ quizid: 1 }} />);

    // Initially checkbox should not be checked
    const checkbox = screen.getByTestId('proceed');
    expect(checkbox).not.toBeChecked();

    // Simulate clicking on the checkbox
    fireEvent.click(checkbox);

    // Now checkbox should be checked
    expect(checkbox).toBeChecked();
  });
});
