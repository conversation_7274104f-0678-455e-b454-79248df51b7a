// DashboardLayout.test.tsx

import React from 'react';
import { render, screen } from '@testing-library/react';
import DashboardLayout from '../../app/dashboard/layout';

jest.mock('@/components/ui/header/NavigationHeader', () => {
  const NavigationHeader = () => <div>Navigation Header</div>;
  NavigationHeader.displayName = 'NavigationHeader'; // Set displayName
  return NavigationHeader;
});

jest.mock('@/components/Sidebar', () => {
  const Sidebar = () => <div>Sidebar Component</div>;
  Sidebar.displayName = 'Sidebar'; // Set displayName
  return Sidebar;
});

describe('DashboardLayout Component', () => {
  test('renders NavigationHeader component', () => {
    render(<DashboardLayout><div>Child Component</div></DashboardLayout>);

    // Check if NavigationHeader component is rendered
    const headerElement = screen.getByText('Navigation Header');
    expect(headerElement).toBeInTheDocument();
  });

  test('renders Sidebar component', () => {
    render(<DashboardLayout><div>Child Component</div></DashboardLayout>);

    // Check if Sidebar component is rendered
    const sidebarElement = screen.getByText('Sidebar Component');
    expect(sidebarElement).toBeInTheDocument();
  });

  test('renders children', () => {
    render(<DashboardLayout><div>Child Component</div></DashboardLayout>);

    // Check if children are rendered
    const childElement = screen.getByText('Child Component');
    expect(childElement).toBeInTheDocument();
  });
});
