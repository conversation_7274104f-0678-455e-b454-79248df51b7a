import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ModulePage from '../../../app/dashboard/module/page';
import { useGetAllModules } from '@/hook/admin/module/useGetAllModules';

// Mock the hook
jest.mock('@/hook/admin/module/useGetAllModules');

const mockModulesData = [
  {
    module_id: '1',
    module_name: 'Module 1',
    module_headline: 'Headline 1',
    created_by: 'User 1',
    module_description: 'Description 1',
  },
  {
    module_id: '2',
    module_name: 'Module 2',
    module_headline: 'Headline 2',
    created_by: 'User 2',
    module_description: 'Description 2',
  },
];

describe('ModulePage', () => {
  it('displays loading state initially', () => {
    useGetAllModules.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });

    render(<ModulePage />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('displays error state when there is an error', () => {
    useGetAllModules.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });

    render(<ModulePage />);
    
    expect(screen.getByText('Error fetching modules')).toBeInTheDocument();
  });

  it('displays modules data when fetched successfully', async () => {
    useGetAllModules.mockReturnValue({
      data: mockModulesData,
      isLoading: false,
      isError: false,
    });

    render(<ModulePage />);
    expect(screen.getByText('Module 2')).toBeInTheDocument();
    expect(screen.getByText('Headline 2')).toBeInTheDocument();
    expect(screen.getByText('By- User 2')).toBeInTheDocument();
  });
});
