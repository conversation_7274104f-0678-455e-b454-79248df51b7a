// __tests__/ModuleContentTable.test.js

import React from 'react';
import { render, screen, fireEvent, waitFor  } from '@testing-library/react';
import '@testing-library/jest-dom';
import ModuleContentTable from '../../../components/dashboard/module/ModuleContentTable';
import ModuleAssessmentTable from '../../../components/dashboard/module/ModuleAssessmentTable';
import { useModuleContentByModuleId } from '@/hook/admin/module/useGetModuleContentByModuleId';
import { useGetModuleAssessmentByModuleId } from '@/hook/admin/module/useGetModuleAssessmentByModuleId';
import userEvent from '@testing-library/user-event';

jest.mock('@/hook/admin/module/usegetModuleContentByModuleId');
jest.mock('@/hook/admin/module/useGetModuleAssessmentByModuleID');

describe('ModuleContentTable', () => {
  const mockData = [
    { content_id: 1, content_name: 'Title 1', content_description: 'Description 1', topics: 'Topic 1' },
    { content_id: 2, content_name: 'Title 2', content_description: 'Description 2', topics: 'Topic 2' },
    { content_id: 3, content_name: 'Title 3', content_description: 'Description 3', topics: 'Topic 3' },
    { content_id: 4, content_name: 'Title 4', content_description: 'Description 4', topics: 'Topic 4' },
  ];

  it('renders loading state', () => {
    useModuleContentByModuleId.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });

    render(<ModuleContentTable moduleIDProp="1" />);

    expect(screen.getByText(/Loading.../i)).toBeInTheDocument();
  });

  it('renders error state', () => {
    useModuleContentByModuleId.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });

    render(<ModuleContentTable moduleIDProp="1" />);

    expect(screen.getByText(/Error fetching contents/i)).toBeInTheDocument();
  });

  it('renders data correctly', () => {
    useModuleContentByModuleId.mockReturnValue({
      data: mockData,
      isLoading: false,
      isError: false,
    });

    render(<ModuleContentTable moduleIDProp="1" />);

    mockData.forEach((item) => {
      expect(screen.getByText(item.content_id)).toBeInTheDocument();
      expect(screen.getByText(item.content_name)).toBeInTheDocument();
      expect(screen.getByText(item.content_description)).toBeInTheDocument();
      expect(screen.getByText(item.topics)).toBeInTheDocument();
    });
  });

  

  it('renders no content available when there is no data', () => {
    useModuleContentByModuleId.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });

    render(<ModuleContentTable moduleIDProp="1" />);

    expect(screen.getByText('No Content Available')).toBeInTheDocument();
  });
});

describe('ModuleAssessmentTable', () => {
  it('should show loading state', () => {
    useGetModuleAssessmentByModuleId.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });

    render(<ModuleAssessmentTable moduleIDProp="1" />);

    expect(screen.getByText(/Loading.../i)).toBeInTheDocument();
  });

  it('should show error state', () => {
    useGetModuleAssessmentByModuleId.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });

    render(<ModuleAssessmentTable moduleIDProp="1" />);

    expect(screen.getByText(/Error fetching assessments/i)).toBeInTheDocument();
  });

  it('should render data correctly', async () => {
    const mockData = [
      { assessment_id: 1, assessment_name: 'Test 1', instructions: 'Instructions 1' },
      { assessment_id: 2, assessment_name: 'Test 2', instructions: 'Instructions 2' },
    ];
    useGetModuleAssessmentByModuleId.mockReturnValue({
      data: mockData,
      isLoading: false,
      isError: false,
    });

    render(<ModuleAssessmentTable moduleIDProp="1" />);

    await waitFor(() => expect(screen.getByText('Test 1')).toBeInTheDocument());
    expect(screen.getByText('Test 2')).toBeInTheDocument();
    expect(screen.getAllByText(/Instructions/i)).toHaveLength(3);
 
  });

  it('should render empty state', async () => {
    useGetModuleAssessmentByModuleId.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });

    render(<ModuleAssessmentTable moduleIDProp="1" />);

    await waitFor(() => expect(screen.getByText(/No Assessments Available/i)).toBeInTheDocument());
    expect(screen.getByText(/There are currently no Assessments to display./i)).toBeInTheDocument();
  });

});