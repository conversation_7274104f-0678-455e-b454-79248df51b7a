import { render, screen, waitFor } from "@testing-library/react";
import FAQGroupId from "../../../app/dashboard/result/[groupId]/page";
import { useGetRedoContent } from "@/hook/dashboard/result/useGetRedoContent";
import { useGetToDisplayContent } from "@/hook/dashboard/result/useGetToDisplayContent";
import { useGetModuleAttemptAssessmentResult } from "@/hook/dashboard/result/useGetModuleAttemptAssessmentResult";
import { useParams, useSearchParams } from "next/navigation";

jest.mock("next/navigation", () => ({
  useParams: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock("@/hook/dashboard/result/useGetRedoContent", () => ({
  useGetRedoContent: jest.fn(),
}));

jest.mock("@/hook/dashboard/result/useGetToDisplayContent", () => ({
  useGetToDisplayContent: jest.fn(),
}));

jest.mock("@/hook/dashboard/result/useGetModuleAttemptAssessmentResult", () => ({
  useGetModuleAttemptAssessmentResult: jest.fn(),
}));

describe("FAQGroupId Component with Action Parameter", () => {
  beforeEach(() => {
    useParams.mockReturnValue({ groupId: "1" });
    useSearchParams.mockReturnValue({
      get: (key) => (key === 'action' ? 'moduleresult' : null),
    });

    useGetRedoContent.mockReturnValue({
      data: { 1: { id: 1, content: "Redo Content" } },
      isLoading: false,
      isError: false,
    });
    useGetToDisplayContent.mockReturnValue({
      data: [{ module_id: 1, contents: [{ content_id: 1 }] }],
      isLoading: false,
      isError: false,
    });
    useGetModuleAttemptAssessmentResult.mockReturnValue({
      data: {
        1: [
          {
            module_id: 1,
            module_name: "Module 1",
            assessment_id: 101,
            attempt_total: 3,
            total_marks: 90,
            question_category: "RECALL",
          },
        ],
      },
      isLoading: false,
      isError: false,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test("renders loading state initially", () => {
    useGetRedoContent.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });
    render(<FAQGroupId />);
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  test("renders error state", () => {
    useGetRedoContent.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });
    render(<FAQGroupId />);
    expect(screen.getByText("Error fetching data")).toBeInTheDocument();
  });

});
