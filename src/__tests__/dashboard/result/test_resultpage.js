// File: Page.test.js

import { render, screen, fireEvent } from "@testing-library/react";
import Page from "../../../app/dashboard/result/page";
import { useGetUserAssessments } from "@/hook/assessments/useGetUserAssessments";
import { useGetAllProgram } from "@/hook/dashboard/program/useGetAllProgram";
import { useGetModuleAttemptAssessmentResult } from "@/hook/dashboard/result/useGetModuleAttemptAssessmentResult";
import { getUser } from "@/api/user.localStorage";

// Mock the necessary hooks and modules
jest.mock("@/hook/assessments/useGetUserAssessments", () => ({
  useGetUserAssessments: jest.fn(),
}));

jest.mock("@/hook/dashboard/program/useGetAllProgram", () => ({
  useGetAllProgram: jest.fn(),
}));

jest.mock("@/hook/dashboard/result/useGetModuleAttemptAssessmentResult", () => ({
  useGetModuleAttemptAssessmentResult: jest.fn(),
}));

jest.mock("@/api/user.localStorage", () => ({
  getUser: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
}));

describe("Page Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders loading state initially", () => {
    useGetUserAssessments.mockReturnValue({ data: null, isLoading: true });
    useGetAllProgram.mockReturnValue({ data: null, isLoading: true, isError: false });
    useGetModuleAttemptAssessmentResult.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });
    getUser.mockReturnValue('{"user_id": 3}');

    render(<Page />);

    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("renders error message on error", () => {
    useGetUserAssessments.mockReturnValue({ data: null, isLoading: false, isError: true });
    useGetAllProgram.mockReturnValue({ data: null, isLoading: false, isError: true });
    useGetModuleAttemptAssessmentResult.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });
    getUser.mockReturnValue('{"user_id": 3}');

    render(<Page />);

    expect(screen.getByText("Error fetching modules")).toBeInTheDocument();
  });

 
  it("renders 'No Program Available' when there are no programs", () => {
    useGetUserAssessments.mockReturnValue({ data: [], isLoading: false, isError: false });
    useGetAllProgram.mockReturnValue({ data: [], isLoading: false, isError: false });
    useGetModuleAttemptAssessmentResult.mockReturnValue({
      data: {},
      isLoading: false,
      isError: false,
    });
    getUser.mockReturnValue('{"user_id": 3}');

    render(<Page />);

    fireEvent.click(screen.getByText("Module Result"));

    expect(screen.getByText("No Program Available")).toBeInTheDocument();
    expect(screen.getByText("There are currently no Program to display. Please check back later.")).toBeInTheDocument();
  });
});
