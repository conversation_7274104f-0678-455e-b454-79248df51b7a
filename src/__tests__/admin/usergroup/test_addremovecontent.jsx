import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Page from '../../../app/admin/usergroup/addremovecontent/page';
import AddRemoveContentForGroup from '../../../components/admin/userGroup/AddRemoveContentForGroup';
import Button from "../../../components/Button"; 

// Mocking components
jest.mock('@/components/ui/Heading', () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

jest.mock('../../../app/admin/usergroup/addremovecontent/page', () => ({
  __esModule: true,
  default: ({ dataforAll }) => <div>{JSON.stringify(dataforAll)}</div>,
}));

jest.mock('@/components/admin/SearchBar', () => ({
  __esModule: true,
  default: ({ onSearch }) => (
    <input
      type="text"
      onChange={(e) => onSearch(e.target.value)}
      placeholder="Search..."
    />
  ),
}));

// Default mock implementations
const getContentByGroupIdDataMock = jest.fn();
const useGetSearchFromAllContentInTableMock = jest.fn();
const useGetSearchFromGroupContentTableMock = jest.fn();

jest.mock('@/hook/admin/group/useGetAllGroups', () => ({
  getContentByGroupIdData: () => getContentByGroupIdDataMock(),
}));

jest.mock('@/hook/admin/usergroup/content/usegetSearchFormContentInGroup', () => ({
  useGetSearchFromAllContentInTable: () => useGetSearchFromAllContentInTableMock(),
}));

jest.mock('@/hook/admin/usergroup/content/useGetSearchFromGroupContentTable', () => ({
  useGetSearchFromGroupContentTable: () => useGetSearchFromGroupContentTableMock(),
}));

describe('Page Component', () => {
  const queryClient = new QueryClient();

  beforeEach(() => {
    useGetSearchFromAllContentInTableMock.mockReturnValue({
      data: [{ id: 1, name: 'Assessment 1' }],
      isLoading: false,
      isError: false,
    });

    useGetSearchFromAllContentInTableMock.mockReturnValue({
      data: [],
    });

    useGetSearchFromGroupContentTableMock.mockReturnValue({
      data: [],
    });
  });

  const renderWithClient = (ui) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {ui}
      </QueryClientProvider>
    );
  };

//   test('displays loading state', () => {
//     useGetSearchFromAllContentInTableMock.mockReturnValue({
//       data: null,
//       isLoading: true,
//       isError: false,
//     });

//     renderWithClient(<Page />);
//     expect(screen.getByText('Loading...')).toBeInTheDocument();
//   });

//   test('displays error state', () => {
//     useGetSearchFromAllContentInTableMock.mockReturnValue({
//       data: null,
//       isLoading: false,
//       isError: true,
//     });

//     renderWithClient(<Page />);
//     expect(screen.getByText('Error fetching content')).toBeInTheDocument();
//   });

  test('renders correctly Content in group', () => {
    renderWithClient(<AddRemoveContentForGroup type="remove" />);
    expect(screen.getByText('Group Contents')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
   
  });

  test('renders correctly All Existing Content', () => {
    renderWithClient(<AddRemoveContentForGroup type="all" />);
    expect(screen.getByText('All Existing Content')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
   
  });

  test('handles search correctly all content', () => {
    useGetSearchFromAllContentInTableMock.mockReturnValue({
      data: [{ content_id: 2, content_name: 'CONTENT NAME',content_description :'description', topics:'topics',file_path:'abc.pdf'}],
    });

    renderWithClient(<AddRemoveContentForGroup type="all" />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
   
  });

  test('handles search correctly all content in group', () => {
    useGetSearchFromGroupContentTableMock.mockReturnValue({
      data: [{ content_id: 2, content_name: 'CONTENT NAME',content_description :'description', topics:'topics',file_path:'abc.pdf'}],
    });

    renderWithClient(<AddRemoveContentForGroup type="remove" />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
   
  });
 

  test("renders Save & Back button name", () => {
    render(<Button btnName="Save & Back" />);
    expect(screen.getByText("Save & Back")).toBeInTheDocument();
  });

  
  test("calls onClickFunction when clicked Save & Back", () => {
    const handleClick = jest.fn();
    render(<Button btnName="Save & Back" onClickFunction={handleClick} />);
    fireEvent.click(screen.getByText((content, element) => content.trim() === "Save & Back"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  test("is clickable Save & Back", () => {
    render(<Button btnName="Save & Back" />);
    const button = screen.getByText((content, element) => content.trim() === "Save & Back");
    expect(button).toBeEnabled();
  });
});
