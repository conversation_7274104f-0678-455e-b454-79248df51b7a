import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Page from '../../../app/admin/usergroup/addremovecontent/page';
import AddRemoveModuleForUser from '../../../components/admin/userGroup/AllUser';
import Button from "../../../components/Button"; 

// Mocking components
jest.mock('@/components/ui/Heading', () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

jest.mock('../../../app/admin/usergroup/alluser/page', () => ({
  __esModule: true,
  default: ({ dataforAll }) => <div>{JSON.stringify(dataforAll)}</div>,
}));

jest.mock('@/components/admin/SearchBar', () => ({
  __esModule: true,
  default: ({ onSearch }) => (
    <input
      type="text"
      onChange={(e) => onSearch(e.target.value)}
      placeholder="Search..."
    />
  ),
}));

// Default mock implementations
const getContentByGroupIdDataMock = jest.fn();
const useGetSearchFromGroupModuleInTableMock = jest.fn();
const useGetSearchFromAllModuleInTableMock = jest.fn();

jest.mock('@/hook/admin/group/useGetAllGroups', () => ({
  getContentByGroupIdData: () => getContentByGroupIdDataMock(),
}));

jest.mock('@/hook/admin/usergroup/module/useGetSearchFromGroupModuleInTable', () => ({
  useGetSearchFromGroupModuleInTable: () => useGetSearchFromGroupModuleInTableMock(),
}));

jest.mock('@/hook/admin/usergroup/module/useGetSearchFromAllModuleInTable', () => ({
  useGetSearchFromAllModuleInTable: () => useGetSearchFromAllModuleInTableMock(),
}));

describe('Page Component', () => {
  const queryClient = new QueryClient();

  beforeEach(() => {
    getContentByGroupIdDataMock.mockReturnValue({
      data: [{ id: 1, name: 'Assessment 1' }],
      isLoading: false,
      isError: false,
    });

    useGetSearchFromGroupModuleInTableMock.mockReturnValue({
      data: [],
    });

    useGetSearchFromAllModuleInTableMock.mockReturnValue({
      data: [],
    });
  });

  const renderWithClient = (ui) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {ui}
      </QueryClientProvider>
    );
  };

  test('renders correctly All Other User', () => {
    renderWithClient(<AddRemoveModuleForUser type="remove" />);
    expect(screen.getByText('All Existing User')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
   
  });

  test('renders correctly Group User', () => {
    renderWithClient(<AddRemoveModuleForUser type="all" />);
    expect(screen.getByText('Group User')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
   
  });

  test('handles search correctly all user', () => {
    useGetSearchFromGroupModuleInTableMock.mockReturnValue({
      data: [{ user_id: 2, user_name: 'Name',phone:'**********' ,email:'<EMAIL>'}],
    });

    renderWithClient(<AddRemoveModuleForUser type="all" />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
   
  });

  test('handles search correctly all user in group', () => {
    useGetSearchFromAllModuleInTableMock.mockReturnValue({
      data: [{ user_id: 2, user_name: 'Name',phone:'**********' ,email:'<EMAIL>'}],
    });

    renderWithClient(<AddRemoveModuleForUser type="remove" />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
   
  });
 

  test("renders Save & Back button name", () => {
    render(<Button btnName="Save & Back" />);
    expect(screen.getByText("Save & Back")).toBeInTheDocument();
  });

  
  test("calls onClickFunction when clicked Save & Back", () => {
    const handleClick = jest.fn();
    render(<Button btnName="Save & Back" onClickFunction={handleClick} />);
    fireEvent.click(screen.getByText((content, element) => content.trim() === "Save & Back"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  test("is clickable Save & Back", () => {
    render(<Button btnName="Save & Back" />);
    const button = screen.getByText((content, element) => content.trim() === "Save & Back");
    expect(button).toBeEnabled();
  });
});
