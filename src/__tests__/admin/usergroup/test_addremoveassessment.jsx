import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Page from '../../../app/admin/usergroup/assessments/page';
import AddRemoveAssesmentForGroup from '../../../components/admin/assessments/AllAssessments';
import Button from "../../../components/Button"; 

// Mocking components
jest.mock('@/components/ui/Heading', () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

jest.mock('../../../app/admin/usergroup/addremovecontent/page', () => ({
  __esModule: true,
  default: ({ dataforAll }) => <div>{JSON.stringify(dataforAll)}</div>,
}));

jest.mock('@/components/admin/SearchBar', () => ({
  __esModule: true,
  default: ({ onSearch }) => (
    <input
      type="text"
      onChange={(e) => onSearch(e.target.value)}
      placeholder="Search..."
    />
  ),
}));

// Default mock implementations

const useGetGroupAssessmentMock = jest.fn();
const useGetNonGroupAssessmentMock = jest.fn();


jest.mock('@/hook/admin/usergroup/assessments/useGetGroupAssessment', () => ({
    useGetGroupAssessment: () => useGetGroupAssessmentMock(),
}));

jest.mock('@/hook/admin/usergroup/assessments/useGetNonGroupAssessment', () => ({
    useGetNonGroupAssessment: () => useGetNonGroupAssessmentMock(),
}));

describe('Page Component', () => {
  const queryClient = new QueryClient();

  beforeEach(() => {

    useGetGroupAssessmentMock.mockReturnValue({
      data: [],
    });

    useGetNonGroupAssessmentMock.mockReturnValue({
      data: [],
    });
  });

  const renderWithClient = (ui) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {ui}
      </QueryClientProvider>
    );
  };

//   test('displays loading state', () => {
//     useGetGroupAssessmentMock.mockReturnValue({
//       data: null,
//       isLoading: true,
//       isError: false,
//     });

//     renderWithClient(<Page />);
//     expect(screen.getByText('Loading...')).toBeInTheDocument();
//   });

//   test('displays error state', () => {
//     useGetGroupAssessmentMock.mockReturnValue({
//       data: null,
//       isLoading: false,
//       isError: true,
//     });

//     renderWithClient(<Page />);
//     expect(screen.getByText('Error fetching content')).toBeInTheDocument();
//   });

  test('renders correctly Group Assessment in group', () => {
    renderWithClient(<AddRemoveAssesmentForGroup type="remove" />);
    expect(screen.getByText('Group Assessment')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
   
  });

  test('renders correctly All Assessment', () => {
    renderWithClient(<AddRemoveAssesmentForGroup type="add" />);
    expect(screen.getByText('All Assessment')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
   
  });

  test('handles search correctly All Assessment', () => {
    useGetGroupAssessmentMock.mockReturnValue({
        data: [{ assessment_id: 2, assessment_name: 'Assessment1',instructions :'instructions', total_time_allowed:'60',total_marks:'90' ,source:'xyz'}],
    });

    renderWithClient(<AddRemoveAssesmentForGroup type="add" />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
   
  });

  test('handles search correctly Assessment in group', () => {
    useGetNonGroupAssessmentMock.mockReturnValue({
      data: [{ assessment_id: 2, assessment_name: 'Assessment1',instructions :'instructions', total_time_allowed:'60',total_marks:'90' ,source:'xyz'}],
    });

    renderWithClient(<AddRemoveAssesmentForGroup type="remove" />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
   
  });
 

  test("renders Save & Back button name", () => {
    render(<Button btnName="Save & Back" />);
    expect(screen.getByText("Save & Back")).toBeInTheDocument();
  });

  
  test("calls onClickFunction when clicked Save & Back", () => {
    const handleClick = jest.fn();
    render(<Button btnName="Save & Back" onClickFunction={handleClick} />);
    fireEvent.click(screen.getByText((content, element) => content.trim() === "Save & Back"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  test("is clickable Save & Back", () => {
    render(<Button btnName="Save & Back" />);
    const button = screen.getByText((content, element) => content.trim() === "Save & Back");
    expect(button).toBeEnabled();
  });
});
