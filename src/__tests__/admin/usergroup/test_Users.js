import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import Page from "../../../app/admin/usergroup/users/page";
import SubmitModal from "../../../components/SubmitModal";
import AddUserModal from "../../../components/admin/userGroup/AddUserModal";
// import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// Mock the required components and hooks
jest.mock("@/components/ui/SubmitModal", () => {
  const SubmitModal = ({ modalName, onClose }) => (
    <div data-testid="submit-modal">
      <h1>{modalName}</h1>
      <button onClick={onClose}>Close</button>
    </div>
  );
  SubmitModal.displayName = "SubmitModal";
  return SubmitModal;
});

jest.mock("@/components/admin/userGroup/AddUserModal", () => {
  const AddUserModal = ({ onClose, onSubmit }) => (
    <div data-testid="add-user-modal">
      <button onClick={onClose}>Close</button>
      <button onClick={onSubmit}>Submit</button>
    </div>
  );
  AddUserModal.displayName = "AddUserModal";
  return AddUserModal;
});

jest.mock("@/components/admin/userGroup/AddRemoveUserToGroup", () => {
  const AddRemoveUserToGroup = ({ userValues }) => (
    <div data-testid="add-remove-user-group">{JSON.stringify(userValues)}</div>
  );
  AddRemoveUserToGroup.displayName = "AddRemoveUserToGroup";
  return AddRemoveUserToGroup;
});

jest.mock("@/components/admin/SearchBar", () => {
  const SearchBar = ({ onSearch }) => (
    <input
      data-testid="search-bar"
      onChange={(e) => onSearch(e.target.value)}
    />
  );
  SearchBar.displayName = "SearchBar";
  return SearchBar;
});

jest.mock("@/components/Button", () => {
  const Button = ({ btnName, onClickFunction }) => (
    <button onClick={onClickFunction}>{btnName}</button>
  );
  Button.displayName = "Button";
  return Button;
});


jest.mock("@/hook/admin/useGetAllUserExt", () => ({
  useGetAllUserExt: () => ({
    data: [
      { id: 1, name: "User1" },
      { id: 2, name: "User2" },
    ],
    isLoading: false,
    isError: false,
  }),
}));

jest.mock("@/hook/admin/useAddBulkUser", () => ({
  useAddBulkUser: () => ({
    mutate: jest.fn(),
  }),
}));

jest.mock("@/hook/admin/useGetSearchedUsers", () => ({
  useGetSearchedUsers: (search) => ({
    data:
      search === "Search"
        ? [
            { id: 1, name: "User1" },
            { id: 2, name: "User2" },
          ]
        : [{ id: 3, name: "User3" }],
  }),
}));

beforeEach(() => {
});

describe("Page Component", () => {
  const renderComponent = () => render(<Page />);

  // const queryClient = new QueryClient();

  // // const renderComponent2 = () =>
  //   render(
  //     <QueryClientProvider client={queryClient}>
  //       <Page />
  //     </QueryClientProvider>
  //   );

  test("renders the heading", () => {
    renderComponent();
    const headingElement = screen.getByRole("heading", { name: /Users/<USER>
    expect(headingElement).toBeInTheDocument();
  });

  test("renders the SearchBar test props", () => {
    renderComponent();
    const sb = screen.getByTestId("search-bar");
    expect(sb).toBeInTheDocument();

    fireEvent.change(sb, { target: { value: "New Search" } });
    expect(sb.value).toBe("New Search");
  });

  test("renders Add CSV User Button and props test", () => {
    renderComponent();

    // const fileInputRef = {current: { click: jest.fn()}}

    const csvBtn = screen.getByText(/Bulk User Upload/i);
    expect(csvBtn).toBeInTheDocument();

    // fireEvent.click(csvBtn)
    // expect(fileInputRef.current.click).toBeCalled()
  });

  test("renders Add User Button and props test", () => {
    renderComponent();
    const addUserBtn = screen.getByText(/Add User/i);
    fireEvent.click(addUserBtn);
    expect(addUserBtn).toBeInTheDocument();
  });



  test("AddRemoveUserToGroup test with props", () => {
    renderComponent();
    const addRemoveUserGroup = screen.getByTestId(/add-remove-user-group/i);

    expect(addRemoveUserGroup).toBeInTheDocument();
    expect(addRemoveUserGroup.textContent).toBe(
      JSON.stringify([
        { id: 1, name: "User1" },
        { id: 2, name: "User2" },
      ])
    );
  });

  test("renders AddUserModal with props testing ", () => {
    const onClickClose = jest.fn();
    const onClickSubmit = jest.fn();

    render(<AddUserModal onClose={onClickClose} onSubmit={onClickSubmit} />);

    const addUserModal = screen.getByTestId("add-user-modal");
    expect(addUserModal).toBeInTheDocument();

    const closeBtn = screen.getByText("Close");
    fireEvent.click(closeBtn);
    expect(onClickClose).toBeCalled();

    const submitBtn = screen.getByText("Submit");
    fireEvent.click(submitBtn);
    expect(onClickSubmit).toBeCalled();
  });

  test("renders submitModal and props test", () => {
    const onClickSubmit = jest.fn();
    render(
      <SubmitModal onClose={onClickSubmit} modalName="User Upload Success" />
    );

    const modalElement = screen.getByTestId("submit-modal");
    expect(modalElement).toBeInTheDocument();
    expect(screen.getByText("User Upload Success")).toBeInTheDocument();

    const closeBtn = screen.getByText("Close");
    fireEvent.click(closeBtn);

    expect(onClickSubmit).toBeCalled();
  });
});
