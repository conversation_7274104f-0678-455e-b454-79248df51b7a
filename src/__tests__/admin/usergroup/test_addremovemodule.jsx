import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ModuleTable from '../../../components/admin/module/ModuleTable';
import Button from "../../../components/Button"; 

// Mocking components
jest.mock('@/components/ui/Heading', () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

jest.mock('@/components/ui/Nothing', () => ({
  __esModule: true,
  default: ({ title, para }) => <div>{title} - {para}</div>,
}));

jest.mock('@/components/admin/SearchBar', () => ({
  __esModule: true,
  default: ({ onSearch }) => (
    <input
      type="text"
      onChange={(e) => onSearch(e.target.value)}
      placeholder="Search..."
    />
  ),
}));

// Default mock implementations
const useGetSearchFromGroupModuleInTableMock = jest.fn();
const useGetSearchFromAllModuleInTableMock = jest.fn();

jest.mock('@/hook/admin/usergroup/module/useGetSearchFromGroupModuleInTable', () => ({
  useGetSearchFromGroupModuleInTable: () => useGetSearchFromGroupModuleInTableMock(),
}));

jest.mock('@/hook/admin/usergroup/module/useGetSearchFromAllModuleInTable', () => ({
  useGetSearchFromAllModuleInTable: () => useGetSearchFromAllModuleInTableMock(),
}));

describe('ModuleTable Component', () => {
  const queryClient = new QueryClient();

  beforeEach(() => {
    useGetSearchFromGroupModuleInTableMock.mockReturnValue({
      data: [],
    });

    useGetSearchFromAllModuleInTableMock.mockReturnValue({
      data: [],
    });
  });

  const renderWithClient = (ui) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {ui}
      </QueryClientProvider>
    );
  };

  test('renders correctly Group Modules', () => {
    renderWithClient(<ModuleTable type="remove" groupId={1} />);
    expect(screen.getByText('Group Modules')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
  });

  test('renders correctly All Modules', () => {
    renderWithClient(<ModuleTable type="add" groupId={1} modules={[]} />);
    expect(screen.getByText('All Existing Modules')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
  });

  test('handles search correctly all content', () => {
    useGetSearchFromAllModuleInTableMock.mockReturnValue({
      data: [{ module_id: 2, module_name: 'MODULE_NAME', module_headline: 'Headline', module_description: 'Description', creation_date: '2021-01-01T00:00:00Z', created_by: 'Admin' }],
    });

    renderWithClient(<ModuleTable type="add" groupId={1} modules={[]} />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
    expect(screen.getByText('MODULE_NAME')).toBeInTheDocument();
  });

  test('handles search correctly all content in group', () => {
    useGetSearchFromGroupModuleInTableMock.mockReturnValue({
      data: [{ module_id: 2, module_name: 'MODULE_NAME', module_headline: 'Headline', module_description: 'Description' }],
    });

    renderWithClient(<ModuleTable type="remove" groupId={1} />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
    expect(screen.getByText('MODULE_NAME')).toBeInTheDocument();
  });

  test("renders Save & Back button name", () => {
    render(<Button btnName="Save & Back" />);
    expect(screen.getByText("Save & Back")).toBeInTheDocument();
  });

  test("calls onClickFunction when clicked Save & Back", () => {
    const handleClick = jest.fn();
    render(<Button btnName="Save & Back" onClickFunction={handleClick} />);
    fireEvent.click(screen.getByText((content, element) => content.trim() === "Save & Back"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test("is clickable Save & Back", () => {
    render(<Button btnName="Save & Back" />);
    const button = screen.getByText((content, element) => content.trim() === "Save & Back");
    expect(button).toBeEnabled();
  });
});

