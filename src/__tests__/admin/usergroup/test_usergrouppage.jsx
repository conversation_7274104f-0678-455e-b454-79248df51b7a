import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Page from '../../../app/admin/usergroup/page';
import Button from "../../../components/Button"; 

// Mocking components
jest.mock('@/components/ui/Heading', () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

jest.mock('@/components/admin/SearchBar', () => ({
  __esModule: true,
  default: ({ onSearch }) => (
    <input
      type="text"
      onChange={(e) => onSearch(e.target.value)}
      placeholder="Search..."
    />
  ),
}));

jest.mock('@/components/admin/userGroup/UserGroupTable', () => ({
  __esModule: true,
  default: ({ userValues }) => <div>{JSON.stringify(userValues)}</div>,
}));

// Default mock implementations
const useGetAllGroupsMock = jest.fn();
const useSearchUserGroupsMock = jest.fn();

jest.mock('@/hook/admin/group/useGetAllGroups', () => ({
  useGetAllGroups: () => useGetAllGroupsMock(),
}));

jest.mock('@/hook/user/useSearchUserGroups', () => ({
  useSearchUserGroups: () => useSearchUserGroupsMock(),
}));

describe('Page Component', () => {
  const queryClient = new QueryClient();

  beforeEach(() => {
    useGetAllGroupsMock.mockReturnValue({
      data: [{ id: 1, name: 'Assessment 1' }],
      isLoading: false,
      isError: false,
    });

    useSearchUserGroupsMock.mockReturnValue({
      data: [],
    });
  });

  const renderWithClient = (ui) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {ui}
      </QueryClientProvider>
    );
  };

  test("renders Bulk user  button name", () => {
    renderWithClient(<Button btnName="Bulk user " />);
    expect(screen.getByText("Bulk user")).toBeInTheDocument();
  });

  test("calls onClickFunction when clicked Bulk user ", () => {
    const handleClick = jest.fn();
    render(<Button btnName="Bulk user " onClickFunction={handleClick} />);
    fireEvent.click(screen.getByText("Bulk user"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test("is clickable Bulk user ", () => {
    render(<Button btnName="Bulk user " />);
    const button = screen.getByText("Bulk user");
    expect(button).toBeEnabled();
  });
  
  


  test("renders Create Group button name", () => {
    renderWithClient(<Button btnName="Create Group" />);
    expect(screen.getByText("Create Group")).toBeInTheDocument();
  });

  test("calls onClickFunction when clicked Create Group", () => {
    const handleClick = jest.fn();
    render(<Button btnName="Create Group" onClickFunction={handleClick} />);
    fireEvent.click(screen.getByText("Create Group"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test("is clickable Create Group", () => {
    render(<Button btnName="Create Group" />);
    const button = screen.getByText("Create Group");
    expect(button).toBeEnabled();
  });

  test('renders correctly', () => {
    renderWithClient(<Page />);
    expect(screen.getByText('User Groups')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    expect(screen.getByText('[{"id":1,"name":"Assessment 1"}]')).toBeInTheDocument();
  });

  test('handles search correctly', () => {
    useSearchUserGroupsMock.mockReturnValue({
      data: [{ id: 2, name: 'Assessment for Test' }],
    });

    renderWithClient(<Page />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
    expect(screen.getByText('[{"id":2,"name":"Assessment for Test"}]')).toBeInTheDocument();
  });

  test('displays loading state', () => {
    useGetAllGroupsMock.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });

    renderWithClient(<Page />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('displays error state', () => {
    useGetAllGroupsMock.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });

    renderWithClient(<Page />);
    expect(screen.getByText('Error fetching user groups data')).toBeInTheDocument();
  });
});
