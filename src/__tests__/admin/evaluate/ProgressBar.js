
import ProgressBar from '@/components/admin/evaluate/ProgressBar';
import React from 'react';
import { render, waitFor } from '@testing-library/react';
import useGetNumberOfEvaluted from '@/hook/admin/evaluatelist/useGetNumberOfEvaluted';


// Mocking the useGetNumberOfEvaluted hook
jest.mock('@/hook/admin/evaluatelist/useGetNumberOfEvaluted');

describe('ProgressBar Component', () => {
  test('renders loading state', async () => {
    // Mock loading state
    useGetNumberOfEvaluted.mockImplementation(() => ({
      data: undefined,
      isLoading: true,
      isError: false,
    }));

    const { getByText } = render(<ProgressBar assessmentId={1} />);
    expect(getByText('Loading...')).toBeInTheDocument();

    // Clean up mock
    useGetNumberOfEvaluted.mockImplementation(() => ({})); // Reset mock
  });

  test('renders error state', async () => {
    // Mock error state
    useGetNumberOfEvaluted.mockImplementation(() => ({
      data: undefined,
      isLoading: false,
      isError: true,
    }));

    const { getByText } = render(<ProgressBar assessmentId={1} />);
    expect(getByText('Error loading data')).toBeInTheDocument();

    // Clean up mock
    useGetNumberOfEvaluted.mockImplementation(() => ({})); // Reset mock
  });

  test('renders correctly with data', async () => {
    // Mock data
    const mockData = {
      evaluated: 5,
      non_evaluated: 10,
    };

    useGetNumberOfEvaluted.mockImplementation(() => ({
      data: mockData,
      isLoading: false,
      isError: false,
    }));

    const { getByText } = render(<ProgressBar assessmentId={1} />);
    await waitFor(() => {
      expect(getByText(`${mockData.evaluated}/${mockData.non_evaluated}`)).toBeInTheDocument();
    });

    // Clean up mock
    useGetNumberOfEvaluted.mockImplementation(() => ({})); // Reset mock
  });
});
