import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'react-router-dom';
import ContentPage from '../../../components/content/ContentPage';
import { useGetAllContentInTable } from '@/hook/content/useGetAllContentInTable';
import { useSearchContent } from '@/hook/content/useSearchContent';
import { useContentDelete } from '@/hook/content/useContentDelete';
import { useRouter, useSearchParams } from 'next/navigation';

// Mocking the hooks
jest.mock('@/hook/content/useGetAllContentInTable');
jest.mock('@/hook/content/useSearchContent');
jest.mock('@/hook/content/useContentDelete');

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

const queryClient = new QueryClient();

const mockContentData = [
  { content_id: 1, title: 'Test Content 1' },
  { content_id: 2, title: 'Test Content 2' },
];

describe('ContentPage Component', () => {
  beforeEach(() => {
    useGetAllContentInTable.mockReturnValue({
      data: mockContentData,
      isLoading: false,
      error: null,
    });
    useSearchContent.mockReturnValue({
      data: mockContentData,
    });
    useContentDelete.mockReturnValue({
      mutate: jest.fn(),
    });
    useRouter.mockReturnValue({
      push: jest.fn(),
      prefetch: jest.fn(),
      query: {},
    });
    useSearchParams.mockReturnValue({
      get: jest.fn().mockReturnValue(null),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders Content heading', () => {
    render(
      <MemoryRouter>
        <QueryClientProvider client={queryClient}>
          <ContentPage />
        </QueryClientProvider>
      </MemoryRouter>
    );

  const contentHeading = screen.getByRole('heading', { name: /content/i });
  expect(contentHeading).toBeInTheDocument();
  });

  test('renders Add Content button and opens AddContentModal on click', () => {
    render(
      <MemoryRouter>
        <QueryClientProvider client={queryClient}>
          <ContentPage />
        </QueryClientProvider>
      </MemoryRouter>
    );

    const addButton = screen.getByText(/add content/i);
    expect(addButton).toBeInTheDocument();

    fireEvent.click(addButton);


  });

  test('renders SearchBar and performs search', async () => {
    render(
      <MemoryRouter>
        <QueryClientProvider client={queryClient}>
          <ContentPage />
        </QueryClientProvider>
      </MemoryRouter>
    );

    const searchBar = screen.getByPlaceholderText(/search/i);
    expect(searchBar).toBeInTheDocument();

   
  });

  


  test('closes AddContentModal and refreshes content on content addition', async () => {
    render(
      <MemoryRouter>
        <QueryClientProvider client={queryClient}>
          <ContentPage />
        </QueryClientProvider>
      </MemoryRouter>
    );

    const addButton = screen.getByText(/add content/i);
    fireEvent.click(addButton);

  });

 
});
