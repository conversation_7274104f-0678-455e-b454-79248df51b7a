import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import React from "react";
import "@testing-library/jest-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import Page from "../../../app/admin/searchquestions/page";
import SubmitModal from "../../../components/SubmitModal";


describe("Page component", () => {
  const queryClient = new QueryClient();

  const renderComponent = () =>
    render(
      <QueryClientProvider client={queryClient}>
        <Page />
      </QueryClientProvider>
    );

  test("renders the heading", () => {
    renderComponent();
    const headingElement = screen.getByRole("heading", {
      name: /Question Table/i,
    });
    expect(headingElement).toBeInTheDocument();
  });



  test("renders the search bar and handles search", () => {

    renderComponent();
    const searchInput = screen.getByPlaceholderText(/Search/i);
    fireEvent.change(searchInput, { target: { value: "test" } });
    expect(searchInput.value).toBe("test");
  });


  test("handles reset button click", () => {
    renderComponent();
    const resetButton = screen.getByRole("button", { name: /Reset/i });
    fireEvent.click(resetButton);
    expect(resetButton).toBeInTheDocument()
  });

  test("handles add question button click", () => {
    renderComponent();
    const addButton = screen.getByRole("button", { name: /Add Question/i });
    fireEvent.click(addButton);
    expect(addButton).toBeInTheDocument()
  });


  test('handles Bulk Question button click', () => {
    renderComponent()
    const bulkButton = screen.getByRole("button", { name: /Bulk Question/i });
    fireEvent.click(bulkButton)
    expect(bulkButton).toBeInTheDocument();
  })


  test('render the Submit Modal component', () => { 

  const onClickSubmit = jest.fn();
  render(
    <SubmitModal onClose={onClickSubmit} modalName="Question Added Success" />
  );

  })
});
