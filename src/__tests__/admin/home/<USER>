import { render, screen } from "@testing-library/react";
import '@testing-library/jest-dom'; // Correctly import jest-dom
import DashboardPage from "../../../app/admin/page";
import UploadForm from "@/components/admin/UploadQuestion";
import React from 'react';

it("test the heading of home page", () => {
  render(<DashboardPage />);
  
  const homeHeadings = screen.getAllByText(/Upload Questions File/i);
  
  expect(homeHeadings.length).toBeGreaterThan(0);

  expect(homeHeadings[0]).toBeInTheDocument();
});


it("test the inputfiled of home page" ,()=>{
    render(<UploadForm/>);
    const inputFile = screen.getByTitle("Choose file");
    expect(inputFile).toBeInTheDocument();
})

it("test the button of home page" ,()=>{
    render(<UploadForm/>);
    
    const buttontitle = screen.getByTitle("submit bulk question");
    expect(buttontitle).toBeInTheDocument();

  
})


