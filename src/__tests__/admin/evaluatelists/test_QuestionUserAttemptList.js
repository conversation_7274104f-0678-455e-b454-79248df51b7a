import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import Page from '../../../app/admin/evaluatelist/questionuserattemptlist/page';


// Mocking components
jest.mock('@/components/ui/Heading', () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

jest.mock('@/components/admin/SearchBar', () => ({
  __esModule: true,
  default: ({ onSearch }) => (
    <input
      type="text"
      onChange={(e) => onSearch(e.target.value)}
      placeholder="Search..."
    />
  ),
}));

jest.mock('@/components/admin/evaluate/ManualQuestionTable', () => ({
  __esModule: true,
  default: ({ UserData }) => <div>{JSON.stringify(UserData)}</div>,
}));

// Mocking hooks
const useGetAllManualUserMock = jest.fn();
const useGetSearchedUsersMock = jest.fn();
const useSearchParamsMock = jest.fn();

jest.mock('@/hook/admin/evaluatelist/useGetAllManualUser', () => ({
  useGetAllManualUser: () => useGetAllManualUserMock(),
}));

jest.mock('@/hook/admin/useGetSearchedUsers', () => ({
  useGetSearchedUsers: () => useGetSearchedUsersMock(),
}));

jest.mock('next/navigation', () => ({
  useSearchParams: () => useSearchParamsMock(),
}));


describe('Page Component', () => {
  beforeEach(() => {
    useSearchParamsMock.mockReturnValue({
      get: jest.fn().mockReturnValue('mock-assessment-id'),
    });

    useGetAllManualUserMock.mockReturnValue({
      data: [{ id: 1, name: 'User 1' }],
      isLoading: false,
      isError: false,
    });

    useGetSearchedUsersMock.mockReturnValue({
      data: [],
    });
  });

  test('renders correctly', () => {
    render(<Page />);
    expect(screen.getByText('User Manual Question List')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    expect(screen.getByText('[{"id":1,"name":"User 1"}]')).toBeInTheDocument();
  });

  test('handles search correctly', () => {
    

    render(<Page />);
    const searchBar = screen.getByPlaceholderText('Search...');
    expect(searchBar).toBeInTheDocument()
    const test = 'search test'
    fireEvent.change(searchBar, test);
    expect(searchBar).toBeInTheDocument(test);
  });

  test('displays loading state', () => {
    useGetAllManualUserMock.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });

    render(<Page />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('displays error state', () => {
    useGetAllManualUserMock.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });

    render(<Page />);
    expect(screen.getByText('Error fetching assessments')).toBeInTheDocument();
  });
});

