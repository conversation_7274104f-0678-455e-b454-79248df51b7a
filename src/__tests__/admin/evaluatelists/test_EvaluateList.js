import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import Page from '../../../app/admin/evaluatelist/page';


// In JavaScript, when using the jest.mock function to mock ES6 modules, the __esModule: true property is used to indicate that the module being mocked is an ES module. This allows <PERSON><PERSON> to handle the mock correctly, especially when the module being mocked uses named exports or a default export.

// When you mock an ES module in Jest, you need to tell <PERSON><PERSON> that the module should be treated as an ES module. This is where __esModule: true comes into play.




// Mocking components
jest.mock('@/components/ui/Heading', () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

jest.mock('@/components/admin/SearchBar', () => ({
  __esModule: true,
  default: ({ onSearch }) => (
    <input
      type="text"
      onChange={(e) => onSearch(e.target.value)}
      placeholder="Search..."
    />
  ),
}));

jest.mock('@/components/admin/evaluate/ManualAssessmentTable', () => ({
  __esModule: true,
  default: ({ AssessmentData }) => <div>{JSON.stringify(AssessmentData)}</div>,
}));



// Default mock implementations
const useGetAllManualAssessmentMock = jest.fn();
const useGetSearchedAssessmentMock = jest.fn();

jest.mock('@/hook/admin/evaluatelist/useGetAllManualAssessment', () => ({
  useGetAllManualAssessment: () => useGetAllManualAssessmentMock(),
}));

jest.mock('@/hook/assessments/useGetSearchedAssessment', () => ({
  useGetSearchedAssessment: () => useGetSearchedAssessmentMock(),
}));

describe('Page Component', () => {
  beforeEach(() => {
    useGetAllManualAssessmentMock.mockReturnValue({
      data: [{ id: 1, name: 'Assessment 1' }],
      isLoading: false,
      isError: false,
    });

    useGetSearchedAssessmentMock.mockReturnValue({
      data: [],
    });
  });

  test('renders correctly', () => {
    render(<Page />);
    expect(screen.getByText('Manual Assessment List')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    expect(screen.getByText('[{"id":1,"name":"Assessment 1"}]')).toBeInTheDocument();
  });

  test('handles search correctly', () => {
    useGetSearchedAssessmentMock.mockReturnValue({
      data: [{ id: 2, name: 'Assessment for Test' }],
    });

    render(<Page />);
    const searchBar = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchBar, { target: { value: 'Test' } });
    expect(screen.getByText('[{"id":2,"name":"Assessment for Test"}]')).toBeInTheDocument();
  });

  test('displays loading state', () => {
    useGetAllManualAssessmentMock.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });

    render(<Page />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('displays error state', () => {
    useGetAllManualAssessmentMock.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });

    render(<Page />);
    expect(screen.getByText('Error fetching assessments')).toBeInTheDocument();
  });
});
