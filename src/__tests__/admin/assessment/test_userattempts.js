import { render, screen, fireEvent } from "@testing-library/react";
import Page from "../../../components/admin/userGroup/ShowUserAttemptsTable"; // Update with the correct path
import "@testing-library/jest-dom";
import { ArrowDownTrayIcon } from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";

jest.mock("@heroicons/react/24/outline", () => ({
  ArrowDownTrayIcon: jest.fn(() => <div>ArrowDownTrayIcon</div>),
}));

jest.mock("@/components/ui/Heading", () => ({
  __esModule: true,
  default: ({ pgHeading }) => <div>{pgHeading}</div>,
}));

describe("Page Component", () => {
  const mockHandleDownload = jest.fn();
  const mockUserValues = {
    assessment1: {
      assessment_info: {
        assessment_name: "Assessment 1",
        total_marks: 100,
      },
      attempts: [
        {
          user_id: "user1",
          user_assessment_id: "ua1",
          user_full_name: "User One",
          attempt_evaluation: "Passed",
          attempt_start_date: "2023-09-21T10:00:00Z",
          attempt_end_date: "2023-09-21T11:00:00Z",
        },
      ],
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the main layout correctly", () => {
    render(<Page handleDownload={mockHandleDownload} userValues={mockUserValues} />);

    // Check if the main layout is rendered
    expect(screen.getByRole("main")).toBeInTheDocument();
  });

  it("renders the Heading component with correct text", () => {
    render(<Page handleDownload={mockHandleDownload} userValues={mockUserValues} />);

    // Check if the Heading component is rendered with correct text
    expect(screen.getByText("Certificate")).toBeInTheDocument();
  });

  it("renders the download button and handles click event", () => {
    render(<Page handleDownload={mockHandleDownload} userValues={mockUserValues} />);

    // Check if the download button is rendered
    const downloadButton = screen.getByRole("button", { name: /Download/i });
    expect(downloadButton).toBeInTheDocument();

    // Simulate click event
    fireEvent.click(downloadButton);
    expect(mockHandleDownload).toHaveBeenCalled();
  });

  it("renders assessment details correctly", () => {
    render(<Page handleDownload={mockHandleDownload} userValues={mockUserValues} />);

    // Check if assessment details are rendered
    expect(screen.getByText("Assessment Name: Assessment 1")).toBeInTheDocument();
    expect(screen.getByText("Total Marks: 100")).toBeInTheDocument();
    expect(screen.getByText("User ID: user1")).toBeInTheDocument();
    expect(screen.getByText("User Assessment ID: ua1")).toBeInTheDocument();
    expect(screen.getByText("User Name: User One")).toBeInTheDocument();
    expect(screen.getByText("Evaluation: Passed")).toBeInTheDocument();
    expect(screen.getByText("Start Date: 9/21/2023")).toBeInTheDocument();
    expect(screen.getByText("End Date: 9/21/2023")).toBeInTheDocument();
    expect(screen.getByText("Start Time: 10:00:00 AM")).toBeInTheDocument();
    expect(screen.getByText("End Time: 11:00:00 AM")).toBeInTheDocument();
  });
});
