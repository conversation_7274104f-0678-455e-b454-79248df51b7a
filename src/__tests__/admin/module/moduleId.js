import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useParams, useRouter } from 'next/navigation';
import { useGetModuleById } from '@/hook/admin/module/useGetModuleById';
import { useUpdateModule } from '@/hook/admin/module/useUpdateModule';
import Page from '@/app/admin/module/[moduleId]/Page';


// Mock the dependencies
jest.mock('next/navigation', () => ({
  useParams: jest.fn(),
  useRouter: jest.fn(),
}));

jest.mock('@/hook/admin/module/useGetModuleById', () => ({
  useGetModuleById: jest.fn(),
}));

jest.mock('@/hook/admin/module/useUpdateModule', () => ({
  useUpdateModule: jest.fn(),
}));

jest.mock('@/components/ui/Heading', () => {
  const Heading = () => <div>Heading</div>;
  Heading.displayName = 'Heading'; // Add displayName here
  return Heading;
});

jest.mock('@/components/admin/module/AddRemoveAssessmentFromModule', () => {
  const AddRemoveAssessmentFromModule = () => <div>AddRemoveAssessmentFromModule</div>;
  AddRemoveAssessmentFromModule.displayName = 'AddRemoveAssessmentFromModule'; // Add displayName here
  return AddRemoveAssessmentFromModule;
});

jest.mock('@/components/admin/module/AddRemoveContentFromModule', () => {
  const AddRemoveContentFromModule = () => <div>AddRemoveContentFromModule</div>;
  AddRemoveContentFromModule.displayName = 'AddRemoveContentFromModule'; // Add displayName here
  return AddRemoveContentFromModule;
});

jest.mock('@/components/admin/module/AssessmentInModule', () => {
  const AssessmentInModule = () => <div>AssessmentInModule</div>;
  AssessmentInModule.displayName = 'AssessmentInModule'; // Add displayName here
  return AssessmentInModule;
});

jest.mock('@/components/admin/module/ContentInModule', () => {
  const ContentInModule = () => <div>ContentInModule</div>;
  ContentInModule.displayName = 'ContentInModule'; // Add displayName here
  return ContentInModule;
});


describe('Page Component', () => {
  const mockModuleData = {
    module_name: 'Test Module',
    module_headline: 'Test Heading',
    module_description: 'Test Description',
  };

  const mockUseParams = useParams;
  const mockUseRouter = useRouter;
  const mockUseGetModuleById = useGetModuleById;
  const mockUseUpdateModule = useUpdateModule;

  beforeEach(() => {
    mockUseParams.mockReturnValue({ moduleId: '1' });
    mockUseRouter.mockReturnValue({
      push: jest.fn(),
      asPath: '/admin/module/1?action=edit',
    });
    mockUseGetModuleById.mockReturnValue({
      data: mockModuleData,
      isError: false,
      isLoading: false,
    });
    mockUseUpdateModule.mockReturnValue({
      mutate: jest.fn((data, { onSuccess }) => onSuccess()),
      isSuccess: false,
      isError: false,
    });
    Object.defineProperty(window, 'location', {
      writable: true,
      value: { search: '?action=edit' },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state', () => {
    mockUseGetModuleById.mockReturnValueOnce({
      data: null,
      isError: false,
      isLoading: true,
    });
    render(<Page />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('renders error state', () => {
    mockUseGetModuleById.mockReturnValueOnce({
      data: null,
      isError: true,
      isLoading: false,
    });
    render(<Page />);
    expect(screen.getByText('Error loading content details.')).toBeInTheDocument();
  });

  test('renders and updates form fields correctly', async () => {
    render(<Page />);

    expect(screen.getByLabelText('Module Name').value).toBe(mockModuleData.module_name);
    expect(screen.getByLabelText('Module Heading').value).toBe(mockModuleData.module_headline);
    expect(screen.getByLabelText('Module Description').value).toBe(mockModuleData.module_description);

    fireEvent.change(screen.getByLabelText('Module Name'), { target: { value: 'Updated Module' } });
    fireEvent.change(screen.getByLabelText('Module Heading'), { target: { value: 'Updated Heading' } });
    fireEvent.change(screen.getByLabelText('Module Description'), { target: { value: 'Updated Description' } });

    expect(screen.getByLabelText('Module Name').value).toBe('Updated Module');
    expect(screen.getByLabelText('Module Heading').value).toBe('Updated Heading');
    expect(screen.getByLabelText('Module Description').value).toBe('Updated Description');
  });

  test('validates form fields and displays errors', async () => {
    render(<Page />);

    fireEvent.change(screen.getByLabelText('Module Name'), { target: { value: '' } });
    fireEvent.change(screen.getByLabelText('Module Heading'), { target: { value: '' } });
    fireEvent.change(screen.getByLabelText('Module Description'), { target: { value: '' } });

    fireEvent.click(screen.getByText('Save & Next'));

    await waitFor(() => {
      expect(screen.getByText('Module Name is required')).toBeInTheDocument();
      expect(screen.getByText('Module Heading is required')).toBeInTheDocument();
      expect(screen.getByText('Module Description is required')).toBeInTheDocument();
    });
  });

  test('transitions to next screen on successful form submission', async () => {
    const mockMutate = jest.fn((data, { onSuccess }) => onSuccess());
    mockUseUpdateModule.mockReturnValueOnce({ mutate: mockMutate, isSuccess: true, isError: false });

    render(<Page />);

    // Log to ensure button click works
    console.log('Clicking Save & Next button');

    fireEvent.click(screen.getByText('Save & Next'));

    // Log to ensure waitFor is reached
    console.log('Waiting for mutation to be called');

    await waitFor(() => {
    //   expect(mockMutate).toHaveBeenCalled();
      expect(screen.getByText((content) => content.includes('AddRemoveAssessmentFromModule'))).toBeInTheDocument();
    });
  });
});




// AddRemoveContentFromModule test Component for "edit" //

import AddRemoveContentFromModule from '@/components/admin/module/AddRemoveContentFromModule';

// Mock the useModuleContentByModuleId and useGetAllContentInTable hooks
jest.mock('@/hook/admin/module/usegetModuleContentByModuleId', () => ({
  useModuleContentByModuleId: jest.fn(() => ({
    data: [{ id: 1, name: 'Content 1' }],
    isLoading: false,
    isError: false,
  })),
}));

jest.mock('@/hook/content/useGetAllContentInTable', () => ({
  useGetAllContentInTable: jest.fn(() => ({
    data: [{ id: 1, name: 'Content 1' }, { id: 2, name: 'Content 2' }],
    isLoading: false,
    isError: false,
  })),
}));

describe('AddRemoveContentFromModule component', () => {
  it('renders loading state initially', async () => {
    render(<AddRemoveContentFromModule moduleIDProp="1" />);
    // expect(screen.getByText('Loading...')).toBeInTheDocument();
   
  });

  it('renders content lists when loaded', async () => {
    render(<AddRemoveContentFromModule moduleIDProp="1" />);
   
  });

  it('renders error message when there is an error fetching content', async () => {
    jest.spyOn(console, 'error').mockImplementation(() => {}); // Suppress console errors for this test
    jest.requireMock('@/hook/admin/module/usegetModuleContentByModuleId').useModuleContentByModuleId.mockReturnValue({
      isError: true,
    });

    render(<AddRemoveContentFromModule moduleIDProp="1" />);
   
  });
});




// AddRemoveAssessmentFromModule test Component for "edit" //

import AddRemoveAssessmentFromModule from '@/components/admin/module/AddRemoveAssessmentFromModule';

// Mock the useGetModuleAssessmentByModuleId hook
jest.mock('@/hook/admin/module/useGetModuleAssessmentByModuleID', () => ({
  useGetModuleAssessmentByModuleId: jest.fn((moduleId) => ({
    data: [{ id: 1, name: 'Assessment 1' }, { id: 2, name: 'Assessment 2' }],
    isLoading: false,
    isError: false,
  })),
}));

// Mock the useGetAllAssessments hook
jest.mock('@/hook/assessments/useGetAllAssessments', () => ({
  useGetAllAssessments: jest.fn(() => ({
    data: [{ id: 1, name: 'Assessment 1' }, { id: 2, name: 'Assessment 2' }],
    isLoading: false,
    isError: false,
  })),
}));

describe('AddRemoveAssessmentFromModule component', () => {
  it('renders component with loaded data', async () => {
    render(<AddRemoveAssessmentFromModule moduleIDProp="1" />);

  });

  it('handles errors when fetching data', async () => {
    // Mock an error scenario for assessments
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.requireMock('@/hook/assessments/useGetAllAssessments').useGetAllAssessments.mockReturnValueOnce({
      data: null,
      isLoading: false,
      isError: true,
    });

    render(<AddRemoveAssessmentFromModule moduleIDProp="1" />);
 
  });
});




/// ContentInModule test Component for "view" ///

import ContentInModule from '@/components/admin/module/ContentInModule';

// Mock the useModuleContentByModuleId hook
jest.mock('@/hook/admin/module/usegetModuleContentByModuleId', () => ({
  useModuleContentByModuleId: jest.fn(),
}));

describe('ContentInModule', () => {
  it('renders loading state initially', async () => {
    // Mock loading state
    const mockUseModuleContentByModuleId = jest.fn().mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
    });
    require('@/hook/admin/module/usegetModuleContentByModuleId').useModuleContentByModuleId = mockUseModuleContentByModuleId;

    render(<ContentInModule moduleIDProp={1} />);

    // Debug rendered output
    console.log(screen.debug());
    
  });

  it('renders error state when there is an error', async () => {
    // Mock error state
    const mockUseModuleContentByModuleId = jest.fn().mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: true,
    });
    require('@/hook/admin/module/usegetModuleContentByModuleId').useModuleContentByModuleId = mockUseModuleContentByModuleId;

    render(<ContentInModule moduleIDProp={1} />);

    // Debug rendered output
    console.log(screen.debug());
 
  });

  it('renders content when data is loaded', async () => {
    // Mock loaded state
    const mockData = [
      {
        content_id: 1,
        content_name: 'Test Content',
        content_description: 'This is a test content',
        topics: 'Test Topic',
        file_path: '/test/path',
      },
    ];
    const mockUseModuleContentByModuleId = jest.fn().mockReturnValue({
      data: mockData,
      isLoading: false,
      isError: false,
    });
    require('@/hook/admin/module/usegetModuleContentByModuleId').useModuleContentByModuleId = mockUseModuleContentByModuleId;

    render(<ContentInModule moduleIDProp={1} />);

    // Debug rendered output
    console.log(screen.debug());
    
  });
});




/// AssessmentInModule test Component for "view" ///

import AssessmentInModule from '@/components/admin/module/AssessmentInModule';

jest.mock('@/hook/admin/module/useGetModuleAssessmentByModuleID', () => ({
  useGetModuleAssessmentByModuleId: jest.fn(),
}));

describe('AssessmentInModule Component', () => {
  beforeEach(() => {
    // Reset mock implementation before each test
    jest.clearAllMocks();
  });

  it('renders loading state initially', async () => {
    // Mock the hook return value for loading state
    const mockUseGetModuleAssessmentByModuleId = jest.fn();
    mockUseGetModuleAssessmentByModuleId.mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
    });

    render(<AssessmentInModule moduleIDProp="123" />);
 
  });

  it('renders assessments correctly', async () => {
    // Mock the hook return value for assessments
    const mockUseGetModuleAssessmentByModuleId = jest.fn();
    mockUseGetModuleAssessmentByModuleId.mockReturnValue({
      data: [
        {
          assessment_id: 1,
          assessment_name: 'Mock Assessment 1',
          instructions: 'Mock instructions',
          total_time_allowed: 3600, // in seconds
          total_marks: 100,
        },
        {
          assessment_id: 2,
          assessment_name: 'Mock Assessment 2',
          instructions: 'Another mock instructions',
          total_time_allowed: 1800, // in seconds
          total_marks: 80,
        },
      ],
      isLoading: false,
      isError: false,
    });

    render(<AssessmentInModule moduleIDProp="123" />);
    
  });

  it('handles error state', async () => {
    // Mock the hook return value for error state
    const mockUseGetModuleAssessmentByModuleId = jest.fn();
    mockUseGetModuleAssessmentByModuleId.mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: true,
    });

    render(<AssessmentInModule moduleIDProp="123" />);
    
  });
});