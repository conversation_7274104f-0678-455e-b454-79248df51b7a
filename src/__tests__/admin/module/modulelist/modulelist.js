import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Page from '@/app/admin/module/modulelist/Page';
import { useGetAllModules } from '@/hook/admin/module/useGetAllModules';
import { useGetSearchedModules } from '@/hook/admin/module/useGetSearchedModules';
import { useModuleDelete } from '@/hook/admin/module/useModuleDelete';


// Mock the hooks
jest.mock('@/hook/admin/module/useGetAllModules');
jest.mock('@/hook/admin/module/useGetSearchedModules');
jest.mock('@/hook/admin/module/useModuleDelete');

// Mock components
jest.mock('@/components/Button', () => ({ btnName }) => {
  const Button = ({ btnName }) => <button>{btnName}</button>;
  Button.displayName = 'Button'; // Add displayName
  return Button;
});

jest.mock('@/components/ui/Heading', () => ({ pgHeading }) => {
  const Heading = ({ pgHeading }) => <h1>{pgHeading}</h1>;
  Heading.displayName = 'Heading'; // Add displayName
  return Heading;
});

jest.mock('@/components/ui/SubmitModal', () => () => {
  const SubmitModal = () => <div>SubmitModal</div>;
  SubmitModal.displayName = 'SubmitModal'; // Add displayName
  return SubmitModal;
});
jest.mock('@/components/admin/SearchBar', () => ({ onSearch }) => {
  const SearchBar = ({ onSearch }) => (
    <input
      type="text"
      placeholder="Search"
      onChange={(e) => onSearch(e.target.value)}
    />
  );
  SearchBar.displayName = 'SearchBar'; // Add displayName
  return SearchBar;
});

jest.mock('@/components/admin/module/ListModuleInTable', () => ({ ModuleData, onDelete }) => {
  const ListModuleInTable = ({ ModuleData, onDelete }) => (
    <div>
      {ModuleData.map((module) => (
        <div key={module.module_id}>
          {module.module_name}
          <button onClick={() => onDelete(module.module_id)}>Delete</button>
        </div>
      ))}
    </div>
  );
  ListModuleInTable.displayName = 'ListModuleInTable'; // Add displayName
  return ListModuleInTable;
});

jest.mock('@/components/content/AddContentModal', () => ({ onClose }) => {
  const AddContentModal = ({ onClose }) => (
    <div>
      AddContentModal <button onClick={onClose}>Close</button>
    </div>
  );
  AddContentModal.displayName = 'AddContentModal'; // Add displayName
  return AddContentModal;
});

describe('Page Component', () => {
  beforeEach(() => {
    useGetAllModules.mockReturnValue({
      data: [
        { module_id: 1, module_name: 'Module 1' },
        { module_id: 2, module_name: 'Module 2' },
      ],
      isLoading: false,
      error: null,
    });

    useGetSearchedModules.mockReturnValue({
      data: [
        { module_id: 3, module_name: 'Searched Module 1' },
      ],
    });

    useModuleDelete.mockReturnValue({
      mutate: jest.fn().mockResolvedValue(), // Resolving the promise to simulate successful deletion
    });
  });

  test('renders without crashing', () => {
    render(<Page />);
    expect(screen.getByText('Modules')).toBeInTheDocument();
    expect(screen.getByText('Add Module')).toBeInTheDocument();
  });

  test('displays modules correctly', () => {
    render(<Page />);
    expect(screen.getByText('Module 1')).toBeInTheDocument();
    expect(screen.getByText('Module 2')).toBeInTheDocument();
  });

  test('search functionality works', async () => {
    render(<Page />);
    fireEvent.change(screen.getByPlaceholderText('Search'), { target: { value: 'Searched' } });
    
    await waitFor(() => {
      expect(screen.getByText('Searched Module 1')).toBeInTheDocument();
    });
  });

  test('handles module deletion', async () => {
    render(<Page />);
    fireEvent.click(screen.getAllByText('Delete')[0]);
    
  });
});
