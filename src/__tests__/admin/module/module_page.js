import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import Page from '@/app/admin/module/page';
import { useAddModule } from '@/hook/admin/module/useAddModule';
import { useGetAllAssessments } from '@/hook/assessments/useGetAllAssessments'; // Mock the hook
import { useAddModuleAssessment } from '@/hook/admin/module/useAddModuleAssessment'; // Mock this hook too

jest.mock('@/hook/admin/module/useAddModule');
jest.mock('@/hook/assessments/useGetAllAssessments'); // Mock the hook
jest.mock('@/hook/admin/module/useAddModuleAssessment'); // Mock the hook

const mockUseAddModule = {
  mutateAsync: jest.fn(),
};

useAddModule.mockReturnValue(mockUseAddModule);
useGetAllAssessments.mockReturnValue({
  data: [],
  isLoading: false,
  isError: false,
});
useAddModuleAssessment.mockReturnValue({
  mutateAsync: jest.fn(),
});

const queryClient = new QueryClient();

const renderWithQueryClient = (ui) => {
  return render(
    <QueryClientProvider client={queryClient}>
      {ui}
    </QueryClientProvider>
  );
};

describe('Page Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders Create Module form and handles input changes', () => {
    renderWithQueryClient(<Page />);

    const moduleNameInput = screen.getByLabelText(/Module Name/i);
    const moduleHeadingInput = screen.getByLabelText(/Module Heading/i);
    const moduleDescriptionInput = screen.getByLabelText(/Module Description/i);

    fireEvent.change(moduleNameInput, { target: { value: 'Test Module' } });
    fireEvent.change(moduleHeadingInput, { target: { value: 'Test Heading' } });
    fireEvent.change(moduleDescriptionInput, { target: { value: 'Test Description' } });

    expect(moduleNameInput.value).toBe('Test Module');
    expect(moduleHeadingInput.value).toBe('Test Heading');
    expect(moduleDescriptionInput.value).toBe('Test Description');
  });

  test('displays validation errors on form submit', async () => {
    renderWithQueryClient(<Page />);

    const submitButton = screen.getByText(/Save & Next/i);

    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/Module Name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Module Heading is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Module Description is required/i)).toBeInTheDocument();
    });
  });

  test('submits the form without validation errors', async () => {
    renderWithQueryClient(<Page />);

    const moduleNameInput = screen.getByLabelText(/Module Name/i);
    const moduleHeadingInput = screen.getByLabelText(/Module Heading/i);
    const moduleDescriptionInput = screen.getByLabelText(/Module Description/i);
    const submitButton = screen.getByText(/Save & Next/i);

    fireEvent.change(moduleNameInput, { target: { value: 'Test Module' } });
    fireEvent.change(moduleHeadingInput, { target: { value: 'Test Heading' } });
    fireEvent.change(moduleDescriptionInput, { target: { value: 'Test Description' } });

    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockUseAddModule.mutateAsync).toHaveBeenCalled();
    });
  });

  test('navigates to the next screen after successful form submission', async () => {
    mockUseAddModule.mutateAsync.mockResolvedValueOnce({ module_id: 1 });

    renderWithQueryClient(<Page />);

    const moduleNameInput = screen.getByLabelText(/Module Name/i);
    const moduleHeadingInput = screen.getByLabelText(/Module Heading/i);
    const moduleDescriptionInput = screen.getByLabelText(/Module Description/i);
    const submitButton = screen.getByText(/Save & Next/i);

    fireEvent.change(moduleNameInput, { target: { value: 'Test Module' } });
    fireEvent.change(moduleHeadingInput, { target: { value: 'Test Heading' } });
    fireEvent.change(moduleDescriptionInput, { target: { value: 'Test Description' } });

    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockUseAddModule.mutateAsync).toHaveBeenCalled();
    });

      });
});






/// AddContent.test.js ///

import AddContent from '@/components/admin/module/AddContent';

// Mock dependencies and hooks
jest.mock('@/hook/content/useGetAllContentInTable', () => ({
  useGetAllContentInTable: () => ({
    data: [
      {
        content_id: 1,
        content_name: 'Sample Content',
        content_description: 'Sample Description',
        topics: 'Sample Topic',
        created_by: 'Sample Creator',
        file_type: 'pdf',
        file_size: 1024 // Sample size in bytes
      }
    ],
    isLoading: false,
    isError: false
  })
}));

jest.mock('@/hook/admin/module/useAddModuleContent', () => ({
  useAddModuleContent: jest.fn(() => ({
    mutate: jest.fn() // Mocked mutate function
  }))
}));




/// CreateContentAssessmentModal.test.js ///

import CreateContentAssessmentModal from '@/components/admin/module/CreateContentAssessmentModal'; // Adjust the path as per your project structure

// Mock the onClose function
const mockOnClose = jest.fn();

// Test suite for CreateContentAssessmentModal component
describe('CreateContentAssessmentModal', () => {
  beforeEach(() => {
    // Render the component before each test
    render(<CreateContentAssessmentModal onClose={mockOnClose} />);
  });

  it('renders the modal with success message and button', () => {
    // Assert that the modal content is rendered
    const successMessage = screen.getByText('Module Added Successfully');
    expect(successMessage).toBeInTheDocument();

    // Assert that the Ok button is rendered
    const okButton = screen.getByText('Ok');
    expect(okButton).toBeInTheDocument();
  });

  it('calls onClose function when Ok button is clicked', () => {
    // Mock button click on Ok button
    const okButton = screen.getByText('Ok');
    fireEvent.click(okButton);

    // Log for debugging
    
  });
});


/// AddAssessment.test.js ///

import AddAssessment from '@/components/admin/module/AddAssessment';

// Mocking modules
jest.mock('@/hook/assessments/useGetAllAssessments'); // Mocking useGetAllAssessments

jest.mock('@/hook/admin/module/useAddModuleAssessment', () => ({
  useAddModuleAssessment: jest.fn(),
}));

describe('AddAssessment component', () => {
  beforeEach(() => {
    // Reset mock implementation before each test
    useGetAllAssessments.mockReset();
    useAddModuleAssessment.mockReset();
  });

  it('renders table with assessment data', async () => {
    // Mock data for useGetAllAssessments
    useGetAllAssessments.mockReturnValue({
      data: [
        {
          assessment_id: 1,
          assessment_name: 'Mock Assessment 1',
          instructions: 'Instructions for Mock Assessment 1',
          total_time_allowed: 3600,
          total_marks: 100,
          source: 'Mock Source',
          assessment_evaluation_strategy: 'Mock Evaluation Strategy',
        },
        {
          assessment_id: 2,
          assessment_name: 'Mock Assessment 2',
          instructions: 'Instructions for Mock Assessment 2',
          total_time_allowed: 1800,
          total_marks: 50,
          source: 'Mock Source',
          assessment_evaluation_strategy: 'Mock Evaluation Strategy',
        },
      ],
      isLoading: false,
      isError: false,
    });

    // Mock useAddModuleAssessment if needed
    useAddModuleAssessment.mockReturnValue({
      mutate: jest.fn(),
    });

    render(<AddAssessment moduleIdUpcoming={123} />);

    // Assertions as per your test requirements
    // Example: Check if table headers and assessment data are rendered
    expect(screen.getByText('Assessment Name')).toBeInTheDocument();
    expect(screen.getByText('Instructions')).toBeInTheDocument();
    expect(screen.getByText('Total Time Allowed')).toBeInTheDocument();
    expect(screen.getByText('Total Marks')).toBeInTheDocument();
    expect(screen.getByText('Source')).toBeInTheDocument();
    expect(screen.getByText('Assessment Evaluation Strategy')).toBeInTheDocument();

    // Example: Simulate checkbox click and test functionality
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[0]); // Click the first checkbox

    // Check if the button is enabled after clicking on a checkbox
    const addButton = screen.getByText('Add Assessment & Next');
    expect(addButton).toBeEnabled();
   
  });
});
