import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import AllModule from '@/app/admin/module/allmodule';  // Ensure this is named correctly
import ModuleTable from '@/components/admin/module/ModuleTable';

// Mock the ModuleTable component
jest.mock('@/components/admin/module/ModuleTable', () => {
  const MockedModuleTable = () => <div data-testid="module-table">Module Table</div>;
  MockedModuleTable.displayName = "ModuleTable";  // Set display name
  return MockedModuleTable;
});

describe('AllModule Component', () => {
  it('renders the ModuleTable component', () => {
    const { container } = render(<AllModule />);
  });
});
