import React from 'react';
import { render, screen } from '@testing-library/react';
import DashboardLayout from '../../app/admin/layout';
import Header from '@/components/Header';

jest.mock('@/components/Header', () => {
  const MockHeader = () => <div>Header Component</div>;
  MockHeader.displayName = 'Header';  // Add displayName to the mock
  return MockHeader;
});

describe('DashboardLayout Component', () => {
  test('renders Header component', () => {
    render(<DashboardLayout><div>Child Component</div></DashboardLayout>);

    // Check if Header component is rendered
    const headerElement = screen.getByText('Header Component');
    expect(headerElement).toBeInTheDocument();
  });

  test('renders children', () => {
    render(<DashboardLayout><div>Child Component</div></DashboardLayout>);

    // Check if children are rendered
    const childElement = screen.getByText('Child Component');
    expect(childElement).toBeInTheDocument();
  });
});
