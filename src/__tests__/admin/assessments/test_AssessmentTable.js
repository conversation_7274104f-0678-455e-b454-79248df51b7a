import React from "react";
import Page from "../../../app/admin/assessments/assessmenttable/page";
import "@testing-library/jest-dom";
import { fireEvent, render, screen } from "@testing-library/react";
import Heading from "../../../components/Heading";
import SearchBar from "../../../components/admin/SearchBar";
import Button from "../../../components/Button";
import AssessmentTable from "../../../components/admin/assessments/AssessmentTable";





describe("Assessment Table/ Assessment List Component", () => {
  test("renders Heading component", () => {
    const pgHeading = "Assessment List";
    render(<Heading pgHeading={pgHeading} />);
    const headingElement = screen.getByText(/Assessment List/i);
    expect(headingElement).toBeInTheDocument();
  });

  test("renders Search Bar Component and handleSearch", () => {
    const handleSearch = jest.fn();
    render(<SearchBar onSearch={handleSearch} />);
    const searchInput = screen.getByRole("textbox");
    fireEvent.change(searchInput, { target: { value: "test" } });
    expect(handleSearch).toHaveBeenCalledWith("test");
  });

  test("render the Link with Button tag", () => {
    const btnName = "Add Assessment";
    render(<Button btnName={btnName} />);
    const searchBox = screen.getByText(/Add Assessment/i);
    expect(searchBox).toBeInTheDocument();
  });

  // test('renders the Assessment Table component', () => {

  //   const searchedAssessment = jest.fn()
  //   const handleDelete = jest.fn()
  //   render(<AssessmentTable AssessmentData={searchedAssessment} onDelete={handleDelete}  />)
  //   const deleteBtn = screen.getByRole("button")
  //   fireEvent.click(deleteBtn)
  //   expect(handleDelete).toBeCalled()
    
  // })

  

});

