import { render, screen } from "@testing-library/react";
import AllAssessmentAttemptsPage from "../../../app/admin/assessments/allassessmentsattempts/page";
import "@testing-library/jest-dom";
import userEvent from "@testing-library/user-event";
import { useGetAllAttemptsGroupByAssessments } from "@/hook/assessment_attempts/useGetAllAttemptsGroupByAssessments";
import { saveAs } from "file-saver";

// Mock the custom hook and file-saver
jest.mock("@/hook/assessment_attempts/useGetAllAttemptsGroupByAssessments", () => ({
  useGetAllAttemptsGroupByAssessments: jest.fn(),
}));

jest.mock("file-saver", () => ({
  saveAs: jest.fn(),
}));

describe("AllAssessmentAttemptsPage Component", () => {
  const mockData = {
    assessment1: {
      assessment_info: {
        assessment_name: "Assessment 1",
        instructions: "Instructions 1",
        total_marks: 100,
        assessment_evaluation_strategy: "Strategy 1",
        total_time_allowed: 3600, // 1 hour in seconds
        assessment_id: "1",
        source: "Source 1",
      },
      attempts: [
        {
          user_assessment_attempt_id: "123",
          user_id: "user1",
          user_full_name: "John Doe",
          email: "<EMAIL>",
          user_assessment_id: "user_assess_1",
          attempt_total_time: 1800, // 30 minutes in seconds
          attempt_total: 85,
          attempt_evaluation: "Pass",
          attempt_start_date: "2024-09-19T08:00:00Z",
          attempt_end_date: "2024-09-19T08:30:00Z",
        },
      ],
    },
  };

  beforeEach(() => {
    useGetAllAttemptsGroupByAssessments.mockReturnValue({ data: mockData });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with headings and disclosure buttons", () => {
    render(<AllAssessmentAttemptsPage />);

    // Check if heading is rendered
    expect(screen.getByText("All Assessment Attempt")).toBeInTheDocument();

    // Check if the disclosure button for assessment is rendered
    expect(screen.getByText("assessment1")).toBeInTheDocument();
  });

  it("toggles the disclosure panel on button click", () => {
    render(<AllAssessmentAttemptsPage />);

    // Open the disclosure panel
    const viewAttemptsButton = screen.getByText("View Attempts");
    userEvent.click(viewAttemptsButton);

    // Check if the attempts table is visible
    expect(screen.getByRole("table")).toBeInTheDocument();

    // Hide the disclosure panel
    const hideAttemptsButton = screen.getByText("Hide Attempts");
    userEvent.click(hideAttemptsButton);

    // Check if the attempts table is hidden
    expect(screen.queryByRole("table")).not.toBeInTheDocument();
  });

  it("downloads the data when download button is clicked", async () => {
    render(<AllAssessmentAttemptsPage />);

    // Mock the ExcelJS workbook creation and file download
    const handleDownloadButton = screen.getByText("Download");
    userEvent.click(handleDownloadButton);

    // Check if file-saver saveAs function is called
    expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), "UserAttempts.xlsx");
  });

  it("shows 'No Data Available' message when no attempts are present", () => {
    useGetAllAttemptsGroupByAssessments.mockReturnValue({ data: null });
    render(<AllAssessmentAttemptsPage />);

    // Check if 'No Data Available' component is rendered
    expect(screen.getByText("No Data Available")).toBeInTheDocument();
    expect(screen.getByText("There are currently no assessments to display. Please check back later")).toBeInTheDocument();
  });
});
