import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import AdminAssessmentPage from '../../../app/admin/assessments/page';
import { useGetAllAssessments } from '@/hook/assessments/useGetAllAssessments';
import Heading from "@/components/ui/Heading";

// Mock the useGetAllAssessments hook
jest.mock('@/hook/assessments/useGetAllAssessments', () => ({
  useGetAllAssessments: jest.fn(),
}));

// Mock the Heading component
jest.mock('@/components/ui/Heading', () => {
  const HeadingComponent = ({ pgHeading }) => <h2>{pgHeading}</h2>;
  HeadingComponent.displayName = 'Heading'; // Set the displayName for the mock
  return HeadingComponent;
});

// Mock the Nothing component
jest.mock('@/components/ui/Nothing', () => {
  const NothingComponent = ({ title, para, btnName, BtnIcon, onClickFunctionForBtn }) => (
    <div>
      <h2>{title}</h2>
      <p>{para}</p>
      <button onClick={onClickFunctionForBtn}>
        <BtnIcon />
        {btnName}
      </button>
    </div>
  );
  NothingComponent.displayName = 'Nothing'; // Set the displayName for the mock
  return NothingComponent;
});

describe('AdminAssessmentPage', () => {
  it('displays loading state initially', () => {
    useGetAllAssessments.mockReturnValue({
      data: [],
      isLoading: true,
      isError: false,
    });

    render(<AdminAssessmentPage />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('displays error state if there is an error fetching assessments', () => {
    useGetAllAssessments.mockReturnValue({
      data: [],
      isLoading: false,
      isError: true,
    });

    render(<AdminAssessmentPage />);

    expect(screen.getByText('Error fetching assessments')).toBeInTheDocument();
  });

  it('renders heading component', () => {
    const pgHeading = "Assign Assessment";
    render(<Heading pgHeading={pgHeading} />);
    const headingElement = screen.getByText(pgHeading);
    expect(headingElement).toBeInTheDocument();
  });

  it('displays assessments if data is successfully fetched', async () => {
    useGetAllAssessments.mockReturnValue({
      data: [
        {
          assessment_id: '1',
          assessment_name: 'Assessment 1',
          instructions: 'Follow these instructions',
          total_time_allowed: 3600,
          total_marks: 100,
          source: 'Source 1',
        },
      ],
      isLoading: false,
      isError: false,
    });

    render(<AdminAssessmentPage />);

    // Click the Disclosure button to open the panel
    fireEvent.click(screen.getByText('Assessment 1'));

    await waitFor(() => {
      expect(screen.getByText('Instructions: Follow these instructions')).toBeInTheDocument();
    });

    expect(screen.getByText('Total time allowed: 60.00 mins')).toBeInTheDocument();
    expect(screen.getByText('Total marks: 100')).toBeInTheDocument();
    expect(screen.getByText('Source: Source 1')).toBeInTheDocument();
  });

  it('renders Nothing component to display "No Assessments Available" message if no assessments are fetched', () => {
    useGetAllAssessments.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });

    render(<AdminAssessmentPage />);

    expect(screen.getByText('No Assessments Available')).toBeInTheDocument();
    expect(screen.getByText('There are currently no assessments to display. Please check back later, or consider creating new assessments.')).toBeInTheDocument();
    expect(screen.getByText('Create Assessment')).toBeInTheDocument();
  });

  it('handles button click correctly', () => {
    const mockWindowLocation = jest.fn();
    delete window.location;
    window.location = { assign: mockWindowLocation };

    useGetAllAssessments.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });

    render(<AdminAssessmentPage />);

    const createButton = screen.getByText('Create Assessment');
    expect(createButton).toBeInTheDocument();

    fireEvent.click(createButton);
    expect(mockWindowLocation).toHaveBeenCalledWith('/admin/assessments/createassessments');
  });
});
