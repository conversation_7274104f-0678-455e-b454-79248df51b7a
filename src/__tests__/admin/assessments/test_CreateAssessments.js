import React from "react";
import Page from "../../../app/admin/assessments/createassessments/page";
import "@testing-library/jest-dom";
import { QueryClient, QueryClientProvider } from "react-query"; // Import QueryClient and QueryClientProvider
import { useRouter } from "next/navigation";

import { render, screen } from "@testing-library/react";
import Heading from "../../../components/Heading";

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

describe("Create Assessment Component", () => {
  // Create a new instance of QueryClient
  const queryClient = new QueryClient();

  test("renders Heading component", () => {
    render(<Heading pgHeading="Create Assessment" />);
    const headingElement = screen.getByText(/Create Assessment/i);
    expect(headingElement).toBeInTheDocument();
  });

});
