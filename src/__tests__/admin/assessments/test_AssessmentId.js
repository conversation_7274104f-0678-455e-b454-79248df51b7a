import React from "react";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import AdminSingleAssessmentPage from "../../../app/admin/assessments/[assessmentid]/page";
import { useGetUsersForAssessment } from "@/hook/assessments/useGetUsersForAssessment";
import { useGetAllUserIds } from "@/hook/admin/useGetAllUserIds";
import { useGetAllUserExt } from "@/hook/admin/useGetAllUserExt";
import { useParams } from "next/navigation";

// Mock the hooks
jest.mock("@/hook/assessments/useGetUsersForAssessment");
jest.mock("@/hook/admin/useGetAllUserIds");
jest.mock("@/hook/admin/useGetAllUserExt");
jest.mock("next/navigation", () => ({
  useParams: jest.fn(),
}));

// Mock the FormComponent
jest.mock("@/components/admin/assessments/FormComponent", () => {
  const MockedFormComponent = () => <div>Mocked Form Component</div>;
  MockedFormComponent.displayName = "FormComponent"; // Add displayName
  return MockedFormComponent;
});




describe("AdminSingleAssessmentPage", () => {
  beforeEach(() => {
    useParams.mockReturnValue({ assessmentid: "1" });

    useGetUsersForAssessment.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });
    useGetAllUserIds.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });
    useGetAllUserExt.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });
  });

  it("renders loading state correctly", () => {
    useGetUsersForAssessment.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });
    useGetAllUserIds.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });

    render(<AdminSingleAssessmentPage />);

    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("renders error state correctly", () => {
    useGetUsersForAssessment.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });
    useGetAllUserIds.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });

    render(<AdminSingleAssessmentPage />);

    expect(screen.getByText("Error fetching other data")).toBeInTheDocument();
  });

  it("renders the component with data correctly", async () => {
    useGetUsersForAssessment.mockReturnValue({
      data: [
        {
          user_id: 1,
          user_assessment_id: 1,
          assessment_id: 1,
          start_date: "2023-06-23T10:00:00Z",
          end_date: "2023-06-23T11:00:00Z",
          user_score: 95,
          total_time: 3600,
          total_attempts: 3,
          max_attempts: 5,
          last_attempt_date: "2023-06-23T11:00:00Z",
        },
      ],
      isLoading: false,
      isError: false,
    });

    useGetAllUserIds.mockReturnValue({
      data: [1, 2, 3],
      isLoading: false,
      isError: false,
    });

    useGetAllUserExt.mockReturnValue({
      data: [
        {
          user_id: 1,
          user_full_name: "John Doe",
        },
      ],
      isLoading: false,
      isError: false,
    });

    render(<AdminSingleAssessmentPage />);

    await waitFor(() =>
      expect(screen.getByText("User Details")).toBeInTheDocument()
    );

    // Use getAllByText to avoid ambiguity
    expect(screen.getAllByText("1")).toHaveLength(2);
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("95")).toBeInTheDocument();
  });

  it("handles button clicks and form updates", async () => {
    render(<AdminSingleAssessmentPage />);

    const addButton = screen.getByText("Add User");
    const updateButton = screen.getByText("Update User");

    expect(addButton).toBeInTheDocument();
    expect(updateButton).toBeInTheDocument();

    fireEvent.click(addButton);
    expect(addButton).toHaveClass("bg-cyan-800");

    fireEvent.click(updateButton);
    expect(updateButton).toHaveClass("bg-cyan-800");
  });

  it("renders FormComponent correctly", () => {
    render(<AdminSingleAssessmentPage />);

    expect(screen.getByText("Mocked Form Component")).toBeInTheDocument();
  });

  it("renders thead", () => {
    render(
      <table>
        <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
          <tr>
            <th scope="col" className="px-6 py-3">
              User ID
            </th>
            <th scope="col" className="px-6 py-3">
              Name
            </th>
            <th scope="col" className="px-6 py-3">
              Assessment ID
            </th>
            <th scope="col" className="px-6 py-3">
              Start Date
            </th>
            <th scope="col" className="px-6 py-3">
              End Date
            </th>
            <th scope="col" className="px-6 py-3">
              User Score
            </th>
            <th scope="col" className="px-6 py-3">
              Total Time Allowed
            </th>
            <th scope="col" className="px-6 py-3">
              Total Attempts
            </th>
            <th scope="col" className="px-6 py-3">
              Max Attempts
            </th>
            <th scope="col" className="px-6 py-3">
              Last Attempt Date
            </th>
          </tr>
        </thead>
      </table>
    );

    expect(screen.getByText("User ID")).toBeInTheDocument();
    expect(screen.getByText("Name")).toBeInTheDocument();
    expect(screen.getByText("Assessment ID")).toBeInTheDocument();
    expect(screen.getByText("Start Date")).toBeInTheDocument();
    expect(screen.getByText("End Date")).toBeInTheDocument();
    expect(screen.getByText("User Score")).toBeInTheDocument();
    expect(screen.getByText("Total Time Allowed")).toBeInTheDocument();
    expect(screen.getByText("Total Attempts")).toBeInTheDocument();
    expect(screen.getByText("Max Attempts")).toBeInTheDocument();
    expect(screen.getByText("Last Attempt Date")).toBeInTheDocument();
  });
});
