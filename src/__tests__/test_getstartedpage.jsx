// Import necessary testing utilities
import React from 'react';
import { render, screen } from '@testing-library/react';
import { motion } from 'framer-motion'; // Import motion from framer-motion
import Home from '../app/page'; // Adjust import path based on your project structure


describe("LoginPage", () => {
it("test the heading of login page", () => {
  render(<Home />);

  const loginHeadings = screen.getByText(/About Skilling/i);

  expect(loginHeadings).toBeInTheDocument();
});

//Reach out to <NAME_EMAIL>
//Get Started
});