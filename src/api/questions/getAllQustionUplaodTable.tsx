import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";
import { UploadQuestionTable } from "@/types/LMSTypes";

export function getAllQuestionUplaodTable() {
  const user = getUser();

  const url = new URL(`${API_URL}questions/search_question`);

  console.log("URL: ", url.toString());

  return fetch(url.toString(), {
    mode: "cors",
    method: "GET",
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`,
    },
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}