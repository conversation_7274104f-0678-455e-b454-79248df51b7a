import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";
import { FilterSearchReturn, filterSearch } from "@/types/LMSTypes";

export function getAllFilteredQuestion(filterSearch: filterSearch) {
  const {
    question_string,
    question_type,
    difficulty_level,
    topic,
    question_source,
    question_category
  } = filterSearch;
  const user = getUser();

  const url = new URL(`${API_URL}questions/search_question`);
  url.searchParams.append("question_string", question_string);
  url.searchParams.append("topic", topic);
  url.searchParams.append("question_source", question_source);
  url.searchParams.append("question_type", question_type);
  url.searchParams.append("question_category", question_category);
  url.searchParams.append("difficulty_level", difficulty_level.toString());


  console.log("URL: ", url.toString());

  return fetch(url.toString(), {
    mode: "cors",
    method: "GET",
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`,
    },
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}