import { API_URL } from '@/utils/constants'
import { QuestionsList } from '@/types/LMSTypes';

export function fetchQuestions(assessment_id: number) {
  return (fetch("https://fierce-reef-08646-bad06c62edc6.herokuapp.com/ "+ "assessment_questions/" + assessment_id, {
    mode: 'cors',
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json'
    }
  })).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
} 