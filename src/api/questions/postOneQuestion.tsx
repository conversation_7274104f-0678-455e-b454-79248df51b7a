// Import necessary utilities and types
import { getUser } from "@/api/user.localStorage";
import {  One_Questions } from "@/types/LMSTypes";
import { API_URL } from "@/utils/constants";
import { useGetAllFilteredQuestion } from "@/hook/questions/useGetAllFilteredQuestion";

export function postOneQuestion(OneQuestionsData : One_Questions[] ) {
  const user = getUser();

  const body = JSON.stringify(OneQuestionsData);
  console.log("body in postOneQuestion:", body); // Ensure this logs expected data

  const url = `${API_URL}questions/questions`;
  console.log("URL : ",url);

  return fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`,
    },
    body: body,
  }).then((res) => {
    if (!res.ok) {
      throw new Error("Failed to add question");
    }else{    
      return res.json();
    }
    
  });
}
