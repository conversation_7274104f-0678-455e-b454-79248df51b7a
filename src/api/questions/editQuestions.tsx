
import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";
import { Edit_Questions } from "@/types/LMSTypes";

export function editQuestions(question_id: number[], questionValue: Edit_Questions) {
  const { question, question_type, question_difficulty, option1, option2, option3, option4, question_source, picture, answer_option, answer_text, marks, answer_explanation, topics_string, default_text,question_category } = questionValue;
  const user = getUser();

  const queryParams = {
    question,
    question_type,
    question_difficulty: question_difficulty.toString(),
    option1,
    option2,
    option3,
    option4,
    question_source,
    // picture,
    answer_option: answer_option.toString(),
    answer_text,
    marks: marks.toString(),
    answer_explanation,
    topics_string,
    default_text,
    question_category
  };
  

  // Construct query parameters
  const queryParamsString = new URLSearchParams(queryParams).toString();

  // Construct the URL with query parameters
  const url = `${API_URL}questions/edit_question/${question_id}?${queryParamsString}`;

  return fetch(url, {
    mode: 'cors',
    method: "POST",
    body: picture,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}