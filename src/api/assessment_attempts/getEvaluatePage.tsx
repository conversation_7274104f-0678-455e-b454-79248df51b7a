import { API_URL } from "@/utils/constants";
import { AssessmentSubmittedAnswers } from "@/types/LMSTypes"
import { getUser } from "../user.localStorage";


export function getEvaluatePage(user_assessment_attempt_id: number) {
  const user = getUser();
  return (fetch(API_URL + `assessment_attempts/get_all_submitted_answers/${user_assessment_attempt_id}`, {
    mode: 'cors',
    method: "GET",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
  })).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}
