import { API_URL } from "@/utils/constants";
import { SubmitAttempt } from "@/types/LMSTypes"
import { getUser } from "../user.localStorage";
export function submitAssessmentAnswers(user_assessment_attempt_id:number, answers:SubmitAttempt[]) {
  const user = getUser();
  const url = API_URL + `assessment_attempts/submit_attempt/${user_assessment_attempt_id}`
  return (fetch(url, {
    mode: 'cors',
    method: "POST",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
    body:JSON.stringify(answers)
  })).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}
