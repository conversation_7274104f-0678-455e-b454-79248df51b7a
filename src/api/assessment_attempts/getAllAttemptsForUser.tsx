import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";
import { UserAssessmentAttempt } from "@/types/LMSTypes"



export function getAllAttemptsForUser(assessment_id: number, user_id: number) {
  
  const user = getUser(); 
  
  const url = new URL(`${API_URL}assessment_attempts/get_all_attempts/${assessment_id}/${user_id}`);


  return (fetch(url.toString(), {
    mode: 'cors',
    method: "GET",
    headers: { 
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
  })).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}
