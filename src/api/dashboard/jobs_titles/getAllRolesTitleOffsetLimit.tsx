import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export function getAllrolesTitleOffsetLimit(offset: number, limit: number) {
    const user = getUser();
    const url = `${API_URL}user/job-titles?offset=${offset}&limit=${limit}`; // Dynamically adding offset and limit to the URL

    return fetch(url, {
        mode: 'cors',
        method: "GET",
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.token}`
        },
    }).then(response => {
        if (response.status === 401) {
            window.location.href = '/login'; 
            throw new Error("Unauthorized");
        }
        return response.json();
    }) as Promise<any>;
}
