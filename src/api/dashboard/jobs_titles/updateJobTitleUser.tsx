import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export function updateJobTitleUser(jobTitleId: number) {
    const user = getUser();
  
    // Dynamically form the URL based on the job title
    const url = `${API_URL}user/job-titles/${jobTitleId}`;
  
    return fetch(url, {
      method: 'PUT',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',  // Important for PUT requests with JSON payloads
        'Authorization': `Bearer ${user?.token}`,
      },
    })
    .then(res => {
      if (!res.ok) {
        if (res.status === 401) {
          window.location.href = '/login';
        }
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      return res.json();  // Optionally, handle the response here (if necessary)
    })
    .catch(error => {
      console.error("Error updating user job title:", error);
      throw error;
    });
  }
  
