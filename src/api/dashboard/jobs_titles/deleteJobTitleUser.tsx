import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export function deleteJobTitleUser(jobTitleId: number) {
    const user = getUser();
  
    const url = `${API_URL}user/job-title/${jobTitleId}/current`;
  
    return fetch(url, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${user?.token}`,
      },
    })
    .then(res => {
      if (!res.ok) {
        if (res.status === 401) {
          window.location.href = '/login';
        }
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      return res.json();  // Optionally, handle the response here (if necessary)
    })
    .catch(error => {
      console.error("Error deleting user job title:", error);
      throw error;
    });
  }
  
