import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export function postJobTitleUser(jobTitleId: number) {
  const user = getUser();

  // Dynamically form the URL based on the job title and target
  const url = `${API_URL}user/job-titles/${jobTitleId}/current`;

  return fetch(url, {
    mode: 'cors',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
  })
  .then(res => {
    if (!res.ok) {
      if (res.status === 401) {
        window.location.href = '/login';
      }
      throw new Error(`HTTP error! Status: ${res.status}`);
    }
    return res.json();
  })
  .catch(error => {
    // Handle any errors
    console.error("Error posting user progress:", error);
    // Re-throwing the error to be handled by the caller
    throw error;
  });
}
