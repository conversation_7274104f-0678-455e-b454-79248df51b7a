import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export function getAllrolesTitle() {
    const user = getUser();
    const url = API_URL + `user/me/job-titles`
    return (fetch(url, {
        mode: 'cors',
        method: "GET",
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.token}`
        },
    }).then(response => {
        if (response.status === 401) {
   
            window.location.href = '/login'; 
            throw new Error("Unauthorizedddddddddd"); 
          }
        return response.json();}
    )) as Promise<any>;
}