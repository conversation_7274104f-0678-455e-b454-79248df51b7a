import { API_URL } from "@/utils/constants";
import { Assessments } from "@/types/LMSTypes"
import { getUser } from "../user.localStorage";


export function getAllAttemptedAssessmentOfuser(offset: number, limit: number) {
  const user = getUser();
  const url = API_URL+`assessments/get_all_assessments?offset=${offset}&limit=${limit}`
  return (fetch(url, {
    mode: 'cors',
    method: "GET",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
}))
}