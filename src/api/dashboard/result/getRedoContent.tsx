import { API_URL } from "@/utils/constants";
import { getUser } from "../../user.localStorage";


export function getRedoContent( group_id: number) {
    const user = getUser();
    const url = API_URL + `assessments/redo_topics/${group_id}`
    console.log("url for redo content", url)
    return (fetch(url, {
        mode: 'cors',
        method: "GET",
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.token}`
        },
    }).then(response => {
        if (response.status === 401) {
   
            window.location.href = '/login'; 
            throw new Error("Unauthorizedddddddddd"); 
          }
        return response.json();}
    )) as Promise<any>;
}