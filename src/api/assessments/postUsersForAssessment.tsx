import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";
import { PostUserAssessmentsOptions } from "@/types/LMSTypes";

export function postUsersForAssessment(assessment_id: number, options: PostUserAssessmentsOptions) {
  const { userIds, startDate, endDate, maxAttempts, totalTimeAllowed } = options;
  const user = getUser();

  // Convert array of user IDs to a JSON string
  const body = JSON.stringify(userIds);
  // Manually construct the query string
  const queryParams = `start_date=${encodeURIComponent(startDate.toISOString())}` +
    `&end_date=${encodeURIComponent(endDate.toISOString())}` +
    `&max_attempts=${Number(options.maxAttempts)}` +
    `&total_time_allowed=${Number(options.totalTimeAllowed)}`;

  const apiUrl = API_URL.endsWith('/') ? API_URL.slice(0, -1) : API_URL;

  const url = `${apiUrl}/assessments/user_assessments/${assessment_id}?${queryParams}`;

  return fetch(url, {
    mode: 'cors',
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}

