import { API_URL } from "@/utils/constants";
import { UserDetails } from "@/types/LMSTypes"
import { getUser } from "../user.localStorage";
export function getUserPrompt(user_prompt: string) {

  const url = `${API_URL}questions/get_prompt_questions/${user_prompt}`;
  const user = getUser();
  return (fetch(url, {
    mode: 'cors',
    method: "GET",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
  })).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}


