import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";
import { EditUserAssessmentsOptions } from "@/types/LMSTypes";

export function editUsersForAssessment(userAssessmentIdsValue: number[], userValueChange: EditUserAssessmentsOptions) {
  const { userAssessmentId, startDate, endDate, maxAttempts, totalTimeAllowed } = userValueChange;
  const user = getUser();

  // Convert array of user IDs to a JSON string
  const body = JSON.stringify(userAssessmentIdsValue);

  const queryParams = new URLSearchParams({
    start_date: startDate.toISOString(),
    end_date: endDate.toISOString(),
    max_attempts: (userValueChange.maxAttempts),
    total_time_allowed: (userValueChange.totalTimeAllowed),
  });

  const apiUrl = API_URL.endsWith('/') ? API_URL.slice(0, -1) : API_URL;



  return fetch(url, {
    mode: 'cors',
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}

