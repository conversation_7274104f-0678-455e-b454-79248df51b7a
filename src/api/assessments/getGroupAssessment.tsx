import { API_URL } from "@/utils/constants";
import { Assessments } from "@/types/LMSTypes"
import { getUser } from "../user.localStorage";


export function getGroupAssessment() {
  const user = getUser();
  
  return (fetch(API_URL + "admin/group/assessment/all_group_assessment", {
    mode: 'cors',
    method: "GET",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bear<PERSON> ${user?.token}`
    },
  })).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}
