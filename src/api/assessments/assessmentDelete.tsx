import { getUser } from "@/api/user.localStorage";
import { API_URL } from "@/utils/constants";

export function assessmentDelete(assessment_id: number) {
    const user = getUser();
    
    const url = `${API_URL}assessments/${assessment_id}`;
    return fetch(url, {
      mode: 'cors',
      method: "DELETE",
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user?.token}`,
      },
    
    })
    .then((res) => {
      if (res.status === 401) {
     
        window.location.href = '/login'; 
        throw new Error("Unauthorized"); 
      }
     
      return res.json();
    })
    .catch((error) => {
      console.error('Delete assessment error:', error);
      throw error;
    }) as Promise<any>;
  }