import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";
import { AssessmentForSelectQuestion } from "@/types/LMSTypes";

export function postCreateAssesmentWithQuestion( options: AssessmentForSelectQuestion) {

  const user = getUser();

  // Convert array of user IDs to a JSON string
  const body = JSON.stringify(options);

  const url = `${API_URL}assessments/assessment_with_selected_questions`;

  // Perform the POST request
  return fetch(url, {
    mode: "cors",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`, // Assuming the user object contains a token
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}
