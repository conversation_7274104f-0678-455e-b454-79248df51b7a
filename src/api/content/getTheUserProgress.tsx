import { API_URL } from "@/utils/constants";
import { getUserProgress } from "@/types/LMSTypes"
import { getUser } from "../user.localStorage";



export function getTheUserProgress(module_id: number, group_id: number, user_id: number, content_id: number) {
  const user = getUser();
  
  return (fetch(API_URL + `modules/progress/${user_id}/${group_id}/${module_id}/${content_id}`, {
    mode: 'cors',
    method: "GET",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
  })).then((res) => {
    if (res.status === 401) {

      window.location.href = '/login';
      throw new Error("Unauthorizedddddddddd");
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}
