import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";

export function getContent(content_id) {
    const user = getUser();
    const url = `${API_URL}contents/file_content/${content_id}`;

    return fetch(url, {
      mode: 'cors',
      method: "GET",
      headers: {
        Authorization: `Bearer ${user?.token}`,
      },
    })
      .then((res) => {
        if (res.status === 401) {
          window.location.href = '/login';
          throw new Error("Unauthorized");
        }
        // Use blob() for binary data like files
        return res.blob();  // Return the blob data
      })
      .then((data) => {
        if (data instanceof Blob) {
          return URL.createObjectURL(data); // Create object URL for files
        }
        return data;
      })
      .catch((error) => {
        console.error("Error fetching content:", error);
        throw error;
      });
}