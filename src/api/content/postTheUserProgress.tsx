import { API_URL } from "@/utils/constants";
import { PostUserProgress } from "@/types/LMSTypes";
import { getUser } from "../user.localStorage";

export function postTheUserProgress(data: PostUserProgress) {
  const user = getUser();
  const body = JSON.stringify(data);

  console.log("saving progres... bodyw",body)

  return fetch(`${API_URL}modules/progress`, {
    mode: 'cors',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
    body
  })
  .then(res => {
    if (!res.ok) {
      if (res.status === 401) {
        window.location.href = '/login';
      }
      throw new Error(`HTTP error! Status: ${res.status}`);
    }
    return res.json();
  })
  .catch(error => {
    // Handle any errors
    console.error("Error posting user progress:", error);
    // Re-throwing the error to be handled by the caller
    throw error;
  });
}
