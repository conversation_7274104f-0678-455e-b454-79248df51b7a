import { API_URL } from '@/utils/constants'
import { getUser } from "../user.localStorage";
import { ContentTable } from '@/types/LMSTypes';

export function getAllContentInTable() {

  const user = getUser();
  const url = new URL(`${API_URL}contents/all_content`);

  // Return the fetch promise so that it can be used by the caller of this function
  return fetch(url.toString(), {
    mode: "cors",
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      Authorization: `Bearer ${user?.token}`,
    },
  })
    .then(response => {
      if (response.status === 401) {
   
        window.location.href = '/login'; 
        throw new Error("Unauthorizedddddddddd"); 
      }
    
      // Parse and return the JSON data from the response
      return response.json();
    })
    .catch(error => {
      // Log the error and re-throw it to be handled by the caller of this function
      console.error('There has been a problem with your fetch operation:', error);
      throw error;
    });
}

export function deleteContent(content_id: number) {
  const user = getUser();
  const body = JSON.stringify({ content_id });
  const url = `${API_URL}contents`;
  return fetch(url, {
    mode: 'cors',
    method: "DELETE",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  })
  .then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
   
    return res.json();
  })
  .catch((error) => {
    console.error('Delete content error:', error);
    throw error;
  }) as Promise<any>;
}

// Include this function in your existing API file
export function createContent(options, file) {
  const { content_name, content_description, linkPath, topics, compression } = options;
  const user = getUser();
  console.log("2linkPath",linkPath)

  const formData = new FormData();
  if (file) {
    formData.append('file', file);
  }

  const queryParams = new URLSearchParams({
    content_name: content_name,
    content_description: content_description,
    link_path: linkPath,
    topics: topics,
    compression: compression,
  }).toString();

  const url = `${API_URL}contents/?${queryParams}`;

  return fetch(url, {
    mode: 'cors',
    method: "POST",
    headers: {
      Authorization: `Bearer ${user?.token}`,
    },
    body: file ? formData : undefined,  // Only send formData if file is present
  })
  .then(response => {
    if (!response.ok && response.status === 401) {
      window.location.href = '/login';
      throw new Error(`Network response was not ok, status: ${response.status}`);
    }
    return response.json();
  })
  .catch(error => {
    throw error;
  });
}
