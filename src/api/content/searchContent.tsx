import { API_URL } from '@/utils/constants'
import { getUser } from "../user.localStorage";
import { ContentTable } from '@/types/LMSTypes';

export function searchContent(searchText: string) {
  const user = getUser();
  const url = new URL(`${API_URL}contents/search/${searchText}`);

  // Return the fetch promise so that it can be used by the caller of this function
  return fetch(url.toString(), {
    mode: "cors",
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      Authorization: `Bearer ${user?.token}`,
    },
  })
    .then(response => {
      if (response.status === 401) {
        window.location.href = '/login'; 
        throw new Error("Unauthorizedddddddddd"); 
      }
      return response.json();
      // Parse and return the JSON data from the response
      return response.json();
    })
    .catch(error => {
      // Log the error and re-throw it to be handled by the caller of this function
      console.error('There has been a problem with your fetch operation:', error);
      throw error;
    });
}




