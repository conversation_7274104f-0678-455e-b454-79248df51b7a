import { getUser } from "@/api/user.localStorage";
import { API_URL } from "@/utils/constants";

export function getUserAttempts(user_id: number) {
    const user = getUser();
    
    const url = `${API_URL}assessment_attempts/all_attempts/${user_id}`;

  console.log("url for user id ",url)
  
    return fetch(url, {
      mode: 'cors',
      method: "GET",
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user?.token}`,
      },
    
    })
    .then((res) => {
      if (res.status === 401) {
        window.location.href = '/login'; 
        throw new Error("Unauthorized"); 
      }
     
      return res.json();
    })
    .catch((error) => {
      console.error('GET user attempts :', error);
      throw error;
    }) as Promise<any>;
  }