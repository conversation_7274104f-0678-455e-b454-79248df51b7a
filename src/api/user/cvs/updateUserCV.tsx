import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export function updateUserCV(file: File) {
    const user = getUser();
    
    const url = `${API_URL}user/cvs`;
  
    const formData = new FormData();
    formData.append('file', file, file.name); // Append the file to the FormData object
  
    return fetch(url, {
      method: 'PUT',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${user?.token}`,
      },
      body: formData,  // Send the FormData with the file
    })
    .then(res => {
      if (!res.ok) {
        if (res.status === 401) {
          window.location.href = '/login';
        }
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      return res.json();  // Optionally, handle the response here
    })
    .catch(error => {
      console.error("Error updating CV:", error);
      throw error;
    });
  }
  