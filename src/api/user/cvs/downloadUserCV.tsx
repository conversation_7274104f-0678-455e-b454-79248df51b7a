import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export function downloadUserCV(filepath: string) {
    const user = getUser();

    if (!user || !user.token) {
        throw new Error("User is not authenticated");
    }

    const url = `${API_URL}user/cvs/${filepath}`;

    return fetch(url, {
        method: 'GET',
        headers: {
            'Accept': 'application/pdf', // Adjust according to the file type
            'Authorization': `Bearer ${user.token}`,
        },
    })
    .then(res => {
        if (!res.ok) {
            if (res.status === 401) {
                window.location.href = '/login';
            }
            throw new Error(`HTTP error! Status: ${res.status}`);
        }
        return res.blob(); // Use blob for binary files
    })
    .then(blob => {
        // Create a link element, use it to download the file
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filepath; // or a more descriptive filename
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
    })
    .catch(error => {
        console.error("Error fetching user CV:", error);
        throw error;
    });
}