import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export function getUserCV() {
  const user = getUser();

  const url = `${API_URL}user/cvs`;

  return fetch(url, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Authorization': `Bearer ${user?.token}`,
    },
  })
  .then(res => {
    if (!res.ok) {
      if (res.status === 401) {
        window.location.href = '/login';
      }
      throw new Error(`HTTP error! Status: ${res.status}`);
    }
    return res.json();  // Optionally, handle the response here (e.g., returning CV data)
  })
  .catch(error => {
    console.error("Error fetching user CVs:", error);
    throw error;
  });
}
