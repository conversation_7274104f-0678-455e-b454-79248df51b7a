import { API_URL } from "@/utils/constants";
import { getUser } from "@/api/user.localStorage";

export function postUserCV(file: File) {
    const user = getUser();
  
    const url = `${API_URL}user/cvs`;
  
    const formData = new FormData();
    formData.append('file', file, file.name); // Append the file to FormData
  
    return fetch(url, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${user?.token}`,
      },
      body: formData,  // Send the FormData with the file
    })
    .then(res => {
      if (!res.ok) {
        if (res.status === 401) {
          window.location.href = '/login';
        }
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      return res.json();  // Handle the response, if necessary
    })
    .catch(error => {
      console.error("Error uploading CV:", error);
      throw error;
    });
  }
  