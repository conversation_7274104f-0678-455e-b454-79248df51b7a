import { getUser } from "@/api/user.localStorage";
import { API_URL } from "@/utils/constants";


export function getSearchFromGroupModuleInTable(group_id: number,search_text:string) {
    const user = getUser();

    const url = API_URL + `modules/group/${group_id}/${search_text}`

    return (fetch(url, {
        mode: 'cors',
        method: "GET",
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.token}`
        },
    })).then((res) => {
        if (res.status === 401) {
       
          window.location.href = '/login'; 
          throw new Error("Unauthorizedddddddddd"); 
        }
        return res.json();
      }).catch(error => {
        // Handle any errors
        console.error("Error fetching contents:", error);
        // You can choose to handle the error here or rethrow it for the caller to handle
        throw error;
      });
    }