
import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";


export function addBulkUser(uploadFile: File) { 
  const user = getUser();


  const formData = new FormData();
  formData.set('user_file', uploadFile); 

  const url = `${API_URL}admin/add_users/bulk_upload`;

  return fetch(url, {
    mode: 'cors',
    method: "POST",
    headers: {
      Authorization: `Bearer ${user?.token}`,
    },
    body:formData,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}
