import { API_URL } from "@/utils/constants";
import { getUser } from "../user.localStorage";
import { UpdateUser } from "@/types/LMSTypes";

//Update group from the backend from its ID
export function updateUser(data: UpdateUser) {
  const user = getUser();
  const body = JSON.stringify(data);
  const url = `${API_URL}admin/edit_user`;
  return fetch(url, {
    mode: 'cors',
    method: "PUT",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}
