import { API_URL } from "@/utils/constants";
import { CommentMarks } from "@/types/LMSTypes";
import { getUser } from "@/api/user.localStorage";

export function postCommentMarks(CommentMarks:CommentMarks[] , user_assessment_attempt_ext_id:number) {
   
    const user = getUser();
    const body = JSON.stringify(CommentMarks);
    const url = `${API_URL}assessment_attempts/update_manual_scores/${user_assessment_attempt_ext_id}`;

    
    return fetch(url, {
        mode: 'cors',
        method: "POST",
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.token}`,
        },
        body,
    }).then((res) => {
        if (res.status === 401) {
       
          window.location.href = '/login'; 
          throw new Error("Unauthorizedddddddddd"); 
        }
        return res.json();
      }).catch(error => {
        // Handle any errors
        console.error("Error fetching contents:", error);
        // You can choose to handle the error here or rethrow it for the caller to handle
        throw error;
      });
    }
    