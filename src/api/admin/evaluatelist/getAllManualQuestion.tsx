import { API_URL } from "@/utils/constants";
import { getUser } from "../../user.localStorage";

export function getAllManualQuestion(user_assessment_attempt_id :number) {
    const url = `${API_URL}assessment_attempts/all_manual_submitted_answers/${user_assessment_attempt_id }`;
  const user = getUser();
  return fetch(url, {
    mode: 'cors',
    method: "GET",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
  })
  .then((res) => {
    if (!res.ok) {
      if (res.status === 401) {
        window.location.href = '/login'; 
        throw new Error("Unauthorized access - please log in again.");
      }
      throw new Error('Failed to fetch data');
    }
    return res.json();
  })
  .catch((error) => {
    console.error('Fetch error:', error);
    throw error;
  });
}