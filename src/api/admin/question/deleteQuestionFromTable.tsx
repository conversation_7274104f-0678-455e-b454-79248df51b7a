import { getUser } from "@/api/user.localStorage";
import { API_URL } from "@/utils/constants";

export function deleteQuestionFromTable(question_id: number): Promise<any> {
  const user = getUser();

  const url = `${API_URL}questions/${question_id}`;

  return fetch(url, {
    mode: 'cors',
    method: "DELETE",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
  })
  .then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json().catch(() => ({ message: "Delete successful, no question returned." }));
  })
  .catch((error) => {
    console.error('Delete VS error:', error);
    throw error;
  });
}
