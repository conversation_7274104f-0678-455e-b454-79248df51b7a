import { getUser } from "@/api/user.localStorage";
import { API_URL } from "@/utils/constants";


export function getModuleAssessmentByModuleId(module_id:number) {
  const user = getUser();

  return (fetch(API_URL + `modules/assessment/${module_id}`, {
    mode: 'cors',
    method: "GET",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
  }).then((res) => res.json() 
  )) as Promise<any>;
}