import { getUser } from "@/api/user.localStorage";
import { UpdateModuleSeqContentOptions } from "@/types/LMSTypes";
import { API_URL } from "@/utils/constants";

export function putUpdateModuleSeqContent(options: UpdateModuleSeqContentOptions) {
  const user = getUser();
  const body = JSON.stringify(options);
  const url = `${API_URL}modules/content/`;


  return fetch(url, {
    mode: "cors",
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
      window.location.href = '/login';
      throw new Error("Unauthorized");
    }
    return res.json();
  });
}
