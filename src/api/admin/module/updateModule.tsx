import { API_URL } from "@/utils/constants";
import { getUser } from "../../user.localStorage";
import { UpdateModule } from "@/types/LMSTypes";

//Update group from the backend from its ID
export function updateModule(data: UpdateModule) {
  const user = getUser();
  const body = JSON.stringify(data);
  ("body for updateModule" + body)
  const url = `${API_URL}modules/`;
  ("url for updateModule" + url)
  return fetch(url, {
    mode: 'cors',
    method: "PUT",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  }).then((res) => res.json()) as Promise<any>;
}