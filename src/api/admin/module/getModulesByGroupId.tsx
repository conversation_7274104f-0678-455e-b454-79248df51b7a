import { API_URL } from "@/utils/constants";
import { UserDetails } from "@/types/LMSTypes"
import { getUser } from "../../user.localStorage";


export function getModulesByGroupId(group_id: number) {
    const user = getUser();
    const url = API_URL + `modules/group/${group_id}`
    return (fetch(url, {
        mode: 'cors',
        method: "GET",
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.token}`
        },
    })).then((res) => {
        if (res.status === 401) {
       
          window.location.href = '/login'; 
          throw new Error("Unauthorizedddddddddd"); 
        }
        return res.json();
      }).catch(error => {
        // Handle any errors
        console.error("Error fetching contents:", error);
        // You can choose to handle the error here or rethrow it for the caller to handle
        throw error;
      });
    }