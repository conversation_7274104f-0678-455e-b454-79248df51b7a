import { getUser } from "@/api/user.localStorage";
import { UpdateModuleSeqAssessmentOptions } from "@/types/LMSTypes";
import { API_URL } from "@/utils/constants";

export function putUpdateModuleSeqAssessment(options: UpdateModuleSeqAssessmentOptions) {
  const user = getUser();
  const body = JSON.stringify(options);
  const url = `${API_URL}modules/assessment/`;

  return fetch(url, {
    mode: "cors",
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
      window.location.href = '/login';
      throw new Error("Unauthorized");
    }
    return res.json();
  });
}
