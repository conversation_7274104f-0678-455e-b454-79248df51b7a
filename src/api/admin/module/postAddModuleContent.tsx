// Import necessary utilities and types
import { getUser } from "@/api/user.localStorage";
import {  AddModuleContentOptions } from "@/types/LMSTypes";
import { API_URL } from "@/utils/constants";

export function postAddModuleContent(options: AddModuleContentOptions) {

  const user = getUser();

  const body = JSON.stringify(options);

  const url = `${API_URL}modules/content/`;

  // Perform the POST request
  return fetch(url, {
    mode: "cors",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`, // Assuming the user object contains a token
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }) as Promise<any>; // You might want to replace any with a more specific type based on your API response
}
