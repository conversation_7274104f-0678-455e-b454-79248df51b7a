import { getUser } from "@/api/user.localStorage";
import { API_URL } from "@/utils/constants";

export function deleteModuleContent(
    module_id: number,
    content_id: number
): Promise<any> {

    const user = getUser();

    const url = `${API_URL}modules/content/${module_id}/${content_id}`;

    return fetch(url, {
        mode: "cors",
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${user?.token}`,
        },
    })
        .then((res) => {
            if (res.status === 401) {
   
                window.location.href = '/login'; 
                throw new Error("Unauthorizedddddddddd"); 
              }
            return res
                .json()
                .catch(() => ({ message: "Delete successful, no content returned." }));
        })
        .catch((error) => {
            console.error("Delete content error:", error);
            throw error;
        });
}
