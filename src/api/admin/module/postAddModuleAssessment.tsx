// Import necessary utilities and types
import { getUser } from "@/api/user.localStorage";
import { AddModuleAssessmentOptions } from "@/types/LMSTypes";
import { API_URL } from "@/utils/constants";

export function postAddModuleAssessment(options: AddModuleAssessmentOptions) {

  const user = getUser();

  const body = JSON.stringify(options);

  const url = `${API_URL}modules/assessment/`;

  // Perform the POST request
  return fetch(url, {
    mode: "cors",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${user?.token}`, // Assuming the user object contains a token
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}