import { API_URL } from "@/utils/constants";
import { getUser } from "../../user.localStorage";
import { AddRemoveUserToGroup } from "@/types/LMSTypes";

//Delete new group from the backend 
export function removeModuleFromGroup(moduleId: number, groupId: number) {
  const user = getUser();
  const url = `${API_URL}modules/group/${moduleId}/${groupId}`;
  return fetch(url, {
    mode: 'cors',
    method: "DELETE",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}