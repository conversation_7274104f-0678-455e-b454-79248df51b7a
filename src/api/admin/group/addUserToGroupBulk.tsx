import { API_URL } from "@/utils/constants";
import { getUser } from "../../user.localStorage";

export function addUserToGroupBulk(fileData: File) { 
  const user = getUser();

  const formData = new FormData();
  formData.append('group_users_file', fileData); 
  const url = `${API_URL}admin/group/user/bulk_upload/`;

  return fetch(url, {
    mode: 'cors',
    method: "POST",
    headers: {
      Authorization: `Bearer ${user?.token}`,
    },
    body: formData,
  }).then((res) => {
    if (res.status === 401) {
      window.location.href = '/login'; 
      throw new Error("Unauthorized");
    }
    return res.json();
  }).catch(error => {
    console.error("Error in fetch:", error);
    throw error;
  });
}
