import { API_URL } from "@/utils/constants";
import { getUser } from "../../user.localStorage";
import { AddRemoveModuleGroup } from "@/types/LMSTypes";

export function addModuleToGroup(data: AddRemoveModuleGroup) { 
  const user = getUser();
  const body = JSON.stringify(data);
  const url = `${API_URL}modules/group/`;
  return fetch(url, {
    mode: 'cors',
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}