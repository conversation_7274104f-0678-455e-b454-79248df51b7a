import { API_URL } from "@/utils/constants";
import { getUser } from "../../user.localStorage";

//Delete new group from the backend 
export function deleteGroup(group_id: number) {
  const user = getUser();
  const body = JSON.stringify(group_id);
  const url = `${API_URL}/admin/group`;
  return fetch(url, {
    mode: 'cors',
    method: "DELETE",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}