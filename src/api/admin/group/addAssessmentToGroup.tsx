import { API_URL } from "@/utils/constants";

import { AddAssessmentToGroupInput } from "@/types/LMSTypes";
import { getUser } from "../../user.localStorage";

export function addAssessmentToGroup(data: AddAssessmentToGroupInput) { 
  const user = getUser();
  // Convert array of user IDs to a JSON string
  const body = JSON.stringify(data);
  const url = `${API_URL}admin/group/assessment`;
  return fetch(url, {
    mode: 'cors',
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`,
    },
    body,
  }).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}

