import { API_URL } from "@/utils/constants";
import { UserDetails } from "@/types/LMSTypes"
import { getUser } from "../user.localStorage";


export function getAllUserExt() {
  const user = getUser();
  return (fetch(API_URL + "admin/get_all_users_ext", {
    mode: 'cors',
    method: "GET",
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${user?.token}`
    },
  })).then((res) => {
    if (res.status === 401) {
   
      window.location.href = '/login'; 
      throw new Error("Unauthorizedddddddddd"); 
    }
    return res.json();
  }).catch(error => {
    // Handle any errors
    console.error("Error fetching contents:", error);
    // You can choose to handle the error here or rethrow it for the caller to handle
    throw error;
  });
}
