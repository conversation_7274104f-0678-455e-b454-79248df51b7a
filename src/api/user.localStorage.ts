import { UserDetails } from "@/types/LMSTypes";
import Cookies from 'js-cookie';


const USER_LOCAL_STORAGE_KEY = "userDetails";

export function saveUser(user: UserDetails): void {
  // Set firstLogin to true by default if not present
  const updatedUser = {
    ...user,
    firstLogin: user.firstLogin !== undefined ? user.firstLogin : true,
  };
  
  hasAdminRole(user.roles);
  localStorage.setItem(USER_LOCAL_STORAGE_KEY, JSON.stringify(updatedUser));
}
 
export function setFirstLogin(val:boolean): void {
  const user = getUser(); // Get the user object from local storage
  if (user) {
    // Update the firstLogin property to true
    const updatedUser = { ...user, firstLogin: val };
    localStorage.setItem(USER_LOCAL_STORAGE_KEY, JSON.stringify(updatedUser)); // Save the updated user object
  }
}

export function setContentLocalStorageData(contentId, moduleId, groupId) {
  if (contentId !== undefined) localStorage.setItem('content_id', contentId);
  if (moduleId !== undefined) localStorage.setItem('module_id', moduleId);
  if (groupId !== undefined) localStorage.setItem('group_id', groupId);
}


export function getContentLocalStorageData() {
  const contentId = localStorage.getItem('content_id');
  const moduleId = localStorage.getItem('module_id');
  const groupId = localStorage.getItem('group_id');
  
  return {
    content_id: contentId,
    module_id: moduleId,
    group_id: groupId
  };
}


export function getUser(): UserDetails | undefined {
  if (typeof window !== "undefined") {
    const user = localStorage.getItem(USER_LOCAL_STORAGE_KEY);
    return user ? JSON.parse(user) : undefined;
  }
}

export function removeUser(): void {
  localStorage.removeItem(USER_LOCAL_STORAGE_KEY);
}

function hasAdminRole(roles: Array<{ ROLE: string; SCOPE: string }>): void {
  if (roles.some((role) => role.ROLE.includes("ADMIN"))){
    Cookies.set('admin', 'true')
  } else {
    Cookies.set('admin', 'false')
  }
}