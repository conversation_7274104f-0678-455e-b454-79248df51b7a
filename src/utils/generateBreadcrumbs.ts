import { getUser } from "@/api/user.localStorage";

export const generateBreadcrumbs = (pathname) => {
  const user = getUser();

  // Handle empty or root path
  if (!pathname || pathname === '/') {
    return [{ label: 'Home', path: '/' }];
  }

  // Split pathname into segments and filter out empty segments
  let pathSegments = pathname.split('/').filter((segment) => segment !== '');

  // Remove the last segment if its length is less than or equal to 2
  if (pathSegments.length >= 1) {
    const lastSegment = pathSegments[pathSegments.length - 1];
    if (lastSegment.length <= 2) {
      pathSegments.pop();
    }
  }

  console.log("pathSegments", pathSegments);

  // Generate breadcrumbs
  const crumbs = pathSegments.map((segment, index) => {
    const path = `/${pathSegments.slice(0, index + 1).join('/')}`;
    return {
      label: segment.charAt(0).toUpperCase() + segment.slice(1), // Capitalize the label
      path: path, // Include the path
    };
  });

  // Add a "Home" breadcrumb based on user role
  if (user?.roles?.[0]?.ROLE === "COMPANY_ADMIN" || user?.roles?.[0]?.ROLE === "USER_ADMIN") {
    crumbs.unshift({ label: 'Home', path: '/admin/roles' });
  } else {
    crumbs.unshift({ label: 'Home', path: '/dashboard/roles' });
  }

  // Remove the segment immediately after "Home"
  if (crumbs.length > 1) {
    crumbs.splice(1, 1); // Remove the second element (index 1)
  }

  return crumbs;
};