import { create } from "zustand";

type ModuleContentCount = {
    module_id: number | null;
    content_id: number | null;
    group_id: number | null;
    setModule_id: (id: number) => void;
    setContent_id: (id: number) => void;
    setGroup_id: (id: number) => void;
}

export const useModuleContentCounterStore = create<ModuleContentCount>((set) => ({
    content_id: 0,
    module_id: 0,
    group_id: 0,
    setContent_id: (id: number) => {
        set({ content_id: id })
    },
    setModule_id: (id: number) => {
        set({ module_id: id })
    },
    setGroup_id: (id: number) => {
        set({ group_id: id })
    }
}));