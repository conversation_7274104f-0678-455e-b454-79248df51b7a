"use client";
import React, { createContext, useContext, useState, ReactNode } from 'react';

// Define the shape of our context
interface ModuleContentContextType {
  content_id: number;
  module_id: number;
  group_id: number;
  setContent_id: (id: number) => void;
  setModule_id: (id: number) => void;
  setGroup_id: (id: number) => void;
}

// Create the context with a default value of null
const ModuleContentContext = createContext<ModuleContentContextType | null>(null);

// Props interface for the provider
interface ModuleContentProviderProps {
  children: ReactNode;
}

// Create a provider component
export function ModuleContentProvider({ children }: ModuleContentProviderProps) {
  const [content_id, setContent_id] = useState<number>(1);
  const [module_id, setModule_id] = useState<number>(1);
  const [group_id, setGroup_id] = useState<number>(1);

  // Value to be provided to consumers
  const value: ModuleContentContextType = {
    content_id,
    module_id,
    group_id,
    setContent_id,
    setModule_id,
    setGroup_id
  };

  return (
    <ModuleContentContext.Provider value={value}>
      {children}
    </ModuleContentContext.Provider>
  );
}

// Custom hook to use the context
export function useModuleContentStore(): ModuleContentContextType {
  const context = useContext(ModuleContentContext);
  if (!context) {
    throw new Error('useModuleContentStore must be used within a ModuleContentProvider');
  }
  return context;
}

// Simple export for compatibility with existing code
export const useModuleContentCounterStore = useModuleContentStore;