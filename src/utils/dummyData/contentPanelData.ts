

export const dummyContentPanelData = [
  {
    group_id: 1,
    group_name: "Frontend Development",
    module_id: 1,
    module_name: "React Fundamentals",
    module_headline: "Master the basics of React",
    module_description: "Learn the core concepts of React including components, props, state, and hooks.",
    contents: [
      {
        content_id: 1,
        content_name: "Introduction to React",
        content_description: "An overview of React and its core principles. https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        topics: "React, JavaScript, Web Development",
        file_type: "None",
        link_path: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        file_path: null,
        last_update: "2023-12-01T00:00:00.000Z",
        completed: false,
        sequence: 1
      },
      {
        content_id: 2,
        content_name: "Components and Props",
        content_description: "Understanding React components and how to pass data with props.",
        topics: "Components, Props, JSX",
        file_type: "application/pdf",
        link_path: null,
        file_path: "uploads/sample.pdf",
        last_update: "2023-12-02T00:00:00.000Z",
        completed: true,
        sequence: 2
      },
      {
        content_id: 3,
        content_name: "State and Lifecycle",
        content_description: "Managing state in React components and understanding lifecycle methods.",
        topics: "State, Lifecycle, Class Components",
        file_type: "video/mp4",
        link_path: null,
        file_path: "uploads/sample.mp4",
        last_update: "2023-12-03T00:00:00.000Z",
        completed: false,
        sequence: 3
      }
    ]
  },
  {
    group_id: 1,
    group_name: "Frontend Development",
    module_id: 2,
    module_name: "Advanced React",
    module_headline: "Take your React skills to the next level",
    module_description: "Dive deeper into React with advanced concepts like context, optimization, and advanced hooks.",
    contents: [
      {
        content_id: 4,
        content_name: "Context API",
        content_description: "Learn how to use React Context for state management across components.",
        topics: "Context API, State Management",
        file_type: "application/vnd.ms-powerpoint",
        link_path: null,
        file_path: "uploads/presentation.ppt",
        last_update: "2023-12-04T00:00:00.000Z",
        completed: false,
        sequence: 1
      },
      {
        content_id: 5,
        content_name: "Performance Optimization",
        content_description: "Techniques to optimize React applications for better performance.",
        topics: "Performance, Memoization, React.memo",
        file_type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        link_path: null,
        file_path: "uploads/data.xlsx",
        last_update: "2023-12-05T00:00:00.000Z",
        completed: true,
        sequence: 2
      },
      {
        content_id: 6,
        content_name: "Custom Hooks",
        content_description: "Building reusable logic with custom hooks.",
        topics: "Hooks, Custom Hooks, React Patterns",
        file_type: "None",
        link_path: "https://example.com/custom-hooks",
        last_update: "2023-12-06T00:00:00.000Z",
        completed: false,
        sequence: 3
      }
    ]
  },
  {
    group_id: 1,
    group_name: "Frontend Development",
    module_id: 3,
    module_name: "React Ecosystem",
    module_headline: "Explore the React ecosystem",
    module_description: "Learn about popular libraries and tools that integrate with React.",
    contents: [
      {
        content_id: 7,
        content_name: "React Router",
        content_description: "Navigation and routing in React applications.",
        topics: "Routing, SPA, Navigation",
        file_type: "application/pdf",
        link_path: null,
        file_path: "uploads/react-router.pdf",
        last_update: "2023-12-07T00:00:00.000Z",
        completed: true,
        sequence: 1
      },
      {
        content_id: 8,
        content_name: "Redux",
        content_description: "Global state management with Redux.",
        topics: "Redux, State Management, Actions",
        file_type: "video/mp4",
        link_path: null,
        file_path: "uploads/redux.mp4",
        last_update: "2023-12-08T00:00:00.000Z",
        completed: false,
        sequence: 2
      }
    ]
  }
];

export const dummyUserProgress = {
  user_id: 3,
  group_id: 1,
  module_id: 1,
  content_id: 1,
  completed: false,
  page_number: 0,
  video_progress: 45.5, // in seconds
  total_progress: "2023-12-15T00:00:00.000Z"
};

export const dummyContentDetails = {
  content_id: 1,
  content_name: "Introduction to React",
  content_description: "An overview of React and its core principles. https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  topics: "React, JavaScript, Web Development",
  file_type: "None",
  link_path: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  file_path: null,
  last_update: "2023-12-01T00:00:00.000Z"
};

// Helper function to get content details by ID
export const getContentById = (contentId) => {
  for (const module of dummyContentPanelData) {
    for (const content of module.contents) {
      if (content.content_id === contentId) {
        return content;
      }
    }
  }
  return null;
};

// Helper function to simulate API response format
export const simulateGetAllContentInSlide = (contentId) => {
  return getContentById(contentId);
};

export const simulateGetAllGroupModuleContent = (groupId) => {
  return dummyContentPanelData.filter(item => item.group_id === groupId);
};

export const simulateGetTheUserProgress = (data) => {
  // Return progress data based on provided parameters
  return {
    ...dummyUserProgress,
    module_id: data.module_id || dummyUserProgress.module_id,
    group_id: data.group_id || dummyUserProgress.group_id
  };
};

// Additional dummy data for the React content
export const dummyUser = {
  user_id: 3,
  username: "student",
  email: "<EMAIL>",
  role: "student"
};