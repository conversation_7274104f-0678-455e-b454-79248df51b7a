// Dummy data for Quiz Page (src/app/quiz/page.tsx)
// For development purposes only

// Mock data for useGetAssessmentQuestions hook
export const mockAssessmentQuestions = [
    {
      assessment_question_id: 1,
      question: "What is React's virtual DOM primarily used for?",
      question_type: "MCQ",
      option1: "To create real DOM elements faster",
      option2: "To bypass the browser's rendering engine",
      option3: "To efficiently update the real DOM by minimizing direct manipulations",
      option4: "To store component state in memory",
      correct_option: 3, // For development reference only
      marks: 5
    },
    {
      assessment_question_id: 2,
      question: "Which hook would you use to perform side effects in a functional component?",
      question_type: "MCQ",
      option1: "useState",
      option2: "useEffect",
      option3: "useContext",
      option4: "useReducer",
      correct_option: 2, // For development reference only
      marks: 5
    },
    {
      assessment_question_id: 3,
      question: "Explain the concept of 'lifting state up' in React and provide an example of when you would use it.",
      question_type: "SUBJECTIVE",
      marks: 10
    },
    {
      assessment_question_id: 4,
      question: "Is React a library or a framework?",
      question_type: "T/F",
      option1: "React is a library",
      option2: "React is a framework",
      correct_option: 1, // For development reference only
      marks: 2
    },
    {
      assessment_question_id: 5,
      question: "Write a simple React component that displays a counter with increment and decrement buttons.",
      question_type: "CODING",
      marks: 15
    },
    {
        assessment_question_id: 1,
        question: "What is React's virtual DOM primarily used for?",
        question_type: "MCQ",
        option1: "To create real DOM elements faster",
        option2: "To bypass the browser's rendering engine",
        option3: "To efficiently update the real DOM by minimizing direct manipulations",
        option4: "To store component state in memory",
        correct_option: 3, // For development reference only
        marks: 5
      },
      {
        assessment_question_id: 2,
        question: "Which hook would you use to perform side effects in a functional component?",
        question_type: "MCQ",
        option1: "useState",
        option2: "useEffect",
        option3: "useContext",
        option4: "useReducer",
        correct_option: 2, // For development reference only
        marks: 5
      },
      {
        assessment_question_id: 3,
        question: "Explain the concept of 'lifting state up' in React and provide an example of when you would use it.",
        question_type: "SUBJECTIVE",
        marks: 10
      },
      {
        assessment_question_id: 4,
        question: "Is React a library or a framework?",
        question_type: "T/F",
        option1: "React is a library",
        option2: "React is a framework",
        correct_option: 1, // For development reference only
        marks: 2
      },
      {
        assessment_question_id: 5,
        question: "Write a simple React component that displays a counter with increment and decrement buttons.",
        question_type: "CODING",
        marks: 15
      }
  ];
  
  // Mock data for useStartUserAssessments hook
  export const mockUserAssessmentDetails = {
    user_assessment_attempt_id: "UA12345",
    assessment_id: "EXAM001",
    assessment_name: "React Development Fundamentals",
    user_id: "USER123",
    start_time: "2025-04-19T10:00:00Z",
    end_time: null,
    status: "IN_PROGRESS",
    score: null,
    max_score: 37,
    time_allowed: 60, // in minutes
    questions_count: 5
  };
  
  // Mock data for liveStateCheck
  // Values: 1 = Not Visited, 2 = Not Answered, 3 = Answered, 4 = Marked for Review
  export const mockLiveStateCheck = ["1", "1", "1", "1", "1"]; // All questions not visited initially
  
  // Mock data for the selected answers
  export const mockSelectedAnswers = [
    {
      assessment_question_id: 0,
      submitted_answer_option: 0,
      answer_text: null
    },
    {
      assessment_question_id: 0,
      submitted_answer_option: 0,
      answer_text: null
    },
    {
      assessment_question_id: 0,
      submitted_answer_option: 0,
      answer_text: null
    },
    {
      assessment_question_id: 0,
      submitted_answer_option: 0,
      answer_text: null
    },
    {
      assessment_question_id: 0,
      submitted_answer_option: 0,
      answer_text: null
    }
  ];
  
  // Mock for the submit assessment answers data (this would normally be generated dynamically)
  export const mockSubmitAssessmentAnswers = [
    // Initially empty - will be populated as user answers questions
  ];
  
  // Mock complete answers in the format expected by the QuestionUserBox component
  export const mockCompleteAnswers = [
    {
      question: "What is React's virtual DOM primarily used for?",
      user_assessment_attempt_id: "UA12345",
      assessment_question_id: 1,
      submitted_answer_option: null,
      answer_text: null,
      submitted_answer_marks: 5,
      submitted_answer_status: "SUBMITTED",
      evaluation_comments: "None",
      evaluated_by_email: "None",
      time_take_to_answer_in_sec: 0,
      number_of_screen_switch: 0,
      used_back_button: 0,
      changed_answer: 0
    },
    {
      question: "Which hook would you use to perform side effects in a functional component?",
      user_assessment_attempt_id: "UA12345",
      assessment_question_id: 2,
      submitted_answer_option: null,
      answer_text: null,
      submitted_answer_marks: 5,
      submitted_answer_status: "SUBMITTED",
      evaluation_comments: "None",
      evaluated_by_email: "None",
      time_take_to_answer_in_sec: 0,
      number_of_screen_switch: 0,
      used_back_button: 0,
      changed_answer: 0
    },
    {
      question: "Explain the concept of 'lifting state up' in React and provide an example of when you would use it.",
      user_assessment_attempt_id: "UA12345",
      assessment_question_id: 3,
      submitted_answer_option: null,
      answer_text: null,
      submitted_answer_marks: 0, // Subjective questions start with 0 marks
      submitted_answer_status: "SUBMITTED",
      evaluation_comments: "None",
      evaluated_by_email: "None",
      time_take_to_answer_in_sec: 0,
      number_of_screen_switch: 0,
      used_back_button: 0,
      changed_answer: 0
    },
    {
      question: "Is React a library or a framework?",
      user_assessment_attempt_id: "UA12345",
      assessment_question_id: 4,
      submitted_answer_option: null,
      answer_text: null,
      submitted_answer_marks: 2,
      submitted_answer_status: "SUBMITTED",
      evaluation_comments: "None",
      evaluated_by_email: "None",
      time_take_to_answer_in_sec: 0,
      number_of_screen_switch: 0,
      used_back_button: 0,
      changed_answer: 0
    },
    {
      question: "Write a simple React component that displays a counter with increment and decrement buttons.",
      user_assessment_attempt_id: "UA12345",
      assessment_question_id: 5,
      submitted_answer_option: null,
      answer_text: null,
      submitted_answer_marks: 0, // Coding questions start with 0 marks
      submitted_answer_status: "SUBMITTED",
      evaluation_comments: "None",
      evaluated_by_email: "None",
      time_take_to_answer_in_sec: 0,
      number_of_screen_switch: 0,
      used_back_button: 0,
      changed_answer: 0
    },
    
  ];
  
  // Mock function for UseSubmitAssessmentAnswers hook
  export const mockUseSubmitAssessmentAnswers = (attemptId, answers) => {
    return {
      mutate: async () => {
        console.log("Development: Mock submitting answers", answers);
        console.log("For attempt ID:", attemptId);
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { success: true };
      }
    };
  };