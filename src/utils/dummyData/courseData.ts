// src/utils/dummyData/courseData.ts

export const courseData = [
    {
      group_id: 1,
      group_name: "Frontend Development",
      module_id: 1,
      module_name: "React Fundamentals",
      module_headline: "Master the basics of React",
      module_description: "Learn the core concepts of React including components, props, state, and hooks. This module provides a solid foundation for building modern web applications with React.",
      contents: [
        {
          content_id: 1,
          content_name: "Introduction to React",
          content_description: "A comprehensive introduction to React by the React team. This video covers the philosophy behind React and why it has become one of the most popular JavaScript libraries for building user interfaces.",
          topics: "React, JavaScript, Web Development",
          file_type: "None",
          link_path: "https://www.youtube.com/watch?v=dpw9EHDh2bM", // Real React intro video
          file_path: null,
          last_update: "2023-12-01T00:00:00.000Z",
          completed: true,
          sequence: 1
        },
        {
          content_id: 2,
          content_name: "Components and Props",
          content_description: "Understanding React components and how to pass data with props. Learn how to create reusable components and manage data flow in your React application.",
          topics: "Components, Props, JSX",
          file_type: "application/pdf",
          link_path: null,
          file_path: "/samplejd.pdf", // This would be a real PDF in your public folder
          last_update: "2023-12-02T00:00:00.000Z",
          completed: false,
          sequence: 2
        },
        {
          content_id: 3,
          content_name: "State and Lifecycle",
          content_description: "Managing state in React components and understanding lifecycle methods. This video tutorial demonstrates how to use state effectively and respond to component lifecycle events.",
          topics: "State, Lifecycle, Class Components",
          file_type: "None",
          link_path: "https://www.youtube.com/watch?v=IYvD9oBCuJI", // React state tutorial
          file_path: null,
          last_update: "2023-12-03T00:00:00.000Z",
          completed: false,
          sequence: 3
        }
      ]
    },
    {
      group_id: 1,
      group_name: "Frontend Development",
      module_id: 2,
      module_name: "Advanced React",
      module_headline: "Take your React skills to the next level",
      module_description: "Dive deeper into React with advanced concepts like context, optimization, and advanced hooks. This module helps you build more complex applications with better performance.",
      contents: [
        {
          content_id: 4,
          content_name: "Context API",
          content_description: "Learn how to use React Context for state management across components. This guide explains when and how to use Context effectively in your applications.",
          topics: "Context API, State Management",
          file_type: "application/vnd.ms-powerpoint",
          link_path: null,
          file_path: "react-context-api.pptx",
          last_update: "2023-12-04T00:00:00.000Z",
          completed: false,
          sequence: 1
        },
        {
          content_id: 5,
          content_name: "Performance Optimization",
          content_description: "Techniques to optimize React applications for better performance. Learn about memoization, code splitting, and other strategies to make your React apps faster.",
          topics: "Performance, Memoization, React.memo",
          file_type: "None",
          link_path: "https://developer.mozilla.org/en-US/docs/Web/Performance/How_browsers_work",
          file_path: null,
          last_update: "2023-12-05T00:00:00.000Z",
          completed: false,
          sequence: 2
        },
        {
          content_id: 6,
          content_name: "Custom Hooks",
          content_description: "Building reusable logic with custom hooks. This video shows you how to extract component logic into reusable functions and create your own custom hooks.",
          topics: "Hooks, Custom Hooks, React Patterns",
          file_type: "None",
          link_path: "https://www.youtube.com/watch?v=J-g9ZJha8FE", // Custom hooks tutorial
          file_path: null,
          last_update: "2023-12-06T00:00:00.000Z",
          completed: false,
          sequence: 3
        }
      ]
    },
    {
      group_id: 1,
      group_name: "Frontend Development",
      module_id: 3,
      module_name: "React Ecosystem",
      module_headline: "Explore the React ecosystem",
      module_description: "Learn about popular libraries and tools that integrate with React. This module covers key additions to the React ecosystem that help you build full-featured applications.",
      contents: [
        {
          content_id: 7,
          content_name: "React Router",
          content_description: "Navigation and routing in React applications. Learn how to create multi-page experiences in your single-page React applications.",
          topics: "Routing, SPA, Navigation",
          file_type: "application/pdf",
          link_path: null,
          file_path: "/sample-pdf.pdf",
          last_update: "2023-12-07T00:00:00.000Z",
          completed: false,
          sequence: 1
        },
        {
          content_id: 8,
          content_name: "Redux",
          content_description: "Global state management with Redux. This comprehensive guide covers Redux core concepts and how to integrate it with React applications.",
          topics: "Redux, State Management, Actions",
          file_type: "None",
          link_path: "https://www.youtube.com/watch?v=poQXNp9ItL4", // Redux tutorial
          file_path: null,
          last_update: "2023-12-08T00:00:00.000Z",
          completed: false,
          sequence: 2
        },
        {
          content_id: 9,
          content_name: "Testing React Apps",
          content_description: "Learn how to test React applications using Jest and React Testing Library. This guide covers component testing, integration testing, and test-driven development.",
          topics: "Testing, Jest, React Testing Library",
          file_type: "None",
          link_path: "https://www.youtube.com/watch?v=ZmVBCpefQe8",
          file_path: null,
          last_update: "2023-12-09T00:00:00.000Z",
          completed: false,
          sequence: 3
        }
      ]
    },
    {
      group_id: 2,
      group_name: "Backend Development",
      module_id: 4,
      module_name: "Node.js Basics",
      module_headline: "Get started with server-side JavaScript",
      module_description: "Learn the fundamentals of Node.js and how to build server-side applications with JavaScript. This module covers core concepts, modules, and asynchronous programming.",
      contents: [
        {
          content_id: 10,
          content_name: "Introduction to Node.js",
          content_description: "An introduction to Node.js and its event-driven, non-blocking I/O model. Learn why Node.js is perfect for data-intensive real-time applications.",
          topics: "Node.js, JavaScript, Server-side",
          file_type: "None",
          link_path: "https://www.youtube.com/watch?v=TlB_eWDSMt4",
          file_path: null,
          last_update: "2023-12-10T00:00:00.000Z",
          completed: false,
          sequence: 1
        },
        {
          content_id: 11,
          content_name: "Node.js Modules",
          content_description: "Understanding the module system in Node.js. Learn how to create, export, and import modules to organize your code.",
          topics: "Modules, CommonJS, ES Modules",
          file_type: "application/pdf",
          link_path: null,
          file_path: "/nodejs-modules.pdf",
          last_update: "2023-12-11T00:00:00.000Z",
          completed: false,
          sequence: 2
        }
      ]
    }
  ];
  
  // Helper function to simulate getting all content for a group
  export const simulateGetAllGroupModuleContent = (groupId) => {
    return courseData.filter(module => module.group_id === groupId);
  };
  
  // Helper function to simulate getting content for a specific ID
  export const simulateGetAllContentInSlide = (contentId) => {
    for (const module of courseData) {
      const content = module.contents.find(c => c.content_id === contentId);
      if (content) {
        return content;
      }
    }
    return null;
  };
  
  // Mock user progress data
  export const dummyUserProgress = {
    video_progress: 30,
    completed_content_ids: [1]
  };