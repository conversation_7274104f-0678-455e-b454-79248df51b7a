import { create } from "zustand";

type VideoContextCount = {
    video_progress: number;
    setVideo_progress: (id: number) => void;
    video_link: string;
    setVideo_link: (videoLink: string) => void
}

export const useVideoContextCount = create<VideoContextCount>((set) => ({
    video_progress: 0,
    video_link: "",
    setVideo_progress: (inputNumber: number) => {
        set({ video_progress: inputNumber })
    },
    setVideo_link: (videoLink: string) => {
        set({video_link: videoLink})
    }
})); 