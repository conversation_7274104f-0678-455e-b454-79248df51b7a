import Player from 'video.js/dist/types/player';
import { twMerge } from 'tailwind-merge';
import { type ClassValue, clsx } from 'clsx';

export interface Segment {
    start: number;
    end: number;
    title: string;
  }

  export const convertTimeToSeconds = (timeStr: string): number => {
    return (timeStr || '').split(':').reduce((acc, time) => 60 * acc + +time, 0);
  };

  export const createSegmentMarkerElements = (
    player: Player,
    segment: Segment,
    lastSegment: Segment,
  ) => {
    const segmentEnd = isFinite(segment.end)
      ? (segment.end / (player.duration() || lastSegment.end)) * 100
      : 100;
  
    // Create gap element
    const gapEl = document.createElement('div');
    gapEl.className = 'segment-gap absolute';
    gapEl.style.left = `${segmentEnd}%`;
    gapEl.style.height = '100%';
    gapEl.style.width = '4px';
    gapEl.style.background = '#2f3640';
  
    return { gapEl };
  };

  export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
  }
  
  export const createSegmentMarkersWithoutDuration = (
    player: any,
    allSegments: Segment[],
  ) => {
    const seekBar = player.controlBar.progressControl.seekBar.el();
    const fragment = document.createDocumentFragment();
  
    seekBar
      .querySelectorAll('.segment-marker, .segment-tooltip')
      .forEach((el: any) => el.remove());
  
    allSegments.forEach((segment) => {
      const { gapEl } = createSegmentMarkerElements(
        player,
        segment,
        allSegments[allSegments.length - 1],
      );
  
      fragment.appendChild(gapEl);
    });
  
    seekBar.appendChild(fragment);
  };

  export const getCurrentSegmentName = (
    timeStr: string,
    segments: Segment[],
  ): string => {
    const timeInSeconds = convertTimeToSeconds(timeStr);
    const currentSegment = segments.find(
      (segment) => segment.start <= timeInSeconds && timeInSeconds <= segment.end,
    );
    return currentSegment ? currentSegment.title : '';
  };

  export const formatTime = (seconds: number): string => {
    const date = new Date(seconds * 1000);
    const hh = date.getUTCHours();
    const mm = date.getUTCMinutes();
    const ss = String(date.getUTCSeconds()).padStart(2, '0');
    return hh ? `${hh}:${String(mm).padStart(2, '0')}:${ss}` : `${mm}:${ss}`;
  };

export const handleMarkAsCompleted = async (
    markAsCompleted: boolean,
    contentId: number,
  ) => {
    // const response = await fetch('/api/course/videoProgress/markAsCompleted', {
    //   body: JSON.stringify({
    //     markAsCompleted,
    //     contentId,
    //   }),
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    // });
    // return await response.json();
  };