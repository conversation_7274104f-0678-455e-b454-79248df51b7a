//Present tests list
export interface QuizLists {
  exam_id: number;
  time_allowed_minutes: number;
  exam_instructions: string;
  questions_list: Array<Number>;
}

export interface AddUser {
  email: string;
  password: string;
  full_name: string;
  phone: string;
}

export interface CreateContentOptions {
  content_name: string;
  content_description: string;
  linkPath: string;
  topics: string;
  compression: boolean;
}

export interface ContentData {
  options: {
    content_name: string;
    content_description: string;
    linkPath: string;
    topics: string;
    compression: boolean;
  };
  file?: File;
}

export interface CommentMarks {
  user_assessment_attempt_id: string;
  submitted_answer_marks: string;
  assessment_question_id: number;
  evaluation_comments: string;
}

export interface AddAssessmentToGroupInput {
  group_id: number;
  assessment_id: number;
  is_active: number;
  start_date: Date;
  end_date: Date;
  max_attempts: number;
}

export interface UpdateUser {
  user_id: Number;
  email?: string;
  password?: string;
  user_full_name?: string;
  phone?: string;
}

//QuestionsList - get a list of questions for a quiz
export interface QuestionsList {
  question_id: number;
  question_options: { [key: string]: string };
  question_tags: Array<Object>;
  question_text: string;
  question_type: string;
}

//Roles and scope
interface roles {
  ROLE: string;
  SCOPE: string;
}

export interface AssessmentSubmittedAnswers {
  submitted_answer_id: number;
  user_assessment_attempt_id: number;
  assessment_question_id: number;
  submitted_answer_option: number;
  submitted_answer_text: string;
  submitted_answer_marks: number;
  submitted_answer_status: string;
  evaluation_comments: string;
  evaluated_by_email: string;
}

export interface SubmitAttempt {
  submitted_answer_id: number;
  question: string;
  user_assessment_attempt_id: number;
  assessment_question_id: number;
  submitted_answer_option: number;
  answer_text: string;
  submitted_answer_marks: number;
  submitted_answer_status: string;
  evaluation_comments: string;
  evaluated_by_email: string;
  time_take_to_answer_in_sec: number;
  number_of_screen_switch: number;
  used_back_button: number;
  changed_answer: number;
}

//User
export interface UserDetails {
  user_id: number;
  email: string;
  phone: string;
  user_full_name: string;
  roles: Array<roles>;
  company: string | null;
  company_id: number | null;
  groups: Object;
  token: string;
  firstLogin?: boolean
}

export interface Assessments {
  assessment_id: number;
  assessment_name: string;
  instructions: string;
  total_time_allowed: number;
  total_marks: number;
  source: string;
}

export interface PostUserAssessmentsOptions {
  userIds: number[];
  startDate: Date;
  endDate: Date;
  maxAttempts: string;
  totalTimeAllowed: string;
}

export interface AddRemoveModuleGroup {
  module_id: number;
  group_id: number;
  sequence:number;
}

export interface AddRemoveModuleGroupSeq {
  module_id: number;
  group_id: number;
  sequence:number;
}

export interface AddRemoveContentGroup {
  content_id: number;
  group_id: number;
}
export interface ModuleConfig {
  from_attributes: boolean;
}

export interface ContentInformationProps {
  content_id: number;
  content_name: string;
  content_description: string;
  topics: string;
  created_by: string;
  file_type: string;
  file_size: string;
  file_path: string;
}

export interface optionsData {
  label: string;
  content: any;
}

export interface AddModule {
  module_config: ModuleConfig;
  module_name: string;
  module_headline: string;
  module_description: string;
}

export interface AddModuleContent {
  module_id: number;
  content_id: number;
  sequence:number;
}

export interface AddModuleAssessment {
  module_id: number;
  assessment_id: number;
  sequence:number;
}

export interface AddGroup {
  group_name: string;
  isactive?: number;
  created_by: string;
  group_faq:string;
  group_admin_user_id: number;
}

export interface AddRemoveUserToGroup {
  group_id: number;
  user_id: number;
}

export interface RemoveAssessmentToGroup {
  group_assessment_id: number;
}

export interface UpdateGroup {
  group_id: number;
  created_by: string;
  group_name: string;
  group_faq:string;
  group_admin_user_id: number;
  isactive: number;
 
}

export interface EditUserAssessmentsOptions {
  userAssessmentId: number[];
  startDate: Date;
  endDate: Date;
  maxAttempts: string;
  totalTimeAllowed: string;
}

export interface UpcomingUserAssessment {
  user_assessment_id: number;
  user_name: string | null;
  user_id: number;
  assessment_id: number;
  start_date: string;
  end_date: string;
  user_score: number | null;
  total_time: number;
  total_attempts: number;
  max_attempts: number;
  last_attempt_date: string | null;
  assessment_name: string;
  total_time_allowed: number;
  total_marks: number;
  assessment_evaluation_strategy: number;
}

export interface UserAssessment {
  user_assessment_id: number;
  user_id: number;
  assessment_id: number;
  start_date: Date;
  end_date: Date;
  user_score: number;
  total_time: number;
  total_attempts: number;
  max_attempts: number;
  last_attempt_date: Date;
}

export interface UserAssessmentAttempt {
  user_assessment_attempt_id: number;
  user_id: number;
  assessment_id: number;
  user_assessment_id: number;
  attempt_status: number;
  attempt_number: number;
  attempt_start_date: Date;
  attempt_end_date: Date;
  attempt_total_time: number;
  attempt_total: number;
  attempt_evaluation: string;
  total_time_allowed: number;
  total_marks: number;
  start_date: Date;
  end_date: Date;
  max_attempts: number;
  assessment_name: string;
  assessment_evaluation_strategy: number;
}

export interface SubmitUserState {
  start_date: Date;
  end_date: Date;
  max_attempts: number;
  total_time_allowed: number;
  checkboxList: boolean[]; // Array of checkboxes
}

export interface User {
  user_id: number;
  password: string;
  user_status: number;
  phone: string;
  email: string;
  creation_date: string;
  comment: string;
}

export interface User_ext {
  user_id: number;
  user_full_name: string;
  created_by: string;
  creation_date: string;
  user_ext_id: number;
  comment: string;
  user: User;
}

export interface AssessmentAnswer {
  submitted_answer_id: number;
  question: string;
  user_assessment_attempt_id: number;
  assessment_question_id: number;
  submitted_answer_option: number;
  answer_text: string;
  submitted_answer_marks: number;
  submitted_answer_status: string;
  evaluation_comments: string;
  evaluated_by_email: string;
  time_take_to_answer_in_sec: number;
  number_of_screen_switch: number;
  used_back_button: number;
  changed_answer: number;
}

export interface Edit_Questions {
  question: string;
  question_type: string;
  question_difficulty: number;
  option1: string;
  option2: string;
  option3: string;
  option4: string;
  question_source: string;
  picture: string;
  answer_option: number;
  answer_text: string;
  marks: number;
  answer_explanation: string;
  topics_string: string;
  default_text: string;
  question_category: string;
}

export interface One_Questions {
  question: string;
  question_type: string;
  question_difficulty: number;
  question_category: string;
  option1: string;
  option2: string;
  option3: string;
  option4: string;
  picture: string;
  topics_string: string;
  question_source: string;
  default_text: string;
  answer_option: number;
  answer_text: string;
  marks: number;
  answer_explanation: string;
}

export interface filterSearch {
  question_string: string;
  question_type: string;
  difficulty_level: string;
  question_category: string;
  topic: string;
  question_source: string;
}

export interface getUserProgress {
  group_id: number;
  user_id: number | undefined;
  module_id: number;
}

export interface PostUserProgress {
  user_id: number | undefined| null;
  group_id: number | undefined| null;
  module_id: number | undefined| null;
  content_id: number | undefined| null;
  completed: boolean | undefined;
  page_number: number | undefined | string;
  video_progress: number | undefined | string;
  total_progress: string | undefined;
}

export interface assessmentFilter {
  assessment_evaluation_strategy: number;
  assessment_id: number;
  assessment_name: string;
  instructions: string;
  source: string;
  total_marks: number;
  total_time_allowed: number
}

export interface FilterSearchReturn {
  question_id: number;
  question: string;
  question_type: string;
  question_difficulty: number;
  option1: string;
  option2: string;
  option3: string;
  option4: string;
  picture: string;
  topics_string: string;
  default_text: string;
  question_source: string;
  answer_option: number;
  answer_text: string;
  marks: number;
  answer_explanation: string;
  creation_date: Date;
}

export interface AssessmentQuestion {
  assessment_question_id: number;
  assessment_id: number;
  marks: number;
  question: string;
  question_type: "MCQ" | "T/F" | "CODING"; // Assuming question_type can only be one of these three types
  option1: string | null;
  option2: string | null;
  option3: string | null;
  option4: string | null;
  picture: string | null; // Assuming picture is a URL or null
}

export interface UploadQuestionTable {
  question_id: number;
  question: string;
  question_difficulty: number;
  question_source: string;
  topics_string: string;
}

export interface ContentTable {
  content_id: number;
  content_name: string;
  content_description: string;
  topics: string;
  file_type: string;
  file_size: string;
  file_path: string;
  created_by: string;
}

export interface groupIdForMoulde {
  group_id: number;
}

export interface UpdateModule {
  module_name: string;
  module_heading: string;
  module_description: string;
  module_Id: number;
}
export interface AddModuleAssessmentOptions {
  module_id: number;
  assessment_id: number;
  sequence:number;
}
export interface UpdateModuleSeqAssessmentOptions {
  module_id: number;
  assessment_id: number;
  sequence:number;
}
export interface AddModuleContentOptions {
  module_id: number;
  content_id: number;
  sequence:number;
}
export interface UpdateModuleSeqContentOptions {
  module_id: number;
  content_id: number;
  sequence:number;
}


export interface ContentFileDownload {
  file_path: string;
}

export interface SelectAssessmentQuestion {
  question_id: number;
  marks: number;
}

export interface AssessmentForSelectQuestion {
  assessment_id: number;
  assessment_name: string;
  instructions: string;
  total_time_allowed: number;
  total_marks: number;
  source: string;
  assessment_evaluation_strategy: number;
  question_list: SelectAssessmentQuestion[];
}
