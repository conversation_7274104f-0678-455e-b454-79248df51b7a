"use client";
import { NextRequest, NextResponse } from "next/server";
import axios from 'axios';

export async function middleware(req: NextRequest) {
  let isAdmin = req.cookies.get('admin')
  // if (req.nextUrl.pathname === "/admin") {
  //   if (isAdmin?.value === "true") {
  //     return NextResponse.next();
  //   } else {
  //     const loginUrl = new URL("/login", req.nextUrl.origin);
  //     return NextResponse.redirect(loginUrl.href);
  //   }
  // }
  return NextResponse.next();
}



// export function sessionMiddleware(request: NextRequest) {
 
//     const authToken = request.cookies.get('admin=true'); // Assuming the token is stored in a cookie named 'authToken'
  
//     // Redirect to login if attempting to access a protected route without a valid token
//     if (!authToken && request.nextUrl.pathname.startsWith('/protected')) {
//       const url = new URL("/login", request.nextUrl.origin);
//       return NextResponse.redirect(url);
//     }
  
//     return NextResponse.next();
//   }
  
// const sessionExpirationMiddleware = store => next => action => {
//   if (action.type === 'API_CALL_FAILURE' && action.error.status === 401) {
//     // Handle session expiration, e.g., by dispatching a logout action
//     store.dispatch({ type: 'LOGOUT' });
//     // Redirect to login
//     window.location.href = '/login';
//   }
//   return next(action);
// };



// axios.interceptors.response.use(response => response, error => {
//   if (error.response && error.response.status === 401) {
//     window.location.href = '/login';
//   }
//   return Promise.reject(error);
// });