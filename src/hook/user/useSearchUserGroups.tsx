import { useQuery } from '@tanstack/react-query';
import { getSearchUserGroups } from '@/api/admin/group/getSearchUserGroups';

export function useSearchUserGroups(searchText: string) {
  return useQuery(['searchUserGroups', searchText], () => getSearchUserGroups(searchText), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  });
}
