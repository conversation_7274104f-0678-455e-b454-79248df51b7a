
import { useMutation, useQuery } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';
import { getUserAttempts } from '@/api/user/getUserAttempts';


export function useGetUserAttempt(userId:number) {

    return useQuery(['userAttempts',userId], () => getUserAttempts(userId), {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 1000 * 60 * 60 * 1,
    });
  }

    
