import { useMutation } from '@tanstack/react-query';
import { submitAssessmentAnswers } from '@/api/assessment_attempts/submitAssessmentAnswers';
import { SubmitAttempt } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';

export function UseSubmitAssessmentAnswers(user_assessment_attempt_id:number, SubmitAttempt: SubmitAttempt[]) {  
    const queryClient = useQueryClient();
    return useMutation(() => submitAssessmentAnswers(user_assessment_attempt_id, SubmitAttempt), {
        onSuccess: (data) => {
            // Handle success if needed

            console.log('assessment answers submitted successfully:', data);
        },
        onError: (error) => {
            // Handle error if needed
            console.error('Error posting assessment answers:', error);
        },
        onSettled: (data, error) => {
            // Additional handling after mutation is either successful or failed
            console.log('Mutation completed:', { data, error });
        },
    });
}
