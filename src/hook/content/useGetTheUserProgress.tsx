import { useQuery } from '@tanstack/react-query';
import { getUserProgress } from "@/types/LMSTypes"
import { getTheUserProgress } from '@/api/content/getTheUserProgress';

export function useGetTheUserProgress(module_id: number, group_id: number, user_id: number, content_id: number) {
    return useQuery(['getAllGroupModuleContent', group_id, module_id, user_id, content_id], () => getTheUserProgress(group_id, module_id, user_id, content_id), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
        // enabled: !!module_id,
    },);
}


