import { useMutation } from '@tanstack/react-query';
import { PostUserProgress } from "@/types/LMSTypes"
import { postTheUserProgress } from '@/api/content/postTheUserProgress';

export function usePostTheUserProgress(body: PostUserProgress) {
    console.log('SADFdsvs', body);
    return useMutation(['postTheUserProgress', body], () => postTheUserProgress(body), {
        onSuccess: (data) => {
            console.log('bodyTestyep Assessment posted body2:', data);
        }
    },);
} 


