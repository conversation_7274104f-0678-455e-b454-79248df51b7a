import { useQuery } from '@tanstack/react-query';

import { getAllContentInSlide } from '@/api/content/getAllContentInSlide';

export function useGetAllContentInSlide(content_id:number | null | undefined) {
  return useQuery(['fetchAllSlideContent',content_id ], () => getAllContentInSlide(content_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}


