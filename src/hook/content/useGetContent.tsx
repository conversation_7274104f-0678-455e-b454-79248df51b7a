import { useQuery } from '@tanstack/react-query';
import { getUserProgress } from "@/types/LMSTypes";
import { getContent } from '@/api/content/getContent';

export function useGetContent(content_id: number | null | undefined) {
    return useQuery(
        ['getContent', content_id],  // Updated query key to reflect content_id
        () => getContent(content_id), // Pass content_id instead of file_path
        {
            refetchOnWindowFocus: false,
            refetchOnMount: false,
            refetchOnReconnect: false,
            staleTime: 1000 * 60 * 60 * 1,  // Cache data for 1 hour
            // enabled: !!content_id,  // Optional: Ensure the query only runs if content_id is valid
        }
    );
}
