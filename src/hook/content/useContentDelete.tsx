
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';
import { deleteContent } from '@/api/content/getAllContentInTable';


export function useContentDelete() {
  const queryClient = useQueryClient();

  // Define the mutation function using useMutation
  return useMutation((contentId: number) => deleteContent(contentId), {
    // onSuccess callback is called when the mutation is successful
    onSuccess: (data, contentId) => {
      queryClient.invalidateQueries({ queryKey: ['fetchAllContent'] });
      console.log('Content deleted successfully:', data);
    },
    // onError callback is called when there's an error during the mutation
    onError: (error) => {
      console.error('Error deleting content:', error);
    },
    // onSettled callback is called after the mutation is either successful or failed
    onSettled: (data, error, contentId) => {
      // Perform any additional actions here
      console.log('Deletion completed:', { data, error });
    },
  });
}
