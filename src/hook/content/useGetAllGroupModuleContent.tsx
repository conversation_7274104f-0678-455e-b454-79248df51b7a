import { useQuery } from '@tanstack/react-query';

import { getAllGroupModuleContent } from '@/api/content/getAllGroupModuleContent';

export function useGetAllGroupModuleContent(group_id: number) {
    return useQuery(['getAllGroupModuleContent', group_id], () => getAllGroupModuleContent(group_id), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}


