// hooks/useCreateContentModule.ts

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createContent } from '@/api/content/getAllContentInTable';
import { ContentData } from '@/types/LMSTypes';

export function useCreateContentModule() {
  const queryClient = useQueryClient();

  return useMutation(
    (contentData: ContentData) => createContent(contentData.options, contentData.file),
    {
      onSuccess: (data) => {
        console.log('Content added successfully:', data);
        // Invalidate relevant queries to refetch updated data
        queryClient.invalidateQueries(['fetchAllContent']);
      },
      onError: (error) => {
        console.error('Error adding content:', error);
      },
      onSettled: (data, error) => {
        console.log('Mutation settled:', { data, error });
      },
    }
  );
}
