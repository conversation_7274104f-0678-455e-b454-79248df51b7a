


import { useQuery } from '@tanstack/react-query';
import { getRedoContent } from '@/api/dashboard/result/getRedoContent';

export function useGetRedoContent( group_id: number) {
    return useQuery(['getRedoContent',group_id], () => getRedoContent(group_id), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}