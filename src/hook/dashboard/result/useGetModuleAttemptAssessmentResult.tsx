

import { useQuery } from '@tanstack/react-query';
import { getModuleAttemptAssessmentResult } from '@/api/dashboard/result/getModuleAttemptAssessmentResult';

export function useGetModuleAttemptAssessmentResult() {
    return useQuery(['useGetModuleAttemptAssessmentResult'], () => getModuleAttemptAssessmentResult(), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}