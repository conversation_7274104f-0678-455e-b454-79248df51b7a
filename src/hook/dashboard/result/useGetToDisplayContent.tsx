


import { getToDisplayContent } from '@/api/dashboard/result/getToDisplayContent';
import { useQuery } from '@tanstack/react-query';


export function useGetToDisplayContent( group_id: number) {
    return useQuery(['getToDisplayContent',group_id], () => getToDisplayContent(group_id), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}