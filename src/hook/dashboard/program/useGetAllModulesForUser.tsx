


import { useQuery } from '@tanstack/react-query';
import { getAllModulesForUser } from '@/api/dashboard/program/getAllModuleWithGroupID';

export function useGetAllModulesForUser( group_id: number) {
    return useQuery(['getAllModulesForUser',group_id], () => getAllModulesForUser(group_id), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}