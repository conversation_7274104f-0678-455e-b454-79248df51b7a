import { useQuery } from '@tanstack/react-query';
import { getAllAttemptedAssessmentOfuser } from '@/api/dashboard/getAllAttemptedAssessmentOfuser';

export function useGetAllattemptedAssessmentOfuser(offset: number, limit: number) {

  return useQuery(['fetchAllAssessments',limit,offset], () => getAllAttemptedAssessmentOfuser(offset, limit), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  });
}