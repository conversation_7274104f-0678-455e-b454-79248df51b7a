import { getAllrolesTitleOffsetLimit } from '@/api/dashboard/jobs_titles/getAllRolesTitleOffsetLimit';
import { useQuery } from '@tanstack/react-query';


export function useGetAllRolesTitleOffsetLimit(offset: number, limit: number) {
    console.log("try",offset,limit)
    return useQuery(['getAllrolesTitleOffsetLimit',offset,limit], () => getAllrolesTitleOffsetLimit(offset, limit), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}