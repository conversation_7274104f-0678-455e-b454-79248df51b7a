
import { useMutation, useQuery } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';
import { userRefreshToken } from '@/api/userRefreshToken';


export function useUserRefreshToken(userId:number) {
    console.log("refresher")
    return useQuery(['userRefreshToken',userId], () => userRefreshToken(), {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 1000 * 60 * 60 * 1,
    });
  }

    
