import { useQuery } from "@tanstack/react-query";
import { getAllQuestionFilter } from "@/api/questions/getAllQuestionFilter";
import { FilterSearchReturn, filterSearch } from "@/types/LMSTypes"


export function useGetAllQuestionFilter(questionData:any) {
  return useQuery(["getAllQuestionFilter",questionData], () => getAllQuestionFilter(), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  });
}
