import {  useMutation } from '@tanstack/react-query';

import {  One_Questions } from "@/types/LMSTypes"
import { postOneQuestion } from '@/api/questions/postOneQuestion';
 
import { useQueryClient } from '@tanstack/react-query';

export function usePostOneQuestion(OneQuestionsData:One_Questions[]) {
  const queryClient = useQueryClient();
  return useMutation(() => postOneQuestion(OneQuestionsData), {
    onSuccess: (data) => {
      console.log('Question posted successfully:', data);
      queryClient.invalidateQueries({ queryKey: ["fetchAllFilteredQuestion"] });
      queryClient.invalidateQueries({ queryKey: ["fetchQuestions"] });
      
    },
    onError: (error) => {
      console.error('Error posting question:', error);
    },
    onSettled: (data, error) => {
      console.log('Operation completed:', { data, error });
    },
  });
}


