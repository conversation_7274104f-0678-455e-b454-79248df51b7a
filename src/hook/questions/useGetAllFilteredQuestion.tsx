import { useQuery } from "@tanstack/react-query";
import { getAllFilteredQuestion } from "@/api/questions/getAllFilteredQuestion";
import { FilterSearchReturn, filterSearch } from "@/types/LMSTypes"

export function useGetAllFilteredQuestion(filterSearch: filterSearch) {
  return useQuery(["fetchAllFilteredQuestion",filterSearch], () => getAllFilteredQuestion(filterSearch), {
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
    staleTime: 5,
  });
}
