import { useQuery } from "@tanstack/react-query";

import { getAllQuestionUplaodTable } from "@/api/questions/getAllQustionUplaodTable";

export function useGetAllQuestionUplaodTable() {
  return useQuery(
    ["fetchAllFilteredQuestion"],
    () => getAllQuestionUplaodTable(),
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 1000 * 60 * 60 * 1,
    }
  );
}
