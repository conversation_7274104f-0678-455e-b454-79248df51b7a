import { useMutation, useQueryClient } from '@tanstack/react-query';
import { editQuestions } from '@/api/questions/editQuestions';
import { Edit_Questions } from "@/types/LMSTypes"
 
export function useEditQuestion(question_id: number[], questionValue: Edit_Questions) {
  const queryClient = useQueryClient();
  return useMutation(() => editQuestions(question_id, questionValue), {
    
    onSuccess: (data) => {
      // Handle success if needed
      console.log('Question updated successfully:', data);
      queryClient.invalidateQueries({ queryKey: ["fetchAllFilteredQuestion"] });
      queryClient.invalidateQueries({ queryKey: ["fetchQuestions"] });
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error updating Question:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Updation operation completed:', { data, error });
    },
  });
}
