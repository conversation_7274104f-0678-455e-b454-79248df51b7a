import { useQuery } from '@tanstack/react-query';
import { fetchQuestions } from '@/api/questions/getQuestions';

export function useGetQuestions(assessment_id: string | null) {
  return useQuery(['fetchQuestions' + assessment_id], () => fetchQuestions(assessment_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}