import { useQuery } from '@tanstack/react-query';
import { getAssessmentQuestions } from '@/api/assessments/getAssessmentQuestions';

export function useGetAssessmentQuestions(assessment_id: number) {
  return useQuery(['assessmentQuestions' + assessment_id], () => getAssessmentQuestions(assessment_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}