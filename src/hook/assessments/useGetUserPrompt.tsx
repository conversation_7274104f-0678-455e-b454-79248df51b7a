
import { useQuery } from '@tanstack/react-query';
import { getUserPrompt } from '@/api/assessments/getUserPrompt';

export function useGetUserPrompt(user_prompt: string) {
  return useQuery(['userAssessment',user_prompt], () => getUserPrompt(user_prompt), {
  
    refetchOnWindowFocus: false,
    enabled: !!user_prompt,  // Only run the query if prompt is not empty
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}