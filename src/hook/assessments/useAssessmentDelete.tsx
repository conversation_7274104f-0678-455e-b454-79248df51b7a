
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

import { assessmentDelete } from '@/api/assessments/assessmentDelete';


export function useAssessmentDelete() {
  const queryClient = useQueryClient();

  return useMutation((assessmentId: number) => assessmentDelete(assessmentId), {
    
    // onSuccess callback is called when the mutation is successful
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchAllAssessments'] });
      console.log('assessment deleted successfully:');
    },
    // onError callback is called when there's an error during the mutation
    onError: (error) => {
      console.error('Error deleting content:', error);
    },
    // onSettled callback is called after the mutation is either successful or failed
    onSettled: (data, error, contentId) => {
      // Perform any additional actions here
      console.log('Deletion completed:', { data, error });
    },
  });
}
