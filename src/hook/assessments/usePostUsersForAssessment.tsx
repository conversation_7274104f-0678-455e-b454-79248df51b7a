import { useMutation } from '@tanstack/react-query';
import { postUsersForAssessment } from '@/api/assessments/postUsersForAssessment';
import { PostUserAssessmentsOptions } from "@/types/LMSTypes"

export function usePostUsersForAssessment(assessmentId: number, options: PostUserAssessmentsOptions) {

  return useMutation(() => postUsersForAssessment(assessmentId, options), {


    onSuccess: (data) => {
      // Handle success if needed
      console.log('Assessment posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
