import { useQuery } from '@tanstack/react-query';
import { getUserAssessments } from '@/api/assessments/getUserAssessments';

export function useGetUserAssessments(user_id: number, offset: number, limit: number) {
  return useQuery(['userAssessment',user_id, offset, limit], () => getUserAssessments(user_id, offset, limit), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}