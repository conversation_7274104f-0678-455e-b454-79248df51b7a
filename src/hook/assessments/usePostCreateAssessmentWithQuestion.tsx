
import { postCreateAssesmentWithQuestion } from '@/api/assessments/postCreateAssesmentWithQuestion';

import {  AssessmentForSelectQuestion } from '@/types/LMSTypes';
import { useMutation } from '@tanstack/react-query';

import { useQueryClient } from '@tanstack/react-query';

export function usePostCreateAssessmentWithQuestion() {
  const queryClient = useQueryClient();

  return useMutation(postCreateAssesmentWithQuestion, {
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['fetchAllAssessments'] });
      console.log('Assessment created successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 