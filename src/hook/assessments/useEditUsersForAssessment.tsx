import { useMutation } from '@tanstack/react-query';
import { editUsersForAssessment } from '@/api/assessments/editUsersForAssessment';
import { EditUserAssessmentsOptions } from "@/types/LMSTypes"

export function useEditUsersForAssessment(userAssessmentIdsValue: number[], userValueChange: EditUserAssessmentsOptions) {
  return useMutation(() => editUsersForAssessment(userAssessmentIdsValue, userValueChange), {
    onSuccess: (data) => {
      // Handle success if needed
      console.log('Assessment posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
