import { useQuery } from '@tanstack/react-query';
import { getSearchedAssessment } from '@/api/assessments/getSearchedAssessment';
export function useGetSearchedAssessment(searchedString: string) {
    return useQuery(['getSearchedAssessment',searchedString], () => getSearchedAssessment(searchedString), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    });
}