import { useQuery } from '@tanstack/react-query';
import { getUsersForAssessment } from '@/api/assessments/getUsersForAssessment';

export function useGetUsersForAssessment(assessment_id: number) {
  return useQuery(['fetchUsersForAssessment'], () => getUsersForAssessment(assessment_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}