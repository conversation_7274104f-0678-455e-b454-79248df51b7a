import { useQuery } from '@tanstack/react-query';
import { getAllAssessments } from '@/api/assessments/getAllAssessments';

export function useGetAllAssessments(offset: number, limit: number) {

  return useQuery(['fetchAllAssessments',limit,offset], () => getAllAssessments(offset, limit), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  });
}