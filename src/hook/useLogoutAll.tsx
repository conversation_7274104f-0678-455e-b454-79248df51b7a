import { useQuery } from '@tanstack/react-query';
import { getUserDetail } from '@/api/user/getUserDetail';
import { logoutAll } from '@/api/logoutAll';

export function useLogoutAll(tempStatus: boolean) {
  return useQuery(['logoutAll'], () => logoutAll(), {
    enabled:tempStatus,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}