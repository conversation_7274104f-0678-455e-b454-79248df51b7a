import { useQuery } from '@tanstack/react-query';
import { startUserAssessments } from '@/api/assessment_attempts/startUserAssessments';

export function useStartUserAssessments(examId: string) {
  return useQuery(['startAssessment', examId], () => startUserAssessments(examId), {
    onError: (error) => {
      // Handle the error here, you can log it or manage it as needed
      console.error('An error occurred during assessment start:', error);
    },
  });
}
