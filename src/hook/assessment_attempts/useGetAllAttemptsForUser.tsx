import { useQuery } from '@tanstack/react-query';
import { getAllAttemptsForUser } from '@/api/assessment_attempts/getAllAttemptsForUser';

export function useGetAllAttemptsForUser(assessment_id: number, user_id: number) {
  return useQuery(['getAllAttemptsForUser',assessment_id,user_id], () => getAllAttemptsForUser(assessment_id, user_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}

//