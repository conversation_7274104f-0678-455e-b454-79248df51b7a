import { useQuery } from '@tanstack/react-query';
import { getAllAttemptsGroupByAssessments } from '@/api/assessment_attempts/getAllAttemptsGroupByAssessments';

export function useGetAllAttemptsGroupByAssessments() {
  return useQuery(['getAllAttemptsGroupByAssessments'], () => getAllAttemptsGroupByAssessments(), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}

//