import { useQuery } from '@tanstack/react-query';
import { getEvaluatePage } from '@/api/assessment_attempts/getEvaluatePage';

export function useGetEvaluatePage(user_assessment_attempt_id: number) {
  return useQuery(['getEvaluatePage'], () => getEvaluatePage(user_assessment_attempt_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}