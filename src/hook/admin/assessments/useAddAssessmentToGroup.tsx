import { useMutation } from '@tanstack/react-query';

import { AddAssessmentToGroupInput } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';
import { addAssessmentToGroup } from '@/api/admin/group/addAssessmentToGroup';

export function useAddAssessmentToGroup(options: AddAssessmentToGroupInput) {
  const queryClient = useQueryClient();


  return useMutation(() => addAssessmentToGroup(options), {
    onSuccess: (data) => {
      // Handle success if needed
      queryClient.invalidateQueries({ queryKey: ['getAssessmentsInGroup'] })
      queryClient.invalidateQueries({ queryKey: ['getAssessmentsNotInGroup'] })
      console.log('Assessment posted successfully:', data);
    },
    onError: (error: any) => { // Use 'any' as a fallback if error type is unknown
      // Handle error if needed
      console.error('Error posting assessment:', error);
   
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
