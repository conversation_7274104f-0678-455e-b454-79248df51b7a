import { useMutation } from '@tanstack/react-query';
import { RemoveAssessmentToGroup } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';
import { removeAssessmentFromgroup } from '@/api/admin/group/removeAssessmentFromgroup'; 

export function useRemoveAssessmentFromGroup(data: RemoveAssessmentToGroup) {
  const queryClient = useQueryClient();
  return useMutation(() => removeAssessmentFromgroup(data), {
    onSuccess: (data) => { 
    
      queryClient.invalidateQueries({ queryKey: ['getAssessmentsInGroup'] })
      queryClient.invalidateQueries({ queryKey: ['getAssessmentsNotInGroup'] })
      console.log('Assessment posted successfully:', data);
    },
    onError: (error: any) => { // Use 'any' as a fallback if error type is unknown
      // Handle error if needed
      console.error('Error posting assessment:', error);
      
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 

