import { useMutation, useQueryClient } from '@tanstack/react-query';
import { addBulkUser } from '@/api/admin/addBulkUser';

export function useAddBulkUser(uploadFile: File) {
  const queryClient = useQueryClient();
  return useMutation(() => addBulkUser(uploadFile), {
    onSuccess: (data) => {
      // Handle success if needed
      console.log('Assessment posted successfully:', data);
      queryClient.invalidateQueries({ queryKey: ['fetchAllUserExt'] })
      queryClient.invalidateQueries({ queryKey: ['getSearchedUsers'] })
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 