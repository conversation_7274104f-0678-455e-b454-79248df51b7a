import { useQuery } from '@tanstack/react-query';
import { getModulesNotInGroup } from '@/api/admin/group/getModulesNotInGroup';

export function useGetModulesNotInGroup(group_id: number) {
  return useQuery(['getModulesNotInGroup',group_id], () => getModulesNotInGroup(group_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 100000,
  },);
} 