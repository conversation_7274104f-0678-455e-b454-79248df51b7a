import { useMutation } from '@tanstack/react-query';
import { deleteGroup } from '@/api/admin/group/deleteGroup';

export function useAddGroup(group_id: number) {

  return useMutation(() => deleteGroup(group_id), {


    onSuccess: (data) => {
      // Handle success if needed
      console.log('Assessment posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 