
import { putUpdateModuleSeq } from '@/api/admin/group/putUpdateModuleSeq';
import { AddRemoveModuleGroupSeq } from '@/types/LMSTypes';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

export function usePutUpdateModule() {
  const queryClient = useQueryClient();
  return useMutation((options: AddRemoveModuleGroupSeq) => putUpdateModuleSeq(options), {
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['getModulesNotInGroup'] })
      queryClient.invalidateQueries({ queryKey: ['getModulesByGroupId'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleSearchForAllModule'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleSearchForGroupModule'] })
      console.log('Update module seq  posted successfully:', data);
    },
    onError: (error) => {
      console.error('Error posting update module seq :', error);
    },
    onSettled: (data, error) => {
      console.log('Mutation completed:', { data, error });
    },
  });
}
