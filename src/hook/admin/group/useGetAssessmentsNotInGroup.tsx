import { useQuery } from '@tanstack/react-query';
import { getAssessmentsNotInGroup } from '@/api/admin/group/getAssessmentsNotInGroup';

export function useGetAssessmentsNotInGroup(group_id: number) {
  return useQuery(['getAssessmentsNotInGroup',group_id], () => getAssessmentsNotInGroup(group_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 100000,
  },);
} 