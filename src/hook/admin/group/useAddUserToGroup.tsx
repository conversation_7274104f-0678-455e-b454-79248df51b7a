import { useMutation } from '@tanstack/react-query';
import { addUserToGroup } from '@/api/admin/group/addUserToGroup';
import { AddRemoveUserToGroup } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';

export function useAddUserToGroup(options: AddRemoveUserToGroup[]) {
  const queryClient = useQueryClient();
  return useMutation(() => addUserToGroup(options), {

  
    onSuccess: (data) => {
      // Handle success if needed
      
      queryClient.invalidateQueries({ queryKey: ['getUsersNotInGroup'] })
      queryClient.invalidateQueries({ queryKey: ['getUsersInGroup'] })
      queryClient.invalidateQueries({ queryKey: ['getUserInGroupSearch'] })
      queryClient.invalidateQueries({ queryKey: ['getUserNotInGroupSearch'] })
      console.log('Assessment posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 