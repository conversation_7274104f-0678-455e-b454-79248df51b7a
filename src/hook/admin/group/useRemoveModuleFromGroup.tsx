import { useMutation } from '@tanstack/react-query';
import { removeModuleFromGroup } from '@/api/admin/group/removeModuleFromGroup';
import { useQueryClient } from '@tanstack/react-query';

export function useRemoveModuleFromGroup(moduleId: number, groupId: number) {
  const queryClient = useQueryClient();
  return useMutation(() => removeModuleFromGroup(moduleId, groupId), {
    onSuccess: (data) => {
      // Handle success if needed
      
      queryClient.invalidateQueries({ queryKey: ['getModulesNotInGroup'] })
      queryClient.invalidateQueries({ queryKey: ['getModulesByGroupId'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleSearchForAllModule'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleSearchForGroupModule'] })
      console.log('module deleted  successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error deleted module:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 