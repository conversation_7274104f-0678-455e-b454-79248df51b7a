import { useMutation } from '@tanstack/react-query';
import { addUserToGroupBulk } from '@/api/admin/group/addUserToGroupBulk';
import { useQueryClient } from '@tanstack/react-query';

export function useAddUserToGroupBulk() {
  const queryClient = useQueryClient();
  return useMutation((fileData: File) => {
    console.log('Mutation called with fileData:', fileData);
    return addUserToGroupBulk(fileData);
  }, {
    onSuccess: (data) => { 
      console.log('Bulk user addition successful:', data);
      queryClient.invalidateQueries({ queryKey: ['getUsersInGroup'] });
    },
    onError: (error) => {
      console.error('Error adding bulk users:', error);
    },
    onSettled: (data, error) => {
      console.log('Mutation completed:', { data, error });
    },
  });
}
