import { useMutation } from '@tanstack/react-query';
import { removeUserFromGroup } from '@/api/admin/group/removeUserFromGroup';
import { AddRemoveUserToGroup } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';

export function useRemoveUserFromGroup(data: AddRemoveUserToGroup[]) {
  const queryClient = useQueryClient();
  return useMutation(() => removeUserFromGroup(data), {
    onSuccess: (data) => {
      // Handle success if needed
      queryClient.invalidateQueries({ queryKey: ['getUserInGroupSearch'] })
      queryClient.invalidateQueries({ queryKey: ['getUserNotInGroupSearch'] })
      console.log('Assessment posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 