import { useQuery } from '@tanstack/react-query';
import { getAssessmentByGroupId } from '@/api/assessments/getAssessmentByGroupId';

export function useGetAssessmentByGroupId(group_id: number) {
  return useQuery(['getAssessmentByGroupId',group_id], () => getAssessmentByGroupId(group_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 100000,
  },);
} 