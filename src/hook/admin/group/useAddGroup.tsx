import { useMutation } from '@tanstack/react-query';
import { addGroup } from '@/api/admin/group/addGroup';
import { AddGroup } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';

export function useAddGroup(options: AddGroup) {
  const queryClient = useQueryClient();
  return useMutation(() => addGroup(options), {
    onSuccess: (data) => {
      // Handle success if needed
      queryClient.invalidateQueries({ queryKey: ['getAllGroups'] })
      console.log('Assessment posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 