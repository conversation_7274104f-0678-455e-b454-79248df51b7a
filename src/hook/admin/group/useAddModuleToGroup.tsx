import { useMutation } from '@tanstack/react-query';

import { AddRemoveModuleGroup } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';
import { addModuleToGroup } from '@/api/admin/group/addModuleToGroup';

export function useAddModuleToGroup(options: AddRemoveModuleGroup) {
  const queryClient = useQueryClient();
  return useMutation(() => addModuleToGroup(options), {

  
    onSuccess: (data) => {
      // Handle success if needed
      queryClient.invalidateQueries({ queryKey: ['getModulesNotInGroup'] })
      queryClient.invalidateQueries({ queryKey: ['getModulesByGroupId'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleSearchForAllModule'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleSearchForGroupModule'] })
      console.log('Assessment posted successfully:', data);
    },
    
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },

    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 