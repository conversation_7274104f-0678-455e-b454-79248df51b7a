import { useMutation } from '@tanstack/react-query';
import { updateGroup } from '@/api/admin/group/updateGroup';
import { UpdateGroup } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';

export function useUpdateGroup(data: UpdateGroup) { 
  const queryClient = useQueryClient();
  return useMutation(() => updateGroup(data), {
    onSuccess: (data) => {
      // Handle success if needed
      
      queryClient.invalidateQueries({ queryKey: ['getAllGroups'] })
      console.log('Assessment posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 