import { useMutation } from '@tanstack/react-query';
import { UpdateUser } from "@/types/LMSTypes";
import { updateUser } from '@/api/admin/updateUser';
import { useQueryClient } from '@tanstack/react-query';

export function useUpdateUser(data: UpdateUser) {
  const queryClient = useQueryClient();
  console.log("abc",queryClient);
  return useMutation(() => updateUser(data), {
    onSuccess: (data) => {
      
      queryClient.invalidateQueries({ queryKey: ['fetchAllUserExt'] })
      queryClient.invalidateQueries({ queryKey: ['getAllModules'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleAssessmentByModuleId'] })
      // Handle success if needed
      console.log('User edit successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 