import { useMutation } from '@tanstack/react-query';
import { addNewUser } from '@/api/admin/addNewUser';
import { AddUser } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';

export function useAddNewUser(AddUser: AddUser[]) { 
    const queryClient = useQueryClient();
    return useMutation(() => addNewUser(AddUser), {


        onSuccess: (data) => {
            
            // Handle success if needed
            
      queryClient.invalidateQueries({ queryKey: ['fetchAllUserExt'] })
            console.log('user posted successfully:', data);
        },
        onError: (error) => {
            // Handle error if needed
            console.error('Error posting assessment:', error);
        },
        onSettled: (data, error) => {
            // Additional handling after mutation is either successful or failed
            console.log('Mutation completed:', { data, error });
        },
    });
}
