import { useQuery } from '@tanstack/react-query';
import { getModuleAssessmentByModuleId } from '@/api/admin/module/getModuleAssessmentByModuleID';

export function useGetModuleAssessmentByModuleId(module_id: number) {
  return useQuery(['getModuleAssessmentByModuleId', module_id], () => getModuleAssessmentByModuleId(module_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 100000,
  });
}
