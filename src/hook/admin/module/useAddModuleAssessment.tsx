import { useMutation } from '@tanstack/react-query';
import { AddModuleAssessment } from "@/types/LMSTypes";
import { addModuleAssessment } from '@/api/admin/module/addModuleAssessment';

export function useAddModuleAssessment(AddModuleAssessmentData: AddModuleAssessment[]) {
  return useMutation(() => addModuleAssessment(AddModuleAssessmentData), {
    onSuccess: (data) => {
      // Handle success if needed

      //   queryClient.invalidateQueries({ queryKey: ['getAllModules'] })
      console.log('Content Added into Module successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting Content Module:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
