import { useMutation } from '@tanstack/react-query';

import { AddModule } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';
import { addModule } from '@/api/admin/module/addModule';

export function useAddModule(addModuleData: AddModule) {
  const queryClient = useQueryClient();
  return useMutation(() => addModule(addModuleData), {

    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['getAllModules'] })
      console.log('Module posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting Module:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 