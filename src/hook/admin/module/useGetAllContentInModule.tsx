import { useQuery } from '@tanstack/react-query';
import { getAllContentInModule } from '@/api/admin/module/getAllContentInModule';

export function useGetAllContentInModule(module_id: number) {

  return useQuery(['getAllContentInModule',module_id], () => getAllContentInModule(module_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  });
}