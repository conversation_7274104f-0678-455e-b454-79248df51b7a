import { useQuery } from '@tanstack/react-query';
import { getAllModules } from '@/api/admin/module/getAllModules';

export function useGetAllModules(offset: number, limit: number) {
    return useQuery(['getAllModules',offset,limit], () => getAllModules(offset, limit), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}