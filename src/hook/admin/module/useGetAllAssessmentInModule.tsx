import { useQuery } from '@tanstack/react-query';
import { getAllAssessmentInModule } from '@/api/admin/module/getAllAssessmentInModule';

export function useGetAllAssessmentInModule(module_id: number) {

  return useQuery(['getAllAssessmentInModule',module_id], () => getAllAssessmentInModule(module_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  });
}