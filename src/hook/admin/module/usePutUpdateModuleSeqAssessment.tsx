
import { putUpdateModuleSeqAssessment } from '@/api/admin/module/putUpdateModuleSeqAssessment';
import { UpdateModuleSeqAssessmentOptions } from '@/types/LMSTypes';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

export function usePutUpdateModuleSeqAssessment() {
  const queryClient = useQueryClient();
  return useMutation((options: UpdateModuleSeqAssessmentOptions) => putUpdateModuleSeqAssessment(options), {
    onSuccess: (data) => {
      // Handle success if needed
      queryClient.invalidateQueries({ queryKey: ['getAllModules'] })
      queryClient.invalidateQueries({ queryKey: ['getModulesById'] });
      queryClient.invalidateQueries({ queryKey: ['getModuleAssessmentByModuleId'] })
      queryClient.invalidateQueries({ queryKey: ['getAllAssessmentInModule'] })
      queryClient.invalidateQueries({ queryKey: ['fetchAllContent'] });
      queryClient.invalidateQueries({ queryKey: ['getModuleContentByModuleId'] });
      queryClient.invalidateQueries({ queryKey: ["getAllContentInModule"] });

      console.log('module Update module seq Assessment posted successfully:', data);
    },
    onError: (error) => {
      console.error('Error posting update module seq Assessment:', error);
    },
    onSettled: (data, error) => {
      console.log('Mutation completed:', { data, error });
    },
  });
}
