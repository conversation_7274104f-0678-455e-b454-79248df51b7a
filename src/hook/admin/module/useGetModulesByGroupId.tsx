import { useQuery } from '@tanstack/react-query';
import { getModulesByGroupId } from '@/api/admin/module/getModulesByGroupId';

export function useGetModulesByGroupId(group_id:number) {
    return useQuery(['getModulesByGroupId',group_id], () => getModulesByGroupId(group_id), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}