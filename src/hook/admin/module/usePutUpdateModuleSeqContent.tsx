
import { putUpdateModuleSeqContent } from '@/api/admin/module/putUpdateModuleSeqCntent';
import { UpdateModuleSeqContentOptions } from '@/types/LMSTypes';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

export function usePutUpdateModuleSeqContent() {
  const queryClient = useQueryClient();
  return useMutation((options: UpdateModuleSeqContentOptions) => putUpdateModuleSeqContent(options), {
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['fetchAllContent'] });
      queryClient.invalidateQueries({ queryKey: ['getModuleContentByModuleId'] });
      queryClient.invalidateQueries({ queryKey: ["getAllContentInModule"] });
      queryClient.invalidateQueries({ queryKey: ['getAllModules'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleAssessmentByModuleId'] })
      queryClient.invalidateQueries({ queryKey: ['getAllAssessmentInModule'] })
      queryClient.invalidateQueries({ queryKey: ['getModulesById'] });
      console.log('Update module seq content posted successfully:', data);
    },
    onError: (error) => {
      console.error('Error posting update module seq content:', error);
    },
    onSettled: (data, error) => {
      console.log('Mutation completed:', { data, error });
    },
  });
}
