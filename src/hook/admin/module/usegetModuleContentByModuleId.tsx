import { useQuery } from '@tanstack/react-query';
import { getModuleContentByModuleId } from '@/api/admin/module/getModuleContentByModuleId';

export function useModuleContentByModuleId(module_id: number) {
  return useQuery(['getModuleContentByModuleId',module_id], () => getModuleContentByModuleId(module_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 100000,
  },);
}