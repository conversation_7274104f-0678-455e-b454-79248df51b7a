import { useMutation } from '@tanstack/react-query';
import { addGroup } from '@/api/admin/group/addGroup';
import { AddModuleContent } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';
import { addModuleContent } from '@/api/admin/module/addModuleContent';

export function useAddModuleContent(AddModuleContentData: AddModuleContent[]) {
  const queryClient = useQueryClient();
  return useMutation(() => addModuleContent(AddModuleContentData), {
    onSuccess: (data) => {
      // Handle success if needed

      //   queryClient.invalidateQueries({ queryKey: ['getAllModules'] })
      console.log('Content Added into Module successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting Content Module:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
