
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteModuleAssessment } from '@/api/admin/module/deleteModuleAssessment';

export function useDeleteModuleAssessment() {
  const queryClient = useQueryClient(); // Get access to the queryClient

  return useMutation(
   
    ({ module_id, assessment_id }) => deleteModuleAssessment(module_id, assessment_id),
    {
      onSuccess: () => {
        // Invalidate and refetch
        // Replace 'assessments' with the actual query key you used for fetching the assessments
        queryClient.invalidateQueries({ queryKey: ['getAllModules'] })
        queryClient.invalidateQueries({ queryKey: ['getModulesById'] });
      queryClient.invalidateQueries({ queryKey: ['getModuleAssessmentByModuleId'] })
      queryClient.invalidateQueries({ queryKey: ['getAllAssessmentInModule'] })
      queryClient.invalidateQueries({ queryKey: ['fetchAllContent'] });
      queryClient.invalidateQueries({ queryKey: ['getModuleContentByModuleId'] });
      queryClient.invalidateQueries({ queryKey: ["getAllContentInModule"] });
      },
    }
  );
}



 