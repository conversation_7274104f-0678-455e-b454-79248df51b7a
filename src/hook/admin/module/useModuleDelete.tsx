
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';
import { moduleDelete } from '@/api/admin/module/moduleDelete';


export function useModuleDelete() {
  const queryClient = useQueryClient();

  // Define the mutation function using useMutation
  return useMutation((moduleId: number) => moduleDelete(moduleId), {
    // onSuccess callback is called when the mutation is successful
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getAllModules'] });
      queryClient.invalidateQueries({ queryKey: ['getModuleAssessmentByModuleId'] })
      queryClient.invalidateQueries({ queryKey: ["getModuleContentByModuleId"]});
      console.log('module deleted successfully:');
    },
    // onError callback is called when there's an error during the mutation
    onError: (error) => {
      console.error('Error deleting content:', error);
    },
    // onSettled callback is called after the mutation is either successful or failed
    onSettled: (data, error, contentId) => {
      // Perform any additional actions here
      console.log('Deletion completed:', { data, error });
    },
  });
}
