
import { postAddModuleContent } from '@/api/admin/module/postAddModuleContent';
import {  AddModuleContentOptions } from '@/types/LMSTypes';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

export function usePostAddModuleContent(options: AddModuleContentOptions) {
  const queryClient = useQueryClient();
  return useMutation(() => postAddModuleContent(options), {
    onSuccess: (data) => {
      // Handle success if needed
      queryClient.invalidateQueries({ queryKey: ['fetchAllContent'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleContentByModuleId'] })
      queryClient.invalidateQueries({ queryKey: ["getAllContentInModule"] });
      queryClient.invalidateQueries({ queryKey: ['getAllModules'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleAssessmentByModuleId'] })
      queryClient.invalidateQueries({ queryKey: ['getAllAssessmentInModule'] })
      queryClient.invalidateQueries({ queryKey: ['getModulesById'] });
      console.log('moduleAssessment posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting assessment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 