import { useMutation, useQueryClient } from "@tanstack/react-query";

import { deleteModuleContent } from "@/api/admin/module/deleteModuleContent";

export function useDeleteModuleContent() {
  const queryClient = useQueryClient(); // Get access to the queryClient

  return useMutation(
    // Mutation function
    ({ module_id, content_id }) => deleteModuleContent(module_id, content_id),

    // onSuccess callback
    {
      onSuccess: () => {
        // Invalidate and refetch
        // Replace 'assessments' with the actual query key you used for fetching the assessments
        queryClient.invalidateQueries({ queryKey: ["fetchAllContent"] });
        queryClient.invalidateQueries({ queryKey: ["getModuleContentByModuleId"]});
        queryClient.invalidateQueries({ queryKey: ["getAllContentInModule"] });
        queryClient.invalidateQueries({ queryKey: ['getAllModules'] })
        queryClient.invalidateQueries({ queryKey: ['getModuleAssessmentByModuleId'] })
        queryClient.invalidateQueries({ queryKey: ['getAllAssessmentInModule'] })
        queryClient.invalidateQueries({ queryKey: ['getModulesById'] });
      },
    }
  );
}
