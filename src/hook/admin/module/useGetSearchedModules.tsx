import { useQuery } from '@tanstack/react-query';
import { getSearchedModules } from '@/api/admin/module/getSearchedModules';

export function useGetSearchedModules(searchText:string) {
    return useQuery(['getSearchedModules', searchText], () => getSearchedModules(searchText), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}