import { useMutation } from '@tanstack/react-query';
import { updateGroup } from '@/api/admin/group/updateGroup';
import {  UpdateModule } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';
import { updateModule } from '@/api/admin/module/updateModule';

export function useUpdateModule() {
  const queryClient = useQueryClient();
  
  return useMutation((data: UpdateModule) => updateModule(data), {
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['getAllModules'] });
      queryClient.invalidateQueries({ queryKey: ['getModulesById'] });
      queryClient.invalidateQueries({ queryKey: ['getModuleContentByModuleId'] })
      queryClient.invalidateQueries({ queryKey: ['getModuleAssessmentByModuleId'] })
      queryClient.invalidateQueries({ queryKey: ['getAllAssessmentInModule'] })
      queryClient.invalidateQueries({ queryKey: ['fetchAllContent'] });
      queryClient.invalidateQueries({ queryKey: ["getAllContentInModule"] });
      
      console.log('Module updated successfully');
    },
    onError: (error) => {
      console.error('Error updating module:', error);
    },
  });
}

 