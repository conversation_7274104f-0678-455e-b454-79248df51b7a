

import { getSearchFromAllContentInTable } from '@/api/admin/usergroup/content/getSearchFromAllContentInTable';
import { useQuery } from '@tanstack/react-query';


export function useGetSearchFromAllContentInTable(group_id:number ,search_text:string) {
    return useQuery(['getContentByGroupIdforcontnentingroup',group_id,search_text], () => getSearchFromAllContentInTable(group_id,search_text), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}