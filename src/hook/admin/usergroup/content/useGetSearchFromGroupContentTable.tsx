
import { getSearchFromGroupContentTable } from '@/api/admin/usergroup/content/getSearchFromGroupContentTable';
import { useQuery } from '@tanstack/react-query';


export function useGetSearchFromGroupContentTable(group_id:number ,search_text:string) {
    return useQuery(['getContentByGroupId',group_id,search_text], () => getSearchFromGroupContentTable(group_id,search_text), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}