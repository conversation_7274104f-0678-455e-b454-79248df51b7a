import { removeContentFromGroup } from '@/api/admin/usergroup/content/removeContentFromGroup';
import { useMutation } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

export function useRemoveContentFromGroup(contentId: number, groupId: number) {
  const queryClient = useQueryClient();
  return useMutation(() => removeContentFromGroup(contentId, groupId), {
    onSuccess: (data) => {
      // Handle success if needed
      
      queryClient.invalidateQueries({ queryKey: ['getContentByGroupId'] })
      queryClient.invalidateQueries({ queryKey: ['getContentByGroupIdforcontnentingroup'] })
      queryClient.invalidateQueries({ queryKey: ['fetchAllContent'] })
      
      console.log('content deleted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error deleted content:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 