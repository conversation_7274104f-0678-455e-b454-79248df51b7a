import { getContentByGroupId } from '@/api/admin/usergroup/content/getContentByGroupId';
import { useQuery } from '@tanstack/react-query';


export function useGetContentByGroupId(group_id:number) {
    return useQuery(['getContentByGroupId',group_id], () => getContentByGroupId(group_id), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}