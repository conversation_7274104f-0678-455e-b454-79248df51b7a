import { addContentToGroup } from '@/api/admin/usergroup/content/addContentToGroup';
import { AddRemoveContentGroup } from '@/types/LMSTypes';
import { useMutation } from '@tanstack/react-query';

import { useQueryClient } from '@tanstack/react-query';


export function useAddContentToGroup(options: AddRemoveContentGroup) {
  const queryClient = useQueryClient();
  return useMutation(() => addContentToGroup(options), {

  
    onSuccess: (data) => {
      // Handle success if needed
      
     
      queryClient.invalidateQueries({ queryKey: ['getContentByGroupId'] })
      console.log('content posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting content:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 