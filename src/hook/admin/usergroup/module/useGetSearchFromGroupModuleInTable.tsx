
import { getSearchFromGroupModuleInTable } from '@/api/admin/usergroup/module/getSearchFromGroupModuleInTable';
import { useQuery } from '@tanstack/react-query';


export function useGetSearchFromGroupModuleInTable(group_id:number ,search_text:string) {
    return useQuery(['getModuleSearchForGroupModule',group_id,search_text], () => getSearchFromGroupModuleInTable(group_id,search_text), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}