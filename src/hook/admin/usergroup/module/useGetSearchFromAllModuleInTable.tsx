


import { getSearchFromAllModuleInTable } from '@/api/admin/usergroup/module/getSearchFromAllModuleInTable';
import { useQuery } from '@tanstack/react-query';


export function useGetSearchFromAllModuleInTable(group_id:number ,search_text:string) {
    return useQuery(['getModuleSearchForAllModule',group_id,search_text], () => getSearchFromAllModuleInTable(group_id,search_text), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}