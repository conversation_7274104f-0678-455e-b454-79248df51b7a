import { useQuery } from '@tanstack/react-query';
import { getUserSearch } from '@/api/admin/usergroup/alluser/getUserSearch';
 
export function useGetUserSearch(group_id: number, search_text:string) {
    return useQuery(['getUserInGroupSearch',group_id,search_text], () => getUserSearch(group_id,search_text), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}