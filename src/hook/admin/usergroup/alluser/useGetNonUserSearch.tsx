import { getNonUserSearch } from '@/api/admin/usergroup/alluser/getNonUserSearch';
import { useQuery } from '@tanstack/react-query';

 
export function useGetNonUserSearch(group_id: number, search_text:string) {
    return useQuery(['getUserNotInGroupSearch',group_id,search_text], () => getNonUserSearch(group_id,search_text), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },);
}