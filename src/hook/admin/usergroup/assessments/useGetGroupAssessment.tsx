import { useQuery } from '@tanstack/react-query';
import { getGroupAssessment } from '@/api/admin/usergroup/assessments/getGroupAssessment';
import { useQueryClient } from "@tanstack/react-query";


// 'getGroupAssessment' similar key is also there
export function useGetGroupAssessment(group_id: number, search_text:string) {
    
    return useQuery(['getAssessmentsInGroup', group_id, search_text], () => getGroupAssessment(group_id,search_text), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
    },); 
}  