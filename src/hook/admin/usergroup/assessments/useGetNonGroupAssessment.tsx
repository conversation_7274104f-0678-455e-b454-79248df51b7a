import { useQuery } from '@tanstack/react-query';
import { getNonGroupAssessment } from '@/api/admin/usergroup/assessments/getNonGroupAssessment';
import { useQueryClient } from "@tanstack/react-query";

export function useGetNonGroupAssessment(group_id: number, search_text:string) {
    return useQuery(['getAssessmentsNotInGroup', group_id, search_text], () => getNonGroupAssessment(group_id,search_text), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1, 
    },);
} 