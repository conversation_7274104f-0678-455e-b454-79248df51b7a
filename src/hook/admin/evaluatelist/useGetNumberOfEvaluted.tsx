import { getNumberOfEvaluted } from '@/api/admin/evaluatelist/getNumberOfEvaluated';
import { useQuery } from '@tanstack/react-query';

export default function useGetNumberOfEvaluted(assessment_id:number) {
    return useQuery(['getNumberOfEvaluted',assessment_id], () => getNumberOfEvaluted(assessment_id), {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        staleTime: 1000 * 60 * 60 * 1,
      },);
    }
