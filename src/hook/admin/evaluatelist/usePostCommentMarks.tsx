import { useMutation } from '@tanstack/react-query';
import {  CommentMarks } from "@/types/LMSTypes";
import { useQueryClient } from '@tanstack/react-query';

import { postCommentMarks } from '@/api/admin/evaluatelist/postCommentMarks';

export function usePostCommentMarks(CommentMarks: CommentMarks[] ,user_assessment_attempt_ext_id:number) {
  const queryClient = useQueryClient();
  return useMutation(() => postCommentMarks(CommentMarks ,user_assessment_attempt_ext_id ), {

    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['getAllManualUser'] })
      queryClient.invalidateQueries({ queryKey: ['getAllManualQuestion'] })
      queryClient.invalidateQueries({ queryKey: ['getAllManualAssessment'] })
      console.log('marks and comment posted successfully:', data);
    },
    onError: (error) => {
      // Handle error if needed
      console.error('Error posting amrks and comment:', error);
    },
    onSettled: (data, error) => {
      // Additional handling after mutation is either successful or failed
      console.log('Mutation completed:', { data, error });
    },
  });
}
 