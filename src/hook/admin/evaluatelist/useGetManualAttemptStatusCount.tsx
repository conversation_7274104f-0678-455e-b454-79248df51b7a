
import { useQuery } from '@tanstack/react-query';
import { getManualAttemptStatusCount } from '@/api/admin/evaluatelist/getManualAttemptStatusCount';

export function SetManualAttemptStatusCount(assessment_id :number) {
  return useQuery(['SetManualAttemptStatusCount',assessment_id ], () => getManualAttemptStatusCount(assessment_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}
