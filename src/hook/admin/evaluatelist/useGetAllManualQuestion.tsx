
import { useQuery } from '@tanstack/react-query';
import { getAllManualQuestion } from '@/api/admin/evaluatelist/getAllManualQuestion';

export function useGetAllManualQuestion(user_assessment_attempt_id :number) {
  return useQuery(['getAllManualQuestion',user_assessment_attempt_id ], () => getAllManualQuestion(user_assessment_attempt_id ), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}
