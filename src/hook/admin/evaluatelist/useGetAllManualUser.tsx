import { useQuery } from '@tanstack/react-query';
import { getAllManualUser } from '@/api/admin/evaluatelist/getAllManualUser';

export function useGetAllManualUser(assessment_id:number) {
  return useQuery(['getAllManualUser',assessment_id], () => getAllManualUser(assessment_id), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    staleTime: 1000 * 60 * 60 * 1,
  },);
}
