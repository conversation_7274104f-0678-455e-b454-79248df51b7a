import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteQuestionFromTable } from "@/api/admin/question/deleteQuestionFromTable";

export function useDeleteQuestionFromTable() {
  const queryClient = useQueryClient(); // Get access to the queryClient

  return useMutation(
    // Mutation function
    ({ question_id }) => deleteQuestionFromTable(question_id),

    // onSuccess callback
    {
      onSuccess: () => {
        // Invalidate and refetch
        // Replace 'assessments' with the actual query key you used for fetching the assessments
        queryClient.invalidateQueries({ queryKey: ["fetchAllFilteredQuestion"] });
        queryClient.invalidateQueries({ queryKey: ["getAllQuestionFilter"] });
      
      },
    }
  );
}
