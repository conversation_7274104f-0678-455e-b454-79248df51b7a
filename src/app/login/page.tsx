"use client";

import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { signIn } from "@/api/user/userSignIn";
import { saveUser } from "@/api/user.localStorage";
import ForgetPassword from "@/components/login/ForgetPasswork";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false); // New state for loading
  const router = useRouter();
  const [profileModalOpen, setProfileModalOpen] = useState(false);

  const handleUserNavigation = () => {
    setProfileModalOpen(true);
  };

  const handleCloseProfileModal = () => {
    setProfileModalOpen(false);
  };

  const handleSignIn = async () => {
    setIsLoading(true); // Set loading to true
    try {
      const user = await signIn(email, password);
      if (user?.detail) {
        setIsLoading(false);
        setErrorMessage("Invalid username or password.");
      } else {
        console.log("user", user);
        saveUser(user);
        if (user?.roles.some((role) => role.ROLE.includes("ADMIN"))) {
          router.push("/admin/roles");
        } else {
          router.push("/dashboard/roles");
        }
      }
    } catch (error) {
      setIsLoading(false);
      setErrorMessage("An error occurred. Please try again.");
    }
  };

  return (
    <>
      <div className="flex min-h-full bg-primary flex-1 flex-col justify-center px-6 py-12 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <Image
            className="mx-auto h-10 w-auto"
            src="/logo_colour.png"
            alt="Skilling.ai"
            width={100}
            height={60}
          />

          <h2 className="mt-10 text-center text-2xl font-bold leading-9 tracking-tight text-textPrimary">
            Sign in to your account
          </h2>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
          <div className="space-y-6">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium leading-6 text-textColor"
              >
                Email address
              </label>

              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter Email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  autoComplete="email"
                  required
                  className="block w-full rounded-md border-0 py-1.5 px-1 text-textColor shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium leading-6 text-textColor"
                >
                  Password
                </label>

                <div className="text-sm">
                  <a
                    href="#"
                    onClick={handleUserNavigation}
                    className="font-semibold text-textSecondary hover:text-hoverColor"
                  >
                    Forgot password?
                  </a>
                </div>
              </div>

              <div className="mt-2">
                <input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  autoComplete="current-password"
                  required
                  className="block w-full rounded-md border-0 py-1.5 px-1 text-textColor shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
              </div>
            </div>
            {errorMessage && (
              <div className="flex justify-center text-sm text-red-500">
                {errorMessage}
              </div>
            )}
            <div>
              <button
                title="login btn"
                type="button"
                onClick={handleSignIn}
                disabled={isLoading} // Disable button when loading
                className="flex w-full justify-center rounded-md bg-secondary px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg
                      className="animate-spin h-5 w-5 mr-3 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Signing in...
                  </div>
                ) : (
                  "Sign in"
                )}
              </button>
            </div>
          </div>

          <p className="mt-10 text-center text-sm text-gray-500">
            Not a member?{" "}
            <a
              href="/"
              className="font-semibold leading-6 text-textSecondary hover:text-hoverColor"
            >
              Let&apos;s get started!
            </a>
          </p>
        </div>

        {profileModalOpen && (
          <ForgetPassword isOpen={true} onClose={handleCloseProfileModal} />
        )}
      </div>
    </>
  );
}