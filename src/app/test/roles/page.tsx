"use client"
import Heading from '@/components/ui/Heading';
import Nothing from '@/components/ui/Nothing';
import React, { useState } from 'react';
import Link from 'next/link';
import JobCard from '@/components/dashboard/roles/JobCard';
import { useGetAllRolesTitleOffsetLimit } from '@/hook/dashboard/job_titles/useGetAllRolesTitleOffsetLimit';
import Button001 from '@/components/ui/Button001';

interface Pagination {
  limit: number;
  offset: number;
}

interface Job {
  job_id: number;
  job_title: string;
  job_description?: string;  // Make description optional
}

const rolesData: Job[] = [
  {
    job_id: 1,
    job_title: "Junior Data Associate",
    job_description: "Assist in developing software applications, analyzing data, and building tools to improve business processes."
  },
  {
    job_id: 2,
    job_title: "AI Data Scientist",
    job_description: "Lead the development of AI-based solutions, manage data pipelines, and oversee the entire product lifecycle from planning to execution."
  }
];

// Dummy colors for the job cards
const colors: string[] = [
  'bg-blue-200',
  'bg-green-200',
  'bg-yellow-200',
  'bg-pink-200',
  'bg-purple-200',
];

const Page = () => {

  const [selectedJobIds, setSelectedJobIds] = useState<number[]>([]);  // Track selected job IDs
  const [values, setValues] = useState<Pagination>({
    limit: 0,
    offset: 6
  })

  const handleroleselection = (jobId: number) => {
    setSelectedJobIds((prevSelectedIds) => {
      // Check if the job is already selected
      if (prevSelectedIds.includes(jobId)) {
        // Deselect if already selected
        return prevSelectedIds.filter((id) => id !== jobId);
      }

      // If not selected, check if there are already 3 roles selected
      if (prevSelectedIds.length >= 3) {
        // If already 3 roles are selected, do nothing
        return prevSelectedIds;
      }

      // Add to selection if not already selected and less than 3 roles are selected
      return [...prevSelectedIds, jobId];
    });
  };

  const handleNext = () => {
    setValues(prevValues => ({
      limit: prevValues.limit + 6,
      offset: prevValues.offset + 6
    }));
  }

  const handlePrevious = () => {
    if (!(values.limit <= 0)) {
      setValues(prevValues => ({
        limit: prevValues.limit - 6,
        offset: prevValues.offset - 6
      }));
    }
  }


  const { data: getRolesData } = useGetAllRolesTitleOffsetLimit(values.limit, values.offset);

  console.log("getRolesData", getRolesData)
  console.log("selectedJobIds", selectedJobIds)

  if (!Array.isArray(rolesData)) {
    return (
      <div>Loading...</div>
    )
  }
  else {
    return (
      <main className="relative w-full flex min-h-screen bg-primary flex-col items-center justify-between p-4">
        {/* {true && <SideNotification />} */}
        <div className="w-full h-full">
          <div className="p-3 w-full flex flex-col lg:flex-row gap-4 justify-between">
            <Heading pgHeading="Roles" />
            {/* <div className='w-full sm:w-[320px] lg:w-[460px] flex flex-col lg:flex-row justify-between gap-4'>
              <Button001 href="/dashboard/roles/applied" buttonName="Applied Roles" />
              <Button001 href="/dashboard/roles/saved" buttonName="Roles Selected" />
              <div className="flex-grow">
                <input
                  type="text"
                  // value={query}
                  // onChange={handleChange}
                  placeholder="Search..."
                  className="w-full py-[6px] px-4 text-textSecondary bg-transparent border-2 border-textSecondary placeholder:text-textSecondary rounded focus:outline-none focus:ring-2 focus:ring-textSecondary transition-all duration-300"
                />
              </div>
            </div> */}
          </div>
          <div className="grid grid-cols-1 p-2 md:grid-cols-2 lg:grid-cols-3 flex-wrap gap-5 justify-start bg-white w-full rounded-2xl  h-[74vh] overflow-y-auto">
            {(!rolesData || rolesData.length === 0) ? (
              <div className="w-full justify-center items-center h-full grid grid-cols-3">
                <Nothing
                  title="No Job titles Available"
                  para="There are currently no job titles to display.
                            Please check back later."
                />
              </div>
            ) : (
              rolesData?.map((data, index) => (
                <JobCard
                  key={data.job_title_id}
                  job={data}
                  index={index}
                  colors={colors}
                  selected={selectedJobIds.includes(data.job_title_id)} // Pass selected status
                  onClick={handleroleselection} // Pass the selection handler
                />
              ))
            )}

          </div>
          {/* <div className='w-full h-fit flex justify-between mt-4 lg:px-80'>
            <Link href={``} className="w-[150px]">
              <button
                className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded text-sm w-full disabled:border-gray-400 disabled:text-gray-400"
                onClick={handlePrevious}
                disabled={values.limit <= 0}>
                {"Previous"}

              </button>
            </Link>
            <Link href={``} className="w-[150px]">
              <button
                className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded text-sm w-full"
                onClick={handleNext}
              >
                {"Next"}
              </button>
            </Link>
          </div> */}
        </div>
      </main>
    );
  }
};

export default Page;
