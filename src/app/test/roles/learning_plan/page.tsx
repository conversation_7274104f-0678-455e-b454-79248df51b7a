"use client";
import React, { useEffect, useState } from "react";
import Heading from "@/components/ui/Heading";
import LearningPath from "@/components/dashboard/roles/learning_plan/LearningPath";
import { useGetAllModulesForUser } from "@/hook/dashboard/program/useGetAllModulesForUser";

export default function LearningPathPage() {
  const [dummyData, setdummyData] = useState([]);

  const sample_data = [
    {
      "module_id": 4,
      "group_id": 1,
      "sequence": 1,
      "module_name": "Introduction to Basic Programming",
      "module_headline": "Understanding Basic Programming Concepts",
      "module_description": "This module covers the basics of programming, including variables, data types, and control structures.",
      "group_name": "Beginner Level Programming",
      "isactive": 1,
      "group_admin_user_id": 2,
      "created_by": "<EMAIL>",
      "completed": "in_Progress"
    },
    {
      "module_id": 5,
      "group_id": 2,
      "sequence": 2,
      "module_name": "Advanced Algorithms",
      "module_headline": "Delving Deeper into Algorithm Design",
      "module_description": "This module focuses on advanced algorithmic concepts such as sorting, searching, and optimization techniques.",
      "group_name": "Advanced Programming",
      "isactive": 0,
      "group_admin_user_id": 3,
      "created_by": "<EMAIL>",
      "completed": "not_completed"
    },
    {
      "module_id": 6,
      "group_id": 3,
      "sequence": 3,
      "module_name": "Machine Learning Foundations",
      "module_headline": "Intro to Machine Learning Concepts and Models",
      "module_description": "This module introduces the foundational concepts of machine learning, including supervised and unsupervised learning, and basic algorithms.",
      "group_name": "Expert Level Programming",
      "isactive": 1,
      "group_admin_user_id": 4,
      "created_by": "<EMAIL>",
      "completed": "in_Progress"
    },
    {
      "module_id": 7,
      "group_id": 4,
      "sequence": 4,
      "module_name": "Web Development Basics",
      "module_headline": "Building Your First Website with HTML, CSS, and JavaScript",
      "module_description": "Learn the fundamentals of web development, including HTML, CSS, and JavaScript for creating basic websites.",
      "group_name": "Beginner Web Development",
      "isactive": 1,
      "group_admin_user_id": 5,
      "created_by": "<EMAIL>",
      "completed": "completed"
    },
    {
      "module_id": 8,
      "group_id": 5,
      "sequence": 5,
      "module_name": "Intermediate Web Development",
      "module_headline": "Enhancing Websites with Advanced Techniques",
      "module_description": "This module covers more advanced web development topics such as responsive design, CSS frameworks, and JavaScript frameworks.",
      "group_name": "Intermediate Web Development",
      "isactive": 1,
      "group_admin_user_id": 6,
      "created_by": "<EMAIL>",
      "completed": "completed"
    },
    {
      "module_id": 9,
      "group_id": 6,
      "sequence": 6,
      "module_name": "Full-Stack Development",
      "module_headline": "Building End-to-End Applications with Modern Tools",
      "module_description": "Learn to develop full-stack applications by mastering both front-end and back-end technologies like React, Node.js, and MongoDB.",
      "group_name": "Expert Full-Stack Development",
      "isactive": 1,
      "group_admin_user_id": 7,
      "created_by": "<EMAIL>",
      "completed": "completed"
    }
  ];
  

  const {
    data: modulesDatas,
    isLoading: isLoadingModule,
    isError: isErrorModule,
  } = useGetAllModulesForUser(1);

  useEffect(() => {
    if (Array.isArray(modulesDatas)) {
      setdummyData(modulesDatas.concat(sample_data));
    }
  }, [modulesDatas]);

  if (isLoadingModule) {
    return <div>Loading...</div>;
  }

  if (isErrorModule) {
    return <div>Error fetching modules</div>;
  }

  if (!dummyData || dummyData.length === 0) {
    return <div>No modules found.</div>;
  }

  return (
    <main className="w-full flex min-h-full bg-primary flex-col items-center justify-between p-4" aria-label="Learning Path Page">
      <div className="w-full overflow-auto">
        <div className="p-3">
          <Heading pgHeading="Learning Plan" />
        </div>
        <div className="grid grid-cols-1 gap-5 bg-white w-full h-[70vh] rounded-2xl p-6 overflow-auto border-y-8 border-white">
          {dummyData.map((modulesDetails) => (
            <LearningPath
              key={modulesDetails.module_id}
              moduleData={modulesDetails}
            />
          ))}
        </div>
      </div>
    </main>
  );
}