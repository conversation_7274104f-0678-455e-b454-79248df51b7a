"use client";
import React from "react";
import Heading from "@/components/ui/Heading";
import Link from "next/link"; // Import Link for navigation

export default function SkillGapReportPage() {
    // Example data for skill gaps
    const skillGaps = [
        { skill: "Data Structures", currentLevel: 60, targetLevel: 90 },
        { skill: "Machine Learning", currentLevel: 40, targetLevel: 80 },
        { skill: "Cloud Computing", currentLevel: 50, targetLevel: 85 },
        { skill: "DevOps", currentLevel: 30, targetLevel: 75 },
        { skill: "UI/UX Design", currentLevel: 70, targetLevel: 95 },
    ];

    return (
        <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-4">
            <div className="w-full max-w-6xl overflow-auto">
                <div className="p-3">
                    <Heading pgHeading="Skill Gap Report" />
                </div>
                <div className="flex flex-wrap gap-5 justify-center bg-white w-full rounded-2xl p-5">
                    {skillGaps.map((gap, index) => (
                        <div key={index} className="w-full sm:w-1/2 md:w-1/3 lg:w-1/4 p-2">
                            <div className="bg-gray-100 rounded-lg p-4">
                                <h3 className="text-lg font-semibold mb-2">{gap.skill}</h3>
                                <div className="space-y-2">
                                    <div>
                                        <span className="text-sm text-gray-600">Current Level: {gap.currentLevel}%</span>
                                        <div className="w-full bg-gray-300 rounded-full h-2.5">
                                            <div
                                                className="bg-blue-600 h-2.5 rounded-full"
                                                style={{ width: `${gap.currentLevel}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                    <div>
                                        <span className="text-sm text-gray-600">Target Level: {gap.targetLevel}%</span>
                                        <div className="w-full bg-gray-300 rounded-full h-2.5">
                                            <div
                                                className="bg-green-600 h-2.5 rounded-full"
                                                style={{ width: `${gap.targetLevel}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Buttons Section */}
                <div className="flex flex-col items-center gap-4 mt-8">
                    <Link href="/dashboard/roles/learning_plan">
                        <button
                            onClick={() => {
                                // Add functionality to generate a learning path
                                alert("Generating a learning path...");
                            }}
                            className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
                        >
                            Generate a Learning Plan
                        </button>
                    </Link>
                </div>
            </div>
        </main>
    );
}