"use client";
import React, { useState } from "react";
import Heading from "@/components/ui/Heading";
import Button001 from "@/components/ui/Button001";
import { XMarkIcon } from "@heroicons/react/24/solid"; // Import the X icon from Heroicons
import { useRouter } from 'next/navigation';
import InstructionModal from "@/components/ui/InstructionModal";

export default function ModulePage() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-4">
      {isModalOpen &&

        <InstructionModal
          title="Instructions"
          paragraph={
            <>
              Welcome to the assessment! Please follow these instructions carefully:
              <ul className="list-disc pl-5">
                <li>The assessment consists of <strong>20 questions</strong>.</li>
                <li>You will have <strong>60 minutes</strong> to complete the test.</li>
                <li>Read each question carefully before answering.</li>
                <li>Once you start, the timer will begin, and you cannot pause or restart the test.</li>
                <li>Ensure you have a stable internet connection to avoid interruptions.</li>
                <li>Do not refresh the page or navigate away during the assessment, as it may result in losing your progress.</li>
                <li>Click <strong>Start Assessment</strong> when you are ready to begin.</li>
                <li>Good luck! Do your best to demonstrate your knowledge and skills.</li>
              </ul>
            </>
          }
          closeModal={closeModal}
        />}
      <div className="w-full overflow-auto">
        <div className="p-3">
          <Heading pgHeading="Assessment" />
        </div>
        <div className="flex flex-wrap gap-1 justify-center bg-white w-full rounded-2xl p-4 h-[75vh] overflow-y-auto">
          <div className="h-[80%] w-full flex flex-col gap-10">
            <p>
              In order to assess your current knowledge and skills that overlap with the required skill set, please take this assessment. The questions are all related to the set of skills and tools listed in the skill master, and are designed to assess both your ability to recall appropriate tools/techniques/approaches as well as your ability to apply them correctly
            </p>

            <div className="h-fit flex flex-col">
              <span>Test Duration: 60 mins</span>
              <span>Number of questions: 20</span>
            </div>

            <span>Please read the <button
              onClick={() => openModal()}
              className="text-textSecondary hover:text-blue-800 duration-300">instructions</button> and questions carefully​</span>
          </div>



          {/* Generate Button */}
          <div className="w-full flex justify-center mt-8">
            <Button001 href="/dashboard/2" buttonName="Start Assessment" />
          </div>
        </div>
      </div>
    </main>
  );
}