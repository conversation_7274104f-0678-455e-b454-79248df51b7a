"use client";
import React from "react";
import Heading from "@/components/ui/Heading";
import Link from "next/link"; // Import Link for navigation
import Button001 from "@/components/ui/Button001";

export default function ModulePage() {
  // New skills data based on the combined JSON format
  const skillsData = [
    {
      Skill: "Business Intelligence",
      Score: 80,
      Expected: 100,
      Gap: 20,
      SubSkills: "KPI Development: 10%",
      Weight: "10%",
      Level: "Intermediate",
      Tools_Techniques_Mastery: [],
    },
    {
      Skill: "Data Visualization",
      Score: 82,
      Expected: 100,
      Gap: 18,
      SubSkills: "Interactive Dashboards: 8%",
      Weight: "5%",
      Level: "Intermediate",
      Tools_Techniques_Mastery: ["Tableau: Intermediate"],
    },
    {
      Skill: "Big Data Processing",
      Score: 78,
      Expected: 100,
      Gap: 22,
      SubSkills: "Distributed Computing: 7%",
      Weight: "5%",
      Level: "Advanced",
      Tools_Techniques_Mastery: ["Hadoop: Intermediate"],
    },
    {
      Skill: "Scikit-learn (ML)",
      Score: 83,
      Expected: 100,
      Gap: 17,
      SubSkills: "Feature Engineering: 6%",
      Weight: null,
      Level: "Intermediate",
      Tools_Techniques_Mastery: ["Scikit-learn: Intermediate"],
    },
    {
      Skill: "Tableau",
      Score: 85,
      Expected: 100,
      Gap: 15,
      SubSkills: "",
      Weight: null,
      Level: null,
      Tools_Techniques_Mastery: ["Tableau: Intermediate"],
    },
    {
      Skill: "Machine Learning",
      Score: 79,
      Expected: 100,
      Gap: 21,
      SubSkills: "Regression Analysis: 7%",
      Weight: "5%",
      Level: "Intermediate",
      Tools_Techniques_Mastery: ["Python: Advanced", "Scikit-learn: Intermediate"],
    },
    {
      Skill: "Data Management",
      Score: 80,
      Expected: 100,
      Gap: 20,
      SubSkills: "Data Cleaning: 10%, Missing Value Imputation: 5%, Data Transformation: 5%",
      Weight: "15%",
      Level: "Advanced",
      Tools_Techniques_Mastery: ["SQL: Advanced"],
    },
    {
      Skill: "Statistical Analysis",
      Score: 81,
      Expected: 100,
      Gap: 19,
      SubSkills: "Hypothesis Testing: 9%",
      Weight: "10%",
      Level: "Advanced",
      Tools_Techniques_Mastery: ["Python: Advanced"],
    },
    {
      Skill: "Predictive Analytics",
      Score: 79,
      Expected: 100,
      Gap: 21,
      SubSkills: "Regression Analysis: 6%",
      Weight: "5%",
      Level: "Advanced",
      Tools_Techniques_Mastery: ["Python: Advanced", "Scikit-learn: Intermediate"],
    },
    {
      Skill: "Business Skills",
      Score: 84,
      Expected: 100,
      Gap: 16,
      SubSkills: "Domain Expertise: 4%, Communication Skills: 4%, Project Management: 4%, Ethics and Governance: 4%",
      Weight: "5%",
      Level: "Intermediate",
      Tools_Techniques_Mastery: [],
    },
  ];

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-4">
      <div className="w-full overflow-auto">
        <div className="p-3">
          <Heading pgHeading="Skills Identified from uploaded files" />
        </div>
        <div className="flex flex-wrap gap-1 justify-center bg-white w-full rounded-2xl p-4 h-[62vh] overflow-y-auto">
          {/* Table to display skills data */}
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-200">
                <th className="border border-gray-300 p-2">Skill</th>
                <th className="border border-gray-300 p-2">Score</th>
                <th className="border border-gray-300 p-2">Expected</th>
                <th className="border border-gray-300 p-2">Gap</th>
                <th className="border border-gray-300 p-2">Sub-Skills</th>
                <th className="border border-gray-300 p-2">Weight</th>
                <th className="border border-gray-300 p-2">Level</th>
                <th className="border border-gray-300 p-2">Tools/Techniques Mastery</th>
              </tr>
            </thead>
            <tbody>
              {skillsData.map((skill, index) => (
                <tr key={index} className="border border-gray-300">
                  <td className="border border-gray-300 p-2 font-medium">{skill.Skill}</td>
                  <td className="border border-gray-300 p-2">{skill.Score}</td>
                  <td className="border border-gray-300 p-2">{skill.Expected}</td>
                  <td className="border border-gray-300 p-2">{skill.Gap}</td>
                  <td className="border border-gray-300 p-2">{skill.SubSkills || "N/A"}</td>
                  <td className="border border-gray-300 p-2">{skill.Weight || "N/A"}</td>
                  <td className="border border-gray-300 p-2">{skill.Level || "N/A"}</td>
                  <td className="border border-gray-300 p-2">
                    <ul className="list-disc list-inside">
                      {skill.Tools_Techniques_Mastery.map((tool, idx) => (
                        <li key={idx}>{tool}</li>
                      ))}
                    </ul>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Proceed to Assessment Button */}
        <div className="flex justify-center mt-8 gap-10">
          <Button001 href="/dashboard/skills/assessment" buttonName="Assessments" />
        </div>
      </div>
    </main>
  );
}