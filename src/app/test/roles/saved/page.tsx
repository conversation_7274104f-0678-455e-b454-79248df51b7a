"use client"
import Heading from '@/components/ui/Heading';
import Nothing from '@/components/ui/Nothing';
import React, { useState } from 'react';
import Link from 'next/link';
import rolesavedCard from '@/components/dashboard/roles/saved/rolesavedCard';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

// Dummy data for roles
const rolesData = [
  { id: '1', description: 'Job Description 1 - This is a longer description to demonstrate scrolling.  It should wrap and allow for vertical scrolling.  More text to show the functionality.' },
  { id: '2', description: 'Job Description 2 - Another job description.  This one is shorter.' },
  { id: '3', description: 'Job Description 3 -  A third description, perhaps with different requirements and responsibilities.  More text for scrolling:  A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z' },
  // ... more roles
];

// Dummy colors for the job cards
const colors = [
  'bg-blue-200',
  'bg-green-200',
  'bg-yellow-200',
  'bg-pink-200',
  'bg-purple-200',
];

const JobPopup = ({ job, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center">
      <div className="bg-white p-5 rounded-lg w-[400px]">
        <h2 className="text-xl font-bold mb-4">Job Details</h2>
        <p><strong>Job Title:</strong> {job.job_title}</p>
        <p><strong>CV Uploaded:</strong> Yes</p>
        <div className="mt-4">
          <h3 className="font-semibold">Questions:</h3>
          <ul>
            <li>Why are you interested in this position?</li>
            <li>What relevant experience do you have?</li>
            <li>Where do you see yourself in 5 years?</li>
          </ul>
        </div>
        <button onClick={onClose} className="mt-4 w-full bg-red-500 text-white py-2 rounded">
          Close
        </button>
      </div>
    </div>
  );
};

const Page = () => {
  const [selectedJob, setSelectedJob] = useState(null);
  const [selectedJobIds, setSelectedJobIds] = useState<string[]>([]);

  const handleJobClick = (job) => {
    setSelectedJob(job);
  };

  const handleClosePopup = () => {
    setSelectedJob(null);
  };

  const handleroleselect = (jobId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedJobIds([...selectedJobIds, jobId]);
    } else {
      setSelectedJobIds(selectedJobIds.filter((id) => id !== jobId));
    }
  };

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-4">
      <div className="w-full overflow-auto">
        <div className="p-3 w-full flex justify-between">
          <Heading pgHeading="Roles Saved " />
          <Link href={`/dashboard/roles`} className="w-[150px]">
            <button className="flex items-center justify-center border-2 border-textSecondary text-textSecondary font-bold rounded text-sm w-full gap-2 p-2 hover:bg-textSecondary hover:text-white transition-all duration-300">
              <ArrowLeftIcon className='w-5 h-5' />
              {"Roles Data"}
            </button>
          </Link>
        </div>

        <div className="flex flex-wrap gap-5 justify-start bg-white w-full rounded-2xl p-5 h-[70vh] overflow-y-auto">
          {(!rolesData || rolesData.length === 0) ? (
            <div className="w-full flex justify-center items-center h-full">
              <Nothing
                title="No Job titles Available"
                para="There are currently no job titles to display. Please check back later."
              />
            </div>
          ) : (
            <div className='grid md:grid-cols-2 lg:grid-cols-3 lg:gap-20 py-10'>
              {rolesData.map((data, index) => (
                <rolesavedCard
                  key={data.id}
                  jobDescription={data.description}
                  jobId={data.id}
                  onSelect={handleroleselect}
                />
              ))}
            </div>

          )}
        </div>
        <div className='w-full h-fit flex justify-center mt-4'>
          <Link href={`/dashboard/roles/cvmatch`} className="w-[150px]">
            <button
              className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded text-sm w-full hover:bg-textSecondary hover:text-white transition-all duration-300">
              {"Match CV"}
            </button>
          </Link>
          {/* <Link href={``} className="w-[150px]">
            <button className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded text-sm w-full">
              {"Next"}
            </button>
          </Link> */}
        </div>
      </div>

      {/* Display popup if a job is selected */}
      {selectedJob && (
        <JobPopup job={selectedJob} onClose={handleClosePopup} />
      )}
    </main>
  );
};

export default Page;
