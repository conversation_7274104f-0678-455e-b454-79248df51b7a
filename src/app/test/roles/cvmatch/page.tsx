"use client"
import Heading from '@/components/ui/Heading';
import Nothing from '@/components/ui/Nothing';
import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { ArrowLeftIcon, CloudArrowUpIcon, XMarkIcon } from '@heroicons/react/24/outline';
import QuestionaireModal from '@/components/dashboard/roles/cvmatch/QuestionaireModal';

const UploadForm = ({ uploadedFile, setUploadedFile }) => {

    const handleFileChange = (event) => {
        setUploadedFile(event.target.files[0]);
    };

    return (
        <div className="h-fit w-fit flex flex-col items-center gap-10 p-4 sm:p-6 md:p-8 lg:p-10 border-dashed border-[#407BBF] border-2 rounded-sm bg-white shadow-lg ">

            {uploadedFile && <button
                onClick={() => setUploadedFile(null)}
                className='w-full h-fit flex justify-end
            '>
                <XMarkIcon className='w-8 h-8 text-[#407BBF]' />
            </button>}

            <input
                type="file"
                accept=".pdf, .doc, .docx"
                className="hidden"
                id="fileInput"
                onChange={handleFileChange} // Added onChange handler
            />
            <label
                htmlFor="fileInput"
                className="rounded-md flex flex-col items-center py-2 px-12 sm:px-20 md:px-32 lg:px-48 cursor-pointer" // Added cursor-pointer
            >

                <CloudArrowUpIcon className="w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 text-[#407BBF]" />
                <h1 className="text-center text-xl sm:text-2xl text-[#407BBF] font-semibold mt-4">
                    {uploadedFile ? "File Selected: " + uploadedFile.name : "Upload or Drop your CV here"} {/* Display file name */}
                </h1>
                {uploadedFile && ( // Conditionally render file details
                    <div className="mt-4 text-[#407BBF] w-full lg:px-[2px] gap-1 flex flex-col">
                        <p className='w-full px-4 lg:px-8 flex flex-col lg:flex-row justify-between'><strong>Name:</strong> spa{uploadedFile.name}</p>
                        <p className='w-full px-4 lg:px-8 flex flex-col lg:flex-row justify-between'><strong>Size:</strong> {Math.round(uploadedFile.size / 1024)} KB</p> {/* Display file size */}
                        <p className='w-full px-4 lg:px-8 flex flex-col lg:flex-row justify-between'><strong>Type:</strong> {uploadedFile.type}</p>
                    </div>
                )}
            </label>
        </div>
    );
};


const DetailForm = ({ title, description }) => {
    return (
        <div className="h-fit w-fit flex flex-col items-start gap-6 p-4 sm:p-6 md:p-8 lg:p-10 border-dashed border-[#407BBF] border-2 bg-white shadow-md rounded-lg">
            <h2 className="text-2xl font-semibold text-gray-800">{title}</h2>
            <div className="prose"> {/* Use prose class for better typography */}
                {/* Split the description into paragraphs based on newline characters */}
                {description.split('\n').map((paragraph, index) => (
                    <p key={index}>{paragraph}</p>
                ))}

                {/* Improved "Needs Improvement" section */}
                <div className="mt-4">
                    <h3 className="text-xl font-medium text-gray-700">Areas for Improvement</h3>
                    <ul className="list-disc pl-6 text-gray-600">
                        {/*  Parse improvement points.  Assumes they are listed with bullets or numbers, or separated by newlines. */}
                        {description.split('\n').filter(line => line.trim().startsWith('-') || line.trim().startsWith('*') || /^\d+\./.test(line.trim())).map((improvement, index) => (
                            <li key={index}>{improvement.replace(/^[-*]|\d+\./, '').trim()}</li>
                        ))}
                        {/* If no list items are found, display a default message */}
                        {description.split('\n').filter(line => line.trim().startsWith('-') || line.trim().startsWith('*') || /^\d+\./.test(line.trim())).length === 0 && (
                            <li className="text-gray-500">No specific areas for improvement listed.</li>
                        )}
                    </ul>
                </div>
            </div>
        </div>
    );
};


const Page = () => {
    const [uploadedFile, setUploadedFile] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [reportGenerated, setReportGenerated] = useState(false)
    const [progress, setProgress] = useState(0);

    const animateProgress = () => {
        let startTime = Date.now();
        const duration = 2000;

        const easeInOutQuad = (t) => { // Example: ease-in-out quadratic
            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        };

        const interval = setInterval(() => {
            const elapsedTime = Date.now() - startTime;
            let t = Math.min(elapsedTime / duration, 1); // Normalize time (0-1)
            const easedProgress = easeInOutQuad(t) * 100; // Apply easing function
            setProgress(easedProgress);

            if (t >= 1) {
                clearInterval(interval);
            }
        }, 16);
    };

    // useEffect(() => {
    //     animateProgress();
    // }, []);



    const handleModalOpen = () => {
        setIsModalOpen(true);
    };

    const handleModalClose = () => {
        setIsModalOpen(false);
    };

    const handleFormSubmit = (answers) => {
        console.log("Answers submitted:", answers);
        // Do something with the answers, e.g., send them to an API
    };

    const handleGenerateReport = () => {
        setReportGenerated(true)
        animateProgress()
    }

    return (
        <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-4">
            <div className="w-full overflow-auto">
                <div className="p-3 w-full flex justify-between">
                    <Heading pgHeading="Match CV" />
                    <Link href={`/dashboard/roles/saved`} className="w-[150px]">
                        <button className="flex items-center justify-center border-2 border-textSecondary text-textSecondary font-bold rounded text-sm w-full gap-2 p-2 hover:bg-textSecondary hover:text-white transition-all duration-300">
                            <ArrowLeftIcon className='w-5 h-5' />
                            {"Roles Saved"}
                        </button>
                    </Link>
                </div>

                <div className="flex flex-wrap gap-5 justify-center items-center bg-white w-full rounded-2xl p-5 h-fit min-h-[70vh] overflow-y-auto flex-col">
                    {!reportGenerated ?
                        <UploadForm
                            uploadedFile={uploadedFile}
                            setUploadedFile={setUploadedFile} /> : <DetailForm
                            title="Job Application Feedback"
                            description={`Thank you for your interest in the Software Engineer position. Your application was strong, and we were impressed with your experience in React.
                          
                          However, there are a few areas where you could improve:
                          
                          - Deepen your understanding of state management, particularly Redux or Context API.
                          - Practice more complex algorithm challenges on platforms like LeetCode.
                          *  Showcase more projects on your portfolio that demonstrate your problem-solving skills.
                          1.  Improve your communication skills.
                          -  Gain more experience with testing frameworks like Jest or Cypress.
                          
                          Overall, you are a promising candidate, and we encourage you to address these points and apply again in the future.`}
                        />

                    }


                    <button
                        onClick={() => (!uploadedFile ? handleModalOpen() : handleGenerateReport())}
                        className="flex items-center justify-center border-2 border-[#3469A8] text-[#3469A8] font-medium rounded-md py-2 px-6 hover:bg-textSecondary hover:text-white transition-all duration-300"
                    >
                        {!uploadedFile ? "Don't have a CV?" : "Submit"}

                    </button>
                    <div>
                        {
                            uploadedFile
                            &&
                            <progress
                                className='w-[200px]'
                                style={{
                                    backgroundColor: '#f3f3f3',  // Light gray background
                                    borderRadius: '10px',
                                }}
                                value={progress} />
                        }
                    </div>
                    <QuestionaireModal
                        isOpen={isModalOpen}
                        onClose={handleModalClose}
                        handleSubmit={handleFormSubmit}
                    />
                </div>
            </div>
        </main>
    );
};

export default Page;
