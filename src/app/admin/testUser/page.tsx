import React, { useState } from 'react'
import AddRemoveUserToGroup from "@/components/admin/userGroup/AddRemoveUserToGroup"

const userValues = [
  {
    "user_id": "1",
    "name": "<PERSON>",
    "ph_number": "123-456-7890",
    "email": "<EMAIL>"
  },
  {
    "user_id": "2",
    "name": "<PERSON>",
    "ph_number": "987-654-3210",
    "email": "<EMAIL>"
  },
  {
    "user_id": "3",
    "name": "<PERSON>",
    "ph_number": "555-555-5555",
    "email": "<EMAIL>"
  },
  {
    "user_id": "4",
    "name": "<PERSON>",
    "ph_number": "222-333-4444",
    "email": "<EMAIL>"
  },
  {
    "user_id": "5",
    "name": "<PERSON>",
    "ph_number": "777-888-9999",
    "email": "<EMAIL>"
  },
  {
    "user_id": "6",
    "name": "<PERSON>",
    "ph_number": "666-666-6666",
    "email": "<EMAIL>"
  },
  {
    "user_id": "7",
    "name": "<PERSON> <PERSON>",
    "ph_number": "111-222-3333",
    "email": "<EMAIL>"
  },
  {
    "user_id": "8",
    "name": "David Clark",
    "ph_number": "444-555-6666",
    "email": "<EMAIL>"
  },
  {
    "user_id": "9",
    "name": "Jessica Martinez",
    "ph_number": "999-888-7777",
    "email": "<EMAIL>"
  },
  {
    "user_id": "10",
    "name": "Matthew Taylor",
    "ph_number": "777-666-5555",
    "email": "<EMAIL>"
  },
  {
    "user_id": "1",
    "name": "John Doe",
    "ph_number": "123-456-7890",
    "email": "<EMAIL>"
  },
  {
    "user_id": "2",
    "name": "Jane Smith",
    "ph_number": "987-654-3210",
    "email": "<EMAIL>"
  },
  {
    "user_id": "3",
    "name": "Alice Johnson",
    "ph_number": "555-555-5555",
    "email": "<EMAIL>"
  },
  {
    "user_id": "4",
    "name": "Bob Brown",
    "ph_number": "222-333-4444",
    "email": "<EMAIL>"
  },
  {
    "user_id": "5",
    "name": "Emily Davis",
    "ph_number": "777-888-9999",
    "email": "<EMAIL>"
  },
  {
    "user_id": "6",
    "name": "Michael Wilson",
    "ph_number": "666-666-6666",
    "email": "<EMAIL>"
  },
  {
    "user_id": "7",
    "name": "Sarah Lee",
    "ph_number": "111-222-3333",
    "email": "<EMAIL>"
  },
  {
    "user_id": "8",
    "name": "David Clark",
    "ph_number": "444-555-6666",
    "email": "<EMAIL>"
  },
  {
    "user_id": "9",
    "name": "Jessica Martinez",
    "ph_number": "999-888-7777",
    "email": "<EMAIL>"
  },
  {
    "user_id": "10",
    "name": "Matthew Taylor",
    "ph_number": "777-666-5555",
    "email": "<EMAIL>"
  }
]



const page = () => {
  return (
    <div className='bg-gray-50'>
      <div className="flex h-screen">
        <div>
          Group Name {/* add group name dynamically */}
        </div>
        <div className="m-4 w-1/2 h-90vh">
          {/* <AddRemoveUserToGroup userValues={userValues} type={"add"} /> */}
        </div>
        <div className='m-4 w-1/2 h-90vh'>
          {/* <AddRemoveUserToGroup userValues={userValues} type={"remove"} /> */}
        </div>
      </div>
    </div>
  )
}

export default page