"use client"
import React, { useState, useEffect, useCallback } from "react";
import { useGetAllContentInSlide } from "@/hook/content/useGetAllContentInSlide";
import { useParams } from "next/navigation";
import { motion } from "framer-motion"
import ToolTip from "@/components/ui/ToolTip";
import {
  ArrowLeftCircleIcon,
  ArrowRightCircleIcon,
  PauseCircleIcon,
  PlayCircleIcon,
  InformationCircleIcon,
  ArrowPathIcon
} from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";
import ContentInformation from "@/components/content/ContentInformation";
import { API_URL } from "@/utils/constants";

const options = {
  cMapUrl: '/cmaps/',
  standardFontDataUrl: '/standard_fonts/',
};

const maxWidth = 800;

type PDFFile = string | File | null;

export default function ContentDetail() {
  const params = useParams();
  const content_id: number = parseInt(params.contentId, 10);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [showContentInformation, setshowContentInformation] = useState(false)
  const [numPages, setNumPages] = useState<number>();
  const [autoNext, setautoNext] = useState<boolean>(false);
  const [containerWidth, setContainerWidth] = useState<number>();

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (autoNext) {
      interval = setInterval(() => {
        setPageNumber((prevPageNumber) => prevPageNumber + 1);
      }, 10000); //update to next page after 10 seconds
    } else if (!autoNext && interval) {
      clearInterval(interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoNext]);

  function onDocumentLoadSuccess({ numPages: nextNumPages }: PDFDocumentProxy): void {
    setNumPages(nextNumPages);
  }

  console.log("numPages: ", numPages)
  document.addEventListener('contextmenu', event => {
    event.preventDefault();
  });

  const { data: contentDetails } = useGetAllContentInSlide(content_id);

  console.log("contentDetails: ", contentDetails)
  console.log("showContentInformation: ", showContentInformation)

  return (
    <main className="w-full h-[80vh] flex h-screen bg-primary flex-col items-center justify-start ">
      <div className="flex flex-col w-11/12 h-[80vh] gap-3 justify-start items-center ">
        <div className="flex flex-row justify-between w-full">
          <Heading pgHeading="Content" />
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <InformationCircleIcon
              className="w-[30px] h-[30px] lg:w-[40px] lg:h-[40px] lg:mt-2"
              onClick={() => setshowContentInformation(!showContentInformation)}
            />
          </motion.button>

          {showContentInformation && (
            <ContentInformation contentDetails={contentDetails} isOpen={true} onClose={() => setshowContentInformation(false)} />
          )}
        </div>
        <div className="flex flex-col justify-between overflow-y-auto bg-white mx-2 w-full h-[70vh] xl:h-[65vh] rounded-2xl p-5">
          {contentDetails && contentDetails.file_path && (() => {
            const modifiedFilePath = contentDetails.file_path.replace(/uploads[\/\\]/, '');
            return (
              <div className="flex flex-col text-white justify-between basis-1/2 gap-1 rounded-lg text-center p-2 h-fit">
                <div className="w-full h-[80%]">
                  {/* <Document
                    className="flex flex-col items-center h-80"
                    file={`${API_URL}file_content/${modifiedFilePath}`}
                    onLoadSuccess={onDocumentLoadSuccess}
                    options={options}
                  >

                    <Page
                      pageNumber={pageNumber}
                      height={30}
                      width={containerWidth ? Math.min(containerWidth, maxWidth) : maxWidth}
                    />
                  </Document> */}
                </div>
              </div>
            );
          })()}
        </div>
        <div className="flex flex-col items-center justify-center w-full h-fit gap-4">
          <div className="bg-primary w-fit h-10 rounded-md flex gap-4 justify-center">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ToolTip tooltip="Previous">
                <button
                  className="w-10 h-full bg-secondary rounded-full"
                  onClick={() => (pageNumber > 1 ? setPageNumber(pageNumber - 1) : null)}
                >
                  <ArrowLeftCircleIcon className="text-white z-20" />
                </button>
              </ToolTip>
            </motion.button>
            {autoNext ? (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ToolTip tooltip="Pause the PDF file">
                  <button
                    onClick={() => setautoNext(false)}
                    className="w-10 h-full bg-secondary rounded-full"
                  >
                    <PauseCircleIcon className="text-white z-20" />
                  </button>
                </ToolTip>
              </motion.button>
            ) : (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <ToolTip tooltip="Play the PDF file">
                  <button
                    onClick={() => setautoNext(true)}
                    className="w-10 h-full bg-secondary rounded-full"
                  >
                    <PlayCircleIcon className="text-white z-20" />
                  </button>
                </ToolTip>
              </motion.button>
            )}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ToolTip tooltip="Next">
                <button
                  className="w-10 h-full bg-secondary rounded-full"
                  onClick={() => (pageNumber < numPages ? setPageNumber(pageNumber + 1) : null)}
                >
                  <ArrowRightCircleIcon className="text-white z-20" />
                </button>
              </ToolTip>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ToolTip tooltip="Rewind">
              <button
                className="w-10 h-full bg-secondary rounded-full"
                onClick={() => (setPageNumber(1))}
              >
                <ArrowPathIcon className="text-white z-20" />
              </button>
              </ToolTip>
            </motion.button>
          </div>

          <div className="flex">
            {pageNumber} / {numPages}
          </div>
        </div>
      </div>
    </main>
  );
}
