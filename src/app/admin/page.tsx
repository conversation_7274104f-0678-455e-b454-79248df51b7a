
// import Heading from "@/components/ui/Heading";
// import UploadForm from "@/components/admin/UploadQuestion";

// // Admin Home Page
// export default function DashboardPage() {
//   return (
//     <main className=" w-full h-full p-2 bg-white">
//       <div className=" w-full   ">
//       <Heading pgHeading="Upload Questions File " />
//       </div>
//       <div className="flex justify-center h-[70vh] p-4 ">
//         <div className="flex flex-col justify-start w-full overflow-y-auto mx-auto rounded-2xl p-6  ">
//           <UploadForm />
//         </div>
//       </div>
//     </main>
//   );
// }
import Heading from "@/components/ui/Heading";
import UploadForm from "@/components/admin/UploadQuestion";

// Admin Home Page
export default function DashboardPage() {
  return (
    <div className="w-full">
      <div className="mb-6">
        <Heading pgHeading="Upload Questions File" />
      </div>
      <div className="w-full">
        <UploadForm />
      </div>
    </div>
  );
}