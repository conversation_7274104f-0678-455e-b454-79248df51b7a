"use client";

import Heading from "@/components/ui/Heading";
import SearchBar from "@/components/admin/SearchBar";

import React, { useState, useEffect } from "react";
import { useGetSearchedAssessment } from "@/hook/assessments/useGetSearchedAssessment";
import queastiondata from "../manualQuestion.json";
import ManualQuestionTable from "@/components/admin/evaluate/ManualQuestionTable";
import { useSearchParams } from "next/navigation";
import { useGetAllManualUser } from "@/hook/admin/evaluatelist/useGetAllManualUser";
import { useGetSearchedUsers } from "@/hook/admin/useGetSearchedUsers";



export default function Page() {

  const searchParams = useSearchParams();
  const assessment_ext_id = searchParams.get("assessmentId");
  console.log(assessment_ext_id)
  const [search, setSearch] = useState("")
  const { data: userdata, isLoading, isError } = useGetAllManualUser(assessment_ext_id);
  const { data: searchedUsers } = useGetSearchedUsers(search);


  console.log("searchedUsers:- ", searchedUsers)

  if (isLoading) {
    return <div>Loading...</div>;
  }

  //Check implementation of Retry button
  if (isError) {
    return <div>Error fetching assessments</div>;
  }

  const handleSearch = (searchString: string) => {
    //Call the search users API here and use the searched content here
    console.log(searchString);
    setSearch(searchString);
  };
  function findCommonObjects(array1, array2) {
    // Check if array2 is not an array or undefined, return array1
    if (!Array.isArray(array2) || typeof array2 !== 'object') {
        return array1;
    }

    const commonObjects = [];
    

    // Iterate through the first array
    for (let obj1 of array1) {
        // Check if the object exists in the second array
        const found = array2.find(obj2 => JSON.stringify(obj1) === JSON.stringify(obj2));
        if (found) {
            commonObjects.push(obj1);
        }
    }

    // If no common objects found, return all objects from array1
    if (commonObjects.length === 0) {
        return array1;
    }

    return commonObjects;
}

  const userFilteredValues = findCommonObjects(userdata, searchedUsers)

  console.log("search:-", search)

  console.log("searchedAssessment:-", searchedUsers)

  return (
    <main className="w-full flex h-[90vh] bg-primary flex-col gap-3 p-3 overflow-auto">
     
        <div className='flex flex-col justify-start h-[5vh]'>
         <Heading pgHeading="User Manual Question List" />
         </div>

        <div className="flex flex-row justify-start items-end h-[10vh]">
        <SearchBar onSearch={handleSearch} />
        </div>
      
        <div className=" h-[75vh]">
    
          <ManualQuestionTable UserData={userFilteredValues} /> 
        </div>

    </main>
  );
  
}
