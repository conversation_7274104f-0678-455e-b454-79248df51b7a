"use client";
import CommentBox from "@/components/admin/evaluate/CommentBox";
import { useSearchParams } from "next/navigation";
import { useGetEvaluatePage } from "@/hook/assessment_attempts/useGetEvaluatePage";
import { ClockIcon, CalendarDaysIcon } from "@heroicons/react/24/outline";

import React  from "react";


const Page = () => {
  
  const evaluateData = useGetEvaluatePage(1).data;
  console.log(evaluateData);
  const searchParams = useSearchParams();
  const user_id = searchParams.get("user_id");
  const attempt_status = searchParams.get("attempt_status"); //algo for attempt_status

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col gap-2 p-2">
      <div className="flex flex-col gap-2  w-full p-2">
        <div className="shadow-lg border-y-8 border-white  mx-auto w-full max-w-7xl rounded-2xl bg-white p-3 overflow-auto h-[450px]">
          
          <div className="flex justify-between gap-2 w-full border-[1px] rounded-lg shadow divide-x divide-white  flex-wrap">
            <div className="flex-1 flex flex-col justify-around p-2">
              <h2 className="flex font-semibold  text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
                Status
              </h2>
              <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em] text-textColor">
                {attempt_status}
              </h2>
            </div>
            <div className="flex-1 flex flex-col justify-around p-3 ">
              <h2 className="flex font-semibold text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
                User Id
              </h2>
              <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em]  text-textColor">
                {user_id}
              </h2>
            </div>
            <div className="flex-1 flex flex-col justify-around p-3">
              <h2 className="flex font-semibold  text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
                Question ID
              </h2>
              <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em] text-textColor">
                Question ID
              </h2>
            </div>
            <div className="flex-1 flex flex-col justify-around p-3">
              <h2 className="flex font-semibold  text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
                Answer ID
              </h2>
              <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em] text-textColor"></h2>
            </div>
          </div>
          <div className="flex flex-col justify-between gap-2 w-full border-[1px]  rounded-lg shadow p-4 my-3">
            <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[15px] text-grey-100 p-1">
              In HTML, which attribute is used to define inline styles?
            </h2>
            <h2 className="flex font-semibold  text-[7px] md:text-[10px] lg:text-[12px] text-grey-100 ">
              Submitted Answer:
            </h2>
            <p className="text-[7px] md:text-[10px] lg:text-[12px] text-grey-100 p-1">
              There are many variations of passages of Lorem Ipsum available, but
              the majority have suffered alteration in some form, by injected
              humour, or randomised words which don't look even slightly believable.
              If you are going to use a passage of Lorem Ipsum, you need to be sure
              there isn't anything embarrassing hidden in the middle of text. All
              the Lorem Ipsum generators on the Internet tend to repeat predefined
              chunks as necessary, making this the first true generator on the
              Internet. It uses a dictionary of over 200 Latin words, combined with
              a handful of model sentence structures, to generate Lorem Ipsum which
              looks reasonable. The generated Lorem Ipsum is therefore always free
              from repetition, injected humour, or non-characteristic words etc.
            </p>
          </div>
          <CommentBox />
          </div></div>
        </main>
        );
};

        export default Page;
