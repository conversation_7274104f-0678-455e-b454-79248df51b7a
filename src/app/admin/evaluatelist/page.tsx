"use client";
import Heading from "@/components/ui/Heading";
import SearchBar from "@/components/admin/SearchBar";
import React, { useState, useEffect } from "react";
import { useGetSearchedAssessment } from "@/hook/assessments/useGetSearchedAssessment";
import ManualAssessmentTable from "@/components/admin/evaluate/ManualAssessmentTable";
import { useGetAllManualAssessment } from "@/hook/admin/evaluatelist/useGetAllManualAssessment";

export default function Page() {

  const [search, setSearch] = useState("")
 const { data: assessments, isLoading, isError } = useGetAllManualAssessment();
 const { data: searchedAssessment } = useGetSearchedAssessment(search)


  if (isLoading) {
    return <div>Loading...</div>;
  }

  //Check implementation of Retry button
  if (isError) {
    return <div>Error fetching assessments</div>;
  }

  const handleSearch = (groupSearch: string) => {
    //Call the filter content API and use the filtered content here
    console.log("groupSearch:-", groupSearch)
    setSearch(groupSearch);
  };

  
  
  console.log("search:-", search)

  console.log("searchedAssessment:-", searchedAssessment)

  return (
    <main className="w-full flex h-[90vh] bg-white flex-col gap-3 p-3 overflow-auto">
     
        <div className='flex flex-col justify-start h-[5vh]'>
         <Heading pgHeading="Manual Assessment List" />
         </div>

        <div className="flex flex-row justify-start items-end h-[10vh]">
        <SearchBar onSearch={handleSearch} />
        </div>
      
        <div className=" h-[75vh]">
        {(search?.length) > 2 && searchedAssessment ?
          <ManualAssessmentTable AssessmentData={searchedAssessment} /> : <ManualAssessmentTable AssessmentData={assessments} />}
        </div>

    </main>
  );
}
