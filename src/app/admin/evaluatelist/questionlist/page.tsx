"use client"; // This is a client component
import { Disclosure, Transition } from "@headlessui/react";
import React, { useState} from "react";
import { ChevronUpIcon, StarIcon } from "@heroicons/react/20/solid";

import Heading from "@/components/ui/Heading";
import Nothing from "@/components/ui/Nothing";

import { useSearchParams } from "next/navigation";

import { InformationCircleIcon } from "@heroicons/react/24/outline";
import { useGetAllManualQuestion } from "@/hook/admin/evaluatelist/useGetAllManualQuestion";
import { usePostCommentMarks } from "@/hook/admin/evaluatelist/usePostCommentMarks";
import { CommentMarks } from "@/types/LMSTypes";
import SubmitModal from "@/components/dashboard/quiz/SubmitModal";

// Type for form data
interface FormData {
  comments: string;
  marks: string;
  assessment_question_id: number;
}

export default function AdminAssessmentPage() {
  const [comments, setComments] = useState("");
  const [marks, setMarks] = useState("");
  const [assessment_question_id, setAssessment_question_id] = useState<number | null>(null);
  const [submitted, setSubmitted] = useState("submit")
  const [indexing, setindexing] = useState(0 || 0)
  const [submittedAnswerId, setSubmittedAnswerId] = useState(0)
  const [question, setQuestion] = useState("none")
  const [answerText, setAnswerText] = useState("none")
  const [evaluatedByEmail, setevaluatedByEmail] = useState("none")
  const [submittedAnswerStatus, setsubmittedAnswerStatus] = useState("none")
  const [commentMarkArray, setCommentMarkArray] = useState<CommentMarks[]>([]);
  const [submitModal, setsubmitModal] = useState(false)


  const [formData, setFormData] = useState<FormData>({
    comments: '',
    marks: '',
    assessment_question_id: assessment_question_id ?? 0,
  });
  const [errors, setErrors] = useState({});
  const searchParams = useSearchParams();
  const user_assessment_attempt_ext_id = searchParams.get(
    "userAssessmentAttemptId"
  );
  const { data: questiondata } = useGetAllManualQuestion(user_assessment_attempt_ext_id);

  const addcommentMarks = usePostCommentMarks(commentMarkArray, user_assessment_attempt_ext_id);

  const user_ext_id = searchParams.get("userId");
  // console.log("user_ext_id", user_ext_id);
  console.log("user_assessment_attempt_ext_id", user_assessment_attempt_ext_id);

  if (!user_assessment_attempt_ext_id) {
    console.error("user_assessment_attempt_ext_id is undefined or not retrieved properly");
    return; // Optionally handle this case more gracefully
  }




  const myModule: CommentMarks = {
    submitted_answer_id: submittedAnswerId,
    question: question,
    user_assessment_attempt_id: parseInt(user_assessment_attempt_ext_id, 10),
    submitted_answer_marks: marks,
    assessment_question_id: formData.assessment_question_id,
    evaluation_comments: comments,
    answer_text: answerText,
    evaluated_by_email: evaluatedByEmail,
    submitted_answer_status: submittedAnswerStatus           //submittedAnswerStatus, //Hardcoded as of now
  };

  type CommentMarks = {
    submitted_answer_id: number;
    question: string;
    user_assessment_attempt_id: number;
    submitted_answer_marks: string;
    assessment_question_id: number;
    evaluation_comments: string;
    answer_text: string;
    evaluated_by_email: string;
    submitted_answer_status: string;
  };



  const validateForm = () => {
    let formErrors = {};
    if (!formData.comments) formErrors.comments = 'Comment is required';
    if (!formData.marks) formErrors.marks = 'Marks are required';
    setErrors(formErrors);
    return Object.keys(formErrors).length === 0;
  };

  const handleSelectQuestion = (assessment_question_id: number) => {
    setFormData(prev => ({ ...prev, assessment_question_id }));
  };

  const handleSave = () => {
    console.log("myModule", myModule);

    // Check if an object with the same assessment_question_id already exists
    const index = commentMarkArray.findIndex(item => item.assessment_question_id === myModule.assessment_question_id);

    if (index !== -1) {
      // Object with the same assessment_question_id exists, replace it with the new one
      const newArray = [...commentMarkArray];
      newArray[index] = myModule;
      setCommentMarkArray(newArray);
    } else {
      // Object with the same assessment_question_id doesn't exist, add the new one
      setCommentMarkArray(prevArray => [...prevArray, myModule]);
    }

    console.log("saved answers", commentMarkArray);
  };

  const handleEdit = (index: number, e, type: number) => {
    setindexing(index)
    // setComments(e.target.value)

    if (type === 1) {
      setComments(e.target.value)
      const myModule2: CommentMarks = {
        submitted_answer_id: submittedAnswerId,
        question: question,
        user_assessment_attempt_id: parseInt(user_assessment_attempt_ext_id, 10),
        submitted_answer_marks: commentMarkArray[index]?.submitted_answer_marks,
        assessment_question_id: formData.assessment_question_id,
        evaluation_comments: e.target.value,
        answer_text: answerText,
        evaluated_by_email: evaluatedByEmail,
        submitted_answer_status: submittedAnswerStatus           //submittedAnswerStatus, //Hardcoded as of now
      };

      const tempArray = [...commentMarkArray];
      tempArray[index] = myModule2
      setCommentMarkArray(tempArray);
    } else if (type === 2) {
      setMarks(e.target.value.toString())
      const myModule2: CommentMarks = {
        submitted_answer_id: submittedAnswerId,
        question: question,
        user_assessment_attempt_id: parseInt(user_assessment_attempt_ext_id, 10),
        submitted_answer_marks: e.target.value.toString(),
        assessment_question_id: formData.assessment_question_id,
        evaluation_comments: commentMarkArray[index]?.evaluation_comments,
        answer_text: answerText,
        evaluated_by_email: evaluatedByEmail,
        submitted_answer_status: submittedAnswerStatus           //submittedAnswerStatus, //Hardcoded as of now
      };

      const tempArray = [...commentMarkArray];
      tempArray[index] = myModule2
      setCommentMarkArray(tempArray);
    }
  }

  

  const handleSubmit = async (e) => {
    console.log("working")
    e.preventDefault();
    const formErrors = validateForm();
    setErrors(formErrors);

    if (Object.keys(formErrors).length === 0) {
      try {

        const adddata = await addcommentMarks.mutate(); // Assuming mutateAsync() returns a promise with the created module
        console.log("comment and marks is", adddata);

        setSubmitted("Submitting...")

        setTimeout(() => {

          setSubmitted("Submitted")
          setsubmitModal(false)
          window.location.href = '/admin/evaluatelist'; 
          setTimeout(() => {

            setSubmitted("Submit")
          }, 1000);

        }, 2000);



      } catch (error) {
        // Handle any errors
        console.error("Module creation failed:", error);
      }
    }
  };


  return (
    <main className="w-full  min-h-screen bg-primary ">
      <div className="flex flex-col gap-3  w-full p-2">
        <Heading pgHeading="Evaluate" />

        <div className="shadow-lg border-y-8 border-white  mx-auto w-full rounded-2xl bg-white p-4  h-[80vh]">
          {questiondata && questiondata.length > 0 ? (
            <div className="overflow-y-auto p-2 gap-3  w-full h-full justify-between ">
              <div className="flex flex-col justify-start items-start w-full">
                {questiondata?.map((unevaluatedAttempt, index) => (
                  <Disclosure key={index} >
                    {({ open }) => (
                      <>
                        <Disclosure.Button onClick={() => handleSelectQuestion(unevaluatedAttempt.assessment_question_id)} className="mb-3 flex w-full justify-between rounded-lg bg-secondary px-4 py-2 text-left text-sm font-medium text-white transition duration-300 hover:bg-textSecondary hover:text-white focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75">
                          <div className="flex items-center gap-4">
                            <span
                              className={`m-1 w-3 h-3 rounded-full ${unevaluatedAttempt.submitted_answer_status.toLowerCase() === "evaluated"
                                ? "bg-green-500"
                                : "bg-red-500"
                                }`}
                            >

                            </span>
                            <span className="pr-2">
                              {unevaluatedAttempt.question}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <ChevronUpIcon
                              className={`${open ? "rotate-180 transform" : ""
                                } h-5 w-5 text-white`}
                            />
                          </div>
                        </Disclosure.Button>
                        <Transition
                          className="w-full"
                          enter="transition duration-150 ease-out"
                          enterFrom="transform scale-95 opacity-0"
                          enterTo="transform scale-100 opacity-100"
                          leave="transition duration-75 ease-out"
                          leaveFrom="transform scale-100 opacity-100"
                          leaveTo="transform scale-95 opacity-0"
                        >
                          <Disclosure.Panel className="flex px-4 text-sm text-gray-500 w-full">
                            {/* <div className="m-1">Instrcutions: {unevaluatedAttempt.instructions}</div> 

                          <div className="flex justify-between">
                           
                            <div className="m-2 flex flex-col">
                              <span className="m-1">Total time allowed: {unevaluatedAttempt.total_time_allowed}</span>
                              <span className="m-1">Total marks: {unevaluatedAttempt.total_marks}</span>
                              <span className="m-1">Source: {assessments.source}</span> 
                            </div>

                       
                          <div className="m-2">
                              <button
                                onClick={() => {
                                  setLoadingSelect(true);
                                  const assessmentId = unevaluatedAttempt.assessment_id;
                                  window.location.href = `/admin/evaluatelist/evaluate`;
                                }}
                                className="bg-secondary hover:bg-hoverColor transition duration-150  text-white font-bold py-2 px-4 rounded"
                                disabled={loadingSelect} // Disable the button when loading 'Loading...'
                              >
                                {loadingSelect ? "Loading..." : "Evaluate"}

                              </button>
                            </div> 
                          </div> 
                           */}

                            <div className="flex flex-col gap-2  w-full p-2">
                              <div className="shadow-lg border-y-8 border-white  mx-auto w-full  rounded-2xl bg-white p-3 overflow-auto ">
                                <div className="flex justify-between gap-2 w-full border-[1px] rounded-lg shadow divide-x divide-white  flex-wrap">
                                  <div className="flex-1 flex flex-col justify-around p-2">
                                    <h2 className="flex font-semibold  text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
                                      Status
                                    </h2>
                                    <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em] text-textColor">
                                      {
                                        unevaluatedAttempt.submitted_answer_status
                                      }
                                    </h2>
                                  </div>
                                  <div className="flex-1 flex flex-col justify-around p-3 ">
                                    <h2 className="flex font-semibold text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
                                      User Id
                                    </h2>
                                    <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em]  text-textColor">
                                      {user_ext_id}
                                    </h2>
                                  </div>
                                  <div className="flex-1 flex flex-col justify-around p-3">
                                    <h2 className="flex font-semibold  text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
                                      Assessment Question ID
                                    </h2>
                                    <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em] text-textColor">
                                      {
                                        unevaluatedAttempt.assessment_question_id
                                      }
                                    </h2>
                                  </div>
                                  <div className="flex-1 flex flex-col justify-around p-3">
                                    <h2 className="flex font-semibold  text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
                                      Answer ID
                                    </h2>
                                    <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em] text-textColor">
                                      {
                                        unevaluatedAttempt.submitted_answer_id
                                      }
                                    </h2>
                                  </div>
                                </div>
                                <div className="flex flex-col justify-between gap-2 w-full border-[1px]  rounded-lg shadow p-4 my-3">
                                  <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[15px] text-black p-1">
                                    Question : {unevaluatedAttempt.question}
                                  </h2>
                                  <h2 className="flex font-semibold  text-[7px] md:text-[10px] lg:text-[12px] text-grey-100 ">
                                    Submitted Answer:
                                  </h2>
                                  {<pre className="flex justify-start items-center w-[100%] text-[10px] md:text-[12px] lg:text-[14px] text-black p-1 lg:leading-loose whitespace-pre-wrap">
                                    {unevaluatedAttempt.answer_text}
                                    {console.log("unevaluatedAttempt.answer_text",unevaluatedAttempt.answer_text)}
                                  </pre>}
                                </div>
                                <form
                                  onSubmit={(e) => {
                                    handleSave();
                                    e.preventDefault();
                                  }}
                                  className="flex flex-col w-full"
                                >
                                  <label
                                    className="block text-gray-700 text-sm font-bold mb-2"
                                    htmlFor="textarea"
                                  >
                                    Enter Comment:
                                  </label>
                                  <textarea
                                    id="comments"
                                    name="comments"
                                    value={commentMarkArray[index]?.evaluation_comments}
                                    onChange={(e) => {
                                      handleEdit(index, e, 1)
                                    }}
                                    className="resize-none border shadow rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                    rows={4}
                                    placeholder="Type Comment..."
                                  />

                                  {errors.comments && (
                                    <p className="text-red-500 text-xs">
                                      {errors.comments}
                                    </p>
                                  )}
                                  <div className="py-2">
                                    <label
                                      className="block text-gray-700 text-sm font-bold mb-2"
                                      htmlFor="number"
                                    >
                                      Enter a Marks out of {" "}
                                      {
                                        unevaluatedAttempt.question_marks
                                      }
                                      :
                                    </label>

                                    <input
                                      id="marks"
                                      name="marks"
                                      type="number"
                                      value={commentMarkArray[index]?.submitted_answer_marks}
                                      onChange={(e) => {
                                        handleEdit(index, e, 2)
                                      }}
                                      placeholder="Enter Marks..."
                                      className="resize-none border shadow rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                    />
                                    {(errors.marks || (commentMarkArray[index]?.submitted_answer_marks <= unevaluatedAttempt.question_marks)) && (
                                      <p className="text-red-500 text-xs">
                                        {errors.marks}
                                      </p>
                                    )}
                                  </div>
                                  <div className="w-full flex justify-end items-end ">
                                    <button
                                      type="submit"
                                      className="  bg-secondary hover:bg-hoverColor text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                                    >
                                      {(commentMarkArray[index]?.submitted_answer_marks && commentMarkArray[index]?.evaluation_comments) ? " Saved " : "Save"}
                                    </button>
                                  </div>
                                </form>
                              </div>
                            </div>
                          </Disclosure.Panel>
                        </Transition>
                      </>
                    )}
                  </Disclosure>
                ))}
              </div>
              <div className="w-full  flex justify-center items-end p-2">
                <button
                  type="submit"
                  className=" flex gap-1 bg-secondary hover:bg-hoverColor text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  onClick={(e) => {handleSubmit(e), setsubmitModal(true)}}
                >
                  {submitted}
                  <InformationCircleIcon
                    className="h-5 w-5 text-white"
                    title="Submit Button will post the all question at once"
                  />
                </button>
              </div>
            </div>
          ) : (
            <Nothing
              title="No Evaluate Available"
              para="There are currently no evaluate to display.
            Please check back later."
            />
          )}
        </div>
      </div>
      {submitModal && <SubmitModal modalName={"Evaluate Submitted"} modalText={"Thanks for evaluating the question"} onClose={() => setsubmitModal(false)} 
      />}
    </main>
  );
}