"use client";
import SearchBar from "@/components/admin/SearchBar";
import Dropdown from "@/components/admin/searchQuestion/Dropdown";
import SearchTable from "@/components/admin/searchQuestion/SearchTable";
import React, { useEffect, useState } from "react";
import { useGetAllFilteredQuestion } from "@/hook/questions/useGetAllFilteredQuestion";
import { useQueryClient } from "@tanstack/react-query";
import { useGetAllTopics } from "@/hook/questions/useGetAllTopics";
import Heading from "@/components/ui/Heading";
import SubmitModal from "@/components/ui/SubmitModal";
import AddOneQuestionModal from "@/components/admin/searchQuestion/AddOneQuestionModal";
import { useGetAllQuestionFilter } from "@/hook/questions/useGetAllQuestionFilter";
import Link from "next/link";

export default function Page() {
  const topics_data = useGetAllTopics().data;
  const [questionSearch, setQuestionSearch] = useState("");
  const [questionType, setQuestionType] = useState("");
  const [difficultyLevel, setDifficultyLevel] = useState("");
  const [topic, setTopic] = useState("");
  const [questionCategory, setquestionCategory] = useState("")
  const [questionSource, setQuestionSource] = useState("");
  const [filterList, setFilterList] = useState([]);
  const [topics, setTopics] = useState([]);
  const queryClient = useQueryClient();
  const [showModal, setShowModal] = useState(false);
  const [indexNumber, setIndexNumber] = useState(Number);
  const [showEditQuestionModal, setShowEditQuestionModal] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [option1, setoption1] = useState([]);
  const [option2, setoption2] = useState([]);
  const [option3, setoption3] = useState([]);
  const [option4, setoption4] = useState([]);
  const [option5, setoption5] = useState([]);

  const closeModal = () => setShowModal(false);

  useEffect(() => {
    setTopics(topics_data);
  }, [topics_data]);

  let filterSearch = { 
    question_string: questionSearch,
    question_type: questionType,
    difficulty_level: difficultyLevel,
    topic: topic,
    question_source: questionSource,
    question_category: questionCategory
  };

  const { data: questionData } = useGetAllFilteredQuestion(filterSearch);

  const { data: questionFilterData } = useGetAllQuestionFilter(questionData);
  useEffect(() => {
    const firstValue = [
      {
        label: "Question Source",
        content: questionFilterData?.question_source.filter(
          (item) => item !== null
        ),
      },
    ];
    const secondValue = [
      {
        label: "Question Type",
        content: questionFilterData?.question_type.map((item) => item),
      },
    ];
    const thirdValue = [
      {
        label: "Difficulty Level",
        content: questionFilterData?.question_difficulty.map((item) => item),
      },
    ];
    const forthValue = [
      {
        label: "Topic",
        content: questionFilterData?.topics_string.filter(
          (item) => item !== null
        ),
      },
    ];

    const questionCategory = [
      {
        label: "Question Category",
        content: ["Recall", "Application", "Communication"],
      },
    ];
    


    setoption1(firstValue);
    setoption2(secondValue);
    setoption3(thirdValue);
    setoption4(forthValue);
    setoption5(questionCategory)
  }, [questionFilterData]);

  console.log(indexNumber);

  // const filterSearchRef = useRef({
  //   question_string: questionSearch,
  //   question_type: questionType,
  //   difficulty_level: difficultyLevel,
  //   topic: topic,
  //   question_source: questionSource,
  // });

  const handleSearch = (questionStringByUser) => {
    //Call the filter questions API and use the filtered content here
    console.log(questionStringByUser);
    setQuestionSearch(questionStringByUser);
  };

  const handleReset = () => {
    setQuestionSearch("");
    setQuestionType("");
    setDifficultyLevel("");
    setquestionCategory("")
    setTopic("");
    setQuestionSource("");
  };
  const openModalForNewGroup = () => {
    setShowEditQuestionModal(true);
  };

  const onCloseEditQestionModal = () => {
    setShowEditQuestionModal(false); // Close AddContentModal
    setShowSubmitModal(true);
  };

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
  };
  const CrossCloseBtnModal = () => {
    setShowEditQuestionModal(false);
  };

  return (
    <div className="w-full flex h-[90vh] bg-white flex-col gap-3 p-3 overflow-auto">
      <div className="flex flex-col justify-start h-[5vh]">
        <Heading pgHeading="Question Table" />
      </div>

      <div className="flex w-full gap-1 justify-between items-end flex-col md:flex-row h-[18vh] md:h-[10vh] ">
        <div>
          <SearchBar onSearch={handleSearch} />
        </div>

        <div className="flex w-full  flex-wrap justify-between lg:justify-end gap-1">
          <Dropdown options={option4} set={setTopic} width="120px" />
          <Dropdown options={option1} set={setQuestionSource} width="137px" />
          <Dropdown options={option2} set={setQuestionType} width="132px" />
          <Dropdown options={option3} set={setDifficultyLevel} width="132px" />
          <Dropdown options={option5} set={setquestionCategory} width="132px" />

          <div>
            <button
              onClick={handleReset}
              className="flex justify-center  gap-2 p-3 bg-white text-black w-[140]  rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
            >
              Reset
            </button>
          </div>
          <div>
            <button
              onClick={openModalForNewGroup}
              className="flex  justify-center  gap-2 p-3 bg-textColor w-[120] text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
            >
              Add Question
            </button>
          </div>
          <Link href={"/admin"}>
            <button
              onClick={handleReset}
              className="flex justify-center  gap-2 p-3 bg-textColor w-[120] text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
            >
              Bulk Question
            </button>
          </Link>
        </div>
      </div>
      <div className="h-[72vh] md:h-[70vh]">
        <SearchTable questionData={questionData} />
      </div>

      {showEditQuestionModal && (
        <AddOneQuestionModal
          onClose={onCloseEditQestionModal}
          onCrossClose={CrossCloseBtnModal}
        />
      )}

      {showSubmitModal && (
        <SubmitModal
        data-testid="submit-modal"
       
          modalName="Question Added Success"
          onClose={onCloseSubmitModal}
        />
      )}
    </div>
  );
}
