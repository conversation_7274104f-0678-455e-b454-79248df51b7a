"use client";
import React, { useState, useEffect, useRef } from "react";
import { UserPlusIcon, UsersIcon, InformationCircleIcon, CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import AddRemoveUserToGroup from "@/components/admin/userGroup/AddRemoveUserToGroup";
import SearchBar from "@/components/admin/SearchBar";
import Button from "@/components/ui/Button";
import Heading from "@/components/ui/Heading";
import AddUserModal from "@/components/admin/userGroup/AddUserModal";
import SubmitModal from "@/components/ui/SubmitModal";
import BulkQuesModal from "@/components/admin/BulkQuesModal";

import { useGetAllUserExt } from "@/hook/admin/useGetAllUserExt";
import { useAddBulkUser } from "@/hook/admin/useAddBulkUser";
import { useGetSearchedUsers } from "@/hook/admin/useGetSearchedUsers";

const Page = () => {
  const [search, setSearch] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [showBulkUserPopup, setShowBulkUserPopup] = useState(false);
  const [bulkUserPopupMessage, setBulkUserPopupMessage] = useState("");
  const [modalName, setModalName] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const AddingBulkUsers = useAddBulkUser(file);
  const { data: searchedUsers } = useGetSearchedUsers(search);

  const handleFileAndSubmit = async (event?: React.ChangeEvent<HTMLInputElement>) => {
    console.log("Running pre-event")
    if (event) {
      let uploadedFile = event.target.files?.[0];
      console.log("Running uploadedFile")
      if (uploadedFile && uploadedFile.type === "text/csv") {
        setFile(uploadedFile)
        event.target.value = null;
        toast.info('CSV file selected. Processing...', {
          position: "bottom-right",
          autoClose: 3000,
        });
      } else {
        setBulkUserPopupMessage("Please upload a valid CSV file.");
        setModalName("Error");
        setShowBulkUserPopup(true);
        toast.error('Please upload a valid CSV file.', {
          position: "bottom-right",
          autoClose: 3000,
        });
        return;
      }
    }
  }

  useEffect(() => {
    console.log("Running pre")
    if (file) {
      console.log("debug")
      setIsProcessing(true);
      try {
        AddingBulkUsers.mutateAsync(); // Ensure mutateAsync is used correctly
        setFile(null)
        setBulkUserPopupMessage("Bulk user addition successful!");
        setModalName("Success");
        setShowBulkUserPopup(true);
        setIsProcessing(false);
        toast.success('Bulk user addition successful!', {
          position: "bottom-right",
          autoClose: 3000,
        });
        console.log("Running")

      } catch (error) {
        setBulkUserPopupMessage("Error while adding bulk users.");
        setModalName("Error");
        setIsProcessing(false);
        toast.error('Error while adding bulk users.', {
          position: "bottom-right",
          autoClose: 3000,
        });
      }
    }
  }, [file])

  console.log("Running file", file)

  const { data: userExtValues, isLoading, isError } = useGetAllUserExt();

  useEffect(() => {
    if (!isLoading && !isError) {
      console.log("userExtValues data:", userExtValues);
    }
  }, [userExtValues, isLoading, isError]);

  useEffect(() => {
    // Show welcome toast on first load
    toast.info(`Welcome to User Management`, {
      position: "bottom-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "light",
    });
  }, []);

  if (isLoading) {
    return (
      <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
        <div className="w-full max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm p-6 flex items-center justify-center">
            <div className="flex items-center space-x-3">
              <div className="animate-spin h-6 w-6 border-3 border-blue-500 border-t-transparent rounded-full"></div>
              <span className="text-blue-600 font-medium">Loading users...</span>
            </div>
          </div>
        </div>
      </main>
    );
  }

  if (isError) {
    return (
      <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
        <div className="w-full max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="text-center py-8">
              <div className="rounded-full bg-red-50 p-3 mx-auto w-16 h-16 flex items-center justify-center mb-4">
                <XMarkIcon className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Users</h3>
              <p className="text-gray-600">There was an error fetching the user data. Please try again.</p>
            </div>
          </div>
        </div>
      </main>
    );
  }

  const handleSearch = (searchString: string) => {
    setSearch(searchString);
  };

  const handleAddUser = () => {
    setShowAddUserModal(true);
  };

  const onCloseAddUserModal = () => {
    setShowAddUserModal(false);
  };

  const onSubmitAddUserModal = () => {
    setShowAddUserModal(false);
    setShowSubmitModal(true);
    toast.success('User added successfully!', {
      position: "bottom-right",
      autoClose: 3000,
    });
  };

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
  };

  const onCloseBulkUserPopup = () => {
    setShowBulkUserPopup(false);
  };

  function findCommonObjects(array1, array2) {
    if (!Array.isArray(array2) || typeof array2 !== "object") {
      return array1;
    }

    const commonObjects = array1.filter(obj1 =>
      array2.some(obj2 => JSON.stringify(obj1) === JSON.stringify(obj2))
    );

    return commonObjects.length === 0 ? array1 : commonObjects;
  }

  const userFilteredValues = findCommonObjects(userExtValues, searchedUsers);

  return (
    <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
      <ToastContainer 
        position="bottom-right" 
        autoClose={5000} 
        hideProgressBar={false} 
        newestOnTop={false} 
        closeOnClick={true} 
        rtl={false} 
        pauseOnFocusLoss 
        draggable 
        pauseOnHover 
        theme="light" 
      />

      <div className="w-full max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <Heading pgHeading="User Management" />
              <button 
                onClick={() => toast.info('Manage users, add new users individually or in bulk via CSV upload.')}
                className="text-gray-500 hover:text-blue-600 transition-colors"
                aria-label="Information"
              >
                <InformationCircleIcon className="w-5 h-5" />
              </button>
            </div>
            <div className="text-sm text-gray-500">
              {userFilteredValues?.length || 0} users found
            </div>
          </div>
        </div>

        {/* Search and Actions Section */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
            <div className="flex-1 max-w-md">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Users
              </label>
              <SearchBar onSearch={handleSearch} />
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <input
                type="file"
                accept="text/csv"
                ref={fileInputRef}
                style={{ display: "none" }}
                onChange={handleFileAndSubmit}
              />
              
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isProcessing}
                className={`inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg shadow-sm transition-all duration-200 ${
                  isProcessing 
                    ? 'bg-gray-400 cursor-not-allowed text-white' 
                    : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white hover:shadow-md transform hover:scale-[1.02]'
                }`}
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <UsersIcon className="h-5 w-5 mr-2" />
                    Bulk User Upload
                  </>
                )}
              </button>
              
              <button
                onClick={handleAddUser}
                className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 text-white shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-[1.02]"
              >
                <UserPlusIcon className="h-5 w-5 mr-2" />
                Add User
              </button>
            </div>
          </div>
          
          {/* Upload Instructions */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start">
              <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-1">Bulk Upload Instructions</h4>
                <p className="text-sm text-blue-700">
                  Upload a CSV file to add multiple users at once. Make sure your CSV includes the required columns: name, email, and role.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Users List Section */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-medium text-gray-900">
              {search ? `Search Results for "${search}"` : 'All Users'}
            </h2>
            {userFilteredValues?.length > 0 && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                {userFilteredValues.length} user{userFilteredValues.length !== 1 ? 's' : ''}
              </span>
            )}
          </div>

          <div className="min-h-[400px]">
            <AddRemoveUserToGroup userValues={userFilteredValues} />
          </div>
        </div>
      </div>

      {/* Modals */}
      {showAddUserModal && (
        <AddUserModal
          onClose={onCloseAddUserModal}
          onSubmit={onSubmitAddUserModal}
        />
      )}
      
      {showSubmitModal && (
        <SubmitModal
          modalName="User Upload Success"
          onClose={onCloseSubmitModal}
        />
      )}
      
      {showBulkUserPopup && (
        <BulkQuesModal
          modalName={modalName}
          modalText={bulkUserPopupMessage}
          onClose={onCloseBulkUserPopup}
        />
      )}
    </main>
  );
};

export default Page;
