"use client"
import ModuleTable from '@/components/admin/module/ModuleTable';
import React, { useState } from 'react'
import { useRouter, useSearchParams } from "next/navigation";
import { useGetModulesByGroupId } from '@/hook/admin/module/useGetModulesByGroupId';
import { useGetModulesNotInGroup } from '@/hook/admin/group/useGetModulesNotInGroup';
import Button from '@/components/ui/Button';
import CreateModal from '@/components/admin/assessments/CreateModal';



export default function AddremovemodulePage() {

  const searchParams = useSearchParams();
  const group_id = searchParams.get("groupId");
  const [groupId, setGroupId] = useState(parseInt(group_id || ''));

  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const router = useRouter();

  const onSaveClickFunction=()=>{
    setShowSubmitModal(true)
  }

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
    router.push("/admin/usergroup");
  };


  const { data: getAllModuleData, isLoading: getAllModuleLoading, isError: getAllModuleError } = useGetModulesNotInGroup(groupId);

  const { data: getModulesByGroupIdData, isLoading: getModulesByGroupIdLoading, isError: getModulesByGroupIdError } = useGetModulesByGroupId(groupId);



  if (getAllModuleLoading || getModulesByGroupIdLoading) {
    return <div>Loading...</div>;
  }

  if (getAllModuleError || getModulesByGroupIdError) {
    return <div>Error fetching assessments</div>;
  }

  return (
    <div className="flex flex-col w-full h-[90vh] overflow-auto  ">
    {/* Use flex-grow for equal height division */}
    <div className="h-[40vh]">
        <ModuleTable type={"remove"} modules={getModulesByGroupIdData} groupId={groupId} />
        </div>
  <div className="h-[40vh]">
        <ModuleTable type={"add"} modules={getAllModuleData} groupId={groupId} />
        </div>
  <div className="flex justify-center items-center h-[10vh]">
    
    <Button btnName="Save & Back "  onClickFunction={onSaveClickFunction} />
    
  </div>

  {showSubmitModal && (
         <CreateModal modulename="Changes have been successfully saved" onClose={onCloseSubmitModal} />
      )}
</div>

  );
}
