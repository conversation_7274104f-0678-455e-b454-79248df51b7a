"use client"
import React, { useEffect, useState } from "react";
import { useGetUsersNotInGroup } from "@/hook/admin/group/useGetUsersNotInGroup";
import { useRouter, useSearchParams } from "next/navigation";

import AllUser from "@/components/admin/userGroup/AllUser";
import Heading from "@/components/ui/Heading";
import { useGetUsersInGroup } from "@/hook/admin/group/useGetUsersInGroup";
import Button from "@/components/ui/Button";
import CreateModal from "@/components/admin/assessments/CreateModal";
import { useGetUserSearch } from "@/hook/admin/usergroup/alluser/useGetUserSearch";
import { useGetNonUserSearch } from "@/hook/admin/usergroup/alluser/useGetNonUserSearch";


const Page = () => {
  const searchParams = useSearchParams();
  const group_id = searchParams.get("groupId");
  const group_name = searchParams.get("groupName");
  const [groupId, setGroupId] = useState(parseInt(group_id || ''));
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [search, setsearch] = useState("");
  const [search2, setsearch2] = useState("");
  const router = useRouter();

  const onSaveClickFunction=()=>{
    setShowSubmitModal(true)
  }

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
    router.push("/admin/usergroup");
  };
  const { data: groupUserSearchData } = useGetUserSearch(groupId, search2)
  const { data: nonGroupUserSearchData } = useGetNonUserSearch(groupId, search)

  console.log("nonGroupUserSearchData:-", nonGroupUserSearchData)
  console.log("groupUserSearchData:-", groupUserSearchData)

  return (
    <div className="flex flex-col w-full h-[90vh] overflow-auto  ">
    {/* Use flex-grow for equal height division */}
    <div className="h-[40vh]">
        <AllUser type={"all"} groupId={groupId} />
        </div>
  <div className="h-[40vh]">
        <AllUser type={"remove"} groupId={groupId} />
        </div>
  <div className="flex justify-center items-center h-[10vh]">
    
    <Button btnName="Save & Back "  onClickFunction={onSaveClickFunction} />
    
  </div>

  {showSubmitModal && (
         <CreateModal modulename="Changes have been successfully saved" onClose={onCloseSubmitModal} />
      )}
</div>

  );
}


export default Page;