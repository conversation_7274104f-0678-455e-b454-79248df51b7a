"use client"
import React, { useState, useEffect, useRef } from "react";
import { useGetAllGroups } from "@/hook/admin/group/useGetAllGroups";
import SearchBar from "@/components/admin/SearchBar";
import ToolTip from "@/components/ui/ToolTip";

//Libraries
import { UserPlusIcon, UserGroupIcon } from "@heroicons/react/24/outline";

//Hooks
import { useSearchUserGroups } from "@/hook/user/useSearchUserGroups";

//Components
import GroupModal from "@/components/admin/userGroup/GroupModal";
import Heading from "@/components/ui/Heading";
import Button from "@/components/ui/Button";
import UserGroupTable from "@/components/admin/userGroup/UserGroupTable";
import SubmitModal from "@/components/ui/SubmitModal";
import { useAddUserToGroupBulk } from "@/hook/admin/group/useAddUserToGroupBulk";
import BulkQuesModal from "@/components/admin/BulkQuesModal";
import { getUser, setFirstLogin} from "@/api/user.localStorage";

const defaultGroupID = 0;
const defaultType = "createGroup";
const mockGroups = [
  {
    group_id: 1,
    group_admin_user_id: 101,
    group_name: "Frontend Team",
    isactive: 1,
    created_by: "Admin",
    total_member: "4",
  },
  {
    group_id: 2,
    group_admin_user_id: 102,
    group_name: "Backend Team",
    isactive: 1,
    created_by: "Admin",
    total_member: "3",
  },
  {
    group_id: 3,
    group_admin_user_id: 103,
    group_name: "QA Team",
    isactive: 0,
    created_by: "Admin",
    total_member: "2",
  },
];
const Page = () => {
  const user = getUser()
  const isFirstTime = useRef(user?.firstLogin)
  const [search, setSearch] = useState<string>("");
  const [file, setFile] = useState<File | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  //Using Mock Data - Uncomment below line to use API
  //const { data: userValues, isLoading, isError } = useGetAllGroups();
  //const { data: searchedUserGroups } = useSearchUserGroups(search);
  const [showAddGroupModal, setShowAddGroupModal] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [showBulkUserPopup, setShowBulkUserPopup] = useState(false);
  const [bulkUserPopupMessage, setBulkUserPopupMessage] = useState("");
  const [modalName, setModalName] = useState("");
  const fileInputRef = useRef(null);
  const [isHovering, setIsHovering] = useState(false);

  const sendingBulkUserToGroupUpload = useAddUserToGroupBulk();

  const handleSubmit = async (event?: React.ChangeEvent<HTMLInputElement>) => {
    console.log("Running pre-event");
    if (event) {
      let uploadedFile = event.target.files?.[0];
      console.log("Uploaded file:", uploadedFile);
      if (uploadedFile && uploadedFile.type === "text/csv") {
        setFile(uploadedFile);
        event.target.value = null;
      } else {
        setBulkUserPopupMessage("Please upload a valid CSV file.");
        setModalName("Error");
        setShowBulkUserPopup(true);
        return;
      }
    }
  };

  useEffect(() => {
    console.log("Running pre");
    if (file) {
      console.log("File ready for upload:", file);
      sendingBulkUserToGroupUpload.mutateAsync(file) // Pass the file data here
        .then(() => {
          setFile(null);
          setBulkUserPopupMessage("Bulk user addition successful!");
          setModalName("Success");
          setShowBulkUserPopup(true);
          console.log("Mutation success");
        })
        .catch((error) => {
          setBulkUserPopupMessage("Error while adding bulk users.");
          setModalName("Error");
          setShowBulkUserPopup(true);
          console.error("Mutation error:", error);
        });
    }
  }, [file]);

  
  useEffect(() => {
    setFirstLogin(true)
  }, [user?.firstLogin])

   // Uncomment below lines to use API
  // if (isLoading) {
  //   return <div>Loading...</div>;
  // }

  // if (isError) {
  //   return <div>Error fetching user groups data</div>;
  // }

  const handleSearch = (groupSearch: string) => {
    console.log("Group search:", groupSearch);
    setSearch(groupSearch);
  };

  const openModalForNewGroup = () => {
    setIsModalOpen(true);
    setShowAddGroupModal(true);
  };

  const onCloseAddGroupModal = () => {
    setShowAddGroupModal(false);
  };

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
  };

  const onCloseBulkUserPopup = () => {
    setShowBulkUserPopup(false);
  };

  const onSubmitAddGroupModal = () => {
    setShowAddGroupModal(false);
    setShowSubmitModal(true);
  };


  return (
    <main className="w-full flex h-[90vh] bg-white flex-col gap-3 p-3 overflow-auto">
      <div className='flex flex-col justify-between h-[5vh]'>
        <Heading pgHeading="User Groups" />
      </div>
      <div className="flex flex-col md:flex-row gap-2 h-[10vh] w-full items-end justify-between">
        <SearchBar onSearch={handleSearch} />
        <div className="flex gap-2">
          <input
            type="file"
            accept="text/csv"
            ref={fileInputRef}
            style={{ display: "none" }}
            onChange={handleSubmit}
          />
          <div>
            <Button
              btnName="Bulk user"
              BtnIcon={UserGroupIcon}
              onClickFunction={() => fileInputRef.current?.click()}
            />
          </div>
          <div>
            <Button
              btnName="Create Group"
              BtnIcon={UserPlusIcon}
              onClickFunction={openModalForNewGroup}
            />
          </div>
        </div>
      </div>
      <div className='h-[75vh]'>
        {/* Uncomment to use APIS */}
        {/* {search.length > 2 && searchedUserGroups ? (
          <UserGroupTable userValues={searchedUserGroups} />
        ) : (
          <UserGroupTable userValues={userValues} />
        )} */}
         <UserGroupTable />
      </div>
      {showAddGroupModal && isFirstTime.current && (
        <GroupModal onClose={onCloseAddGroupModal} onSubmit={onSubmitAddGroupModal} groupID={defaultGroupID} type={defaultType} />
      )}
      {showSubmitModal && (
        <SubmitModal modalName="Group is Created Success" onClose={onCloseSubmitModal} />
      )}
      {showBulkUserPopup && (
        <BulkQuesModal modalName={modalName} modalText={bulkUserPopupMessage} onClose={onCloseBulkUserPopup} />
      )}
    </main>
  );
};

export default Page;
