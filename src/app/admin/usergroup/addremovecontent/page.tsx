"use client";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState } from "react";
import AddRemoveContentForGroup from "@/components/admin/userGroup/AddRemoveContentForGroup";
import { useGetContentByGroupId } from "@/hook/admin/usergroup/content/useGetContentByGroupId";
import { useGetAllContentInTable } from "@/hook/content/useGetAllContentInTable";
import Button from "@/components/ui/Button";
import SubmitModal from "@/components/ui/SubmitModal";
import Link from "next/link";
import CreateModal from "@/components/admin/assessments/CreateModal";

export default function Page() {
  const searchParams = useSearchParams();
  const group_id = searchParams.get("groupId");

  const [groupId, setGroupId] = useState(parseInt(group_id || ""));
  const [refreshTrigger, setRefreshTrigger] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const router = useRouter();

  const onSaveClickFunction=()=>{
    setShowSubmitModal(true)
  }

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
    router.push("/admin/usergroup");
  };

  

  const {
    data: getContentByGroupIdData,
    isLoading: getContentByGroupIdLoading,
    isError: getContentByGroupIdError,
  } = useGetContentByGroupId(groupId);

  

  if ( getContentByGroupIdLoading) {
    return <div>Loading...</div>;
  }

  if ( getContentByGroupIdError) {
    return <div>Error fetching content</div>;
  }

 
  return (
    <div className="flex flex-col w-full h-[90vh] overflow-auto  ">
  {/* Use flex-grow for equal height division */}
  <div className="h-[40vh]">
  <AddRemoveContentForGroup
      type="remove"
      groupId={groupId}
    />
  </div>
  <div className="h-[40vh]">
   
    <AddRemoveContentForGroup
      type="all"
      dataforAll={getContentByGroupIdData}
      groupId={groupId}
    />
  </div>
  <div className="flex justify-center items-center h-[10vh]">
    
    <Button btnName="Save & Back "  onClickFunction={onSaveClickFunction} />
    
  </div>

  {showSubmitModal && (
         <CreateModal modulename="Changes have been successfully saved" onClose={onCloseSubmitModal} />
      )}
</div>

  );
}
