"use client";

import React, { useEffect, useState } from "react";
import AllAssessment from "@/components/admin/assessments/AllAssessments";
import { useGetAssessmentByGroupId } from "@/hook/admin/group/useGetAssessmentByGroupId";
import { useRouter, useSearchParams } from "next/navigation";

// import { useGetAllAssessmentsForGroup } from "@/hook/assessments/useGetAllAssessmentForGroup";
import { useGetAssessmentsNotInGroup } from "@/hook/admin/group/useGetAssessmentsNotInGroup";
import CreateModal from "@/components/admin/assessments/CreateModal";
import Button from "@/components/ui/Button";


export default function AdminAssessmentPage() {

  const searchParams = useSearchParams();
  const group_id = searchParams.get("groupId");
  const [groupId, setGroupId] = useState(parseInt(group_id || ''));
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const router = useRouter();

  const onSaveClickFunction=()=>{
    setShowSubmitModal(true)
  }

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
    router.push("/admin/usergroup");
  };

  return (
    <div className="flex flex-col w-full h-[90vh] overflow-auto  ">
  {/* Use flex-grow for equal height division */}
  <div className="h-[40vh]">
        <AllAssessment type={"remove"} groupId={groupId} />
        </div>
        <div className="h-[40vh]">
        <AllAssessment type={"add"} groupId={groupId} />
      </div>
      <div className="flex justify-center items-center h-[10vh]">
    
    <Button btnName="Save & Back"  onClickFunction={onSaveClickFunction} />
    
  </div>

  {showSubmitModal && (
         <CreateModal modulename="Changes have been successfully saved" onClose={onCloseSubmitModal} />
      )}
</div>
    
  );
}
