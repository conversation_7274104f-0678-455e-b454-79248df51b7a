"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  XMarkIcon,
  InformationCircleIcon,
  DocumentTextIcon,
  ArrowUpTrayIcon,
  ChevronDownIcon,
  ArrowRightIcon,
  LightBulbIcon,
  ClipboardDocumentCheckIcon,
  UsersIcon,
  SparklesIcon,
  ArrowLeftIcon
} from "@heroicons/react/24/outline";

// UI Components
import Heading from "@/components/ui/Heading";
import Button from "@/components/ui/Button001";
import SampleDocumentModal from "@/components/ui/SampleDocumentModal";
import Nothing from "@/components/ui/Nothing";

// Custom Components
import RoleCard from "@/components/admin/roles/JobCard";
import FileDropzone from "@/components/admin/roles/FileDropzone";

interface Job {
  job_id: number;
  job_title: string;
  job_description?: string;
  sectors?: string[];
  domains?: string[]; 
  created_at?: string;
  status?: 'active' | 'draft' | 'archived';
  source_documents?: string[]; 
}

interface UploadedFile {
  name: string;
  size: string | number;
  type: string;
  lastModified: string | number;
}

// Document preview modal with Generate Skills Master button
const DocumentPreviewWithGenerateButton = ({ 
  document, 
  onClose, 
  onGenerateClick 
}: { 
  document: UploadedFile, 
  onClose: () => void, 
  onGenerateClick: () => void 
}) => {
  return (
    <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="font-medium text-lg">{document.name}</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6 overflow-auto flex-1">
          <iframe 
            src="/samplejd.pdf" 
            className="w-full h-[600px]" 
            title="Document Preview"
          />
        </div>
        
        <div className="p-4 border-t flex justify-end gap-3">
          <Button 
            buttonName="Close" 
            onClick={onClose}
            variant="secondary"
          />
          <Button 
            buttonName="Generate Skills Master" 
            onClick={onGenerateClick}
            variant="primary"
            icon={<SparklesIcon className="h-4 w-4" />}
          />
        </div>
      </div>
    </div>
  );
};

// Document list modal with document viewer
const DocumentListModal = ({ 
  documents, 
  onClose
}: { 
  documents: string[], 
  onClose: () => void
}) => {
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null);
  
  const handleDocumentSelect = (doc: string) => {
    setSelectedDocument(doc);
  };
  
  const handleBackToList = () => {
    setSelectedDocument(null);
  };
  
  return (
    <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="font-medium text-lg">
            {selectedDocument ? (
              <div className="flex items-center">
                <button 
                  onClick={handleBackToList}
                  className="mr-3 text-blue-600 hover:text-blue-800 flex items-center"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-1" />
                 
                </button>
                <span>{selectedDocument}</span>
              </div>
            ) : (
              "Source Documents"
            )}
          </h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6 overflow-auto flex-1">
          {selectedDocument ? (
            // Document viewer
            <iframe 
              src="/samplejd.pdf" // In a real app, this would be a dynamic path to the actual document
              className="w-full h-[600px] border rounded" 
              title="Document Preview"
            />
          ) : (
            // Document list
            documents.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No documents available
              </div>
            ) : (
              <div className="space-y-3">
                {documents.map((doc, index) => (
                  <div 
                    key={index}
                    className="flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => handleDocumentSelect(doc)}
                  >
                    <DocumentTextIcon className="h-5 w-5 text-blue-600 mr-3 flex-shrink-0" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">{doc}</p>
                    </div>
                    <ArrowRightIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
                  </div>
                ))}
              </div>
            )
          )}
        </div>
        
        <div className="p-4 border-t flex justify-end">
        </div>
      </div>
    </div>
  );
};

export default function RolesManagerPage() {
  const router = useRouter();
  
  // Dummy variable to switch between admin types
  // In a real app, this would come from user authentication and context or localstorage
  const adminType = "adminb2i"; // Change this to "adminb2b" or "adminb2c" to test different layouts
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploadStatus, setUploadStatus] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedJobIds, setSelectedJobIds] = useState<Set<number>>(new Set());
  const [selectedDocument, setSelectedDocument] = useState<UploadedFile | null>(null);
  const [isDocumentListModalOpen, setIsDocumentListModalOpen] = useState(false);
  const [selectedJobDocuments, setSelectedJobDocuments] = useState<string[]>([]);

  // Sample roles data with corresponding document information
  const rolesData: Job[] = [
    { 
      job_id: 1, 
      job_title: "Junior Data Associate", 
      job_description: "Entry-level role focused on data processing and analysis.",
      sectors: ["Technology", "Data"],
      domains: ["Finance", "Healthcare"],
      created_at: "2023-11-15",
      status: 'active',
      source_documents: ["junior_data_associate_requirements.pdf", "data_skills_framework.docx"]
    },
    { 
      job_id: 2, 
      job_title: "AI Data Scientist", 
      job_description: "Developing and deploying AI-based solutions and models.",
      sectors: ["Technology", "AI", "Research"],
      domains: ["Marketing", "Product"],
      created_at: "2023-12-10",
      status: 'active',
      source_documents: ["ai_scientist_job_description.pdf", "ai_competency_framework.pdf", "research_methodologies.docx"]
    },
    { 
      job_id: 3, 
      job_title: "ML Engineer", 
      job_description: "Building scalable ML systems and infrastructure.",
      sectors: ["Technology", "ML"],
      domains: ["Manufacturing", "Retail"],
      created_at: "2024-01-20",
      status: 'active',
      source_documents: ["ml_engineer_skills.pdf", "machine_learning_roadmap.pdf"]
    }
  ];

  useEffect(() => {
    // Load previously uploaded files from session storage
    const storedData = sessionStorage.getItem("uploadedFiles");
    let parsedData = storedData ? JSON.parse(storedData) : [];
    setUploadedFiles(parsedData);

    // Show toast notification on first load
    toast.info(`Welcome to Skilling.AI`, {
      position: "bottom-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "light",
    });
  }, []);

  const handleFileChange = (newFiles: UploadedFile[]) => {
    if (newFiles.length > 0) {
      setUploadedFiles((prevUploadedFiles) => [...prevUploadedFiles, ...newFiles]);
      setUploadStatus(`${newFiles.length} file(s) added. Ready to generate skills.`);
    }
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(newFiles);
    sessionStorage.setItem("uploadedFiles", JSON.stringify(newFiles));
    setUploadStatus(`File removed. ${newFiles.length} file(s) remaining.`);
  };

  const handleGenerateSkills = () => {
    if (uploadedFiles.length > 0) {
      setIsProcessing(true);
      setUploadStatus("Processing documents and extracting skills...");
      
      // Simulate processing time
      setTimeout(() => {
        sessionStorage.setItem("uploadedFiles", JSON.stringify(uploadedFiles));
        toast.success('Skills extracted successfully!');
        
        // Navigate directly to skill master page
        router.push('/admin/roles/skill_master?role=1');
      }, 2000);
    } else {
      toast.error('Please upload at least one document.');
      setUploadStatus("Please upload at least one document.");
    }
  };

  const handleRoleSelection = (jobId: number) => {
    setSelectedJobIds((prevSelectedIds) => {
      const newSelectedIds = new Set(prevSelectedIds);
      if (newSelectedIds.has(jobId)) {
        newSelectedIds.delete(jobId);
      } else {
        newSelectedIds.add(jobId);
      }
      return newSelectedIds;
    });
  };

  const handleViewDocuments = (jobId: number) => {
    const job = rolesData.find(job => job.job_id === jobId);
    if (job && job.source_documents) {
      setSelectedJobDocuments(job.source_documents);
      setIsDocumentListModalOpen(true);
    } else {
      toast.info('No documents available for this role');
    }
  };

  const handleViewSpecificDocument = (fileName: string) => {
    // This function is now handled internally by the DocumentListModal
    // We'll keep it for compatibility with the JobCard component
    toast.info(`Viewing document: ${fileName}`);
    setIsDocumentListModalOpen(false);
    setIsModalOpen(true);
  };

  const handleViewPreviousDocument = (file: UploadedFile) => {
    setSelectedDocument(file);
    setIsModalOpen(true);
  };

  const handleViewSkillMaster = (jobId: number) => {
    router.push(`/admin/roles/skill_master?role=${jobId}`);
  };

  // Render the How It Works section (only for adminb2i)
  const renderHowItWorks = () => {
    if (adminType !== 'adminb2i') return null;
    
    return (
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border-l-4 border-blue-500">
        <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <LightBulbIcon className="h-5 w-5 text-blue-600 mr-2" />
          How It Works: Automated Skill Extraction
        </h2>
        
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 py-2">
          {/* Step 1 */}
          <div className="flex-1 text-center">
            <div className="bg-blue-50 mx-auto rounded-full p-4 h-16 w-16 flex items-center justify-center mb-2">
              <DocumentTextIcon className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-800">1. Upload Document</h3>
            <p className="text-sm text-gray-600 mt-1">Upload job descriptions or role requirements</p>
          </div>
          
          {/* Arrow */}
          <div className="hidden md:block text-gray-400">
            <ArrowRightIcon className="h-5 w-5" />
          </div>
          <div className="block md:hidden text-gray-400">
            <ChevronDownIcon className="h-5 w-5" />
          </div>
          
          {/* Step 2 */}
          <div className="flex-1 text-center">
            <div className="bg-purple-50 mx-auto rounded-full p-4 h-16 w-16 flex items-center justify-center mb-2">
              <div className="relative">
                <SparklesIcon className="h-8 w-8 text-purple-600" />
                <div className="absolute -top-1 -right-1 rounded-full bg-purple-600 text-white text-xs px-1">
                  AI
                </div>
              </div>
            </div>
            <h3 className="font-medium text-gray-800">2. AI Analyzes</h3>
            <p className="text-sm text-gray-600 mt-1">Our system understands requirements and related skills</p>
          </div>
          
          {/* Arrow */}
          <div className="hidden md:block text-gray-400">
            <ArrowRightIcon className="h-5 w-5" />
          </div>
          <div className="block md:hidden text-gray-400">
            <ChevronDownIcon className="h-5 w-5" />
          </div>
          
          {/* Step 3 */}
          <div className="flex-1 text-center">
            <div className="bg-green-50 mx-auto rounded-full p-4 h-16 w-16 flex items-center justify-center mb-2">
              <ClipboardDocumentCheckIcon className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="font-medium text-gray-800">3. Creates Skill Master</h3>
            <p className="text-sm text-gray-600 mt-1">Hierarchical skill structure is generated</p>
          </div>
          
          {/* Arrow */}
          <div className="hidden md:block text-gray-400">
            <ArrowRightIcon className="h-5 w-5" />
          </div>
          <div className="block md:hidden text-gray-400">
            <ChevronDownIcon className="h-5 w-5" />
          </div>
          
          {/* Step 4 */}
          <div className="flex-1 text-center">
            <div className="bg-amber-50 mx-auto rounded-full p-4 h-16 w-16 flex items-center justify-center mb-2">
              <UsersIcon className="h-8 w-8 text-amber-600" />
            </div>
            <h3 className="font-medium text-gray-800">4. Job Role Created</h3>
            <p className="text-sm text-gray-600 mt-1">Ready to view and customize</p>
          </div>
        </div>
      </div>
    );
  };

  // Render the Upload Document UI (only for adminb2i)
  const renderUploadSection = () => {
    if (adminType !== 'adminb2i') return null;
    
    return (
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <Heading pgHeading="Upload Job Requirement" />
            <button 
              onClick={() => toast.info('Upload job descriptions to automatically extract skills and create skill maps.')}
              className="text-gray-500 hover:text-blue-600 transition-colors"
              aria-label="Information"
            >
              <InformationCircleIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            <div className="bg-gray-50 rounded-lg p-5 border border-gray-200 relative">
              {isProcessing && (
                <div className="absolute inset-0 bg-white/80 flex flex-col items-center justify-center z-10 rounded-lg">
                  <div className="flex items-center justify-center space-x-2 mb-3">
                    <div className="animate-spin h-6 w-6 border-3 border-blue-500 border-t-transparent rounded-full"></div>
                    <span className="text-blue-600 font-medium">Processing...</span>
                  </div>
                  <div className="bg-blue-50 text-blue-800 text-sm rounded-md px-4 py-2 max-w-md text-center">
                    <p>Our AI is analyzing your document to extract skills and create a skill master</p>
                  </div>
                </div>
              )}
            
              <h3 className="text-lg font-medium mb-3 flex items-center">
                <ArrowUpTrayIcon className="h-5 w-5 text-blue-600 mr-2" />
                Select Document
              </h3>
              <p className="text-gray-600 mb-4">
                Upload a document that contains the job description, skills requirements, or role profile.{" "}
                <button 
                  className="text-blue-600 hover:underline" 
                  onClick={() => {
                    setSelectedDocument(null);
                    setIsModalOpen(true);
                  }}
                >
                  (See sample document)
                </button>
              </p>
              
              <FileDropzone 
                onFilesAdded={handleFileChange} 
                acceptedFileTypes=".pdf,.doc,.docx"
                maxFileSizeMB={5}
              />
              
              <div className="mt-6">
                <button
                  onClick={handleGenerateSkills}
                  disabled={isProcessing}
                  className={`w-full py-3 px-4 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 
                    ${isProcessing 
                      ? 'bg-blue-400 cursor-not-allowed' 
                      : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transform hover:scale-[1.01]'
                    } 
                    text-white flex items-center justify-center gap-2 relative overflow-hidden group`}
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                      <span>Processing...</span>
                    </>
                  ) : (
                    <>
                      <SparklesIcon className="h-5 w-5 group-hover:animate-pulse" />
                      <span>Generate Skills Master</span>
                    </>
                  )}
                </button>
                <p className="text-sm text-center text-gray-500 mt-2">
                  Our AI will analyze your document to identify and extract required skills to create a complete Skills Master
                </p>
              </div>
            </div>
            
            {uploadedFiles.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-medium">Selected Files</h3>
                  {uploadStatus && (
                    <span className="text-sm text-gray-600">{uploadStatus}</span>
                  )}
                </div>
                
                <div className="space-y-2 max-h-[250px] overflow-y-auto">
                  {uploadedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg border border-gray-100">
                      <div className="flex items-center space-x-3">
                        <DocumentTextIcon className="h-5 w-5 text-blue-600" />
                        <span className="text-sm truncate max-w-[250px]">{file.name}</span>
                      </div>
                      <button 
                        onClick={() => handleRemoveFile(index)} 
                        className="text-gray-400 hover:text-red-500 transition-colors"
                        aria-label="Remove file"
                      >
                        <XMarkIcon className="h-5 w-5" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div className="bg-gray-50 rounded-lg p-5 border border-gray-200">
            <h3 className="text-lg font-medium mb-3">Previously Uploaded Files</h3>
            {uploadedFiles.length > 0 ? (
              <div className="space-y-2 max-h-[400px] overflow-y-auto">
                {uploadedFiles.map((file, index) => (
                  <div 
                    key={index} 
                    onClick={() => handleViewPreviousDocument(file)}
                    className="flex items-center py-2 px-3 bg-white rounded-lg border border-gray-100 cursor-pointer hover:border-blue-200 transition-all duration-200"
                  >
                    <DocumentTextIcon className="h-5 w-5 text-blue-600 mr-3" />
                    <div className="flex-1">
                      <p className="text-sm font-medium truncate">{file.name}</p>
                      <p className="text-xs text-gray-500">
                        {typeof file.size === 'number' 
                          ? `${Math.round(file.size / 1024)} KB` 
                          : file.size}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Image 
                  src="/pdf.png" 
                  width={40} 
                  height={40} 
                  alt="PDF Icon" 
                  className="mb-2 opacity-40" 
                />
                <p className="text-gray-500">No files uploaded yet</p>
                <p className="text-xs text-gray-400 mt-1">Upload documents above to extract skills</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Visual connector (only for adminb2i)
  const renderConnector = () => {
    if (adminType !== 'adminb2i') return null;
    
    return (
      <div className="flex justify-center mb-6">
        <div className="text-center">
          <ChevronDownIcon className="h-8 w-8 text-blue-500 mx-auto" />
          <span className="text-sm font-medium text-gray-600">Uploaded documents create job roles below</span>
        </div>
      </div>
    );
  };

  // Render RoleCards with different actions based on admin type
  const renderRoleCards = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
        {rolesData.length === 0 ? (
          <div className="col-span-3 flex justify-center items-center py-16">
            <Nothing 
              title="No Roles Available" 
              para="There are currently no job roles to display." 
            />
          </div>
        ) : (
          rolesData.map((role, index) => (
            <div key={role.job_id} className="h-full w-full">
              <RoleCard
                job={role}
                index={index}
                colors={['bg-blue-100', 'bg-emerald-100', 'bg-amber-100', 'bg-rose-100', 'bg-violet-100']}
                selected={selectedJobIds.has(role.job_id)}
                onClick={() => handleRoleSelection(role.job_id)}
                onViewSkills={() => handleViewSkillMaster(role.job_id)}
                // Conditionally render View Document button based on admin type
                onViewDocument={
                  adminType === 'adminb2b' 
                    ? undefined 
                    : () => handleViewDocuments(role.job_id)
                }
                // Pass admin type to control UI elements
                adminType={adminType}
                // Function to view a specific document directly
                onViewSpecificDocument={(fileName) => handleViewSpecificDocument(fileName)}
              />
            </div>
          ))
        )}
      </div>
    );
  };

  return (
    <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
      {/* Conditionally render modal based on what's being viewed */}
      {isModalOpen && !selectedDocument && (
        <SampleDocumentModal 
          title="Sample Skill Requirement Document"
          documentUrl="/samplejd.pdf" 
          onClose={() => setIsModalOpen(false)}
        />
      )}

      {isModalOpen && selectedDocument && (
        <DocumentPreviewWithGenerateButton 
          document={selectedDocument}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedDocument(null);
          }}
          onGenerateClick={() => {
            setIsModalOpen(false);
            setSelectedDocument(null);
            handleGenerateSkills();
          }}
        />
      )}
      
      {isDocumentListModalOpen && (
        <DocumentListModal 
          documents={selectedJobDocuments}
          onClose={() => setIsDocumentListModalOpen(false)}
          onDocumentSelect={handleViewSpecificDocument}
        />
      )}
      
      <ToastContainer 
        position="bottom-right" 
        autoClose={5000} 
        hideProgressBar={false} 
        newestOnTop={false} 
        closeOnClick={true} 
        rtl={false} 
        pauseOnFocusLoss 
        draggable 
        pauseOnHover 
        theme="light" 
      />
      
      <div className="w-full max-w-7xl mx-auto">
        {/* How It Works Section (only for adminb2i) */}
        {renderHowItWorks()}
        
        {/* Upload Document UI (only for adminb2i) */}
        {renderUploadSection()}
        
        {/* Visual connector (only for adminb2i) */}
        {renderConnector()}
        
        {/* Created Job Roles Section (for all admin types) */}
        <div id="created-roles-section" className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4">
              <Heading pgHeading={`Job Roles ${adminType === 'adminb2i' ? '(Generated)' : ''}`} />
              <button 
                onClick={() => toast.info(`Showing job roles.`)}
                className="text-gray-500 hover:text-blue-600 transition-colors"
                aria-label="Information"
              >
                <InformationCircleIcon className="w-5 h-5" />
              </button>
            </div>
          </div>

          {renderRoleCards()}
        </div>
      </div>
    </main>
  );
}