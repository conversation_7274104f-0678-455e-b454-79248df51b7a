"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from 'next/navigation';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  ChevronDownIcon,
  ChevronRightIcon,
  DocumentDuplicateIcon,
  PencilIcon,
  DocumentTextIcon,
  ArrowLeftIcon,
  CheckIcon,
  XMarkIcon,
  UserPlusIcon,
  SparklesIcon
} from "@heroicons/react/24/outline";

// UI Components
import Heading from "@/components/ui/Heading";
import Button from "@/components/ui/Button001";
// Using existing component structure

interface SkillNode {
  level: string;
  description: string;
  subskills?: Record<string, SkillNode>;
}

type SkillsData = Record<string, SkillNode>;

export default function SkillMasterPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const roleId = searchParams.get('role');
  
  const [expandedSkills, setExpandedSkills] = useState<Set<string>>(new Set());
  const [editMode, setEditMode] = useState(false);
  const [editingSkill, setEditingSkill] = useState<string | null>(null);
  const [tempEditData, setTempEditData] = useState<{level: string, description: string} | null>(null);
  const [skillsData, setSkillsData] = useState<SkillsData>({});
  const [roleName, setRoleName] = useState("");
  const [viewMode, setViewMode] = useState<'tree' | 'table'>('tree');
  const [isCreatingRole, setIsCreatingRole] = useState(false);

  // Levels for dropdown selection
  const skillLevels = ["Beginner", "Intermediate", "Advanced", "Expert"];

  useEffect(() => {
    // In a real application, fetch data from API based on roleId
    // For demo, using static data
    const roleTitle = roleId === "1" ? "Junior Data Associate" : "AI Data Scientist";
    setRoleName(roleTitle);
    
    // Sample skills data - would be fetched from API in production
    const sampleData: SkillsData = {
      "Technical Skills": {
        "level": "Advanced",
        "description": "Proficiency in advanced AI and data science techniques",
        "subskills": {
          "Machine Learning": {
            "level": "Advanced",
            "description": "Developing complex predictive models and AI solutions",
            "subskills": {
              "Supervised Learning": {
                "level": "Advanced",
                "description": "Implementing regression, classification, and ensemble learning methods"
              },
              "Unsupervised Learning": {
                "level": "Intermediate",
                "description": "Applying clustering, anomaly detection, and dimensionality reduction techniques"
              },
              "Deep Learning": {
                "level": "Advanced",
                "description": "Building and training neural networks for NLP, computer vision, and tabular data"
              }
            }
          },
          "Natural Language Processing (NLP)": {
            "level": "Intermediate",
            "description": "Working with text data and language models",
            "subskills": {
              "Transformers": {
                "level": "Advanced",
                "description": "Fine-tuning transformer models like BERT and GPT"
              }
            }
          },
          "Computer Vision": {
            "level": "Intermediate",
            "description": "Extracting insights from images and videos using CNNs and transformer-based models"
          },
          "MLOps & AI Deployment": {
            "level": "Advanced",
            "description": "Automating ML workflows, model monitoring, and deploying scalable AI solutions"
          }
        }
      },
      "Business & Ethical Considerations": {
        "level": "Intermediate",
        "description": "Understanding AI ethics, governance, and industry applications",
        "subskills": {
          "AI Governance": {
            "level": "Intermediate",
            "description": "Ensuring fairness, transparency, and accountability in AI-driven decision-making"
          },
          "AI Regulatory Compliance": {
            "level": "Intermediate",
            "description": "Adhering to regulations and standards in AI development and deployment"
          }
        }
      }
    };
    
    setSkillsData(sampleData);
    
    // Set top-level skills as expanded by default
    const initialExpanded = new Set<string>();
    Object.keys(sampleData).forEach(skill => initialExpanded.add(skill));
    setExpandedSkills(initialExpanded);
  }, [roleId]);

  const toggleSkill = (path: string) => {
    setExpandedSkills(prev => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  const startEditSkill = (path: string, level: string, description: string) => {
    if (editMode) {
      setEditingSkill(path);
      setTempEditData({ level, description });
    }
  };

  const saveSkillEdit = (path: string) => {
    if (!tempEditData) return;
    
    // In a real application, this would call an API to update the skill
    // For demo, updating the local state
    const pathParts = path.split('.');
    const newData = JSON.parse(JSON.stringify(skillsData));
    
    let current = newData;
    for (let i = 0; i < pathParts.length - 1; i++) {
      if (i === 0) {
        current = current[pathParts[i]];
      } else {
        current = current.subskills[pathParts[i]];
      }
    }
    
    const lastPart = pathParts[pathParts.length - 1];
    if (pathParts.length === 1) {
      current[lastPart].level = tempEditData.level;
      current[lastPart].description = tempEditData.description;
    } else {
      current.subskills[lastPart].level = tempEditData.level;
      current.subskills[lastPart].description = tempEditData.description;
    }
    
    setSkillsData(newData);
    setEditingSkill(null);
    setTempEditData(null);
    
    toast.success('Skill updated successfully!');
  };

  const handleCreateRole = () => {
    setIsCreatingRole(true);
    toast.success('Creating job role based on skills...');
    
    // Simulate processing time
    setTimeout(() => {
      // Navigate back to roles page to show the created role
      router.push('/admin/roles#created-roles-section');
    }, 1500);
  };

  const cancelEdit = () => {
    setEditingSkill(null);
    setTempEditData(null);
  };

  const exportSkillMaster = () => {
    // In a real application, this would generate a downloadable file
    toast.info('Exporting Skill Master...');
    
    // Simulate file download
    setTimeout(() => {
      toast.success('Skill Master exported successfully!');
    }, 1500);
  };

  // Recursive function to render a skill node in tree view
  const renderSkillNode = (
    skillName: string, 
    skillData: SkillNode, 
    path: string = skillName, 
    depth: number = 0
  ) => {
    const hasSubskills = skillData.subskills && Object.keys(skillData.subskills).length > 0;
    const isExpanded = expandedSkills.has(path);
    const isEditing = editingSkill === path;
    
    const levelColor = {
      'Beginner': 'bg-green-100 text-green-800',
      'Intermediate': 'bg-blue-100 text-blue-800',
      'Advanced': 'bg-purple-100 text-purple-800',
      'Expert': 'bg-red-100 text-red-800'
    };
    
    return (
      <div key={path} className="mb-2">
        <div 
          className={`flex items-start p-3 rounded-lg ${depth === 0 ? 'bg-gray-50' : ''}`}
          style={{ marginLeft: `${depth * 20}px` }}
        >
          {hasSubskills && (
            <button 
              onClick={() => toggleSkill(path)}
              className="mr-2 mt-1 text-gray-500 hover:text-gray-700"
              aria-label={isExpanded ? 'Collapse' : 'Expand'}
            >
              {isExpanded ? 
                <ChevronDownIcon className="h-4 w-4" /> : 
                <ChevronRightIcon className="h-4 w-4" />
              }
            </button>
          )}
          
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="font-medium text-gray-900">{skillName}</div>
                
                {/* {isEditing ? (
                  <div className="mt-2 space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Skill Level
                      </label>
                      <select
                        value={tempEditData?.level || ''}
                        onChange={(e) => setTempEditData({ 
                          ...tempEditData!, 
                          level: e.target.value 
                        })}
                        className="w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 text-sm"
                      >
                        {skillLevels.map(level => (
                          <option key={level} value={level}>{level}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        value={tempEditData?.description || ''}
                        onChange={(e) => setTempEditData({
                          ...tempEditData!,
                          description: e.target.value
                        })}
                        className="w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 text-sm"
                        rows={3}
                      />
                    </div>
                    
                    <div className="flex space-x-2">
                      <button
                        onClick={() => saveSkillEdit(path)}
                        className="inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md bg-blue-50 text-blue-700 hover:bg-blue-100"
                      >
                        <CheckIcon className="h-4 w-4 mr-1" />
                        Save
                      </button>
                      <button
                        onClick={cancelEdit}
                        className="inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md bg-gray-50 text-gray-700 hover:bg-gray-100"
                      >
                        <XMarkIcon className="h-4 w-4 mr-1" />
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : ( */}
                  <>
                    <div className="mt-1 text-sm text-gray-600">{skillData.description}</div>
                    <div className="mt-2 flex items-center">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${levelColor[skillData.level as keyof typeof levelColor] || 'bg-gray-100 text-gray-800'}`}>
                        {skillData.level}
                      </span>
                      
                      {editMode && (
                        <button 
                          onClick={() => startEditSkill(path, skillData.level, skillData.description)}
                          className="ml-2 text-gray-400 hover:text-blue-600"
                          aria-label="Edit skill"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </>
                {/* )} */}
              </div>
            </div>
          </div>
        </div>
        
        {hasSubskills && isExpanded && (
          <div className="mt-2">
            {Object.entries(skillData.subskills!).map(([subName, subData]) => (
              renderSkillNode(subName, subData, `${path}.${subName}`, depth + 1)
            ))}
          </div>
        )}
      </div>
    );
  };

  // Function to render skills in table view
  const renderSkillsTable = () => {
    // Flatten the skills hierarchy for table display
    const flattenSkills = (
      data: SkillsData, 
      parentPath: string = '', 
      result: Array<{name: string, level: string, description: string, path: string}> = []
    ) => {
      Object.entries(data).forEach(([name, node]) => {
        const currentPath = parentPath ? `${parentPath}.${name}` : name;
        result.push({
          name,
          level: node.level,
          description: node.description,
          path: currentPath
        });
        
        if (node.subskills) {
          flattenSkills(node.subskills, currentPath, result);
        }
      });
      
      return result;
    };
    
    const flatSkills = flattenSkills(skillsData);
    
    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Skill Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Level
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              {editMode && (
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {flatSkills.map((skill, index) => (
              <tr key={skill.path} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {skill.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    skill.level === 'Advanced' ? 'bg-purple-100 text-purple-800' :
                    skill.level === 'Intermediate' ? 'bg-blue-100 text-blue-800' :
                    skill.level === 'Beginner' ? 'bg-green-100 text-green-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {skill.level}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {skill.description}
                </td>
                {/* {editMode && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button
                      onClick={() => startEditSkill(skill.path, skill.level, skill.description)}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      Edit
                    </button>
                  </td>
                )} */}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <main className="w-full flex flex-col p-4 relative overflow-auto h-full bg-gray-50">
      <ToastContainer 
        position="bottom-right" 
        autoClose={3000} 
        hideProgressBar={false} 
        closeOnClick={true} 
        pauseOnHover={true} 
      />
      
      <div className="w-full max-w-7xl mx-auto">
        {/* Use your existing Breadcrumb component here */}
        
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
            <div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => router.push('/admin/roles')}
                  className="mr-2 text-gray-500 hover:text-gray-700"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                </button>
                <Heading pgHeading={`Skill Master: ${roleName}`} />
              </div>
              <p className="text-gray-500 mt-1 text-sm">
                View and manage the skills extracted for this job role
              </p>
            </div>
            
            <div className="flex gap-3 w-full sm:w-auto">
              <div className="flex rounded-md shadow-sm">
                <button
                  onClick={() => setViewMode('tree')}
                  className={`px-4 py-2 text-sm font-medium rounded-l-md ${
                    viewMode === 'tree' 
                      ? 'bg-blue-50 text-blue-700 border-blue-200' 
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  } border`}
                >
                  Tree View
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`px-4 py-2 text-sm font-medium rounded-r-md ${
                    viewMode === 'table' 
                      ? 'bg-blue-50 text-blue-700 border-blue-200' 
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  } border`}
                >
                  Table View
                </button>
              </div>
              
              {/* <Button 
                buttonName="Export" 
                onClick={exportSkillMaster}
                icon={<DocumentDuplicateIcon className="w-4 h-4" />}
                variant="secondary"
              />
              
              <Button 
                buttonName={editMode ? "Save Changes" : "Edit Skills"} 
                onClick={() => setEditMode(!editMode)}
                icon={<PencilIcon className="w-4 h-4" />}
                variant={editMode ? "primary" : "secondary"}
              /> */}
            </div>
          </div>
          
          <div className="bg-white rounded-lg">
            {viewMode === 'tree' ? (
              <div className="space-y-3">
                {Object.entries(skillsData).map(([skillName, skillData]) => 
                  renderSkillNode(skillName, skillData)
                )}
              </div>
            ) : (
              renderSkillsTable()
            )}
          </div>
          
          {/* Enhanced Create Role Button */}
          <div className="mt-8 flex justify-center">
            <button
              onClick={handleCreateRole}
              disabled={isCreatingRole}
              className={`py-4 px-8 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 
                ${isCreatingRole 
                  ? 'bg-green-500 cursor-not-allowed' 
                  : 'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 transform hover:scale-[1.02]'
                } 
                text-white flex items-center justify-center gap-3 relative overflow-hidden group`}
            >
              {isCreatingRole ? (
                <>
                  <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Creating Job Role...</span>
                </>
              ) : (
                <>
                  <UserPlusIcon className="h-6 w-6 group-hover:animate-pulse" />
                  <span>Create Job Role</span>
                  <SparklesIcon className="h-5 w-5 group-hover:rotate-12 transition-transform" />
                </>
              )}
            </button>
          </div>
          
          {/* Additional help text */}
          <div className="mt-4 text-center text-sm text-gray-500">
            Create a fully defined job role based on this skill master with all skills automatically mapped
          </div>
        </div>
      </div>
    </main>
  );
}