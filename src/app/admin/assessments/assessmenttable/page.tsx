"use client";
import Button from "@/components/ui/Button";
import Heading from "@/components/ui/Heading";
import SearchBar from "@/components/admin/SearchBar";
import AssessmentTable from "@/components/admin/assessments/AssessmentTable";
import { useAssessmentDelete } from "@/hook/assessments/useAssessmentDelete";
import { useGetAllAssessments } from "@/hook/assessments/useGetAllAssessments";
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { useGetSearchedAssessment } from "@/hook/assessments/useGetSearchedAssessment";


export default function Page() {
  const [limit, setLimit] = useState(100)
  const [offset, setOffset] = useState(0)
  const [search, setSearch] = useState("")

  const [isAssessmentData, setAssessmentData] = useState([]);

  const { data: assessments, isLoading, isError } = useGetAllAssessments(offset, limit);
  const { mutate: AssessmentDelete } = useAssessmentDelete();

  const { data: searchedAssessment } = useGetSearchedAssessment(search)


  if (isLoading) {
    return <div>Loading...</div>;
  }

  //Check implementation of Retry button
  if (isError) {
    return <div>Error fetching assessments</div>;
  }

  const handleSearch = (groupSearch: string) => {
    //Call the filter content API and use the filtered content here
    console.log("groupSearch:-", groupSearch)
    setSearch(groupSearch);
  };

  console.log("search:-", search)

  console.log("searchedAssessment:-", searchedAssessment)


  const handleDelete = (assessmentId: number) => {
    AssessmentDelete(assessmentId, {
      onSuccess: () => {
        // Filter out the deleted item from contentData
        const updatedAssessmentData = assessments.filter(item => item.assessment_id !== assessmentId);
        setAssessmentData(updatedAssessmentData);
      },
      onError: () => {
        console.error('Deletion error:', deleteError);
      }
    });
  };

  return (
    <main className="w-full flex h-[90vh] bg-primary flex-col gap-3 p-3 overflow-auto">
     
        <div className='flex flex-col justify-start h-[5vh]'>
      <Heading pgHeading="Assessment List" />
         </div>

      <div className="flex flex-row justify-end items-end h-[10vh]">
        <SearchBar onSearch={handleSearch} />

        <div className="flex justify-end w-full ">
          <Link href={"/admin/assessments/createassessments"}>
            <Button btnName="Add Assessment" />
          </Link>
        </div>

      </div>
      
      <div className=" h-[75vh]">
        {(search?.length) > 2 && searchedAssessment ?
          <AssessmentTable  AssessmentData={searchedAssessment} onDelete={handleDelete}  /> : <AssessmentTable AssessmentData={assessments} onDelete={handleDelete} />}
      </div>
    </main>
  );
}
