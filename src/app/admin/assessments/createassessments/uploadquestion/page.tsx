"use client";
import Heading from "@/components/ui/Heading";
import SearchBar from "@/components/admin/SearchBar";
import UploadQuestion from "@/components/admin/assessments/UploadQutestion";

import React, { useState } from "react";

export default function Page() {
 

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col gap-2 p-2">
      <Heading pgHeading="Upload Question " />

      <UploadQuestion/>
    </main>
  );
}
