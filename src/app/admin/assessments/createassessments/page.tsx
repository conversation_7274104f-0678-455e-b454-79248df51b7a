"use client";
// React and Next.js hooks
import { useEffect, useState, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";

// Components and hooks imports
import Heading from "@/components/ui/Heading";
import CreateModal from "@/components/admin/assessments/CreateModal";
import DeletedUserModal from "@/components/admin/userGroup/DeletedUserModal";
import EditQuestionModal from "@/components/admin/searchQuestion/EditQuestionModal";
import { useGetAllQuestionUplaodTable } from "@/hook/questions/useGetAllQuestionUploadTable";
import { usePostCreateAssessmentWithQuestion } from "@/hook/assessments/usePostCreateAssessmentWithQuestion";

// Icon imports
import {
  InformationCircleIcon,
  PencilSquareIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import SearchBar from "@/components/admin/SearchBar";

import Propmt from "@/components/admin/assessments/Propmt";
import { useGetUserPrompt } from "@/hook/assessments/useGetUserPrompt";
import SubmitModal from "@/components/ui/SubmitModal";
import { fail } from "assert";


// Type for form data
interface FormData {
  assessmentName: string;
  instructing: string;
  totalTime: string;
  totalMarks: string;
  startDate: string;
  endDate: string;
  totalAttempts: string;
}

interface Question {
  question_id: number;
  picture: string | null;
  question_type: string;
  answer_option: number;
  question_difficulty: number;
  answer_text: string;
  option1: string;
  creation_date: string;
  option2: string;
  marks: number;
  option3: string;
  answer_explanation: string;
  option4: string;
  topics_string: string;
  question: string;
  question_source: string;
  default_text: string | null;
}

// Main component
export default function Page() {
  // State for form data and errors
  const [formData, setFormData] = useState<FormData>({
    assessmentName: "",
    instructing: "",
    totalTime: "",
    totalMarks: "",
    startDate: "",
    endDate: "",
    totalAttempts: "",
  });

  // Errors state for form validation
  const [errors, setErrors] = useState<FormData>({
    assessmentName: "",
    instructing: "",
    totalTime: "",
    totalMarks: "",
    startDate: "",
    endDate: "",
    totalAttempts: "",
  });

  // Various states for UI logic and data management
  const [screenCount, setScreenCount] = useState(1);
  const [selectedQuestions, setSelectedQuestions] = useState<
    { questionId: number; marks: number }[]
  >([]);
  const [showSelectedOnly, setShowSelectedOnly] = useState<boolean>(false);
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [isRemoveModal, setIsRemoveModal] = useState<boolean>(false);
  const [isSQLCodeModal, setIsSQLCodeModal] = useState<boolean>(false);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const [showModal, setShowModal] = useState(false);
  const [indexNumber, setIndexNumber] = useState<number>(0);
  const [questionSearch, setQuestionSearch] = useState("");
  const [text, setText] = useState("")
  const [searchText, setSearchText] = useState('');
  const [filteredQuestions, setFilteredQuestions] = useState(null);
  const [loader, setloader] = useState(false)
  const [submitModal, setsubmitModal] = useState(false)
  const inProgress = useRef(false)
  const [promptError, setPromptError] = useState(false)

  console.log("filteredQuestions", filteredQuestions)


  const router = useRouter();

  // Hook to fetch question data and manage loading state
  const { data: questionData, isLoading } = useGetAllQuestionUplaodTable();

  // Transform question data for UI
  const data =
    questionData?.map((item) => ({
      question_id: item.question_id,
      question: item.question,
      question_difficulty: item.question_difficulty,
      question_source: item.question_source,
      topics_string: item.topics_string,
      selected: selectedQuestions.includes(item.question_id),
    })) ?? [];

  // const [questions, setQuestions] = useState(questionData)

  // const testQuestion = useRef(!!questionData)

  // console.log("Hello data",testQuestion.current)





  // Selected questions data for rendering selected items
  const selectedData = data.filter((item) => item.selected);


  const { data: filteredData, isLoading: isPromptLoading, isFetched: isPromptFetched } = useGetUserPrompt(searchText);

  console.log("filteredData", filteredData)
  console.log("questionDataww", questionData)
  console.log("isPromptLoading", isPromptLoading)

  useEffect(() => {
    if (filteredData) {
      setFilteredQuestions(filteredData);
    }
  }, [filteredData]);

  const handleTextSubmit = (text) => {
    console.log("text", text)
    setSearchText(text);
  };


  const handleSubmitPropmt = () => {
    if (text.length < 1) {
      setPromptError(true)
    } else {
      setPromptError(false)
      handleTextSubmit(text);
      setIsSQLCodeModal(false);
      setScreenCount(2);
      setloader(true)
      setFilteredQuestions(null)
      inProgress.current = true
    }
  };

  const handleResetButton = () => {
    setFilteredQuestions(null)
    setloader(false)
    setsubmitModal(false)
    setIsSQLCodeModal(false);
  }

  useEffect(() => {
    if (isPromptLoading == false) {
      setloader(false)
    }
  }, [isPromptLoading])


  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name as keyof FormData]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Partial<FormData> = {}; // Use Partial<FormData> to allow partial objects
    if (!formData.assessmentName)
      newErrors.assessmentName = "Assessment name is required.";
    if (!formData.instructing)
      newErrors.instructing = "Instruction is required.";
    if (!formData.totalTime) newErrors.totalTime = "Total time is required.";
    if (!formData.totalMarks) newErrors.totalMarks = "Total marks is required.";

    if (!formData.totalAttempts)
      newErrors.totalAttempts = "Total attempts is required.";
    setErrors(newErrors as FormData); // Explicitly cast as FormData if necessary
    return Object.keys(newErrors).length === 0;
  };

  useEffect(() => {
    if (isPromptFetched && (filteredData?.length == 0 || filteredData == null)) {
      console.log("checking.....", isPromptFetched)
      console.log("checking.....", filteredQuestions)
      setsubmitModal(true)
    }
  }, [isPromptFetched])




  console.log("checking", inProgress.current)
  console.log("checking1", loader)
  console.log("checking2", isPromptFetched)
  // console.log("checking2",inProgress.current)
  // console.log("checking",loader)


  const handleUploadQuestionClick = () => {
    // if (validateForm()) {
    //   setScreenCount(screenCount + 1);
    // }
     setScreenCount(screenCount + 1);
  };

  ///////////////////////////////////////////////////////////////

  const displayedQuestions = showSelectedOnly
    ? data.filter((question) =>
      selectedQuestions.some(
        (selected) => selected.questionId === question.question_id
      )
    )
    : data;

  const handleCheckboxChange = (questionId: number, isSelected: boolean) => {
    if (isSelected) {
      if (!selectedQuestions.some(q => q.questionId === questionId)) {
        setSelectedQuestions(prev => [...prev, { questionId, marks: 0 }]);
      }
    } else {
      setSelectedQuestions(prev => prev.filter(q => q.questionId !== questionId));
    }
  };

  console.log("displayedQuestions", displayedQuestions)

  const handleSelectAllChange = () => {
    // Determine if we are currently selecting all or deselecting all
    const isCurrentlySelectingAll = !selectAll;
    setSelectAll(isCurrentlySelectingAll);

    if (isCurrentlySelectingAll) {
      // Select all questions
      const allSelectedQuestions = data.map((item) => ({
        questionId: item.question_id,
        marks: 0, // default marks, adjust if needed
      }));
      setSelectedQuestions(allSelectedQuestions);
    } else {
      // Clear selection
      setSelectedQuestions([]);
    }
  };

  const handleMarksChange = (questionId: number, marks: number) => {
    setSelectedQuestions((prev) =>
      prev.map((q) => (q.questionId === questionId ? { ...q, marks } : q))
    );
  };
  const toggleView = () => setShowSelectedOnly(!showSelectedOnly);

  const handleCloseModal = () => {
    setIsRemoveModal(false);
  };

  const handleSucessModal = () => {
    setShowCreateModal(false);
    router.push("/admin/assessments");
  };

  const handleEditClick = (question_id) => {
    const questionDetails = data.find((item) => item.question_id === question_id);
    if (questionDetails) {
      setShowModal(true);
      setIndexNumber(data.indexOf(questionDetails));
    }
  };

  const handleDeselectQuestion = (questionId) => {
    setSelectedQuestions((currentSelected) =>
      currentSelected.filter((q) => q.questionId !== questionId)
    );
    setIsRemoveModal(true);
  };
  ////////////////////////////to Post

  const createAssessmentMutation = usePostCreateAssessmentWithQuestion();

  console.log("Selected Questions:", selectedQuestions);

  const handleSearch = (questionStringByUser) => {
    //Call the filter questions API and use the filtered content here
    console.log(questionStringByUser);
    setQuestionSearch(questionStringByUser);
  };

  const handleCreateAssessmentClick = (Marks: number) => {
    // Prepare the data for the request
    const assessmentData = {
      assessment_name: formData.assessmentName,
      instructions: formData.instructing,
      total_time_allowed: parseInt(formData.totalTime, 10) * 60,
      total_marks: parseInt(formData.totalMarks, 10),
      // source: "source information", // Adjust accordingly
      // assessment_evaluation_strategy: 1, // Adjust accordingly
      question_list: selectedQuestions.map((q) => ({
        question_id: q.questionId,
        marks: q.marks,
      })),
    };

    console.log("Assessment Data to Display:", assessmentData);
    createAssessmentMutation.mutate(assessmentData);
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  
  return (
    <main className="w-full  min-h-screen bg-primary ">
      <div className="flex flex-col gap-1  w-full p-1">
        <Heading pgHeading="Create Assessment " />
        {screenCount === 1 && (
          <div className="shadow-lg border-y-8 border-white  mx-auto w-full  rounded-2xl bg-white px-4 py-2  h-[70vh]">
            <form className="flex flex-col justify-between h-full">
              <div className="flex flex-col  gap-2">
                {/* Assessment Name Field */}
                <div className="flex justif-between  w-full gap-2">
                  <div className="flex flex-col gap-1 w-full">
                    <label
                      htmlFor="assessmentName"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Assessment Name
                    </label>
                    <input
                      type="text"
                      id="assessmentName"
                      name="assessmentName"
                      value={formData.assessmentName}
                      onChange={handleInputChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-1"
                      required
                    />
                    {errors.assessmentName && (
                      <p className="text-red-500 text-xs">
                        {errors.assessmentName}
                      </p>
                    )}
                  </div>

                  <div className="flex flex-col gap-1  w-full">
                    <label
                      htmlFor="totalAttempts"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Total Attempts
                    </label>
                    <input
                      type="number"
                      id="totalAttempts"
                      name="totalAttempts"
                      value={formData.totalAttempts}
                      onChange={handleInputChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-1"
                      required
                    />
                    {errors.totalAttempts && (
                      <p className="text-red-500 text-xs">
                        {errors.totalAttempts}
                      </p>
                    )}
                  </div>
                </div>

                {/* Instruction Field */}
                <div className="flex flex-col gap-1">
                  <label
                    htmlFor="instructing"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Instruction
                  </label>
                  <textarea
                    id="instructing"
                    name="instructing"
                    rows={3}
                    value={formData.instructing}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-1"
                    required
                  ></textarea>
                  {errors.instructing && (
                    <p className="text-red-500 text-xs">{errors.instructing}</p>
                  )}
                </div>

                <div className="flex justif-between  w-full gap-2">
                  {/* Total Time Field */}
                  <div className="flex flex-col gap-1 w-full">
                    <label
                      htmlFor="totalTime"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Total Time (in minutes)
                    </label>
                    <div className="flex mt-1 relative">
                      <input
                        type="number"
                        placeholder=""
                        id="totalTime"
                        name="totalTime"
                        value={formData.totalTime}
                        onChange={handleInputChange}
                        className="block w-full border border-gray-300 rounded-md shadow-sm p-1 pl-3 pr-16"
                        required
                        min="1"
                        step="1"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">mins</span>
                      </div>
                    </div>
                    {errors.totalTime && (
                      <p className="text-red-500 text-xs">{errors.totalTime}</p>
                    )}
                  </div>

                  {/* Total Marks Field */}
                  <div className="flex flex-col gap-1  w-full">
                    <label
                      htmlFor="totalMarks"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Total Marks
                    </label>
                    <input
                      type="number"
                      id="totalMarks"
                      name="totalMarks"
                      value={formData.totalMarks}
                      onChange={handleInputChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-1"
                      required
                    />
                    {errors.totalMarks && (
                      <p className="text-red-500 text-xs">
                        {errors.totalMarks}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex justify-end items-end h-full ">
                <button
                  type="button"
                  className="flex gap-2 py-2 px-6 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                  onClick={handleUploadQuestionClick}
                >
                  Upload Question
                </button>
              </div>
            </form>
          </div>
        )}
      </div>

      {screenCount === 2 && (
        <div className="flex flex-col justify-start h-[70vh] w-full gap-4  ">
          <div className="flex justify-between h-[10vh]">
            {/* <SearchBar onSearch={setQuestionSearch}  /> */}

            <label className="autoSaverSwitch relative inline-flex cursor-pointer select-none items-center justify-center w-full m-2">
              <input
                type="checkbox"
                name="autoSaver"
                className="sr-only"
                checked={showSelectedOnly}
                onChange={toggleView}
              />
              <span
                className={`slider mr-3 flex h-[26px] w-[50px] items-center rounded-full p-1 duration-200 ${showSelectedOnly ? "bg-secondary" : "bg-secondary"
                  }`}
              >
                <span
                  className={`dot h-[18px] w-[18px] rounded-full bg-white duration-200 ${showSelectedOnly ? "translate-x-6" : ""
                    }`}
                ></span>
              </span>
              <span className="label flex  items-center text-md font-medium text-black">
                {showSelectedOnly
                  ? "Show All Questions"
                  : "Show Selected Questions"}{" "}
              </span>
            </label>
            <div className="flex flex-row gap-2">
              <button className={`flex justify-center item-center w-[150px] mx-1 py-2 px-4 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] gap-2 h-[40px]`} onClick={handleResetButton}>
                Reset
              </button>
              <button onClick={() => { setIsSQLCodeModal(true) }} className={`h-[40px] flex justify-center item-center w-[${loader ? 200 : 150}px] mx-1 py-2 px-4 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] gap-2`}>
                {loader ? <svg aria-hidden="true" className="inline w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor" />
                  <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill" />
                </svg> : ""}
                <div>
                  AI Prompt
                </div>
              </button>
            </div>

          </div>
          <div className="w-full h-[60vh]  rounded-lg  border shadow-md ">
            <div className="overflow-y-auto  w-full  h-[60vh] bg-white  rounded-lg  border">
              <table className="w-full text-sm text-left rtl:text-right text-gray-800 dark:text-gray-800 h-fit">
                <thead className=" bg-secondary  text-white text-sm sticky top-0 uppercase w-full  z-10">
                  <tr className="w-full">
                    <th className="p-2 text-left ">Question No</th>
                    <th className="p-2 text-left">Question</th>
                    <th className="p-2 text-left">Difficulty Level</th>
                    <th className="p-2 text-left">Question Source</th>
                    <th className="p-2 text-left">Topics</th>
                    <th className="p-2 text-left">
                      {showSelectedOnly ? "Marks" : null}
                    </th>
                    <th className="p-2">
                      {showSelectedOnly ? "Remove" : null}
                    </th>
                    <th className="flex h-full justify-start items-center gap-1 p-2 text-left">
                      <div className="">Select</div>
                      {showSelectedOnly ? null :  <span>
                        <input
                          type="checkbox"
                          checked={selectAll}
                          onChange={handleSelectAllChange}
                        />
                      </span>}
                     
                    </th>
                  </tr>
                </thead>
                <tbody className="relative overflow-x-auto">
                  {isPromptLoading && searchText ? (
                    <tr>
                      <td colSpan={8} className="text-center py-10">
                        <div className="flex justify-center items-center">
                          <div className=" " role="status">
                            <span className="visually-hidden text-xl">Loading...</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    ((filteredQuestions?.length >= 1 || filteredQuestions == !null) ? filteredQuestions : displayedQuestions)
                      ?.filter(item => showSelectedOnly ? selectedQuestions.some(q => q.questionId === item.question_id) : true)
                      .map((item) => (
                        <tr key={item.question_id} className="border-b w-full h-fit">
                          <td className="p-2 px-3 text-left">{item.question_id}</td>
                          <td className="p-2 text-left">{item.question}</td>
                          <td className="p-2 text-left">{item.question_difficulty}</td>
                          <td className="p-2 text-left">{item.question_source}</td>
                          <td className="p-2 text-left">{item.topics_string}</td>
                          <td className="p-2 text-left">
                            {showSelectedOnly &&
                              selectedQuestions.find(q => q.questionId === item.question_id) ? (
                              <input
                                type="number"
                                className="border-[2px] rounded-md"
                                value={selectedQuestions.find(q => q.questionId === item.question_id)?.marks || ""}
                                onChange={e => handleMarksChange(item.question_id, parseInt(e.target.value))}
                                placeholder="Enter marks"
                              />
                            ) : null}
                          </td>
                          <td className="p-2 text-center">
                            {showSelectedOnly ? (
                              <button onClick={() => handleDeselectQuestion(item.question_id)}>
                                <TrashIcon className="md:w-[25px] md:h-[25px] w-[13px] h-[13px] text-red-500" />
                              </button>
                            ) : null}
                          </td>
                          <td className="p-2 text-center">
                            {showSelectedOnly ? (
                              <input
                                disabled
                                type="checkbox"
                                checked={item.selected}
                              />
                            ) : (
                              <input
                                type="checkbox"
                                checked={selectedQuestions.some(q => q.questionId === item.question_id)}
                                onChange={e => handleCheckboxChange(item.question_id, e.target.checked)}
                              />
                            )}
                          </td>
                        </tr>
                      ))
                  )}
                </tbody>
  

              </table>
            </div>
          </div>
          <div className="flex justify-center gap-2 items-end h-full w-full ">
            {screenCount > 1 && (
              <button
                className=" px-6 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] w-fit lg:text-[15px] flex gap-2"
                onClick={() => setScreenCount(screenCount - 1)}
              >
                {loader ? <svg aria-hidden="true" className="inline w-6 h-6 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor" />
                  <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill" />
                </svg> : ""}
                Previous
              </button>
            )}

            {showSelectedOnly && screenCount === 2 ? (
              <div className="flex justify-end items-end gap-2">
                <button
                  type="button"
                  className={`flex  gap-2 py-2 px-6 text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] ${selectedQuestions.length > 0
                    ? "bg-textColor" // Button enabled
                    : "bg-gray-400" // Button disabled
                    }`}
                  disabled={selectedQuestions.length === 0} // Button is disabled if selectedQuestions array is empty
                  onClick={() => {
                    handleCreateAssessmentClick(0);
                    setShowCreateModal(true);
                  }}
                >
                  Create Assessment
                </button>
              </div>
            ) : null}
          </div>

          {showCreateModal && (
            <CreateModal
              modulename="Assessment Created Successfully"
              onClose={handleSucessModal}
            />
          )}
          {isRemoveModal && (
            <DeletedUserModal
              deleteName="question"
              onClose={handleCloseModal}
            />
          )}

          {isSQLCodeModal && (
            <div className="modal flex justify-center items-center  fixed inset-0 bg-black bg-opacity-40  overflow-auto z-10">
              <div className="flex flex-col gap-3 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md w-[400px] h-[300px] ">
                <button className="flex justify-end" onClick={() => { setIsSQLCodeModal(false); }} >
                  <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
                </button>{" "}
                <div className="flex flex-col gap-2 justify-center items-center w-full h-full ">

                  <label
                    className="flex  text-gray-700 text-sm font-bold mb-2 gap-2"
                    htmlFor="textarea"
                    title="Submit Button will post the all question at once"
                  >

                    <InformationCircleIcon className="h-[20px] w-[20px] " /> Enter text:
                  </label>
                  <textarea
                    id="text"
                    name="text"
                    onChange={(e) => setText(e.target.value)}
                    className="resize-none border shadow rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    rows={4}
                    placeholder="Type text..."
                  />
                </div>
                
                <div className="w-full  flex flex-col justify-center items-center p-2 gap-2">
                {promptError&& <span className="text-sm italic text-red-500">Please enter the prompt to continue</span>}
                  <button
                    onClick={handleSubmitPropmt}
                    type="submit"
                    className=" flex gap-1 bg-secondary hover:bg-hoverColor text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    submit


                  </button>
                </div>
              </div>
            </div>
          )}

          {showModal && (
            <EditQuestionModal
              questionDetails={data[indexNumber]} // Ensure this is the correct way to access the details, based on how you manage state
              setShowModal={setShowModal}
            />
          )}
        </div>
      )}
      {submitModal && (
        <SubmitModal modalName={"No Result !"} modalText={"Sorry, the given prompt is not able to fetch any result. Please try a different prompt."} type={2} onClose={() => setsubmitModal(false)} />
      )}
    </main>
  );
}