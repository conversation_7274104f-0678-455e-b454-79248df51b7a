"use client"; // This is a client component
import React, { useState, useEffect } from "react";
import { Disclosure, Transition } from "@headlessui/react";
import { ChevronUpIcon } from "@heroicons/react/20/solid";
import { useGetAllAssessments } from "@/hook/assessments/useGetAllAssessments";
import Heading from "@/components/ui/Heading";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import Nothing from "@/components/ui/Nothing";
 
export default function Page() {
  const [loadingSelect, setLoadingSelect] = useState(false);
  const { data: assessments, isLoading, isError } = useGetAllAssessments(0,1000);

  useEffect(() => {
    if (!isLoading && !isError) {
      console.log("Assessments data:", assessments);
    }
  }, [assessments, isLoading, isError]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  //Check implementation of Retry button
  if (isError) {
    return <div>Error fetching assessments</div>;
  }

  return (
    <main className="w-full  min-h-screen bg-white ">
      <div className="flex flex-col gap-3  w-full p-2">

        {/* <Heading pgHeading="Assign Assessment" />  */}

        <div className="shadow-lg border-y-8 border-white  mx-auto w-full max-w-7xl rounded-2xl mt-10 bg-white p-4  h-[450px]">
          {assessments.length > 0 ?
            <div className="overflow-y-auto p-4 w-full h-full">
              {/*Add Typescript Types*/}
              {assessments?.map((assessment, index: Number) => (
                <Disclosure key={index}>
                  {({ open }) => (
                    <>
                      <Disclosure.Button className=" mb-3 flex w-full justify-between rounded-lg text-white bg-secondary px-4 py-2 text-left text-sm font-medium transition duration-300 hover:bg-textSecondary hover:text-white focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75">
                        <div className="flex items-center">
                          <span className="ml-2">|</span>
                          <span className="ml-2">
                            {assessment.assessment_name}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <ChevronUpIcon
                            className={`${open ? "rotate-180 transform" : ""
                              } h-5 w-5 text-white`}
                          />
                        </div>
                      </Disclosure.Button>
                      <Transition
                        enter="transition duration-150 ease-out"
                        enterFrom="transform scale-95 opacity-0"
                        enterTo="transform scale-100 opacity-100"
                        leave="transition duration-75 ease-out"
                        leaveFrom="transform scale-100 opacity-100"
                        leaveTo="transform scale-95 opacity-0"
                      >
                        <Disclosure.Panel className="px-4 pt-4 pb-4 text-sm text-gray-500">
                          <div className="m-1">
                            Instructions: {assessment.instructions}
                          </div>

                          <div className="flex justify-between">
                            {/* Extra details */}
                            <div className="m-2 flex flex-col">
                              <span className="m-1">
                                Total time allowed:{" "}
                                {(assessment.total_time_allowed/60).toFixed(2)} mins
                              </span>
                              <span className="m-1">
                                Total marks: {assessment.total_marks}
                              </span>
                              <span className="m-1">
                                Source: {assessment.source}
                              </span>
                            </div>

                            {/* Button */}
                            <div className="m-2">
                              <button
                                onClick={() => {
                                  setLoadingSelect(true);
                                  const assessmentId = assessment.assessment_id;
                                  window.location.href = `/admin/assessments/${assessmentId}`;
                                }}
                                className="bg-secondary hover:bg-hoverColor transition duration-150  text-white font-bold py-2 px-4 rounded"
                                disabled={loadingSelect} // Disable the button when loading 'Loading...'
                              >
                                {loadingSelect ? "Loading..." : "Select"}
                              </button>
                            </div>
                          </div>
                        </Disclosure.Panel>
                      </Transition>
                    </>
                  )}
                </Disclosure>
              ))}
            </div> :
            <Nothing 
            title="No Assessments Available" 
            para="There are currently no assessments to display.
            Please check back later, or consider creating new assessments." 
            btnName="Create Assessment" 
            btnIcon={DocumentTextIcon} onClickFunctionForBtn={() => {
              window.location.href = `/admin/assessments/createassessments`;
            }} />}
        </div>
      </div>
    </main>
  );
}

