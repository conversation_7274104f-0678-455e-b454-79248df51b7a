"use client";
import React, { useState } from "react";
import Heading from "@/components/ui/Heading";
import { useGetAllAttemptsGroupByAssessments } from "@/hook/assessment_attempts/useGetAllAttemptsGroupByAssessments";
import { Disclosure, Transition } from "@headlessui/react";
import { ChevronUpIcon } from "@heroicons/react/20/solid";
import Nothing from "@/components/ui/Nothing";
import AssessmentAttemptsTable from "@/components/admin/assessments/AssessmentAttemptsTable";
import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import { ArrowDownTrayIcon } from "@heroicons/react/24/outline";

export default function Page() {
  const arr1 = [
    "Assessment Name",
    "Instructions",
    "Total Marks",
    "Assessment Evaluation Strategy",
    "Total Time Allowed",
    "Assessment Id",
    "Source",
  ];
  const AssessmentProperties = {
    ASSESSMENT_NAME: "assessment_name",
    INSTRUCTIONS: "instructions",
    TOTAL_MARKS: "total_marks",
    ASSESSMENT_EVALUATION_STRATEGY: "assessment_evaluation_strategy",
    TOTAL_TIME_ALLOWED: "total_time_allowed",
    ASSESSMENT_ID: "assessment_id",
    SOURCE: "source",
  };

  const [openAttempts, setOpenAttempts] = useState(false);
  const { data: attemptsGroupedByAssessments } =
    useGetAllAttemptsGroupByAssessments();
  console.log("attemptsGroupedByAssessment", attemptsGroupedByAssessments);

  const renderAssessmentInfo = (assessment_info) => {
    return arr1.map((label, index) => {
      const key = Object.keys(AssessmentProperties)[index];
      const value = assessment_info[AssessmentProperties[key]];

      return (
        <div className="m-1 flex flex-col" key={index}>
          {label}: {index === 4 ? value / 60 : value}
        </div>
      );
    });
  };

  const handleDownload = async () => {
    const dataToExport = [];

    Object.keys(attemptsGroupedByAssessments).forEach((assessmentKey) => {
      const { assessment_info, attempts } = attemptsGroupedByAssessments[assessmentKey];

      attempts.forEach((attempt) => {
        dataToExport.push({
         "User Assessment Attempt Id" :attempt.user_assessment_attempt_id,
          "User ID": attempt.user_id,
          "User Name": attempt.user_full_name,
          "Email": attempt.email,
          "User Assessment ID": attempt.user_assessment_id,
          "Assessment Name": assessment_info.assessment_name,
          "Total Marks": assessment_info.total_marks,
          "Attempt Total Time": attempt.attempt_total_time,
          "Attempt Total": attempt.attempt_total,
          "Evaluation": attempt.attempt_evaluation,
          "Start Date": new Date(attempt.attempt_start_date).toLocaleDateString(),
          "End Date": new Date(attempt.attempt_end_date).toLocaleDateString(),
          "Start Time": new Date(attempt.attempt_start_date).toLocaleTimeString(),
          "End Time": new Date(attempt.attempt_end_date).toLocaleTimeString(),
        });
      });
    });

    // Create workbook and worksheet, and add columns
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("User Attempts");

    // Define columns
    worksheet.columns = [
      
      { header: " Attempt Id", key: "User Assessment Attempt Id", width: 20 },
      { header: "User ID", key: "User ID", width: 15 },
      { header: "User Name", key: "User Name", width: 25 },
      { header: "Email", key: "Email", width: 25 },
      { header: "User Assessment ID", key: "User Assessment ID", width: 20 },
      { header: "Assessment Name", key: "Assessment Name", width: 25 },
      { header: "Total Marks", key: "Total Marks", width: 15 },
      { header: "Attempt Total Time", key: "Attempt Total Time", width: 15 },
      { header: "Attempt Total", key: "Attempt Total", width: 15 },
      { header: "Evaluation", key: "Evaluation", width: 15 },
      { header: "Start Date", key: "Start Date", width: 15 },
      { header: "End Date", key: "End Date", width: 15 },
      { header: "Start Time", key: "Start Time", width: 15 },
      { header: "End Time", key: "End Time", width: 15 },
    ];

    // Add rows to the worksheet
    dataToExport.forEach((data) => worksheet.addRow(data));
    console.log("Data to export:", dataToExport);

    // Apply header styles
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.font = {
        bold: true,
        size: 14,
        color: { argb: "FF000000" }, // Black text
      };
      cell.alignment = {
        horizontal: "center",
      };
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "ADD8E6" }, // blue background
      };
    });

    // Apply data rows styles
    worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber === 1) return; // Skip header row

      row.eachCell((cell) => {
        cell.font = {
          size: 12,
          // color: { argb: 'FF000000' } // Black text
        };
        cell.alignment = {
          horizontal: "center",
        };
        // cell.fill = {
        //   // type: 'pattern',
        //   // pattern: 'solid',
        //   // fgColor: { argb: 'FFFFFF' } // white bg
        // };
      });
    });

    // Save the file
    const buffer = await workbook.xlsx.writeBuffer();
    saveAs(
      new Blob([buffer], { type: "application/octet-stream" }),
      "UserAttempts.xlsx"
    );
  };

  return (
    <main className="w-full flex h-[90vh] bg-primary flex-col gap-3 p-3 overflow-auto">
      <div className="flex flex-col justify-start h-[5vh]">
        <Heading pgHeading="All Assessment Attempt" />
      </div>
      <div className="p-4 h-[75vh]">
        <div className="shadow-2xl border-y-8 border-white mx-auto w-full max-w-6xl lg:max-w-[1450px] rounded-2xl bg-white lg:p-4 h-full">
          {attemptsGroupedByAssessments ? (
            <div className="overflow-y-auto p-4 w-full h-full ">
              {Object.keys(attemptsGroupedByAssessments)?.map(
                (assessmentKey, index) => (
                  <Disclosure key={index}>
                    {({ open }) => (
                      <>
                        <Disclosure.Button className=" mb-3 flex w-full justify-between rounded-lg text-white bg-secondary px-4 py-2 text-left text-sm font-medium transition duration-300 hover:bg-textSecondary hover:text-white focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75">
                          <div className="flex items-center">
                            <span className="ml-2">|</span>
                            <span className="ml-2">{assessmentKey}</span>
                          </div>
                          <div className="flex items-center">
                            <ChevronUpIcon
                              className={`${
                                open ? "rotate-180 transform" : ""
                              } h-5 w-5 text-white`}
                            />
                          </div>
                        </Disclosure.Button>
                        <Transition
                          enter="transition duration-150 ease-out"
                          enterFrom="transform scale-95 opacity-0"
                          enterTo="transform scale-100 opacity-100"
                          leave="transition duration-75 ease-out"
                          leaveFrom="transform scale-100 opacity-100"
                          leaveTo="transform scale-95 opacity-0"
                        >
                          <Disclosure.Panel className="px-4 pt-4 pb-4 text-sm text-gray-500 shadow-xl">
                            <div className="flex flex-col  md:flex-row justify-between">
                              <div className="flex flex-col sm:flex-row sm:gap-20 lg:gap-40">
                                <div className="flex flex-col gap-1">
                                  {renderAssessmentInfo(
                                    attemptsGroupedByAssessments[assessmentKey]
                                      .assessment_info
                                  )}
                                </div>
                              </div>
                              <div className="flex flex-col">
                                <div className="m-2 w-full">
                                  <button
                                    onClick={() => {
                                      setOpenAttempts(!openAttempts);
                                    }}
                                    className="bg-secondary transition duration-150  text-white font-bold py-2 px-4 rounded"
                                  >
                                    {openAttempts
                                      ? "Hide Attempts"
                                      : "View Attempts"}
                                  </button>
                                </div>
                                <button
                                  onClick={handleDownload}
                                  className="flex h-fit gap-2 py-2 px-4 m-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                                >
                                  Download<ArrowDownTrayIcon className="w-[20px] h-[20px]" />
                                </button>
                              </div>
                            </div>
                            {openAttempts && (
                              <div className="flex flex-row overflow-x-auto gap-8 p-1">
                                <AssessmentAttemptsTable
                                  attempts={
                                    attemptsGroupedByAssessments[assessmentKey]
                                      .attempts
                                  }
                                />
                              </div>
                            )}
                          </Disclosure.Panel>
                        </Transition>
                      </>
                    )}
                  </Disclosure>
                )
              )}
            </div>
          ) : (
            <Nothing
              title="No Data Available"
              para="There are currently no assessments to display. Please check back later"
            />
          )}
        </div>
      </div>
    </main>
  );
}
