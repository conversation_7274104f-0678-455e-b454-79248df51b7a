"use client"
import React, { useState, useEffect, useRef } from "react";
import SearchBar from "@/components/admin/SearchBar";
import Heading from "@/components/ui/Heading";
import { useGetAllUserExt } from "@/hook/admin/useGetAllUserExt";
import { useAddBulkUser } from "@/hook/admin/useAddBulkUser"
import { useGetSearchedUsers } from "@/hook/admin/useGetSearchedUsers";
import AddUserAttemptsTable from "@/components/admin/userGroup/AddUserAttemptsTable";

interface FileInputRefType {
  click: () => void;
  // Add other properties and methods if necessary
}

const Page = () => {
  const [search, setSearch] = useState("Search");
 
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [file, setFile] = useState<File>()
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
   
  const AddingBulkUsers = useAddBulkUser(file)
  const { data: searchedUsers } = useGetSearchedUsers(search);

  const { data: userExtValues} = useGetAllUserExt();

  const handleFileUpload = async (event: any) => {
    const tempUploadedFile = event.target.files?.[0]; // Access the uploaded file
    setFile(tempUploadedFile)

    if (tempUploadedFile) {
      try {
        await AddingBulkUsers.mutate()
      } catch (error) {
        console.log("Error occured while uploading the file")
      }
    } else {
      console.log("No file found")
    }
    console.log("Uploaded file:", typeof (file));
    // You can perform further processing of the uploaded file here
  };

  console.log("search11",search)


//add a condition here to check array1, array2 list contains value

  
  function findCommonObjects(array1, array2) {
    // Check if array2 is not an array or undefined, return array1

    if (!Array.isArray(array1) || !Array.isArray(array2)) {
      // If not, return an empty array since there are no common objects
      return [];
    }

    if (!Array.isArray(array2) || typeof array2 !== 'object') {
        return array1;
    }

    const commonObjects = [];

    // Iterate through the first array
    for (let obj1 of array1) {
        // Check if the object exists in the second array
        const found = array2.find(obj2 => JSON.stringify(obj1) === JSON.stringify(obj2));
        if (found) {
            commonObjects.push(obj1);
        }
    }

    // If no common objects found, return all objects from array1
    if (commonObjects.length === 0) {
        return array1;
    }

    return commonObjects;
}
  
  const handleSearch = (searchString: string) => {
    //Call the search users API here and use the searched content here
    console.log(searchString);
    setSearch(searchString);
  };
  
  const handleAddUser = () => {
    setShowAddUserModal(true);
  };
  
  const onCloseAddUserModal = () => {
    setShowAddUserModal(false); // Close AddContentModal
   
  };
  const onSubmitAddUserModal = () => {
    setShowAddUserModal(false); // Close AddContentModal
    setShowSubmitModal(true);
  };
  
  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
  };

  const userFilteredValues = findCommonObjects(userExtValues, searchedUsers)


  return (
    <div className="w-full flex h-[90vh] bg-primary flex-col gap-3 p-3 overflow-auto">
  
      <div className='flex flex-col justify-start h-[5vh]'>
      <Heading pgHeading="Users Attempts" />
      </div>

      <div className="flex flex-col md:flex-row gap-2 w-full justify-between items-end h-[10vh]">
      <div> <SearchBar onSearch={handleSearch} /></div>
      </div>
      {/* {search.length > 0 && searchedUsers? 
        <AddRemoveUserToGroup userValues={searchedUsers} />
      :
        <AddRemoveUserToGroup userValues={userExtValues} />
      } */}
      <div className="h-[75vh]">
      <AddUserAttemptsTable userValuesTable={userFilteredValues} />
      </div>
    </div>
  );
};

export default Page;