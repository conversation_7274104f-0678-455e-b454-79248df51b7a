"use client"
import Heading from '@/components/ui/Heading';
import ShowUserAttemptsTable from '@/components/admin/userGroup/ShowUserAttemptsTable';
import { useSearchParams } from 'next/navigation';
import React, { useState } from 'react'

export default function Page() {
  const searchParams = useSearchParams();
  const user_ext_id = searchParams.get("userId");
  console.log(user_ext_id)
  const [userId, setUserId] = useState(parseInt(user_ext_id || ''));

  
  return (
    <div className="flex flex-col flex-grow p-3 gap-2  h-[90vh] overflow-auto ">
      <div className='flex justify-between'>
      <Heading pgHeading="Assessment Attempts For A User" />
 
      </div>
  
 
    <ShowUserAttemptsTable userId={user_ext_id}/>

    </div>
   
);
}
