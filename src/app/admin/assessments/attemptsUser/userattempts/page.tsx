"use client"
import Heading from '@/components/ui/Heading';
import PageForAssessmentUserShow from '@/components/admin/assessments/PageForAssessmentUserShow';
import { useGetAllAssessments } from '@/hook/assessments/useGetAllAssessments';
import { useParams, useSearchParams } from 'next/navigation';
import React, { useState } from 'react'

export default function Page() {

  const searchParams = useSearchParams();
  const user_ext_id = searchParams.get("userId");
  console.log(user_ext_id)
  const [userId, setUserId] = useState(parseInt(user_ext_id || ''));

  const offset = 0;
  const limit = 1000;

  const { data: assessments, isLoading, error } = useGetAllAssessments(offset, limit);
  console.log(assessments); 

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>An error occurred:</div>;
  
  return (
    <div className="flex flex-col flex-grow p-2 gap-2 overflow-auto  h-[90vh]">
    <Heading pgHeading="User Attempts For An Assessment" />

      {assessments && assessments.map(assessment => (
    <div key={assessment.assessment_id}>    
      <PageForAssessmentUserShow userId={user_ext_id} assessmentId={assessment.assessment_id}/> 
    </div>
     ))}
    </div>
  )
}



