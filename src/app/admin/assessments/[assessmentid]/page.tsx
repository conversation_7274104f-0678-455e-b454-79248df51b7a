"use client"; // This is a client component
import React, { useState, useEffect, Fragmentm, useRef } from 'react';
import { useParams } from 'next/navigation'
import { useGetUsersForAssessment } from "@/hook/assessments/useGetUsersForAssessment";
import { useGetAllUserIds } from "@/hook/admin/useGetAllUserIds";
import { useGetAllUserExt } from "@/hook/admin/useGetAllUserExt";
import FormComponent from "@/components/admin/assessments/FormComponent"


export default function Page() {

    const [activeButton, setActiveButton] = useState(1);
    const formArray = [1, 2, 3];
    const [formNoValue, setFormNoValue] = useState<number>(formArray[0]);

    const setFormValueAndUpdateActive = (value: any) => {
        setFormValue(value);
        setActiveButton(value);
        setFormNoValue(1)
        //   console.log(formNoValue)
    };

    const { data: allUserExtData, isLoading: allUserExtLoading, isError: allUserExtError } = useGetAllUserExt();

    useEffect(() => {
        if (!allUserExtLoading && !allUserExtError) {

        }
    }, [allUserExtData, allUserExtLoading, allUserExtError]);

    const params = useParams()
    const assessmentIdValue: number = parseInt(params.assessmentid, 10);
    const usersIds: number[] = []
    const [formValue, setFormValue] = useState(1);

    const { data: userAssessments, isLoading: assessmentLoading, isError: assessmentError } = useGetUsersForAssessment(assessmentIdValue);
    const { data: otherData, isLoading: otherLoading, isError: otherError } = useGetAllUserIds();

    useEffect(() => {
        if (!otherLoading && !otherError && !assessmentLoading && !assessmentError) {
            // Your logic here when both data sets are loaded successfully
        }
    }, [otherData, otherLoading, otherError, userAssessments, assessmentLoading, assessmentError]);

    if (otherLoading || assessmentLoading) {
        return <div>Loading...</div>;
    }

    if (otherError) {
        return <div>Error fetching other data</div>;
    }

    if (assessmentError) {
        return <div>Error fetching assessments</div>;
    }

    // Your main component JSX here    
    const userIdValue: { userId: number; userAssessmentId: number }[] = userAssessments?.map(entry => ({
        userId: entry.user_id,
        userAssessmentId: entry.user_assessment_id
    }));      

    const mergedArray = userAssessments?.map(item1 => {
        const matchingItem = allUserExtData?.find(item2 => item2.user_id === item1.user_id);

        if (matchingItem) {
            // Merge the properties from both arrays
            return { ...item1, ...matchingItem };
        }

        // If there is no match, return the original item from the first array
        return item1;
    });

    function convertIsoToCustomFormat(isoString) {
        // Parse the ISO 8601 string
        const date = new Date(isoString);

        // Get the components of the date
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based, so we add 1
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        // Concatenate the components in the desired format
        const customFormat = `${year}/${month}/${day} ${hours}:${minutes}`;

        return customFormat;
    }

    return (
        <main className="overflow-y-auto mb-4 pb-4 w-full flex min-h-screen bg-primary flex-col items-center" style={{ height: '100vh' }}>
            <div className="mt-10 w-full lg:px-4 lg:pt-10 pb-4 mb-3 lg:flex lg:justify-between lg:relative">
                <div className="shadow-lg border-y-8 border-white mx-auto mt-10 sm:mt-4 lg:mt-0 w-10/12 lg:w-9/12 lg:max-w-7xl rounded-2xl bg-white p-4 mb-4 h-12 sm:h-60 lg:h-96" style={{ height: '44rem' }}>


                    <div className="">
                        <div className="">
                            <h2 className="mb-3 text-lg font-medium leading-tight text-textPrimary">
                                User Details
                            </h2>
                        </div>
                        <div className="overflow-y-auto border-y-8 border-white  mx-auto w-auto max-w-7xl rounded-2xl bg-white p-4 mb-4 h-96" style={{ height: '40rem' }}>
                            <table className="w-full text-sm text-left rtl:text-right text-gray-800 dark:text-gray-800 h-fit">
                                <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                    <tr>
                                        <th scope="col" className="px-6 py-3">
                                            User ID
                                        </th>
                                        <th scope="col" className="px-6 py-3">
                                            Name
                                        </th>
                                        <th scope="col" className="px-6 py-3">
                                            Assessment ID
                                        </th>
                                        <th scope="col" className="px-6 py-3">
                                            Start Date
                                        </th>
                                        <th scope="col" className="px-6 py-3">
                                            End Date
                                        </th>
                                        <th scope="col" className="px-6 py-3">
                                            User Score
                                        </th>
                                        <th scope="col" className="px-6 py-3">
                                            Total Time Allowed
                                        </th>
                                        <th scope="col" className="px-6 py-3">
                                            Total Attempts
                                        </th>
                                        <th scope="col" className="px-6 py-3">
                                            Max Attempts
                                        </th>
                                        <th scope="col" className="px-6 py-3">
                                            Last Attempt Date
                                        </th>
                                    </tr>
                                </thead>



                                <tbody className="relative overflow-x-auto">
                                    {mergedArray?.map((userAssessment, index) => (
                                        <tr key={index} className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 h-fit">
                                            <th scope="row" className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                {userAssessment.user_id}

                                            </th>
                                            <td className="px-6 py-4">
                                                {userAssessment.user_full_name}
                                            </td>
                                            <td className="px-6 py-4">
                                                {userAssessment.assessment_id}

                                            </td>
                                            <td className="px-6 py-4">
                                                {convertIsoToCustomFormat(userAssessment.start_date)}
                                            </td>
                                            <td className="px-6 py-4">
                                                {convertIsoToCustomFormat(userAssessment.end_date)}
                                            </td>
                                            <td className="px-6 py-4">
                                                {userAssessment.user_score || 'N/A'}
                                            </td>
                                            <td className="px-6 py-4">
                                                {(userAssessment.total_time/60).toFixed(2) || 'N/A'} mins
                                            </td>
                                            <td className="px-6 py-4">
                                                {userAssessment.total_attempts}
                                            </td>
                                            <td className="px-6 py-4">
                                                {userAssessment.max_attempts}
                                            </td>
                                            <td className="px-6 py-4">
                                                {convertIsoToCustomFormat(userAssessment.last_attempt_date)}
                                            </td>

                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div className="shadow-lg justify-between border-y-8 border-white mt-20 lg:mt-0 mx-auto w-10/12 lg:w-6/12 max-w-7xl rounded-2xl bg-white p-4 mb-4 h-96 lg:ml-6" style={{ height: '44rem' }}>
                    <div>
                        <div className='mt-4 gap-3 flex justify-center items-center'>

                            <button
                                onClick={() => setFormValueAndUpdateActive(1)}
                                className={`px-3 py-2 text-lg rounded-md w-full text-white ${activeButton === 1 ? 'bg-cyan-800' : 'bg-secondary'
                                    } hover:bg-hoverColor`}
                            >
                                Add User
                            </button>
                            <button
                                onClick={() => setFormValueAndUpdateActive(2)}
                                className={`px-3 py-2 text-lg rounded-md w-full text-white ${activeButton === 2 ? 'bg-cyan-800' : 'bg-secondary'
                                    } hover:bg-hoverColor`}
                            >
                                Update User
                            </button>

                        </div>
                    </div>

                    <div>


                        <FormComponent assessmentIdValue={assessmentIdValue} userIdValue={userIdValue} formNoValue={formNoValue} formValue={formValue} allUserExtData={allUserExtData} />

                    </div>
                </div>
            </div>
            <div className="mt-2 w-full px-4 mb-3 bg-slate-500">

            </div>
        </main>
    )

}
