"use client";
import Button from "@/components/ui/Button";
import Heading from "@/components/ui/Heading";
import AttemptAssessmentTable from "@/components/admin/assessments/AttemptAssessmentTable";
import { useGetUnevaluatedAttempts } from "@/hook/assessment_attempts/useGetUnevaluatedAttempts";

import Link from "next/link";
import React from "react";

export default function Page() {
  const { data: assessments, isLoading, isError } = useGetUnevaluatedAttempts();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  //Check implementation of Retry button
  if (isError) {
    return <div>Error fetching assessments</div>;
  }

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col gap-3 p-3">
      <Heading pgHeading="Assessment List" />
      <div className="flex justify-end w-full">
        <Link href={"/admin/assessments/createassessments"}>
          <Button btnName="Add Assessment" />
        </Link>
      </div>
      <div>
        <AttemptAssessmentTable AssessmentData={assessments} />
      </div>
    </main>
  );
}
