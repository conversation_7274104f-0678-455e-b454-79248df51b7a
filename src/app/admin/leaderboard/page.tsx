"use client"
import Button001 from '@/components/ui/Button001'
import Heading from '@/components/ui/Heading'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import { motion } from "framer-motion"
import { useRouter } from 'next/navigation'
import JobCard from '@/components/dashboard/roles/JobCard'
import Nothing from '@/components/ui/Nothing'

const page = () => {
  const [state, setstate] = useState(false)
  const router = useRouter();
  const [files, setFiles] = useState([]); // State to store multiple files
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [uploadStatus, setUploadStatus] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedJobIds, setSelectedJobIds] = useState<number[]>([]);  // Track selected job IDs
  const [values, setValues] = useState<Pagination>({
    limit: 0,
    offset: 6
  })

  const changeScreen = () => {
    setstate(true)
  }

  const handleroleselection = (jobId: number) => {
    setSelectedJobIds((prevSelectedIds) => {
      // Check if the job is already selected
      if (prevSelectedIds.includes(jobId)) {
        // Deselect if already selected
        return prevSelectedIds.filter((id) => id !== jobId);
      }

      // If not selected, check if there are already 3 roles selected
      if (prevSelectedIds.length >= 3) {
        // If already 3 roles are selected, do nothing
        return prevSelectedIds;
      }

      // Add to selection if not already selected and less than 3 roles are selected
      return [...prevSelectedIds, jobId];
    });
  };

  const rolesData: Job[] = [
    {
      job_id: 1,
      job_title: "Junior Data Associate",
      job_description: "Assist in developing software applications, analyzing data, and building tools to improve business processes."
    },
    {
      job_id: 2,
      job_title: "AI Data Scientist",
      job_description: "Lead the development of AI-based solutions, manage data pipelines, and oversee the entire product lifecycle from planning to execution."
    }
  ];


  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  console.log("uploadedFiles", uploadedFiles)

  const colors: string[] = [
    'bg-blue-200',
    'bg-green-200',
    'bg-yellow-200',
    'bg-pink-200',
    'bg-purple-200',
  ];

  useEffect(() => {
    setUploadStatus(`${files.length} file(s) selected.`);
  }, [files]);

  useEffect(() => {
    const storedFiles = sessionStorage.getItem("uploadedFiles");
    if (storedFiles) {
      setUploadedFiles(JSON.parse(storedFiles)); // Load metadata from sessionStorage
    }
  }, []);

  return (
    <main className="relative w-full flex flex-col items-end justify-end p-4 overflow-auto h-fit">
      <div className="w-full overflow-auto">
        <div className="p-3 w-full flex flex-col gap-4 justify-between">
          <div className='px-4 w-full flex justify-between'>
            <Heading pgHeading="Leader Board" />
          
          </div>

          {state
            ?
            <div className="flex flex-wrap gap-1 justify-center bg-white w-full rounded-2xl p-4 h-[75vh] overflow-y-auto">
              <iframe
                src="http://52.172.41.51:8050/"
                style={{
                  width: '100%',
                  height: '70vh',
                  border: 'none',
                  borderRadius: '10px',
                  overflow: 'hidden',
                }}
                title="Embedded Assessment"
              ></iframe>
            </div>
            :
            <div className="w-full h-full">
              <div className="grid grid-cols-1 sm:p-2 md:grid-cols-2 lg:grid-cols-3 flex-wrap gap-5 justify-start bg-white w-full rounded-2xl h-[75vh] overflow-y-auto">
                {(!rolesData || rolesData.length === 0) ? (
                  <div className="w-full justify-center items-center h-full grid grid-cols-5">
                    <Nothing
                      title="No Job titles Available"
                      para="There are currently no job titles to display. Please check back later."
                    />
                  </div>
                ) : (
                  rolesData?.map((data, index) => (
                    <JobCard
                      key={data.job_id} // Use job_id here
                      job={data}
                      index={index}
                      colors={colors}
                      selected={selectedJobIds.includes(data.job_id)} // Use job_id here
                      onClick={handleroleselection}
                      selectFn={() => changeScreen()}
                    />
                  ))
                )}
              </div>
            </div>
          }
        </div>
      </div>
    </main>
  )
}

export default page