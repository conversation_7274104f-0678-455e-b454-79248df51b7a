"use client";
import React, { useState } from "react";
import Heading from "@/components/ui/Heading";
import AddAssessment from "@/components/admin/module/AddAssessment";

import AddContent from "@/components/admin/module/AddContent";
import CreateContentAssessmentModal from "@/components/admin/module/CreateContentAssessmentModal";
import { useAddModule } from "@/hook/admin/module/useAddModule";
import { AddModule } from "@/types/LMSTypes";

export default function Page() {
  const [screenCount, setScreenCount] = useState(1)
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [moduleNumber, setModuleNumber] =  useState<{ module_id?: number | null }>({});
  const [moduleName, setModuleName] = useState("");
  const [heading, setHeading] = useState("");
  const [moduleDescription, setModuleDescription] = useState("");
  const [errors, setErrors] = useState();

 

  const myModule: AddModule = {
    module_config: {
      from_attributes: true
    },
    module_name: moduleName,
    module_headline: heading,
    module_description: moduleDescription
  };

  const validateForm = () => {

    const newErrors = {};
    // Access state directly or via moduleInfo object based on your state management
    if (!moduleName) newErrors.moduleName = 'Module Name is required';
    if (!heading) newErrors.heading = 'Module Heading is required';
    if (!moduleDescription) newErrors.moduleDescription = 'Module Description is required';
    return newErrors;
  };

  const handleModuleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setModuleName(e.target.value);
  };

  const handleHeadingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHeading(e.target.value);
  };

  const handleModuleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setModuleDescription(e.target.value);
  };

  const addingModule=  useAddModule(myModule);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formErrors = validateForm();
    setErrors(formErrors);
  
    if (Object.keys(formErrors).length === 0) {
      try {
        const createdModule = await addingModule.mutateAsync(); // Assuming mutateAsync() returns a promise with the created module
        console.log("createdModule is", createdModule);
        
        // Update the state with the new module_id
        setModuleNumber({ module_id: createdModule.module_id });
  
        // Navigate to the next screen
        setScreenCount(2);
      } catch (error) {
        // Handle any errors
        console.error("Module creation failed:", error);
      }
    }
  };
  
  const handlePreviousScreen = () => {
    setScreenCount((prevScreenCount) => Math.max(prevScreenCount - 1, 1));
  };

  const handleNextScreen = () => {
    setScreenCount((prevScreenCount) => Math.max(prevScreenCount + 1, 1));
  };

  return (
    <main className="w-full flex h-[88vh] bg-white flex-col p-4 rounded-xl">
      <div className="flex flex-col justify-start gap-1 overflow-y-auto bg-white mx-auto w-full  rounded-2xl p-4 h-[80vh]">
   
        {screenCount === 1 && <>
           <Heading pgHeading="Create Module" />
           <div className="">
            <form onSubmit={handleSubmit} className="flex flex-col justify-between gap-3 p-3 h-full  ">
          
        <div className="flex justify-start w-full  h-full gap-3">
          <div className="flex flex-col  w-full gap-2">
            <div className="flex flex-col gap-1">
              <label
                htmlFor="moduleName"
                className="block text-sm font-medium text-gray-700 "
              >
                Module Name
              </label>
              <input
                type="text"
                id="moduleName"
                name="moduleName"
                value={moduleName}
                onChange={handleModuleNameChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black"
              />
              {errors?.moduleName && (
              <p className="text-red-500 text-xs italic">
                {errors.moduleName}
              </p>
              )}
            </div>
          </div>
          <div className="flex flex-col h-full w-full gap-2">
            <div className="flex flex-col gap-1">
              <label
                htmlFor="heading"
                className="block text-sm font-medium text-gray-700"
              >
                Module Heading
              </label>
              <input
                type="text"
                id="heading"
                name="heading"
               
                value={heading}
                onChange={handleHeadingChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black"
              />
              {errors?.heading && (
              <p className="text-red-500 text-xs italic">
                {errors.heading}
              </p>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <label
            htmlFor="moduleDescription"
            className="block text-sm font-medium text-gray-700"
          >
            Module Description
          </label>
          <textarea
            id="moduleDescription"
            name="moduleDescription"
            value={moduleDescription}
        
            onChange={handleModuleDescriptionChange}
          
            rows={5}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black"
          ></textarea>
          {errors?.moduleDescription && (
            <p className="text-red-500 text-xs italic">
              {errors.moduleDescription}
            </p>
           )}
        </div>
        
        {screenCount === 1 && (
          
          <div className="flex justify-center items-end  my-4 ">
            <button
              type="submit"
              className="px-6 py-2 flex gap-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
            >
              Save & Next
            </button>
          </div>
        
      )}
      </form>
      </div>
      </>
   
        }

        {screenCount === 2 && <>
        <Heading pgHeading="Add Assessment In Module" />
        {/* <AddRemoveAssessmentFromModule moduleIDProp={moduleNumber?.module_id} onPreviousClick={handlePreviousScreen} onNextClick={handleNextScreen}/> */}
        <AddAssessment moduleIdUpcoming={moduleNumber?.module_id} onPreviousClick={handlePreviousScreen} onNextClick={handleNextScreen}/>
        </>}

        {screenCount === 3 && <>
        <Heading pgHeading="Add Content In Module" />
        <AddContent moduleIdUpcoming={moduleNumber?.module_id} onPreviousClick={handlePreviousScreen} onNextClick={handleNextScreen}/>
        </>} 

       
        {showCreateModal && (
          <CreateContentAssessmentModal
            onClose={() => {
              setShowCreateModal(false);
            }}
          />
        )}
      </div>
    </main>

  );
}
