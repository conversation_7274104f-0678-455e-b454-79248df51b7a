"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useGetModuleById } from "@/hook/admin/module/useGetModuleById";
import AddRemoveAssessmentFromModule from "@/components/admin/module/AddRemoveAssessmentFromModule";
import AddRemoveContentFromModule from "@/components/admin/module/AddRemoveContentFromModule";
import { useUpdateModule } from "@/hook/admin/module/useUpdateModule";
import Link from "next/link";
import Heading from "@/components/ui/Heading";
import AssessmentInModule from "@/components/admin/module/AssessmentInmodule";
import ContentInModule from "@/components/admin/module/ContentInModule";

const initialModuleInfo = {
  moduleName: "",
  moduleHeading: "",
  moduleDescription: "",
};

export default function Page() {

  const params = useParams();
  console.log("params for module" + params);
  const module_id: number = parseInt(params.moduleId, 10);
  const [action, setAction] = useState();
  console.log("moduleId " + module_id);
  const { data: moduleData, isError, isLoading } = useGetModuleById(module_id);
  const router = useRouter();

  const [screenCount, setScreenCount] = useState(1);

  const [moduleInfo, setModuleInfo] = useState(initialModuleInfo);
  const [errors, setErrors] = useState({});
  ////display the module data in field//////////
  console.log({ moduleData, isError, isLoading });

  useEffect(() => {
    if (moduleData) {
      // Update all module info state at once
      setModuleInfo({
        moduleName: moduleData.module_name || "",
        moduleHeading: moduleData.module_headline || "",
        moduleDescription: moduleData.module_description || "",
      });
      const searchParams = new URLSearchParams(window.location.search);
      const actionParam = searchParams.get("action");
      if (actionParam) {
        setAction(actionParam);
      }
    }
  }, [moduleData]);

  const { mutate: updateModule, isSuccess, isModuleError } = useUpdateModule();

  const validateForm = () => {
    const newErrors = {};
    // Access state directly or via moduleInfo object based on your state management
    if (!moduleInfo.moduleName.trim()) newErrors.moduleName = 'Module Name is required';
    if (!moduleInfo.moduleHeading.trim()) newErrors.moduleHeading = 'Module Heading is required';
    if (!moduleInfo.moduleDescription.trim()) newErrors.moduleDescription = 'Module Description is required';
    return newErrors;
  };


  const handleSubmit = (event) => {
    event.preventDefault();
    const formErrors = validateForm();
    setErrors(formErrors);

    if (Object.keys(formErrors).length === 0) {
      const updateData = {
        module_name: moduleInfo.moduleName,
        module_headline: moduleInfo.moduleHeading,
        module_description: moduleInfo.moduleDescription,
        module_id: module_id,
      };

      updateModule(updateData, {
        onSuccess: () => {
          setScreenCount(2); // Transition to the next screen on successful form submission
        },
        onError: () => {
          console.log("Error updating module. Please try again.");
        },
      });
    } else {
      console.log("Form is invalid. Display errors.");
    }
  };


  // Component rendering logic...
  if (isLoading) return <div>Loading...</div>;
  if (isError || !moduleData || isModuleError) return <div>Error loading content details.</div>;


  // Input change handler for simplifying input state updates
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setModuleInfo((prevInfo) => ({
      ...prevInfo,
      [name]: value,
    }));
  };



  if (action === 'edit') {
    return (
      <main className="w-full flex h-full bg-primary flex-col p-4">
        <div className="flex flex-col justify-start gap-5 overflow-y-auto mx-auto w-full  rounded-2xl p-3 h-[90vh]">
          {screenCount === 1 && <>
            <Heading pgHeading="Update Module" />
            <form onSubmit={handleSubmit} className="flex flex-col justify-start gap-3 p-3 h-[70vh]  w-full ">
              <div className="flex justify-between  w-full gap-5 h-full ">
                <div className="flex flex-col gap-1 w-full ">
                  <label
                    htmlFor="ModuleName"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Module Name
                  </label>
                  <input
                    aria-label="ModuleName"
                    type="text"
                    id="ModuleName"
                    name="moduleName" // Ensure this matches the key in moduleInfo
                    value={moduleInfo.moduleName} // Updated to use moduleInfo
                    onChange={handleInputChange} // Updated to use the new handler
                    className="mt-1 block w-full border text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
                  ></input>
                  {errors.moduleName && (
                    <p className="text-red-500 text-xs italic">
                      {errors.moduleName}
                    </p>
                  )}
                </div>
                <div className="flex flex-col gap-1 w-full">
                  <label
                    htmlFor="ModuleHeading"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Module Heading
                  </label>
                  <input
                    aria-label="ModuleHeading"
                    id="ModuleHeading"
                    name="moduleHeading" // Ensure this matches the key in moduleInfo
                    value={moduleInfo.moduleHeading} // Updated to use moduleInfo
                    onChange={handleInputChange} // Updated to use the new handler
                    className="mt-1 block w-full border text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
                  ></input>
                  {errors.moduleHeading && (
                    <p className="text-red-500 text-xs italic">
                      {errors.moduleHeading}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex flex-col gap-1 h-full">
                <label
                  htmlFor="ModuleDescription"
                  className="block text-sm font-medium text-gray-700"
                >
                  Module Description
                </label>
                <textarea
                  aria-label="ModuleDescription"
                  id="ModuleDescription"
                  name="moduleDescription" // Ensure this matches the key in moduleInfo
                  value={moduleInfo.moduleDescription} // Updated to use moduleInfo
                  onChange={handleInputChange} // Updated to use the new handler
                  rows={3}
                  className="mt-1 block w-full border text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
                ></textarea>
                {errors.moduleDescription && (
                  <p className="text-red-500 text-xs italic">
                    {errors.moduleDescription}
                  </p>
                )}
              </div>

              {screenCount === 1 && (

                <div className="flex justify-center items-end h-full my-4 ">
                  <button
                    type="submit"
                    className="px-6 py-2 flex justify-center items-end gap-2 bg-textColor h-fit text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                  >
                    Save & Next
                  </button>
                </div>

              )}


            </form>
          </>}

          {screenCount === 2 && (
            <AddRemoveAssessmentFromModule moduleIDProp={module_id} />
          )}
          {screenCount === 3 && (
            <AddRemoveContentFromModule moduleIDProp={module_id} />
          )}

          <div className="flex justify-center gap-2 items-end h-full ">
            {screenCount > 1 && (
              <button
                className="w-24 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                onClick={() => setScreenCount(screenCount - 1)}
              >
                Previous
              </button>
            )}

            {screenCount === 2 ? (
              <button
                type="submit"
                className="w-30 px-2 py-2  bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                onClick={() => {
                  setScreenCount(screenCount + 1);

                }}
              >
                Save & Next
              </button>
            ) : (
              ""
            )}
            {screenCount === 3 ? (

              <button
                type="submit"
                className="w-24 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                onClick={() => { router.push('/admin/module/modulelist'); }}
              >
                Save

              </button>
            ) : (
              ""
            )}
          </div>
        </div>
      </main>
    )
  } else if (action === 'view') {
    return (
      <main className="w-full flex h-[90vh] bg-primary flex-col p-4">
        <div className="flex flex-col h-full justify-between gap-3 overflow-y-auto bg-white mx-auto w-full   rounded-2xl p-3 ">
          {screenCount === 1 && <div >
            <Heading pgHeading="View Module" />
            <form onSubmit={handleSubmit} className="flex flex-col justify-between gap-3 p-3  w-full ">
              <div className="flex justify-between  w-full gap-5 h-full ">
                <div className="flex flex-col gap-1 w-full ">
                  <label
                    htmlFor="ModuleName"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Module Name
                  </label>
                  <input
                    type="text"
                    id="ModuleName"
                    name="moduleName" // Ensure this matches the key in moduleInfo
                    value={moduleInfo.moduleName} // Updated to use moduleInfo
                    className="mt-1 block w-full border text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
                  ></input>

                </div>
                <div className="flex flex-col gap-1 w-full">
                  <label
                    htmlFor="ModuleHeading"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Module Heading
                  </label>
                  <input
                    id="ModuleHeading"
                    name="moduleHeading" // Ensure this matches the key in moduleInfo
                    value={moduleInfo.moduleHeading} // Updated to use moduleInfo

                    className="mt-1 block w-full border text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
                  ></input>
                </div>
              </div>
              <div className="flex flex-col gap-1">
                <label
                  htmlFor="ModuleDescription"
                  className="block text-sm font-medium text-gray-700"
                >
                  Module Description
                </label>
                <textarea
                  id="ModuleDescription"
                  name="moduleDescription" // Ensure this matches the key in moduleInfo
                  value={moduleInfo.moduleDescription} // Updated to use moduleInfo
                  rows={3}
                  className="mt-1 block w-full border text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
                ></textarea>

              </div>

              {screenCount === 1 && (

                <div className="flex justify-center items-end h-full my-4 ">
                  <button
                    type="submit"
                    className="px-6 py-2 flex gap-2 justify-center items-end bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                  >
                    Next
                  </button>
                </div>

              )}


            </form>
          </div>}

          {screenCount === 2 && (
            <div className="h-[60vh]">
              <AssessmentInModule moduleIDProp={module_id} />
            </div>
          )}
          {screenCount === 3 && (
            <div className="h-[60vh]">
              <ContentInModule moduleIDProp={module_id} />
            </div>
          )}

          <div className="flex justify-center gap-2 items-end h-full  ">
            {screenCount > 1 && (
              <button
                className="w-24 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                onClick={() => setScreenCount(screenCount - 1)}
              >
                Previous
              </button>
            )}

            {screenCount === 2 ? (
              <button
                type="submit"
                className="w-24 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"

                onClick={() => {
                  setScreenCount(screenCount + 1);

                }}
              >
                Next
              </button>
            ) : (
              ""
            )}
            {screenCount === 3 ? (

              <button
                type="submit"
                className="w-24 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                onClick={() => { router.push('/admin/module/modulelist'); }}
              >
                Save

              </button>
            ) : (
              ""
            )}
          </div>
        </div>
      </main>
    )
  } else {
    return <div>Invalid action</div>;
  }
}