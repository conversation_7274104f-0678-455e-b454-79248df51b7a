"use client"
import Button from '@/components/ui/Button';
import Heading from '@/components/ui/Heading';
import SearchBar from '@/components/admin/SearchBar';
import ListModuleInTable from '@/components/admin/module/ListModuleInTable';
import { useGetAllModules } from '@/hook/admin/module/useGetAllModules';
import { useGetSearchedModules } from '@/hook/admin/module/useGetSearchedModules';
import { useModuleDelete } from '@/hook/admin/module/useModuleDelete';

import Link from 'next/link';
import React, { useEffect, useState } from 'react';

export default function Page() { // Capitalize component name
    const [showAddContentModal, setShowAddContentModal] = useState(false);
    const [showSubmitModal, setShowSubmitModal] = useState(false);
    const [isModuleData, setModuleData] = useState([]);
    const [search, setSearch] = useState("");
    const { data, isLoading, error } = useGetAllModules(0, 100);
    const { data: searchedModules } = useGetSearchedModules(search);
    

    const { mutate: ModuleDelete } = useModuleDelete();

    useEffect(() => {
        if (!isLoading && !error && data) {
            setModuleData(data); // Update contentData state when data is fetched successfully
        }
    }, [data, isLoading, error]);

    const handleAddContent = () => {
        setShowAddContentModal(true);
    };

    const onCloseAddContentModal = () => {
        setShowAddContentModal(false);
    };

    const onCloseSubmitModal = () => {
        setShowSubmitModal(false);
    };

    const handleSearch = (searchText: string) => {
        //Call the filter content API and use the filtered content here
        setSearch(searchText);
    };

    const handleDelete = (moduleId :number) => {
        ModuleDelete(moduleId, {
          onSuccess: () => {
            // Filter out the deleted item from contentData
            const updatedModuleData = isModuleData.filter(item => item.module_id !== moduleId);
            setModuleData(updatedModuleData);
          },
          onError: () => {
            console.error('Deletion error:', deleteError);
          }
        });
      };

    return (
        <main className="w-full flex h-[90vh] bg-primary flex-col gap-3 p-3">
            <div className='flex flex-col justify-between h-[5vh]'>

            <Heading pgHeading="Modules" />
            </div>
            
            <div className='flex justify-between  h-[10vh]  w-full items-end'>
                <SearchBar onSearch={handleSearch} />
                <Link href={"/admin/module"}>
                    <Button btnName='Add Module' />
                </Link>
            </div>

    
            <div className='h-[75vh]'>
            {search.length > 2 && searchedModules ?
                <ListModuleInTable ModuleData={searchedModules}  onDelete={handleDelete}/> :
                <ListModuleInTable ModuleData={data} onDelete={handleDelete} />
            }
            </div>
        </main>
    );
}
