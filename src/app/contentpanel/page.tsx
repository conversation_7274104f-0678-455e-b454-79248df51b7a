// "use client";
// import { useState, useEffect, useRef } from 'react';
// import {
//   ArrowLeftEndOnRectangleIcon,
//   ArrowRightStartOnRectangleIcon,
//   ChevronUpIcon,
//   ChevronDownIcon,
//   InformationCircleIcon,
//   ChevronRightIcon,
//   ChevronLeftIcon
// } from "@heroicons/react/24/outline";
// import { motion } from 'framer-motion';
// import { useModuleContentCounterStore } from "@/utils/moduleContent";

// // Import dummy data and components
// import { simulateGetAllGroupModuleContent } from "@/utils/dummyData/contentPanelData";
// import ContentInfo from '@/components/contentPanel/ContentInfo';
// import ContentScreen2 from '@/components/contentPanel/ContentScreen2';
// import Collapsible from '@/components/contentPanel/Collapsible';

// // Mock function for localStorage
// const mockLocalStorage = {
//   contentId: null,
//   moduleId: null,
//   groupId: null,

//   getContentLocalStorageData: function() {
//     return {
//       contentId: this.contentId,
//       moduleId: this.moduleId,
//       groupId: this.groupId
//     };
//   },

//   setContentLocalStorageData: function(contentId, moduleId, groupId) {
//     this.contentId = contentId;
//     this.moduleId = moduleId;
//     this.groupId = groupId;
//     console.log("Saved to mock localStorage:", { contentId, moduleId, groupId });
//   }
// };

// const ContentPanelPage = () => {
//   // Mock URL parameters
//   const mockSearchParams = {
//     content_id: "1",
//     group_id: "1",
//     module_id: "1"
//   };

//   const { content_id, setGroup_id, setContent_id, setModule_id } = useModuleContentCounterStore();
//   const storedData = mockLocalStorage.getContentLocalStorageData();
  
//   // Parse incoming params
//   const incomingContentId = parseInt(mockSearchParams.content_id, 10);
//   const incomingGroupId = parseInt(mockSearchParams.group_id, 10);
//   const incomingModuleId = parseInt(mockSearchParams.module_id, 10);

//   useEffect(() => {
//     if (content_id === 0) {
//       mockLocalStorage.setContentLocalStorageData(incomingContentId, incomingModuleId, incomingGroupId);
//     } else {
//       mockLocalStorage.setContentLocalStorageData(undefined, incomingModuleId, incomingGroupId);
//     }
//   }, []);

//   // State Variables
//   const [isSideBarOpen, setIsSideBarOpen] = useState(true);
//   const [isBottomBarOpen, setIsBottomBarOpen] = useState(true);
//   const divRef = useRef(null);
//   const [dimensions, setDimensions] = useState({ width: 800, height: 600 });

//   // Use dummy data instead of API call
//   const contentPanelData = simulateGetAllGroupModuleContent(incomingGroupId);

//   // Set Group, Module, and Content IDs
//   useEffect(() => {
//     if (incomingGroupId && incomingModuleId) {
//       setGroup_id(incomingGroupId);
//       setModule_id(incomingModuleId);
//       setContent_id(incomingContentId);
//     }
//   }, [incomingGroupId, incomingModuleId, incomingContentId, setGroup_id, setModule_id, setContent_id]);

//   useEffect(() => {
//     // Mock ResizeObserver behavior
//     setDimensions({ width: window.innerWidth - 450, height: window.innerHeight - 200 });
    
//     const handleResize = () => {
//       setDimensions({ width: window.innerWidth - 450, height: window.innerHeight - 200 });
//     };
    
//     window.addEventListener('resize', handleResize);
    
//     return () => {
//       window.removeEventListener('resize', handleResize);
//     };
//   }, []);

//   return (
//     <div className="w-full min-h-screen bg-gradient-to-br from-gray-50 to-slate-100 p-2 md:p-6 flex flex-col">
//       <main className="flex-1 flex flex-col md:flex-row gap-6 md:gap-8 max-w-7xl mx-auto w-full h-[calc(100vh-60px)]">
//         {/* Sidebar - fixed, full height, flush left */}
//         <motion.div
//           className="fixed top-0 left-0 h-screen z-30 rounded-none bg-slate-100 shadow-xl border-r border-slate-200 flex flex-col transition-all duration-300"
//           style={{ width: isSideBarOpen ? '320px' : '56px', minWidth: isSideBarOpen ? '320px' : '56px' }}
//           animate={{ width: isSideBarOpen ? '320px' : '56px' }}
//           transition={{ type: 'spring', stiffness: 300, damping: 30 }}
//         >
//           <div className="p-3 border-b border-slate-200 flex items-center justify-between">
//             {isSideBarOpen && <h2 className="font-bold text-slate-700 text-lg tracking-tight">Course Modules</h2>}
//             <button
//               className="p-2 rounded-lg hover:bg-slate-200 transition-all"
//               onClick={() => setIsSideBarOpen(!isSideBarOpen)}
//               aria-label={isSideBarOpen ? 'Collapse sidebar' : 'Expand sidebar'}
//             >
//               {isSideBarOpen ?
//                 <ArrowLeftEndOnRectangleIcon className="w-7 h-7 text-slate-500" /> :
//                 <ArrowRightStartOnRectangleIcon className="w-7 h-7 text-slate-500" />
//               }
//             </button>
//           </div>
//           {isSideBarOpen && (
//             <div className="flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-slate-200">
//               {incomingContentId && contentPanelData && contentPanelData.map((item, index) => (
//                 <Collapsible
//                   key={item.module_id}
//                   data={item}
//                   number={index + 1}
//                   incomingContentId={incomingContentId}
//                 />
//               ))}
//             </div>
//           )}
//           {isSideBarOpen && (
//             <div className="p-4 border-t border-slate-200">
//               <button
//                 className="w-full bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-blue-700 shadow transition-colors"
//                 onClick={() => console.log("Navigate to dashboard")}
//               >
//                 Back to Dashboard
//               </button>
//             </div>
//           )}
//         </motion.div>

//         {/* Main content area - margin left for sidebar, flex row for content and info panel */}
//         <div className="min-h-screen flex flex-row gap-0 max-w-full w-full" style={{ marginLeft: isSideBarOpen ? 320 : 56 }}>
//           {/* Content Area - occupies all available space, no background */}
//           <div className="flex-1 flex flex-col h-full py-8 px-2 md:px-8">
//             {/* Content Screen */}
//             <div
//               ref={divRef}
//               className="flex-1 flex justify-center items-center mb-4 p-0 md:p-4 transition-all duration-300 w-full text-lg md:text-xl bg-transparent shadow-none rounded-none"
//             >
//               {(incomingContentId && incomingModuleId && incomingGroupId) &&
//                 <ContentScreen2
//                   key={`${dimensions.width}-${dimensions.height}`}
//                   height={dimensions.height}
//                   width={dimensions.width}
//                   incomingContentId={incomingContentId}
//                   incomingModuleId={incomingModuleId}
//                   incomingGroupId={incomingGroupId}
//                 />
//               }
//             </div>
//           </div>

//           {/* Info Panel - right side, toggleable */}
//           <div className="relative flex flex-col items-end h-full">
//             <div className="sticky top-0 z-20 flex flex-col items-end">
//               <button
//                 className={`flex items-center gap-2 px-4 h-12 bg-white rounded-l-2xl shadow-lg border border-slate-200 hover:bg-slate-50 transition-all drop-shadow-lg text-base font-medium mt-8 mr-0`}
//                 onClick={() => setIsBottomBarOpen(!isBottomBarOpen)}
//                 aria-label={isBottomBarOpen ? 'Hide Content Info' : 'Show Content Info'}
//                 title={isBottomBarOpen ? 'Hide Content Info' : 'Show Content Info'}
//               >
//             <InformationCircleIcon className="h-6 w-6 text-blue-500" />
//             <span className="hidden md:inline">{isBottomBarOpen ? 'Hide Content Info' : 'Show Content Info'}</span>
//             {isBottomBarOpen
//               ? <ChevronRightIcon className="h-6 w-6 text-slate-500" />
//               : <ChevronLeftIcon className="h-6 w-6 text-slate-500" />
//             }
//   </button>
//             </div>
//             <motion.div
//               className="fixed right-0 top-0 h-screen bg-white rounded-l-3xl shadow-2xl overflow-y-auto"
//               style={{ width: isBottomBarOpen ? 320 : 0, minWidth: 0, transition: 'width 0.3s cubic-bezier(0.4,0,0.2,1)' }}
//               animate={{ width: isBottomBarOpen ? 320 : 0, opacity: isBottomBarOpen ? 1 : 0 }}
//               transition={{ type: 'spring', stiffness: 300, damping: 30 }}
//             >
//               {isBottomBarOpen && <div className="p-8 text-base md:text-lg"><ContentInfo contentInfo={contentPanelData} /></div>}
//             </motion.div>
//           </div>
//         </div>
//       </main>
//     </div>
//   );
// };

// export default ContentPanelPage;
// src/app/contentpanel/page.tsx
"use client";
import NewContentPanel from '@/components/contentPanel/NewContentPanel';
import { ModuleContentProvider } from '@/utils/dummyData/ModuleContentContext';
export default function ContentPanelPage() {
  return (
    <ModuleContentProvider>
      <NewContentPanel />
    </ModuleContentProvider>
  );
}