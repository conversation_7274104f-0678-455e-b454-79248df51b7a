// "use client";
// import NavigationHeader from '@/components/ui/header/NavigationHeader';
// import Sidebar from '@/components/ui/header/Sidebar';
// import Header from '@/components/contentPanel/Header';
// //API Hooks
// import { useGetUserDetail } from '@/hook/user/useGetUserDetail';
// export default function DashboardLayout({ children }: { children: React.ReactNode }) {
//   //console.log("Logging userDetails from user/me api", userDetails);

//   if (true)
//     return (
//       <div>
//         <Header />
//         <div className="flex flex-col lg:flex-row overflow-hidden">
//           {children}
//         </div>
//       </div>
//     )
// }
"use client";
export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen">
      {children}
    </div>
  );
}