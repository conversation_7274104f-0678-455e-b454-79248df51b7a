'use client'
import ContentInfo from '@/components/contentPanel/ContentInfo'
import ContentScreen2 from '@/components/contentPanel/ContentScreen2'
import Sidebar from '@/components/contentPanel/Sidebar'
import { useParams } from "next/navigation";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/20/solid";
import React, { useState, useEffect } from 'react'
import { useGetAllGroupModuleContent } from "@/hook/content/useGetAllGroupModuleContent"
import { getUser } from "@/api/user.localStorage"
import { useModuleContentCounterStore } from "@/utils/moduleContent"
import { useSearchParams } from 'next/navigation'

const Page = () => {

  const params = useParams();
  const searchParams = useSearchParams()
  const contentIdValue: number = parseInt(params.contentId, 10);
  const groupIdValue = searchParams.get('group_id')
  const moduleIdValue = searchParams.get('module_id')
  const [height, setHeight] = useState(window.innerHeight)
  const [width, setWidth] = useState(window.innerWidth)
  const [groupId, setGroupId] = useState(groupIdValue)

  //TODO:- 
  const { data: contentPanelData } = useGetAllGroupModuleContent(groupIdValue?.split("?")[0])
  const { setGroup_id, setContent_id, setModule_id } = useModuleContentCounterStore()
  console.log("contentPanelData21", contentPanelData)
  console.log("datais", getUser()?.user_id)

  useEffect(() => {
    setGroup_id(parseInt(groupIdValue))
    setModule_id(parseInt(moduleIdValue))
    setContent_id((contentIdValue))
  }, [groupIdValue])

  useEffect(() => {
    const handleContextmenu = e => {
        e.preventDefault()
    }
    document.addEventListener('contextmenu', handleContextmenu)
    return function cleanup() {
        document.removeEventListener('contextmenu', handleContextmenu)
    }
}, [ ])

  return (
    <main className="relative h-screen w-full overflow-x-hidden">
      <div className='absolute top-0 left-0 z-20'>
        <Sidebar
          height={height}
          width={width}
          contentPanelData={contentPanelData} />
      </div>
      <div className="relative w-full min-h-[700px]  flex flex-col justify-between">
        <div className='flex-grow flex justify-center items-center overflow-y-auto'>
          {/* <ContentScreen
            height={height}
            width={width}
            contentPanelData={contentPanelData} /> */}
            <ContentScreen2
            height={height}
            width={width}
            contentPanelData={contentPanelData} />
        </div>
        <div className='h-16'>
          <ContentInfo contentInfo={contentPanelData} />
        </div>
      </div>
    </main>
  );
}

export default Page