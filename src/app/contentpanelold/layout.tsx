"use client";
import Header from '@/components/contentPanel/Header';
export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  //console.log("Logging userDetails from user/me api", userDetails);

  if (true)
    return (
      <div>
        <Header />
        <div className="flex flex-col lg:flex-row overflow-hidden">
          {children}
        </div>
      </div>
    )
}
