import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import Providers from "@/utils/provider"

import axios from 'axios';

// Setup Axios interceptor
axios.interceptors.response.use(response => response, error => {
  if (error.response && error.response.status === 401) {
    if (typeof window !== "undefined") {
      window.location.href = '/login';
    }
  }
  return Promise.reject(error);
});

const inter = Inter({ subsets: ['latin'] })


export const metadata: Metadata = {
  title: 'Skilling.ai',
  description: 'AI powered Learning Management System',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html className="h-full bg-white" lang="en">
      <body className={inter.className + " h-full "} >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
