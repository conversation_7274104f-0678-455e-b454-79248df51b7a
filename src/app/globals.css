@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom,
      transparent,
      rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
}

#react-doc-viewer #header-bar {
  display: none;
  background-color: #faf;
}

#react-doc-viewer #proxy-renderer{
  height: 100%;
}

#react-doc-viewer #proxy-renderer #pdf-renderer #pdf-controls #pdf-download{
  display: none;
}

#react-doc-viewer #proxy-renderer #pdf-renderer #pdf-controls{
  display: fixed;
  z-index: 4;
}

#react-doc-viewer #msdoc-renderer{
  height: 100%;
}

#react-doc-viewer #ApplicationContainer #application #WACStatusBarContainer{
  display: none;
}

::-webkit-scrollbar{
  width: 7px;
}

::-webkit-scrollbar-track{
  background: #e5ebf2;
}

::-webkit-scrollbar-thumb{
  background: #c7c7c7;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover{
  background: #b8b8b8;
}


/* Style the progress bar itself */
progress::-webkit-progress-bar {
  background-color: #f3f3f3; /* Light gray background */
  border-radius: 10px;
}

/* Style the progress value (the filling part) */
progress::-webkit-progress-value {
  background-color: #3469A8; /* Blue color for progress */
  border-radius: 10px;
}
/* These styles can be added to your global CSS or a component-specific CSS file */

/* Ensure content viewers take full height */
.content-viewer-container {
  height: calc(100vh - 200px); /* Adjust value based on your header/footer heights */
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

/* Make iframes take full height of their container */
.responsive-iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

/* Update these YouTube-specific styles in your CSS file */

/* YouTube container with fixed height */
.youtube-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  overflow: hidden;
}

.youtube-frame-container {
  flex: 1;
  min-height: 500px;
  height: 500px;
  background-color: black;
}

.youtube-frame-container iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

/* Make PDF viewer scale properly */
.pdf-container {
  width: 100%;
  height: 100%;
  overflow: auto;
}

/* Style for navigation buttons to ensure they're visible over content */
.content-nav-button {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(209, 213, 219, 0.8);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.content-nav-button:hover {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Ensure the content panel grows to fill available space */
.content-panel-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Tab styling */
.content-tab {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.375rem 0.375rem 0 0;
  transition: background-color 0.2s, color 0.2s;
}

.content-tab.active {
  background-color: white;
  color: #2563eb;
  border-top: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
  border-bottom: none;
}

.content-tab:not(.active) {
  background-color: #f3f4f6;
  color: #4b5563;
}

.content-tab:not(.active):hover {
  background-color: #e5e7eb;
}
/* Add these styles to your globals.css or create a dedicated CSS file */

/* Main content container */
.content-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Scrollable content area */
.scrollable-content {
  flex: 1;
  overflow-auto: auto;
  padding: 1.5rem;
}

/* Sticky navigation bar */
.sticky-nav {
  position: sticky;
  bottom: 0;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  padding: 0.75rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

/* Ensure content viewers take full available height */
.content-viewer {
  height: 100%;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

/* Make iframes resize properly */
.responsive-iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

/* PDF container styles */
.pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  overflow: hidden;
}

.pdf-viewer iframe {
  flex: 1;
  width: 100%;
  height: 100%;
  border: 0;
}

/* YouTube embed container */
.youtube-container {
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  position: relative;
  overflow: hidden;
  max-width: 100%;
}

.youtube-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* PowerPoint viewer */
.powerpoint-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  overflow: hidden;
}

.powerpoint-viewer iframe {
  flex: 1;
  width: 100%;
  height: 100%;
  border: 0;
}

/* Web embed container */
.web-embed {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  overflow: hidden;
}

.web-embed iframe {
  flex: 1;
  width: 100%;
  height: 100%;
  border: 0;
}

/* Navigation button styles */
.nav-button {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  transition: all 0.15s ease-in-out;
}

.prev-button {
  background-color: #f3f4f6;
  color: #4b5563;
}

.prev-button:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.next-button {
  background-color: #2563eb;
  color: white;
}

.next-button:hover:not(:disabled) {
  background-color: #1d4ed8;
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mark as completed button */
.complete-button {
  background-color: #dcfce7;
  color: #166534;
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  transition: background-color 0.15s ease-in-out;
}

.complete-button:hover {
  background-color: #bbf7d0;
}
/* Add these info panel styles to your CSS file */

/* Info panel container */
.info-panel {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 320px;
  background-color: white;
  border-left: 1px solid #e5e7eb;
  transition: transform 0.3s ease-out;
  overflow: hidden;
  z-index: 10;
}

.info-panel.visible {
  transform: translateX(0);
}

.info-panel.hidden {
  transform: translateX(100%);
}

/* Info panel toggle button */
.info-panel-toggle {
  position: absolute;
  right: 0;
  top: 4rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-right: none;
  border-radius: 0.375rem 0 0 0.375rem;
  padding: 0.5rem 0.75rem;
  display: flex;
  align-items: center;
  z-index: 20;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
  transition: right 0.3s ease-out;
}

.info-panel-toggle.panel-open {
  right: 320px;
}

/* Make sure the info panel content scrolls properly */
.info-panel-content {
  height: 100%;
  overflow-y: auto;
  padding: 1rem;
}

/* Ensure z-index hierarchy is correct */
.sticky-nav {
  z-index: 15; /* Higher than content, lower than info panel toggle */
}

/* Make sure content transitions properly when info panel opens/closes */
.content-with-panel-margin {
  transition: margin-right 0.3s ease-out;
}