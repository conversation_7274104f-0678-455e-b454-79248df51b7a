'use client' 
 
import { useEffect } from 'react'
 
export default function Error({
  error,
  reset,
}: {
  error: Error
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error)
  }, [error])
 
  return (
    <div className='w-full h-full bg-primary flex items-center justify-center'>
      <div className='mx-auto bg-white p-8'>
        <h2 className='text-lg text-textColor '>Oops! Something went wrong. Please</h2>
        <button
          className='w-44 mt-4 mx-auto justify-center rounded-md border-2 border-textSecondary bg-white px-3 py-1.5 text-sm font-semibold leading-6 text-textSecondary shadow-sm hover:bg-hoverColor hover:text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75'
          onClick={
            // Attempt to recover by trying to re-render the segment
            () => reset()
          }
        >
          Try again
        </button>
      </div>
    </div>
  )
}