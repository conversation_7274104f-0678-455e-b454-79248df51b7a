//Client-rendered
"use client";

//Imported
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";


export default function Home() {
  return (
    <AnimatePresence>
      <div className="min-h-screen w-screen flex flex-col relative bg-primary font-inter ">
        <svg
          style={{ filter: "contrast(125%) brightness(110%)" }}
          className="fixed z-[1] w-full h-full opacity-[35%]"
        >
          <filter id="noise">
            <feTurbulence
              type="fractalNoise"
              baseFrequency=".7"
              numOctaves="3"
              stitchTiles="stitch"
            ></feTurbulence>
            <feColorMatrix type="saturate" values="0"></feColorMatrix>
          </filter>
          <rect width="100%" height="100%" filter="url(#noise)"></rect>
        </svg>
        <main className="flex flex-col justify-center h-full  static w-screen grid-rows-[1fr_repeat(3,auto)_1fr] z-[100] py-6 px-4 md:px-8">
          <div className="w-full h-fit flex justify-start">
            <Image
              className=""
              src="/logo_colour.png"
              alt="Skilling.ai"
              width={100}
              height={60}
            />
          </div>
          <motion.h1
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              delay: 0.15,
              duration: 0.95,
              ease: [0.165, 0.84, 0.44, 1],
            }}
            className="relative  ml-[-10px] my-[30px] font-extrabold text-[8vw] md:text-[60px] lg:text-[100px] font-inter text-textPrimary leading-[1.1] tracking-[-2px] z-[100]"
          >
            Aspire.<br />
            Learn.<br />
            <span className="text-textSecondary">Achieve</span>
            <span className="font-inter text-textSecondary">.</span>
          </motion.h1>
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              delay: 0.15,
              duration: 0.95,
              ease: [0.165, 0.84, 0.44, 1],
            }}
            className="flex flex-row justify-center z-20 my-6 max-w-2xl md:space-x-8 "
          >
            <div className="w-1/2">
              <h2 className="flex items-center font-semibold text-[1em] text-textColor">
                About Skilling
              </h2>
              <p className="text-[14px] leading-[20px] text-textColor font-normal">
                Skilling.AI is an AI powered platform that allows you to identify and bridge the skill gaps  you may have  for the jobs you aspire to
              </p>
            </div>
            <div className="w-1/2">
              <h2 className="flex items-center font-semibold text-[1em] text-textColor">
                Get in touch
              </h2>
              <p className="text-[14px] leading-[20px] text-textColor font-normal">
                Reach out to <NAME_EMAIL>
              </p>
            </div>
          </motion.div>
          <div className="flex gap-[15px] mt-8 md:mt-0 ">
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                delay: 0.55,
                duration: 0.55,
                ease: [0.075, 0.82, 0.965, 1],
              }}
            >
              <Link
                href="/login"
                className="group rounded-full min-w-[340px] px-4 py-2 text-[13px] font-semibold transition-all items-center justify-center bg-secondary text-white hover:bg-hoverColor no-underline flex gap-x-2  active:scale-95 scale-100 duration-75"
              >
                Get Started
                <svg
                  className="w-5 h-5"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.75 6.75L19.25 12L13.75 17.25"
                    stroke="#FFFFFF"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M19 12H4.75"
                    stroke="#FFFFFF"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Link>
            </motion.div>
          </div>
        </main>
      </div>
      {/*Dark div on the side*/}
      <div
        className="fixed top-0 right-0 w-[80%] md:w-1/2 h-screen bg-[#1F2B3A]/20"
        style={{
          clipPath:
            "polygon(100px 0,100% 0,calc(100% + 225px) 100%, 480px 100%)",
        }}
      ></div>
      {/*Bottom bar */}
      <div className="h-[60px] bg-[#1D2B3A] fixed bottom-0 z-20 w-full flex flex-row items-center justify-evenly">
        <p className="text-white/90 text-base md:text-sm font-semibold md:leading-[60px] whitespace-nowrap flex flex-row"></p>
      </div>
      <div className="flex gap-[15px] mt-8 md:mt-0 z-100">
        <motion.div
          initial={{ opacity: 0, y: 0 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: 0.55,
            duration: 0.55,
            ease: [0.075, 0.82, 0.965, 1],
          }}
        ></motion.div>
      </div>
    </AnimatePresence>
  );
} 
