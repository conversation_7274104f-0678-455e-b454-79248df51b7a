"use client"
import React from 'react'
import { useParams } from 'next/navigation';

import Heading from '@/components/ui/Heading';
import ModuleAssessmentTable from '@/components/dashboard/module/ModuleAssessmentTable';
import ModuleContentTable from '@/components/dashboard/module/ModuleContentTable';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation'


export default function ModuleId() {

  const params = useParams();
  const searchParams = useSearchParams()
  const groupId = searchParams.get('group_id')
  const module_id: number = parseInt(params?.moduleId, 10);
  console.log("module_Id " + module_id);
  console.log("group_id " + groupId);
  const router = useRouter();
  // const { query } = router;
  // const data = query.data; 

  // console.log("changes in data",data)

  
  const handleBackClick = () => {
    router.back(); // Go back to the previous page
  };

  return (
    <div className="flex flex-col w-full h-[90vh]   ">
      {/* Use flex-grow for equal height division */}
      <div className="h-[10vh]">
        <div className="p-3">
          <Heading pgHeading="Modules" />
        </div>
      </div>
      <div className="h-[80vh] flex flex-col gap-5  bg-white   w-full  rounded-2xl p-5">
        <div className='h-[40vh]'><ModuleContentTable moduleIDProp={module_id} groupIdProp={groupId}/></div>
        <div className='h-[40vh]'><ModuleAssessmentTable moduleIDProp={module_id} /> </div>
        
        <button
          onClick={handleBackClick}
          className="bg-gray-800 text-white px-4 py-2 rounded w-20 flex justify-center items-center"
        >
          Back
        </button>
      </div>
    </div>

  )
}
