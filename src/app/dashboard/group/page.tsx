"use client";
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Heading from "@/components/ui/Heading";
import Link from "next/link";

import { CodeBracketIcon } from "@heroicons/react/24/outline";
import { useGetAllProgram } from "@/hook/dashboard/program/useGetAllProgram";
import Nothing from "@/components/ui/Nothing";

export default function ModulePage() {
  const [currentPage, setCurrentPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const { data: programData, isLoading, isError } = useGetAllProgram();

  const colors = ["bg-blue-50", "bg-green-50", "bg-yellow-50", "bg-red-50"];

  useEffect(() => {
    if (!isLoading && !isError) {
      console.log("program data:", programData);
    }
  }, [programData, isLoading, isError]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error fetching modules</div>;
  }

  return (
    <main className="w-full flex h-full bg-white flex-col items-center justify-between p-4">
      <div className="w-full overflow-auto">
        <div className="p-3">
          <Heading pgHeading="Program List" />
        </div>
        <div className="flex flex-wrap gap-5 justify-center bg-white w-full rounded-2xl p-5 h-[85vh] overflow-y-auto">
          {(!programData || programData.length === 0) ? (
            <div className="w-full flex justify-center items-center h-full">
              <Nothing
                title="No Program Available"
                para="There are currently no Program to display.
                          Please check back later."
              />
            </div>
          ) : (
            programData.map((data, index) => (
              <div
                key={data.group_id}
                className="flex flex-col w-full md:w-[45%] lg:w-[30%] xl:w-[22%] gap-3 rounded-lg border h-fit border-[#A9A9A9] p-3"
              >
                <div className={`h-40 w-full flex justify-center items-center p-5 ${colors[index % colors.length]}`}>
                  <CodeBracketIcon className="w-16 h-16" />
                </div>

                <div className="flex flex-col w-full justify-center items-center">
                  <h1 className="font-bold text-lg text-center">{data.group_name}</h1>
                </div>

                <div className="flex flex-col items-center justify-center w-full gap-2">
                  <Link href={`group/${data.group_id}`} className="w-full">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded text-sm w-full"
                    >
                      {"Play"}
                    </motion.button>
                  </Link>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </main>
  );
}
