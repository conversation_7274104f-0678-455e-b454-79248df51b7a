"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { motion } from "framer-motion";
import { useGetAllProgram } from "@/hook/dashboard/program/useGetAllProgram";
import { useGetAllModules } from "@/hook/admin/module/useGetAllModules";
import Heading from "@/components/ui/Heading";
import { useGetAllModulesForUser } from "@/hook/dashboard/program/useGetAllModulesForUser";
import { getUser, setFirstLogin } from "@/api/user.localStorage";

export default function FAQGroupId() {
  const user = getUser()
  const [isModalOpen, setIsModalOpen] = useState(user?.firstLogin); // State to control modal visibility
  const params = useParams();

  const group_id = params.groupId ? parseInt(params.groupId, 10) : null;
  const { data: programData, isLoading: isLoadingProgram, isError: isErrorProgram } = useGetAllProgram();
  const { data: modulesData, isLoading: isLoadingModule, isError: isErrorModule } = useGetAllModulesForUser(group_id);

  console.log("modulesData", modulesData)

  if (group_id === null) {
    return <div>Error: Group ID not found</div>;
  }

  console.log("groupId", group_id);


  if (isLoadingProgram || isLoadingModule) {
    return <div>Loading...</div>;
  }

  if (isErrorProgram || isErrorModule) {
    return <div>Error fetching data</div>;
  }

  // Find the FAQ corresponding to the `group_id`
  const faqData = programData?.find(data => data.group_id === group_id);

  const handleCloseModal = () => {
    setFirstLogin(false);
    setIsModalOpen(false);
  };

  return (
    <>
      {faqData?.group_faq && isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50" onClick={handleCloseModal}>
          <div className="flex flex-col justify-between items-center gap-6 bg-white p-6 rounded-lg shadow-lg max-h-[80vh] w-[90%] md:w-3/4 lg:w-1/2 xl:w-2/5 overflow-hidden" onClick={(e) => e.stopPropagation()}>
            {/* Modal Header */}
            <div className="w-full flex justify-between items-center border-b pb-4">
              <h2 className="text-xl font-semibold text-gray-800">Program Details</h2>
              <button onClick={handleCloseModal} className="text-gray-500 hover:text-gray-700 focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal Content */}
            <div className="w-full flex-1 overflow-y-auto text-gray-700 text-md font-sans px-4 py-2">
              {faqData.group_faq}
            </div>

            {/* Modal Footer */}
            <button onClick={handleCloseModal} className="w-full px-6 py-3 bg-secondary text-white font-semibold rounded-lg hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all">
              Close
            </button>
          </div>
        </div>
      )}


      <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-1">
        <div className="w-full overflow-auto">
          <div className="p-3">
            <Heading pgHeading="Modules" />
          </div>
          <div className="flex flex-col gap-3 overflow-y-auto bg-white w-full rounded-2xl p-5 h-[90vh]">
            {modulesData?.map((module) => (
              <div
                key={module.sequence}
                className="flex flex-col w-full justify-between gap-1 rounded-lg border border-[#A9A9A9] text-left px-3 py-2"
              >
                <h1 className="font-bold text-lg">{module.module_name}</h1>

                <div className="flex w-full justify-between">
                  <div className="text-base text-gray-800 font-semibold">
                    {module.module_headline}
                  </div>
                  <div className="text-sm text-gray-800 font-semibold px-1">
                    {/* By- {module.created_by} */}
                  </div>

                </div>
                <div className="flex w-full justify-between gap-2">
                  <p className="text-sm text-gray-800">{module.module_description}</p>
                  <Link
                    href={`module/${module.module_id}?group_id=${group_id}`}
                    className="text-blue-500 underline"
                  >
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <button
                        className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded w-40 text-sm"
                      >
                        {"Open Module"}
                      </button>
                    </motion.button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
    </>
  );
}
