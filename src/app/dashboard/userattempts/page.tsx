"use client";
import { useRouter, useSearch<PERSON>arams } from "next/navigation";
import { getUser } from "@/api/user.localStorage";
import React, { useEffect, useState } from 'react';
import Heading from "@/components/ui/Heading";
import PageForAssessmentUserShow from "@/components/admin/assessments/PageForAssessmentUserShow";
import { useGetUserAssessments } from "@/hook/assessments/useGetUserAssessments";

export default function Page() {
  const [user, setUser] = useState(null);
  const [userId, setUserId] = useState(null);
  const router = useRouter();

  useEffect(() => {
    const fetchData = async () => {
      const userData = await getUser();
      setUser(userData);
    };

    fetchData();
  }, []);

  useEffect(() => {
    if (user) {
      const userData = JSON.parse(JSON.stringify(user));
      const userId = userData.user_id;
      setUserId(userId);
    }
  }, [user]);

  console.log("user", user);
  console.log("userId", userId); // Output: 3 if user is defined

  const { data: assessments } = useGetUserAssessments(userId, 0, 1000);
  //TODO: we might need to add dynamic values to offset and limit



  console.log("assessment_id is", assessments)



  return (
    <div className="flex flex-col flex-grow p-2 gap-2 overflow-auto h-[90vh]">
      <Heading pgHeading="User Attempts For An Assessment" />
      {assessments && Array.isArray(assessments) && assessments.map(assessment => (
        <div key={assessment.assessment_id}>
          <PageForAssessmentUserShow userId={userId} assessmentId={assessment.assessment_id} />
        </div>
      ))}
    </div>
  )

}



