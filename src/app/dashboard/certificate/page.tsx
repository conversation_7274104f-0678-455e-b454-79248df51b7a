import Heading from '@/components/ui/Heading'
import { ArrowTrendingUpIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'
import React from 'react'

export default function page() {
  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-1">
    <div className="w-full overflow-auto">
      <div className="p-3">
        <Heading pgHeading="Certificate" />
      </div>
      <div className="w-full gap-5 flex justify-between p-2">
          <div className="w-[300px] h-[300px] bg-white rounded-lg border-2 border-black p-2 flex flex-col justify-around items-center">
 {/* <ArrowTrendingUpIcon className="w-40 h-40  text-textColor border-4 border-textColor rounded-full p-5" /> */}
 <Image
              src="/certificate-svgrepo-com.svg"  // Corrected path
              width={160} // Adjusted for better visibility
              height={160}
              alt="Progress Icon"
              className="w-40 h-40"
            />

        <h1>An Introduction to Generative AI</h1>
            <button
              type="submit"
              className="w-full px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[15px] lg:text-[18px]"
            >
            <a
              href="/John Doe_Generative AI Fundamentals_certificate.pdf"  
              target="_blank"  // Opens the PDF in a new tab
              rel="noopener noreferrer"  // Security best practices// Path to your PDF file
              className="w-full px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[15px] lg:text-[18px] text-center"
            >
               View Certificate
            </a>
           
             
            </button>
          </div>
      </div>
      </div>
      </main>
  )
}
