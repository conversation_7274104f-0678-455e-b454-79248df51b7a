"use client";
import React, { useEffect, useState } from "react";
import Heading from "@/components/ui/Heading";
import LearningPath from "@/components/dashboard/skills/learning_plan/LearningPath";
import { useGetAllModulesForUser } from "@/hook/dashboard/program/useGetAllModulesForUser";
import { CheckCircleIcon, ClockIcon, ArrowPathIcon, BookOpenIcon } from "@heroicons/react/24/outline";

interface Module {
  module_id: number;
  group_id: number;
  sequence: number;
  module_name: string;
  module_headline: string;
  module_description: string;
  group_name: string;
  isactive: number;
  group_admin_user_id: number;
  created_by: string;
  completed: "completed" | "in_Progress" | "not_completed";
}

/**
 * LearningPlanPage displays a personalized learning plan based on skill gaps
 */
export default function LearningPlanPage() {
  const [modules, setModules] = useState<Module[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Sample modules data for development purposes
  const sampleModules: Module[] = [
    {
      "module_id": 94,
      "group_id": 1,
      "sequence": 1,
      "module_name": "Introduction to Basic Programming",
      "module_headline": "Understanding Basic Programming Concepts",
      "module_description": "This module covers the basics of programming, including variables, data types, and control structures.",
      "group_name": "Beginner Level Programming",
      "isactive": 1,
      "group_admin_user_id": 2,
      "created_by": "<EMAIL>",
      "completed": "in_Progress"
    },
    {
      "module_id": 95,
      "group_id": 2,
      "sequence": 2,
      "module_name": "Advanced Algorithms",
      "module_headline": "Delving Deeper into Algorithm Design",
      "module_description": "This module focuses on advanced algorithmic concepts such as sorting, searching, and optimization techniques.",
      "group_name": "Advanced Programming",
      "isactive": 0,
      "group_admin_user_id": 3,
      "created_by": "<EMAIL>",
      "completed": "not_completed"
    },
    {
      "module_id": 96,
      "group_id": 3,
      "sequence": 3,
      "module_name": "Machine Learning Foundations",
      "module_headline": "Intro to Machine Learning Concepts and Models",
      "module_description": "This module introduces the foundational concepts of machine learning, including supervised and unsupervised learning, and basic algorithms.",
      "group_name": "Expert Level Programming",
      "isactive": 1,
      "group_admin_user_id": 4,
      "created_by": "<EMAIL>",
      "completed": "in_Progress"
    },
    {
      "module_id": 97,
      "group_id": 4,
      "sequence": 4,
      "module_name": "Web Development Basics",
      "module_headline": "Building Your First Website with HTML, CSS, and JavaScript",
      "module_description": "Learn the fundamentals of web development, including HTML, CSS, and JavaScript for creating basic websites.",
      "group_name": "Beginner Web Development",
      "isactive": 1,
      "group_admin_user_id": 5,
      "created_by": "<EMAIL>",
      "completed": "completed"
    },
    {
      "module_id": 98,
      "group_id": 5,
      "sequence": 5,
      "module_name": "Intermediate Web Development",
      "module_headline": "Enhancing Websites with Advanced Techniques",
      "module_description": "This module covers more advanced web development topics such as responsive design, CSS frameworks, and JavaScript frameworks.",
      "group_name": "Intermediate Web Development",
      "isactive": 1,
      "group_admin_user_id": 6,
      "created_by": "<EMAIL>",
      "completed": "completed"
    },
    {
      "module_id": 99,
      "group_id": 6,
      "sequence": 6,
      "module_name": "Full-Stack Development",
      "module_headline": "Building End-to-End Applications with Modern Tools",
      "module_description": "Learn to develop full-stack applications by mastering both front-end and back-end technologies like React, Node.js, and MongoDB.",
      "group_name": "Expert Full-Stack Development",
      "isactive": 1,
      "group_admin_user_id": 7,
      "created_by": "<EMAIL>",
      "completed": "completed"
    }
  ];

  // Fetch user's modules from API
  const {
    data: modulesData,
    isLoading,
    isError,
    error: apiError,
    refetch
  } = useGetAllModulesForUser(1);

  useEffect(() => {
    // For development, immediately use sample modules instead of waiting for API
    setModules(sampleModules);
    setLoading(false);
    setError(null);
    
    // Keep the API fetch logic commented but present for when needed
    /*
    if (isLoading) {
      setLoading(true);
    } else if (isError) {
      setLoading(false);
      setError(apiError?.message || "Failed to load modules");
    } else {
      setLoading(false);
      setError(null);
      
      // Combine API data with sample modules
      const combinedModules = Array.isArray(modulesData) 
        ? [...modulesData, ...sampleModules]
        : [...sampleModules];
      
      // Remove duplicates based on module_id
      const uniqueModules = combinedModules.filter((module, index, self) =>
        index === self.findIndex((m) => m.module_id === module.module_id)
      );
      
      // Sort by sequence number
      const sortedModules = uniqueModules.sort((a, b) => a.sequence - b.sequence);
      
      setModules(sortedModules);
    }
    */
  }, []);

  // Calculate learning progress
  const completedModules = modules.filter(module => module.completed === "completed").length;
  const inProgressModules = modules.filter(module => module.completed === "in_Progress").length;
  const notStartedModules = modules.filter(module => module.completed === "not_completed").length;
  const totalModules = modules.length;
  const progressPercentage = totalModules > 0 
    ? Math.round((completedModules / totalModules) * 100) 
    : 0;

  // Group modules by status for potential filtering
  const getModulesByStatus = (status: Module['completed']) => {
    return modules.filter(module => module.completed === status);
  };

  // Mock refetch function for development
  const handleRefetch = () => {
    // In development mode, just reapply the sample data
    setModules([...sampleModules]);
  };

  // Render loading state
  if (loading) {
    return (
      <div className="w-full flex justify-center items-center p-8 min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="w-full flex flex-col justify-center items-center p-8 min-h-screen">
        <div className="text-red-500 mb-4">{error}</div>
        <button 
          onClick={handleRefetch} 
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <main className="w-full flex flex-col p-6" aria-label="Learning Path Page">
      {/* Header */}
      <div className="flex justify-between items-start sm:items-center mb-6 flex-col sm:flex-row">
        <div>
          <Heading pgHeading="Personalized Learning Plan" />
          <p className="text-gray-600 max-w-3xl mt-2">
            Your customized learning path based on the skill gaps identified in your assessment.
            Complete these modules to improve your skills for the selected role.
          </p>
        </div>
      </div>

      {/* Progress summary */}
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        <h2 className="text-lg font-medium text-gray-800 mb-4">Learning Progress</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div className="bg-green-50 rounded-lg p-4 border border-green-200 flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-green-500 mr-3" />
            <div>
              <div className="text-sm font-medium text-gray-500">Completed</div>
              <div className="text-xl font-bold text-gray-800">{completedModules} modules</div>
            </div>
          </div>
          
          <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200 flex items-center">
            <ClockIcon className="h-8 w-8 text-yellow-500 mr-3" />
            <div>
              <div className="text-sm font-medium text-gray-500">In Progress</div>
              <div className="text-xl font-bold text-gray-800">{inProgressModules} modules</div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 flex items-center">
            <BookOpenIcon className="h-8 w-8 text-gray-500 mr-3" />
            <div>
              <div className="text-sm font-medium text-gray-500">Not Started</div>
              <div className="text-xl font-bold text-gray-800">{notStartedModules} modules</div>
            </div>
          </div>
          
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200 flex items-center">
            <ArrowPathIcon className="h-8 w-8 text-blue-500 mr-3" />
            <div>
              <div className="text-sm font-medium text-gray-500">Overall Progress</div>
              <div className="text-xl font-bold text-gray-800">{progressPercentage}%</div>
            </div>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
          <div 
            className="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-in-out"
            style={{ width: `${progressPercentage}%` }}
            aria-valuenow={progressPercentage}
            aria-valuemin={0}
            aria-valuemax={100}
          />
        </div>
      </div>

      {/* Module lists */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="border-b border-gray-200">
          <div className="px-6 py-4">
            <h2 className="text-lg font-medium text-gray-800">Learning Modules</h2>
            <p className="text-sm text-gray-500">Complete these modules in sequence for optimal learning</p>
          </div>
        </div>
        
        <div>
          {modules.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8">
              <div className="text-gray-500 mb-4">No learning modules available</div>
              <button 
                onClick={handleRefetch} 
                className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Refresh
              </button>
            </div>
          ) : (
            <div className="space-y-3 p-3 sm:space-y-4 sm:p-4">
              {modules.map((module) => (
                <div key={module.module_id} className="bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                  <LearningPath moduleData={module} />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      
      {/* Action buttons */}
      <div className="flex justify-center mt-6">
        <button 
          onClick={() => window.location.href = '/dashboard/roles/assessment'} 
          className="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mr-4"
        >
          Take Another Assessment
        </button>
        <button
          onClick={handleRefetch}
          className="px-5 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors flex items-center"
        >
          <ArrowPathIcon className="h-4 w-4 mr-2" />
          Refresh Modules
        </button>
      </div>
    </main>
  );
}