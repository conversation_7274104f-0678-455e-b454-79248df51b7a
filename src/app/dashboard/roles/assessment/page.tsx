"use client";
import React, { useState } from "react";
import Heading from "@/components/ui/Heading";
import Button001 from "@/components/ui/Button001";
import InstructionModal from "@/components/ui/InstructionModal";
import { ClockIcon, DocumentTextIcon, AcademicCapIcon } from "@heroicons/react/24/outline";

/**
 * Assessment page component for user skills assessment
 * Improved for better responsiveness and modern UI
 */
export default function AssessmentPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <main className="w-full flex flex-col p-4 md:p-6">
      {/* Instructions Modal */}
      {isModalOpen && (
        <InstructionModal
          title="Assessment Instructions"
          paragraph={
            <>
              <p className="mb-6 text-lg">Welcome to the assessment! Please follow these instructions carefully:</p>
              <ul className="space-y-5">
                {[
                  <>The assessment consists of <strong>20 questions</strong>.</>,
                  <>You will have <strong>60 minutes</strong> to complete the test.</>,
                  "Read each question carefully before answering.",
                  "Once you start, the timer will begin, and you cannot pause or restart the test.",
                  "Ensure you have a stable internet connection to avoid interruptions.",
                  "Do not refresh the page or navigate away during the assessment, as it may result in losing your progress.",
                  <>Click <strong>Start Assessment</strong> when you are ready to begin.</>,
                  "Good luck! Do your best to demonstrate your knowledge and skills."
                ].map((item, index) => (
                  <li key={index} className="flex items-center">
                    <span className="text-blue-500 text-lg mr-3 flex-shrink-0 leading-none">•</span>
                    <span className="text-gray-800">{item}</span>
                  </li>
                ))}
              </ul>
            </>
          }
          closeModal={closeModal}
        />
      )}

      {/* Page Header - Fixed alignment */}
      <div className="mb-6 w-full max-w-3xl mx-auto">
        <Heading pgHeading="Skills Assessment" />
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-xl shadow-md p-6 md:p-8 flex-1 w-full max-w-3xl mx-auto">
        <div className="flex flex-col gap-8">
          {/* Assessment Description */}
          <div className="prose max-w-none">
            <p className="text-gray-700 leading-relaxed text-base md:text-lg">
              In order to assess your current knowledge and skills that overlap with the required skill set, 
              please take this assessment. The questions are all related to the set of skills and tools listed 
              in the skill master, and are designed to assess both your ability to recall appropriate tools, 
              techniques, and approaches as well as your ability to apply them correctly.
            </p>
          </div>

          {/* Assessment Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            <div className="bg-blue-50 rounded-lg p-4 md:p-6 flex items-center transition-all hover:shadow-sm">
              <ClockIcon className="h-8 w-8 text-blue-600 mr-4 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-lg text-gray-900">Test Duration</h3>
                <p className="text-gray-700">60 minutes</p>
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-4 md:p-6 flex items-center transition-all hover:shadow-sm">
              <DocumentTextIcon className="h-8 w-8 text-blue-600 mr-4 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-lg text-gray-900">Questions</h3>
                <p className="text-gray-700">20 questions</p>
              </div>
            </div>
          </div>

          {/* Instructions Note */}
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md shadow-sm">
            <div className="flex items-start">
              <AcademicCapIcon className="h-6 w-6 text-yellow-600 mr-3 flex-shrink-0 mt-0.5" />
              <p className="text-yellow-700">
                Please read the{" "}
                <button
                  onClick={openModal}
                  className="font-medium text-blue-600 hover:text-blue-800 underline transition-colors focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-offset-1 rounded"
                >
                  instructions
                </button>
                {" "}and questions carefully.
              </p>
            </div>
          </div>

          {/* Start Assessment Button */}
          <div className="flex justify-center mt-4">
            <Button001 
              href="/dashboard/take_assessment/2" 
              buttonName="Start Assessment" 
              //additionalClasses="px-6 py-3 text-base font-medium transition-transform hover:scale-105"
            />
          </div>
        </div>
      </div>
    </main>
  );
}