"use client";
import React, { useState } from "react";
import Heading from "@/components/ui/Heading";
import Button001 from "@/components/ui/Button001";
import { XMarkIcon, ChartBarIcon, PresentationChartLineIcon, AcademicCapIcon } from "@heroicons/react/24/outline";
import { CheckIcon, ExclamationTriangleIcon } from "@heroicons/react/24/solid";
import { useRouter } from 'next/navigation';

export default function AssessmentResultsPage() {
  const [showAssessment, setShowAssessment] = useState(true);
  const router = useRouter();

  // Skills data
  const assessmentResults = [
    { "Skill": "Business Intelligence", "Score": 80, "Expected": 100, "Gap": 20, "Sub-Skills": "KPI Development: 10%" },
    { "Skill": "Data Visualization", "Score": 82, "Expected": 100, "Gap": 18, "Sub-Skills": "Interactive Dashboards: 8%" },
    { "Skill": "Big Data Processing", "Score": 78, "Expected": 100, "Gap": 22, "Sub-Skills": "Distributed Computing: 7%" },
    { "Skill": "Scikit-learn (ML)", "Score": 83, "Expected": 100, "Gap": 17, "Sub-Skills": "Feature Engineering: 6%" },
    { "Skill": "Tableau", "Score": 85, "Expected": 100, "Gap": 15, "Sub-Skills": "" },
    { "Skill": "Machine Learning", "Score": 79, "Expected": 100, "Gap": 21, "Sub-Skills": "Regression Analysis: 7%" },
    { "Skill": "Data Management", "Score": 80, "Expected": 100, "Gap": 20, "Sub-Skills": "Data Cleaning: 10%, Missing Value Imputation: 5%, Data Transformation: 5%" },
    { "Skill": "Statistical Analysis", "Score": 81, "Expected": 100, "Gap": 19, "Sub-Skills": "Hypothesis Testing: 9%" },
    { "Skill": "Predictive Analytics", "Score": 79, "Expected": 100, "Gap": 21, "Sub-Skills": "Regression Analysis: 6%" },
    { "Skill": "Business Skills", "Score": 84, "Expected": 100, "Gap": 16, "Sub-Skills": "Domain Expertise: 4%, Communication Skills: 4%, Project Management: 4%, Ethics and Governance: 4%" },
  ];

  // Get background color based on score
  const getScoreColor = (score) => {
    if (score >= 80) return 'bg-green-100 text-green-800';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  // Get the background style for the score bar
  const getScoreBarStyle = (score) => {
    if (score >= 80) return { width: `${score}%`, backgroundColor: '#10B981' }; // Green
    if (score >= 60) return { width: `${score}%`, backgroundColor: '#F59E0B' }; // Yellow
    return { width: `${score}%`, backgroundColor: '#EF4444' }; // Red
  };

  // Calculate overall performance
  const calculateOverallScore = () => {
    const totalScore = assessmentResults.reduce((sum, item) => sum + item.Score, 0);
    return Math.round(totalScore / assessmentResults.length);
  };

  const overallScore = calculateOverallScore();
  const proficientSkills = assessmentResults.filter(item => item.Score >= 60).length;
  const gapSkills = assessmentResults.filter(item => item.Score < 60).length;

  return (
    <main className="w-full flex flex-col bg-gray-50 min-h-screen">
      <div className="w-full max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <div>
            <Heading pgHeading="Assessment Results" />
            <p className="mt-2 text-gray-600 max-w-3xl">
              Based on your assessment for this role, here are your scores for each required skill. 
              Scores above 60% indicate proficiency, while lower scores highlight areas for improvement.
            </p>
          </div>
          
          <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-3">
            <Button001
              buttonName={showAssessment ? "View Skill Gap Analysis" : "Show Assessment Results"}
              onClick={() => setShowAssessment(!showAssessment)}
            />
          
          </div>
        </div>
        
        {/* Performance Summary Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          {/* Overall Score Card */}
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="p-4 sm:p-5">
              <div className="flex items-center">
                <div className={`rounded-full p-2 sm:p-3 mr-3 sm:mr-4 ${getScoreColor(overallScore)}`}>
                  <ChartBarIcon className="h-5 w-5 sm:h-6 sm:w-6" />
                </div>
                <div>
                  <h3 className="text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">Overall Score</h3>
                  <div className="flex items-baseline">
                    <p className="text-2xl sm:text-3xl font-bold text-gray-900">{overallScore}%</p>
                    <span className="ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500">out of 100%</span>
                  </div>
                </div>
              </div>
              
              {/* Progress bar */}
              <div className="mt-3 sm:mt-4">
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div 
                    className="h-2.5 rounded-full" 
                    style={getScoreBarStyle(overallScore)}
                  ></div>
                </div>
                <div className="mt-1 text-xs text-right text-gray-500">
                  {overallScore >= 60 ? 'Satisfactory performance' : 'Needs improvement'}
                </div>
              </div>
            </div>
          </div>
          
          {/* Proficient Skills Card */}
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="p-4 sm:p-5">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-full p-2 sm:p-3 mr-3 sm:mr-4">
                  <CheckIcon className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">Proficient Skills</h3>
                  <div className="flex items-baseline">
                    <p className="text-2xl sm:text-3xl font-bold text-gray-900">{proficientSkills}</p>
                    <span className="ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500">out of {assessmentResults.length}</span>
                  </div>
                </div>
              </div>
              <p className="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-600">
                You've demonstrated proficiency in {proficientSkills} out of {assessmentResults.length} required skills for this role.
              </p>
            </div>
          </div>
          
          {/* Skill Gaps Card */}
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="p-4 sm:p-5">
              <div className="flex items-center">
                <div className="bg-yellow-100 rounded-full p-2 sm:p-3 mr-3 sm:mr-4">
                  <ExclamationTriangleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-600" />
                </div>
                <div>
                  <h3 className="text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">Skill Gaps</h3>
                  <div className="flex items-baseline">
                    <p className="text-2xl sm:text-3xl font-bold text-gray-900">{gapSkills}</p>
                    <span className="ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500">skills to improve</span>
                  </div>
                </div>
              </div>
              <p className="mt-3 sm:mt-4 text-xs sm:text-sm text-gray-600">
                {gapSkills > 0 
                  ? `We've identified ${gapSkills} skills that need improvement to meet role requirements.` 
                  : "Great job! You've met the requirements for all skills in this role."}
              </p>
            </div>
          </div>
        </div>
        
        {/* Main Content Area */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
          {showAssessment ? (
            <div className="p-4 sm:p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Detailed Skill Assessment</h2>
              
              {/* Desktop view: Table */}
              <div className="hidden md:block overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skill</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider lg:table-cell hidden">Gap</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {assessmentResults.map((result, index) => (
                      <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                        <td className="px-4 py-3 text-sm font-medium text-gray-900">{result.Skill}</td>
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className="h-2 rounded-full" 
                                style={getScoreBarStyle(result.Score)}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-700">{result.Score}%</span>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-700">{result.Expected}%</td>
                        <td className="px-4 py-3 text-sm text-gray-700 lg:table-cell hidden">{result.Gap}%</td>
                        <td className="px-4 py-3">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getScoreColor(result.Score)}`}>
                            {result.Score >= 60 ? 'Proficient' : 'Gap'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Mobile view: Card-based layout */}
              <div className="md:hidden">
                <div className="grid grid-cols-2 gap-2 mb-4">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Score</div>
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Status</div>
                </div>
                <div className="space-y-3">
                  {assessmentResults.map((result, index) => (
                    <div key={index} className="bg-white rounded-lg border border-gray-100 p-3">
                      <h3 className="text-sm font-medium text-gray-900 mb-2 truncate">{result.Skill}</h3>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <div className="flex items-center">
                            <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className="h-2 rounded-full" 
                                style={getScoreBarStyle(result.Score)}
                              ></div>
                            </div>
                          </div>
                          <div className="text-xs mt-1 font-medium">{result.Score}%</div>
                        </div>
                        <div className="flex items-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getScoreColor(result.Score)}`}>
                            {result.Score >= 60 ? 'Proficient' : 'Gap'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="h-[70vh]">
              <iframe
                src="http://52.172.41.51:8066/"
                style={{
                  width: '100%',
                  height: '100%',
                  border: 'none',
                  borderRadius: '10px',
                  overflow: 'hidden',
                }}
                title="Skill Gap Analysis"
              ></iframe>
            </div>
          )}
        </div>
        
        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <button
            onClick={() => router.push('/dashboard/roles/skill_gap')}
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PresentationChartLineIcon className="h-5 w-5 mr-2" />
            View Detailed Skill Gap
          </button>
          
          <button
            onClick={() => router.push('/dashboard/roles/learning_plan')}
            className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <AcademicCapIcon className="h-5 w-5 mr-2" />
            Create Learning Plan
          </button>
        </div>
      </div>
    </main>
  );
}