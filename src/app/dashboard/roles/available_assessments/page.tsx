"use client";
import React, { useState } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Heading from "@/components/ui/Heading";
import Nothing from '@/components/ui/Nothing';
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

// Import components
import SearchFilterBar from "@/components/dashboard/roles/assessments/SearchFilterBar";
import SectorSection from "@/components/dashboard/roles/assessments/SectorSection";
import { Assessment } from "@/components/dashboard/roles/assessments/types";

// Dummy assessment data
const assessmentsData: Assessment[] = [
  {
    id: 1,
    title: "Data Analyst Assessment",
    description: "Evaluate your data analysis skills including SQL, statistical methods, and data visualization techniques.",
    sector: "Technology",
    domain: "Data Science",
    duration: 60,
    questionCount: 25,
    difficulty: "intermediate",
    requiredSkills: ["SQL", "Statistics", "Data Visualization", "Excel", "Python"]
  },
  {
    id: 2,
    title: "Software Development Fundamentals",
    description: "Test your knowledge of software development principles, algorithms, and coding best practices.",
    sector: "Technology",
    domain: "Software Development",
    duration: 75,
    questionCount: 30,
    difficulty: "intermediate",
    requiredSkills: ["Algorithms", "Data Structures", "Programming Logic", "Software Design"]
  },
  {
    id: 3,
    title: "Cloud Computing & DevOps",
    description: "Assess your understanding of cloud infrastructure, deployment pipelines, and DevOps methodologies.",
    sector: "Technology",
    domain: "Cloud Computing",
    duration: 60,
    questionCount: 25,
    difficulty: "advanced",
    requiredSkills: ["AWS/Azure", "CI/CD", "Infrastructure as Code", "Containerization"]
  },
  {
    id: 4,
    title: "Web Development Skills",
    description: "Evaluate your web development capabilities including HTML, CSS, JavaScript and modern frameworks.",
    sector: "Technology",
    domain: "Software Development",
    duration: 60,
    questionCount: 25,
    difficulty: "beginner",
    requiredSkills: ["HTML", "CSS", "JavaScript", "React/Angular/Vue", "Responsive Design"]
  },
  {
    id: 5,
    title: "Machine Learning Fundamentals",
    description: "Test your understanding of machine learning algorithms, model development, and evaluation metrics.",
    sector: "Technology",
    domain: "Data Science",
    duration: 90,
    questionCount: 30,
    difficulty: "advanced",
    requiredSkills: ["Python", "Statistical Modeling", "ML Algorithms", "Feature Engineering"]
  },
  {
    id: 6,
    title: "Cybersecurity Skills Assessment",
    description: "Evaluate your knowledge of security principles, threat identification, and security best practices.",
    sector: "Technology",
    domain: "Cybersecurity",
    duration: 60,
    questionCount: 25,
    difficulty: "intermediate",
    requiredSkills: ["Network Security", "Threat Analysis", "Security Protocols", "Risk Assessment"]
  },
  {
    id: 7,
    title: "Clinical Research Fundamentals",
    description: "Assess your understanding of clinical trial protocols, medical terminology, and research methodologies.",
    sector: "Healthcare",
    domain: "Clinical Research",
    duration: 60,
    questionCount: 25,
    difficulty: "intermediate",
    requiredSkills: ["Clinical Protocols", "Medical Terminology", "Research Ethics", "Data Collection"]
  },
  {
    id: 8,
    title: "Healthcare Administration Assessment",
    description: "Test your knowledge of healthcare systems, policies, regulations, and administrative procedures.",
    sector: "Healthcare",
    domain: "Healthcare Administration",
    duration: 45,
    questionCount: 20,
    difficulty: "beginner",
    requiredSkills: ["Healthcare Regulations", "Medical Records", "Patient Care", "Healthcare Systems"]
  },
  {
    id: 9,
    title: "Financial Analysis Skills",
    description: "Evaluate your understanding of financial models, analysis techniques, and forecasting methods.",
    sector: "Finance",
    domain: "Financial Analysis",
    duration: 75,
    questionCount: 30,
    difficulty: "advanced",
    requiredSkills: ["Financial Modeling", "Valuation", "Risk Analysis", "Excel", "Financial Statements"]
  },
  {
    id: 10,
    title: "Investment Banking Fundamentals",
    description: "Test your knowledge of capital markets, M&A transactions, and valuation methodologies.",
    sector: "Finance",
    domain: "Investment Banking",
    duration: 60,
    questionCount: 25,
    difficulty: "intermediate",
    requiredSkills: ["Capital Markets", "M&A", "Financial Modeling", "Valuation", "Excel"]
  }
];

// Get unique sectors and domains from data
const sectors = Array.from(new Set(assessmentsData.map(assessment => assessment.sector)));
const domains = Array.from(new Set(assessmentsData.map(assessment => assessment.domain)));

/**
 * AvailableAssessmentsPage displays assessments based on selected sectors and domains
 */
export default function AvailableAssessmentsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get job title from URL params if available
  const jobTitle = searchParams.get('job_title') || 'Selected Role';
  
  // State for filters and search
  const [sectorFilter, setSectorFilter] = useState<string>('');
  const [domainFilter, setDomainFilter] = useState<string>('');
  const [difficultyFilter, setDifficultyFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  
  // Apply filters to assessments data
  const filteredAssessments = assessmentsData.filter(assessment => {
    // Apply search query filter
    if (searchQuery && !assessment.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !assessment.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Apply sector filter
    if (sectorFilter && assessment.sector !== sectorFilter) {
      return false;
    }
    
    // Apply domain filter
    if (domainFilter && assessment.domain !== domainFilter) {
      return false;
    }
    
    // Apply difficulty filter
    if (difficultyFilter && assessment.difficulty !== difficultyFilter) {
      return false;
    }
    
    return true;
  });
  
  // Handle assessment selection
  const handleSelectAssessment = (assessmentId: number) => {
    router.push(`/dashboard/roles/assessment?assessment_id=${assessmentId}`);
  };
  
  // Reset all filters
  const resetFilters = () => {
    setSectorFilter('');
    setDomainFilter('');
    setDifficultyFilter('');
    setSearchQuery('');
  };
  
  // Get sectors that have assessments after filtering
  const filteredSectors = sectors.filter(sector => 
    filteredAssessments.some(assessment => assessment.sector === sector)
  );
  
  // Get domains for a specific sector that have assessments after filtering
  const getDomainsForSector = (sector: string) => {
    return domains.filter(domain => 
      filteredAssessments.some(assessment => assessment.sector === sector && assessment.domain === domain)
    );
  };

  return (
    <main className="w-full flex flex-col bg-gray-50 min-h-screen p-4 sm:p-6">
      {/* Header section */}
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <Heading pgHeading={`Available Assessments for ${jobTitle}`} />
          <p className="text-gray-600 mt-2 max-w-3xl">
            Select an assessment below to evaluate your skills and identify gaps for your selected role.
          </p>
        </div>
        
        <Link href="/dashboard/roles" className="mt-4 sm:mt-0">
          <button className="flex items-center justify-center px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 transition-colors">
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Roles
          </button>
        </Link>
      </div>
      
      {/* Search and filter section */}
      <SearchFilterBar
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        sectorFilter={sectorFilter}
        setSectorFilter={setSectorFilter}
        domainFilter={domainFilter}
        setDomainFilter={setDomainFilter}
        difficultyFilter={difficultyFilter}
        setDifficultyFilter={setDifficultyFilter}
        resetFilters={resetFilters}
        sectors={sectors}
        domains={domains}
        showFilters={showFilters}
        setShowFilters={setShowFilters}
      />
      
      {/* Assessments list */}
      <div className="flex-1">
        {filteredAssessments.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-12">
            <Nothing
              title="No Assessments Found"
              para="No assessments match your search criteria. Try adjusting your filters or search terms."
            />
          </div>
        ) : (
          <div className="space-y-8">
            {/* Group assessments by sector */}
            {filteredSectors.map(sector => (
              <SectorSection
                key={sector}
                sector={sector}
                domains={getDomainsForSector(sector)}
                assessments={filteredAssessments.filter(a => a.sector === sector)}
                onSelectAssessment={handleSelectAssessment}
              />
            ))}
          </div>
        )}
      </div>
    </main>
  );
}