"use client"
import React, { useState } from 'react';
import Link from 'next/link';
import Heading from '@/components/ui/Heading';
import Nothing from '@/components/ui/Nothing';
import { ArrowLeftIcon, XMarkIcon } from '@heroicons/react/24/outline';
import JobSavedCard from '@/components/dashboard/roles/saved/JobSavedCard';

// Mock data for saved roles
const rolesData = [
  { 
    id: '1', 
    description: 'This Software Engineer role focuses on developing and maintaining web applications using React, Node.js, and modern front-end technologies. The ideal candidate will have strong JavaScript skills and experience with responsive design principles.' 
  },
  { 
    id: '2', 
    description: 'As a Data Scientist, you will analyze complex datasets to extract valuable insights. Experience with Python, R, and machine learning frameworks is required.' 
  },
  { 
    id: '3', 
    description: 'The Product Manager role involves working closely with engineering, design, and business teams to define and deliver outstanding product experiences. Strong analytical skills and user empathy are essential. You will be responsible for the product roadmap and ensuring timely delivery of features.' 
  },
];

// JobPopup component for detailed job information - improved responsiveness
const JobPopup = ({ job, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg overflow-auto max-h-[90vh]">
        <div className="sticky top-0 bg-white px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-800">Job Details</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 p-1"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        
        <div className="px-6 py-4 space-y-6">
          <div>
            <p className="font-medium text-gray-700 mb-1">Job ID:</p>
            <p className="text-gray-800">{job.id}</p>
          </div>
          
          <div>
            <p className="font-medium text-gray-700 mb-1">CV Uploaded:</p>
            <p className="text-gray-800">Yes</p>
          </div>
          
          <div>
            <p className="font-medium text-gray-700 mb-1">Description:</p>
            <p className="text-gray-700 whitespace-pre-line">{job.description}</p>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Application Questions:</h3>
            <ul className="list-disc pl-5 space-y-2 text-gray-600">
              <li>Why are you interested in this position?</li>
              <li>What relevant experience do you have?</li>
              <li>Where do you see yourself in 5 years?</li>
            </ul>
          </div>
        </div>
        
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <button 
            onClick={onClose} 
            className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * SavedRolesPage displays roles that the user has saved
 */
export default function SavedRolesPage() {
  const [selectedJob, setSelectedJob] = useState(null);
  const [selectedJobIds, setSelectedJobIds] = useState<string[]>([]);

  const handleJobClick = (job) => {
    setSelectedJob(job);
  };

  const handleClosePopup = () => {
    setSelectedJob(null);
  };

  const handleRoleSelect = (jobId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedJobIds([...selectedJobIds, jobId]);
    } else {
      setSelectedJobIds(selectedJobIds.filter((id) => id !== jobId));
    }
  };

  return (
    <main className="w-full flex flex-col p-4 sm:p-6">
      {/* Header with back button */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <Heading pgHeading="Saved Roles" />
        <Link href="/dashboard/roles" className="mt-2 sm:mt-0">
          <button className="flex items-center justify-center px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 transition-colors">
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Browse Roles
          </button>
        </Link>
      </div>

      {/* Main content area */}
      <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 flex-1 min-h-[70vh]">
        {(!rolesData || rolesData.length === 0) ? (
          <div className="h-full flex justify-center items-center">
            <Nothing
              title="No Saved Roles"
              para="You haven't saved any job roles yet. Browse available roles and save the ones you're interested in."
            />
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {rolesData.map((job) => (
              <JobSavedCard
                key={job.id}
                jobDescription={job.description}
                jobId={job.id}
                onSelect={handleRoleSelect}
                onClick={() => handleJobClick(job)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Action button */}
      {rolesData && rolesData.length > 0 && (
        <div className="mt-6 flex justify-center">
          <Link href="/dashboard/roles/cvmatch">
            <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              Match CV with Selected Roles
            </button>
          </Link>
        </div>
      )}

      {/* Job details popup */}
      {selectedJob && (
        <JobPopup job={selectedJob} onClose={handleClosePopup} />
      )}
    </main>
  );
}