"use client"
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Heading from '@/components/ui/Heading';
import Nothing from '@/components/ui/Nothing';
import JobCard from '@/components/dashboard/roles/JobCard';
import { MagnifyingGlassIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline';
import SectorSelectionModal from '@/components/dashboard/roles/SectorSelectionModal';
import UserJourneyFlow from '@/components/dashboard/roles/UserJourneyFlow';

interface Job {
  job_title_id: number;
  job_title: string;
  job_description?: string;
}

// Sample data for development and testing
const rolesData = [
  {
    job_title_id: 1,
    job_title: "Data Analyst",
    job_description: "Collect description "
  },
  {
    job_title_id: 2,
    job_title: "Clinical Research Associate",
    job_description: "Monitor clinical trials to ensure compliance with protocols and regulations. Review and evaluate clinical data from healthcare research studies."
  },
  {
    job_title_id: 3,
    job_title: "Front-End Developer",
    job_description: "Design and implement responsive user interfaces using modern frameworks like <PERSON>act, Angular, or Vue. Create seamless user experiences and collaborate with designers and back-end developers."
  },
  {
    job_title_id: 4,
    job_title: "DevOps Engineer",
    job_description: "Build and maintain CI/CD pipelines, manage cloud infrastructure, and optimize deployment processes. Ensure system reliability, scalability, and security across development and production environments."
  },
  {
    job_title_id: 5,
    job_title: "Financial Analyst",
    job_description: "Analyze financial data, create financial models, and provide insights to support business decisions. Prepare reports and forecasts to guide strategic planning."
  },
  {
    job_title_id: 6,
    job_title: "Healthcare Administrator",
    job_description: "Manage healthcare facilities, implement policies, and ensure regulatory compliance. Oversee staff, budgets, and coordinate with medical professionals."
  },
];

// Card background colors
const COLORS = [
  'bg-blue-100',
  'bg-green-100', 
  'bg-purple-100', 
  'bg-yellow-100',
  'bg-pink-100',
  'bg-indigo-100'
];

// Sample sectors data with their domains
const sectorData = [
  { 
    id: '1', 
    name: 'Healthcare',
    domains: [
      { id: '101', name: 'Clinical Research' },
      { id: '102', name: 'Healthcare Administration' },
      { id: '103', name: 'Medical Practice' },
      { id: '104', name: 'Public Health' },
      { id: '105', name: 'Pharmaceuticals' }
    ]
  },
  { 
    id: '2', 
    name: 'Technology',
    domains: [
      { id: '201', name: 'Software Development' },
      { id: '202', name: 'Data Science' },
      { id: '203', name: 'Cybersecurity' },
      { id: '204', name: 'Cloud Computing' },
      { id: '205', name: 'Artificial Intelligence' }
    ]
  },
  { 
    id: '3', 
    name: 'Finance',
    domains: [
      { id: '301', name: 'Investment Banking' },
      { id: '302', name: 'Financial Analysis' },
      { id: '303', name: 'Risk Management' },
      { id: '304', name: 'Asset Management' },
      { id: '305', name: 'Corporate Finance' }
    ]
  },
  { 
    id: '4', 
    name: 'Manufacturing',
    domains: [
      { id: '401', name: 'Process Engineering' },
      { id: '402', name: 'Quality Control' },
      { id: '403', name: 'Supply Chain' },
      { id: '404', name: 'Production Planning' },
      { id: '405', name: 'Industrial Design' }
    ]
  },
  { 
    id: '5', 
    name: 'Education',
    domains: [
      { id: '501', name: 'K-12 Education' },
      { id: '502', name: 'Higher Education' },
      { id: '503', name: 'Educational Technology' },
      { id: '504', name: 'Special Education' },
      { id: '505', name: 'Educational Administration' }
    ]
  },
  // Add more sectors as needed
];

/**
 * RolesPage displays available job roles with sector and domain selection
 */
export default function RolesPage() {
  const router = useRouter();
  const [selectedJobIds, setSelectedJobIds] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentJobId, setCurrentJobId] = useState<number | null>(null);
  const [isJourneyExpanded, setIsJourneyExpanded] = useState(true);
  
  // State for sectors and domains
  const [selectedSectors, setSelectedSectors] = useState<{[key: number]: string[]}>({});
  const [selectedDomains, setSelectedDomains] = useState<{[key: number]: {[sectorId: string]: string[]}}>({}); 
  
  // Handle role selection
  const handleRoleSelection = (jobId: number) => {
    setSelectedJobIds(prevSelectedIds => {
      if (prevSelectedIds.includes(jobId)) {
        return prevSelectedIds.filter(id => id !== jobId);
      }
      if (prevSelectedIds.length >= 3) {
        return prevSelectedIds;
      }
      return [...prevSelectedIds, jobId];
    });
  };

  // Handle opening sector modal for a job
  const handleOpenSectorModal = (jobId: number) => {
    setCurrentJobId(jobId);
    setIsModalOpen(true);
  };

  // Handle sector selection within modal
  const handleSectorSelect = (sectorId: string) => {
    if (currentJobId === null) return;
    
    setSelectedSectors(prev => {
      const currentSectors = prev[currentJobId] || [];
      
      // If sector is already selected, remove it (toggling)
      if (currentSectors.includes(sectorId)) {
        // Also remove any domains for this sector
        setSelectedDomains(prevDomains => {
          const jobDomains = {...(prevDomains[currentJobId] || {})};
          delete jobDomains[sectorId];
          
          return {
            ...prevDomains,
            [currentJobId]: jobDomains
          };
        });
        
        return {
          ...prev,
          [currentJobId]: currentSectors.filter(id => id !== sectorId)
        };
      } 
      // Otherwise add the sector
      else {
        return {
          ...prev,
          [currentJobId]: [...currentSectors, sectorId]
        };
      }
    });
  };

  // Handle domain selection within modal
  const handleDomainSelect = (sectorId: string, domainId: string) => {
    if (currentJobId === null) return;
    
    setSelectedDomains(prev => {
      const jobDomains = {...(prev[currentJobId] || {})};
      const sectorDomains = [...(jobDomains[sectorId] || [])];
      
      // Toggle domain selection
      if (sectorDomains.includes(domainId)) {
        const updatedDomains = sectorDomains.filter(id => id !== domainId);
        
        return {
          ...prev,
          [currentJobId]: {
            ...jobDomains,
            [sectorId]: updatedDomains
          }
        };
      } else {
        return {
          ...prev,
          [currentJobId]: {
            ...jobDomains,
            [sectorId]: [...sectorDomains, domainId]
          }
        };
      }
    });
  };

  // Check if job has sectors selected
  const hasSectorsSelected = (jobId: number): boolean => {
    return !!selectedSectors[jobId] && selectedSectors[jobId].length > 0;
  };

  // Get selected sector names for a job
  const getSelectedSectorNames = (jobId: number): string[] => {
    const sectorIds = selectedSectors[jobId] || [];
    return sectorIds.map(id => {
      const sector = sectorData.find(s => s.id === id);
      return sector ? sector.name : "";
    }).filter(Boolean);
  };

  // Get selected domains by sector name for a job
  const getSelectedDomainsByTitle = (jobId: number): {[sectorName: string]: string[]} => {
    const result: {[sectorName: string]: string[]} = {};
    const jobDomains = selectedDomains[jobId] || {};
    
    // For each sector with selected domains
    Object.keys(jobDomains).forEach(sectorId => {
      const sector = sectorData.find(s => s.id === sectorId);
      if (!sector) return;
      
      const domainIds = jobDomains[sectorId] || [];
      if (domainIds.length === 0) return;
      
      // Find domain names for the selected domain IDs
      const domainNames = domainIds.map(domainId => {
        const domain = sector.domains.find(d => d.id === domainId);
        return domain ? domain.name : "";
      }).filter(Boolean);
      
      if (domainNames.length > 0) {
        result[sector.name] = domainNames;
      }
    });
    
    return result;
  };

  // Navigate to role details page
  const handleViewDetails = (jobTitle: string) => {
    router.push(`/dashboard/roles/skill_master?job_title=${encodeURIComponent(jobTitle)}`);
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Filter roles based on search query
  const filteredRoles = rolesData.filter(role => 
    role.job_title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Find job title by ID
  const getJobTitle = (jobId: number): string => {
    const job = rolesData.find(r => r.job_title_id === jobId);
    return job ? job.job_title : "Selected Role";
  };

  return (
    <main className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
      {/* Sector and Domain Selection Modal */}
      {currentJobId !== null && (
        <SectorSelectionModal 
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSectorSelect={handleSectorSelect}
          onDomainSelect={handleDomainSelect}
          selectedSectors={selectedSectors[currentJobId] || []}
          selectedDomains={selectedDomains[currentJobId] || {}}
          jobTitle={getJobTitle(currentJobId)}
        />
      )}
      
      {/* Page header */}
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <Heading pgHeading="Roles" />
            <p className="text-gray-600 mt-1">
              Browse available roles, select sectors and domains for each role, and view details
            </p>
          </div>
          
          {/* Search input */}
          <div className="relative w-full sm:w-64 md:w-72">
            <input
              type="text"
              placeholder="Search roles..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
        </div>
        
        {/* User Journey Flow - How It Works Section - Always shown, with toggle for details */}
        <UserJourneyFlow 
          isExpanded={isJourneyExpanded} 
          onToggle={() => setIsJourneyExpanded(!isJourneyExpanded)} 
        />
        
        {/* Roles grid */}
        {filteredRoles.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-12">
            <Nothing
              title="No Roles Found"
              para="No roles matching your search criteria were found. Try adjusting your search terms."
            />
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRoles.map((job, index) => (
              <JobCard
                key={job.job_title_id}
                job={job}
                index={index}
                colors={COLORS}
                selected={selectedJobIds.includes(job.job_title_id)}
                onClick={() => handleRoleSelection(job.job_title_id)}
                onSectorSelect={() => handleOpenSectorModal(job.job_title_id)}
                onViewDetails={() => handleViewDetails(job.job_title)}
                hasSectorsSelected={hasSectorsSelected(job.job_title_id)}
                selectedSectorNames={getSelectedSectorNames(job.job_title_id)}
                selectedDomainsByTitle={getSelectedDomainsByTitle(job.job_title_id)}
              />
            ))}
          </div>
        )}
      </div>
    </main>
  );
}