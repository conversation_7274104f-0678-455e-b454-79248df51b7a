"use client";
import React, { useEffect, useState } from "react";
import { useSearchParams } from 'next/navigation';
import Heading from "@/components/ui/Heading";
import Link from "next/link";
import { 
  ChevronDownIcon, 
  ChevronUpIcon, 
  AcademicCapIcon, 
  ArrowLeftIcon,
  LightBulbIcon,
  InformationCircleIcon
} from "@heroicons/react/24/outline";
import SkillFeedbackComponent from "@/components/dashboard/roles/SkillFeedback";

/**
 * SkillLevelPage - skills display for career progression with feedback capabilities
 */
export default function SkillLevelPage() {
  // Routing and state management
  const searchParams = useSearchParams();
  const jobTitle = searchParams.get('job_title') || 'Selected Role';
  const [isAssessmentCompleted, setIsAssessmentCompleted] = useState(false);
  const [showSkillGap, setShowSkillGap] = useState(false);
  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({
    "Technical Skills": true,
    "Business & Ethical Considerations": true
  });
  const [expandedSubskills, setExpandedSubskills] = useState<{[key: string]: boolean}>({});
  const [feedbackSubmitted, setFeedbackSubmitted] = useState<{[key: string]: boolean}>({});
  const [showFeedbackSuccess, setShowFeedbackSuccess] = useState(false);

  // Check assessment completion status
  useEffect(() => {
    // Check if assessment was completed
    const completed = sessionStorage.getItem('assessmentCompleted');
    if (completed === 'true') {
      setIsAssessmentCompleted(true);
    }
  }, []);

  // Toggle main section expansion
  const toggleSection = (sectionName: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionName]: !prev[sectionName]
    }));
  };

  // Toggle subskill expansion
  const toggleSubskill = (subskillName: string) => {
    setExpandedSubskills(prev => ({
      ...prev,
      [subskillName]: !prev[subskillName]
    }));
  };

  // Handle feedback submission
  const handleFeedbackSubmit = (feedback) => {
    console.log("Feedback submitted:", feedback);
    
    // In a real implementation, this would send the feedback to your API
    // For now, just mark this skill as having feedback submitted
    setFeedbackSubmitted(prev => ({
      ...prev,
      [feedback.skillName]: true
    }));
    
    // Show success notification
    setShowFeedbackSuccess(true);
    setTimeout(() => setShowFeedbackSuccess(false), 3000);
  };

  // Skills data structure
  const skillsData = {
    "Technical Skills": {
      "level": "Advanced",
      "description": "Proficiency in advanced AI and data science techniques",
      "subskills": {
        "Machine Learning": {
          "level": "Advanced",
          "description": "Developing complex predictive models and AI solutions",
          "subskills": {
            "Supervised Learning": {
              "level": "Advanced",
              "description": "Implementing regression, classification, and ensemble learning methods"
            },
            "Unsupervised Learning": {
              "level": "Intermediate",
              "description": "Applying clustering, anomaly detection, and dimensionality reduction techniques"
            },
            "Deep Learning": {
              "level": "Advanced",
              "description": "Building and training neural networks for NLP, computer vision, and tabular data"
            }
          }
        },
        "Natural Language Processing (NLP)": {
          "level": "Intermediate",
          "description": "Working with text data and language models",
          "subskills": {
            "Transformers": {
              "level": "Advanced",
              "description": "Fine-tuning transformer models like BERT and GPT"
            }
          }
        },
        "Computer Vision": {
          "level": "Intermediate",
          "description": "Extracting insights from images and videos using CNNs and transformer-based models"
        },
        "MLOps & AI Deployment": {
          "level": "Advanced",
          "description": "Automating ML workflows, model monitoring, and deploying scalable AI solutions"
        }
      }
    },
    "Business & Ethical Considerations": {
      "level": "Intermediate",
      "description": "Understanding AI ethics, governance, and industry applications",
      "subskills": {
        "AI Governance": {
          "level": "Intermediate",
          "description": "Ensuring fairness, transparency, and accountability in AI-driven decision-making"
        },
        "AI Regulatory Compliance": {
          "level": "Intermediate",
          "description": "Adhering to regulations and standards in AI development and deployment"
        }
      }
    }
  };

  // Level badge styling
  const getLevelBadgeClasses = (level: string) => {
    switch(level) {
      case 'Advanced':
        return 'bg-blue-100 text-blue-800';
      case 'Intermediate':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  // Get icon background color based on section type
  const getSectionIconBg = (section: string) => {
    if (section === "Technical Skills") {
      return "bg-blue-50";
    } else if (section === "Business & Ethical Considerations") {
      return "bg-green-50";
    }
    return "bg-gray-50";
  };

  // Render subskills for both desktop and mobile views
  const renderSubskills = (subskills: any, parentName: string) => {
    if (!subskills) return null;
    
    return (
      <div className="ml-0 mt-4">
        <div className="mb-2 font-medium text-gray-700">Subskills:</div>
        {Object.entries(subskills).map(([subskillName, details]: [string, any]) => {
          const fullName = `${parentName}-${subskillName}`;
          const hasNestedSubskills = details.subskills && Object.keys(details.subskills).length > 0;
          
          return (
            <div key={fullName} className="border border-gray-100 rounded-md mb-3 overflow-hidden">
              <div 
                className={`flex items-center justify-between p-3 ${hasNestedSubskills ? 'cursor-pointer hover:bg-gray-50' : ''}`}
                onClick={() => hasNestedSubskills && toggleSubskill(fullName)}
              >
                <div className="flex-1">
                  <div className="font-medium text-gray-800">{subskillName}</div>
                  <div className="text-sm text-gray-600">{details.description}</div>
                  
                  {/* Add feedback indicator if feedback has been submitted */}
                  {feedbackSubmitted[subskillName] && (
                    <div className="mt-1 flex items-center text-xs text-blue-600">
                      <InformationCircleIcon className="h-4 w-4 mr-1" />
                      <span>Feedback submitted</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center ml-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getLevelBadgeClasses(details.level)}`}>
                    {details.level}
                  </span>
                  {hasNestedSubskills && (
                    expandedSubskills[fullName] ? 
                      <ChevronUpIcon className="h-5 w-5 text-gray-500 ml-2" /> : 
                      <ChevronDownIcon className="h-5 w-5 text-gray-500 ml-2" />
                  )}
                </div>
              </div>
              
              {/* Add feedback component for this skill */}
              <div className="px-3 pb-2">
                <SkillFeedbackComponent 
                  skillName={subskillName} 
                  onSubmit={handleFeedbackSubmit} 
                />
              </div>
              
              {hasNestedSubskills && expandedSubskills[fullName] && (
                <div className="p-3 pt-0 border-t border-gray-100 bg-gray-50">
                  {renderSubskills(details.subskills, fullName)}
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <main className="w-full flex flex-col min-h-screen bg-gray-50">
      {/* Success notification */}
      {showFeedbackSuccess && (
        <div className="fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg shadow-md p-4 z-50 flex items-center animate-fade-in">
          <div className="bg-green-100 rounded-full p-2 mr-3">
            <svg className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <p className="text-green-800">Your skill feedback has been received!</p>
        </div>
      )}
      
      {/* Header section */}
      <div className="px-4 md:px-8 py-6 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center">
            <Link href="/dashboard/roles" className="mr-3">
              <button className="p-2 rounded-full hover:bg-gray-100">
                <ArrowLeftIcon className="h-5 w-5 text-gray-500" />
              </button>
            </Link>
            <Heading pgHeading={`Skill Master for ${jobTitle}`} />
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 px-4 md:px-8 py-6">
        <div className="max-w-7xl mx-auto">
          {/* Top summary card */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
            <div className="flex items-start">
              <div className="bg-blue-50 p-3 rounded-lg mr-4 flex-shrink-0">
                <LightBulbIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-gray-700 mb-2">
                  This page outlines the skills needed for success in the {jobTitle} role. 
                  Review the required skills and take the assessment to identify your personal skill gaps.
                </p>
                <p className="text-blue-600 text-sm flex items-center">
                  <InformationCircleIcon className="h-4 w-4 mr-1" />
                  You can now provide feedback if you don't know a skill or have limited knowledge!
                </p>
              </div>
            </div>
          </div>
          
          {/* Content container */}
          {!showSkillGap ? (
            <div className="space-y-6">
              {Object.entries(skillsData).map(([sectionName, sectionDetails]: [string, any]) => (
                <div key={sectionName} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                  <div 
                    className="flex items-center justify-between p-4 cursor-pointer"
                    onClick={() => toggleSection(sectionName)}
                  >
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg mr-3 ${getSectionIconBg(sectionName)}`}>
                        <AcademicCapIcon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="font-medium text-lg text-gray-800">{sectionName}</div>
                    </div>
                    <div className="flex items-center">
                      <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getLevelBadgeClasses(sectionDetails.level)}`}>
                        {sectionDetails.level}
                      </span>
                      {expandedSections[sectionName] ? (
                        <ChevronUpIcon className="h-5 w-5 text-gray-500 ml-3" />
                      ) : (
                        <ChevronDownIcon className="h-5 w-5 text-gray-500 ml-3" />
                      )}
                    </div>
                  </div>
                  
                  {expandedSections[sectionName] && (
                    <div className="p-4 border-t border-gray-100">
                      <div className="text-gray-700 mb-4">{sectionDetails.description}</div>
                      {sectionDetails.subskills && renderSubskills(sectionDetails.subskills, sectionName)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-200">
              <iframe
                src="http://************:8066/"
                className="w-full h-[70vh] md:h-[75vh] border-none"
                title="Skill Gap Analysis"
              />
            </div>
          )}
        </div>
      </div>

      {/* Footer - Action Buttons */}
      <div className="sticky bottom-0 w-full bg-white border-t border-gray-200 p-4 md:p-6 mt-auto">
        <div className="max-w-7xl mx-auto flex flex-wrap justify-center items-center gap-4">
          <Link href="/dashboard/roles/available_assessments" className="w-full sm:w-auto">
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors flex items-center justify-center">
              Take Assessment
            </button>
          </Link>
          <Link href="/dashboard/roles" className="w-full sm:w-auto">
            <button className="w-full border border-gray-300 bg-white text-gray-700 font-medium py-3 px-6 rounded-md hover:bg-gray-50 transition-colors flex items-center justify-center">
              Select Another Role
            </button>
          </Link>
          {isAssessmentCompleted && (
            <button
              onClick={() => setShowSkillGap(!showSkillGap)}
              className="w-full sm:w-auto border border-blue-600 text-blue-600 font-medium py-3 px-6 rounded-md hover:bg-blue-50 transition-colors flex items-center justify-center"
            >
              {showSkillGap ? "View Skills" : "Skill Gap Analysis"}
            </button>
          )}
        </div>
      </div>
    </main>
  );
}