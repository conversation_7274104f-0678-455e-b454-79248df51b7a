"use client"
import Heading from '@/components/ui/Heading';
import Nothing from '@/components/ui/Nothing';
import React, { useState } from 'react';
import Link from 'next/link';
import JobCard from '@/components/dashboard/roles/JobCard';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface Job {
    job_id: number;
    job_title: string;
    job_description?: string;  // Make description optional
}

const rolesData: Job[] = [
    { job_id: 1, job_title: "Software Developer", job_description: "Develop software applications and tools." },
    { job_id: 2, job_title: "Product Manager", job_description: "Manage the product lifecycle from planning to execution." },
    { job_id: 3, job_title: "UI/UX Designer", job_description: "Design intuitive and aesthetically pleasing user interfaces and experiences." },
    { job_id: 4, job_title: "Data Scientist", job_description: "Analyze large sets of data to extract valuable insights and support decision-making." },
    { job_id: 5, job_title: "Quality Assurance Engineer", job_description: "Test and ensure the quality of software products through rigorous testing methods." },
    { job_id: 6, job_title: "Business Analyst", job_description: "Analyze business processes and propose solutions to improve efficiency and effectiveness." },
    { job_id: 7, job_title: "Marketing Manager", job_description: "Plan and execute marketing strategies to promote products and drive business growth." },
    { job_id: 8, job_title: "Sales Executive", job_description: "Sell products and services to clients and maintain relationships to drive sales." },
    { job_id: 9, job_title: "DevOps Engineer", job_description: "Manage and automate the infrastructure and deployment pipelines for software applications." },
    { job_id: 10, job_title: "System Administrator", job_description: "Maintain and configure computer systems, networks, and servers to ensure smooth operations." },
    { job_id: 11, job_title: "HR Manager", job_description: "Oversee hiring, employee relations, and organizational development strategies." },
    { job_id: 12, job_title: "Network Engineer", job_description: "Design and maintain networks to ensure connectivity and communication across an organization." },
    { job_id: 13, job_title: "Project Manager", job_description: "Lead and manage projects from inception to completion, ensuring they meet deadlines and objectives." },
    { job_id: 14, job_title: "Product Designer", job_description: "Design and create the overall look and feel of products, from concepts to production." },
    { job_id: 15, job_title: "Full Stack Developer", job_description: "Develop both front-end and back-end components of web applications." },
    { job_id: 16, job_title: "Cloud Architect", job_description: "Design and manage cloud infrastructure and services to support organizational needs." },
    { job_id: 17, job_title: "Content Writer", job_description: "Write articles, blog posts, and other content to engage readers and drive traffic." },
    { job_id: 18, job_title: "Cybersecurity Specialist", job_description: "Protect systems, networks, and data from cyber threats and attacks." },
    { job_id: 19, job_title: "SEO Specialist", job_description: "Optimize websites to improve their ranking on search engines and drive organic traffic." },
    { job_id: 20, job_title: "Operations Manager", job_description: "Oversee the day-to-day operations of a business to ensure efficiency and profitability." }
];

// Dummy colors for the job cards
const colors: string[] = [
    'bg-blue-200',
    'bg-green-200',
    'bg-yellow-200',
    'bg-pink-200',
    'bg-purple-200',
];

const Page = () => {
    const [selectedJobIds, setSelectedJobIds] = useState<number[]>([]);  // Track selected job IDs
    const [previouslyAppliedJobIds, setPreviouslyAppliedJobIds] = useState<number[]>([1, 3, 5]); // Example: User has applied to roles with IDs 1, 3, and 5

    const handleroleselection = (jobId: number) => {
        setSelectedJobIds((prevSelectedIds) => {
            // Check if the job is already selected
            if (prevSelectedIds.includes(jobId)) {
                // Deselect if already selected
                return prevSelectedIds.filter((id) => id !== jobId);
            }

            // If not selected, check if there are already 3 roles selected
            if (prevSelectedIds.length >= 3) {
                // If already 3 roles are selected, do nothing
                return prevSelectedIds;
            }

            // Add to selection if not already selected and less than 3 roles are selected
            return [...prevSelectedIds, jobId];
        });
    };

    // Filter rolesData to only include previously applied roles
    const previouslyAppliedroles = rolesData.filter(job => previouslyAppliedJobIds.includes(job.job_id));

    return (
        <main className="relative w-full flex min-h-screen bg-primary flex-col items-center justify-between p-4">
            {/* {true && <SideNotification />} */}
            <div className="w-full h-full">
                <div className="p-3 w-full flex justify-between">
                    <Heading pgHeading="Previously Applied roles" />
                    <div className='w-full sm:w-[320px] lg:w-[360px] flex justify-end'>
                        <Link href={`/dashboard/roles`} className="w-[150px]">
                            <button className="flex items-center justify-center border-2 border-textSecondary text-textSecondary font-bold rounded text-sm w-full gap-2 p-2 hover:bg-textSecondary hover:text-white transition-all duration-300">
                                <ArrowLeftIcon className='w-5 h-5' />
                                {"roles Data"}
                            </button>
                        </Link>
                    </div>
                </div>

                <div className="flex flex-wrap gap-5 justify-start bg-white w-full rounded-2xl p-5 h-[70vh] overflow-y-auto">
                    {(!previouslyAppliedroles || previouslyAppliedroles.length === 0) ? (
                        <div className="w-full flex justify-center items-center h-full">
                            <Nothing
                                title="No Job titles Available"
                                para="There are currently no job titles to display.
                          Please check back later."
                            />
                        </div>
                    ) : (
                        previouslyAppliedroles.map((data, index) => (
                            <JobCard
                                key={data.job_id}
                                job={data}
                                index={index}
                                colors={colors}
                                selected={selectedJobIds.includes(data.job_id)} // Pass selected status
                                onClick={handleroleselection} // Pass the selection handler
                            />
                        ))
                    )}
                </div>
                <div className='w-full h-fit flex justify-between mt-4 lg:px-80'>
                    <Link href={``} className="w-[150px]">
                        <button className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded text-sm w-full">
                            {"Previous"}
                        </button>
                    </Link>
                    <Link href={``} className="w-[150px]">
                        <button className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded text-sm w-full">
                            {"Next"}
                        </button>
                    </Link>
                </div>
            </div>
        </main>
    );
};

export default Page;