"use client";
import React from "react";
import Heading from "@/components/ui/Heading";
import Link from "next/link";
import { 
  AcademicCapIcon, 
  ChartBarIcon, 
  ArrowTopRightOnSquareIcon,
  LightBulbIcon,
  ChevronRightIcon 
} from "@heroicons/react/24/outline";

interface SkillGap {
  skill: string;
  currentLevel: number;
  targetLevel: number;
  category: 'technical' | 'soft' | 'domain';
  urgency: 'high' | 'medium' | 'low';
}

/**
 * SkillGapReportPage displays the analysis of skill gaps for a selected job role
 */
export default function SkillGapReportPage() {
  // Mock data for skill gaps
  const skillGaps: SkillGap[] = [
    { 
      skill: "Data Structures", 
      currentLevel: 60, 
      targetLevel: 90,
      category: 'technical',
      urgency: 'high'
    },
    { 
      skill: "Machine Learning", 
      currentLevel: 40, 
      targetLevel: 80,
      category: 'technical',
      urgency: 'high'
    },
    { 
      skill: "Cloud Computing", 
      currentLevel: 50, 
      targetLevel: 85,
      category: 'technical',
      urgency: 'medium'
    },
    { 
      skill: "DevOps", 
      currentLevel: 30, 
      targetLevel: 75,
      category: 'technical',
      urgency: 'medium'
    },
    { 
      skill: "UI/UX Design", 
      currentLevel: 70, 
      targetLevel: 95,
      category: 'technical',
      urgency: 'low'
    },
    { 
      skill: "Communication", 
      currentLevel: 75, 
      targetLevel: 90,
      category: 'soft',
      urgency: 'high'
    },
    { 
      skill: "Project Management", 
      currentLevel: 65, 
      targetLevel: 85,
      category: 'soft',
      urgency: 'medium'
    },
    { 
      skill: "Healthcare Domain", 
      currentLevel: 45, 
      targetLevel: 80,
      category: 'domain',
      urgency: 'high'
    }
  ];

  // Calculate overall gap percentage
  const calculateOverallGap = () => {
    let totalGap = 0;
    skillGaps.forEach(gap => {
      totalGap += gap.targetLevel - gap.currentLevel;
    });
    return Math.round((totalGap / (skillGaps.length * 100)) * 100);
  };

  // Get high urgency gaps count
  const getHighUrgencyGapsCount = () => {
    return skillGaps.filter(gap => gap.urgency === 'high').length;
  };

  // Get urgency badge styling based on urgency level
  const getUrgencyBadge = (urgency: string) => {
    switch (urgency) {
      case 'high':
        return "bg-red-50 text-red-700 border border-red-200";
      case 'medium':
        return "bg-yellow-50 text-yellow-700 border border-yellow-200";
      case 'low':
        return "bg-green-50 text-green-700 border border-green-200";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get category badge styling based on category
  const getCategoryBadge = (category: string) => {
    switch (category) {
      case 'technical':
        return "bg-blue-50 text-blue-700";
      case 'soft':
        return "bg-purple-50 text-purple-700";
      case 'domain':
        return "bg-indigo-50 text-indigo-700";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Group skills into rows of 2 for layout
  const skillRows = [] as Array<Array<SkillGap>>;
  for (let i = 0; i < skillGaps.length; i += 2) {
    skillRows.push(skillGaps.slice(i, i + 2));
  }

  return (
    <main className="w-full flex flex-col p-4 sm:p-6 bg-gray-50">
      {/* Header */}
      <div className="mb-6">
        <Heading pgHeading="Skill Gap Analysis" />
        <p className="text-gray-600 max-w-3xl mt-2">
          This analysis shows the difference between your current skill levels and what's required for the selected role.
          Skills with larger gaps are highlighted for prioritized learning.
        </p>
      </div>

      {/* Summary cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        {/* Overall Gap */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden border-l-4 border-blue-500">
          <div className="p-5 flex items-center">
            <div className="bg-blue-100 rounded-lg p-3 mr-4 flex-shrink-0">
              <ChartBarIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider">Overall Gap</h3>
              <p className="text-3xl font-bold text-blue-600">{calculateOverallGap()}%</p>
            </div>
          </div>
        </div>
        
        {/* Priority Skills */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden border-l-4 border-red-500">
          <div className="p-5 flex items-center">
            <div className="bg-red-100 rounded-lg p-3 mr-4 flex-shrink-0">
              <LightBulbIcon className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider">Priority Skills</h3>
              <p className="text-lg font-semibold text-gray-800">
                {getHighUrgencyGapsCount()} high-urgency gaps identified
              </p>
            </div>
          </div>
        </div>
        
        {/* Recommended */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden border-l-4 border-green-500">
          <div className="p-5 flex items-center">
            <div className="bg-green-100 rounded-lg p-3 mr-4 flex-shrink-0">
              <AcademicCapIcon className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider">Recommended</h3>
              <p className="text-lg font-semibold text-gray-800">5 learning paths available</p>
            </div>
          </div>
        </div>
      </div>

      {/* Skills Grid */}
      <div className="space-y-4">
        {skillRows.map((row, rowIndex) => (
          <div key={rowIndex} className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {row.map((gap, colIndex) => {
              const gapSize = gap.targetLevel - gap.currentLevel;
              
              return (
                <div key={`${rowIndex}-${colIndex}`} className="bg-white rounded-xl shadow-sm overflow-hidden">
                  <div className="p-5">
                    {/* Skill header with badge */}
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="text-lg font-medium text-gray-900">{gap.skill}</h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getUrgencyBadge(gap.urgency)}`}>
                        {gap.urgency}
                      </span>
                    </div>
                    
                    {/* Current level */}
                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium text-gray-500">Current</span>
                        <span className="text-sm font-medium text-gray-700">{gap.currentLevel}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-blue-500 h-2.5 rounded-full"
                          style={{ width: `${gap.currentLevel}%` }}
                        />
                      </div>
                    </div>
                    
                    {/* Target level */}
                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium text-gray-500">Target</span>
                        <span className="text-sm font-medium text-gray-700">{gap.targetLevel}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-green-500 h-2.5 rounded-full"
                          style={{ width: `${gap.targetLevel}%` }}
                        />
                      </div>
                    </div>
                    
                    {/* Footer with category and gap */}
                    <div className="flex items-center justify-between mt-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryBadge(gap.category)}`}>
                        {gap.category}
                      </span>
                      <div className="text-sm font-medium flex items-center">
                        <span className={`text-${gapSize > 30 ? 'red' : gapSize > 20 ? 'yellow' : 'green'}-600`}>
                          Gap: {gapSize}%
                        </span>
                        <ChevronRightIcon className="h-4 w-4 ml-1 text-gray-400" />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ))}
      </div>

      {/* Action buttons */}
      <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-8">
        <Link href="/dashboard/roles/learning_plan">
          <button className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <AcademicCapIcon className="h-5 w-5 mr-2" />
            Generate Learning Plan
          </button>
        </Link>
        
        <Link href="/dashboard/roles/assessment">
          <button className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <ArrowTopRightOnSquareIcon className="h-5 w-5 mr-2" />
            Take Another Assessment
          </button>
        </Link>
      </div>
    </main>
  );
}