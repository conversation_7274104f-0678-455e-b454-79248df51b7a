"use client";
import { useSearchParams } from "next/navigation";
import { useGetAllAttemptsForUser } from "@/hook/assessment_attempts/useGetAllAttemptsForUser";
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query'
import AssessmentResultExpanded from "@/components/dashboard/AssessmentResultExpanded";

const Page = () => {
  const queryClient = useQueryClient();
  const searchParams = useSearchParams()
  const assessment_id =  (searchParams.get("assessment_id"))
  const user_id = searchParams.get("user_id")
  console.log("assessment_id for user see result is" , assessment_id)
  console.log("user_id for user see result is" , user_id)
  const {data:AttemptsData} = useGetAllAttemptsForUser(assessment_id ,user_id)
  console.log("assessment_id is" ,assessment_id)

  console.log("AttemptData page: ",AttemptsData)
  return (
    <div className="w-full flex px-4 h-[90vh]">
      <AssessmentResultExpanded AttemptsData={AttemptsData}/> 
    </div>
  );
};

export default Page;