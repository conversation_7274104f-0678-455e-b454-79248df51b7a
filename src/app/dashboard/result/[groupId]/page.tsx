"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Heading from "@/components/ui/Heading";
import { useGetRedoContent } from "@/hook/dashboard/result/useGetRedoContent";
import { useGetToDisplayContent } from "@/hook/dashboard/result/useGetToDisplayContent";
import Nothing from "@/components/ui/Nothing";
import {
  ArrowLeftEndOnRectangleIcon,
  ArrowPathRoundedSquareIcon,
  ArrowRightIcon,
  ChevronUpIcon,
  ClipboardDocumentCheckIcon,
  CodeBracketIcon,
  ComputerDesktopIcon,
  RectangleGroupIcon,
  SquaresPlusIcon,
  WindowIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";
import { Disclosure, Transition } from "@headlessui/react";
import Link from "next/link";
import { useGetModuleAttemptAssessmentResult } from "@/hook/dashboard/result/useGetModuleAttemptAssessmentResult"; 

export default function FAQGroupId() {
  const [isModalOpen, setIsModalOpen] = useState(true);
  const [action, setAction] = useState(); // State to control modal visibility
  const params = useParams();
  const group_id = parseInt(params.groupId, 10);

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const actionParam = searchParams.get("action");
    if (actionParam) {
      setAction(actionParam);
    }
  }, [params]);

  console.log("groupId", group_id);

  const {
    data: contentData,
    isLoading: isLoadingContent,
    isError: isErrorContent,
  } = useGetRedoContent(group_id);
  const {
    data: allContentData,
    isLoading: isLoadingContentDisplay,
    isError: isErrorContentDisplay,
  } = useGetToDisplayContent(group_id);
  const {
    data: programDataforModuleResult,
    isLoading: isLoadingForMpoduleResult,
    isError: isErrorForMpoduleResult,
  } = useGetModuleAttemptAssessmentResult();

  useEffect(() => {
    if (!isErrorContent) {
      console.log("Redo content data:", contentData);
    }
  }, [contentData, isLoadingContent, isErrorContent]);

  useEffect(() => {
    if (!isErrorContentDisplay) {
      console.log("All content data:", allContentData);
    }
  }, [allContentData, isLoadingContentDisplay, isErrorContentDisplay]);

  useEffect(() => {
    if (!isLoadingForMpoduleResult && !isErrorForMpoduleResult) {
      console.log(
        "program data for module result:",
        programDataforModuleResult
      );
    }
  }, [
    programDataforModuleResult,
    isLoadingForMpoduleResult,
    isErrorForMpoduleResult,
  ]);

  if (
    isLoadingContent ||
    isLoadingContentDisplay ||
    isLoadingForMpoduleResult
  ) {
    return <div>Loading...</div>;
  }

  if (isErrorContent || isErrorContentDisplay || isErrorForMpoduleResult) {
    return <div>Error fetching data</div>;
  }

  // Convert redo content data from object to array
  const redoContentArray = contentData ? Object.values(contentData) : [];

  // Function to find module_id for matching content_id
  const findModuleIdForContent = (contentId) => {
    if (!allContentData) return null;

    for (const module01 of allContentData) {
      for (const content of module01.contents) {
        if (content.content_id === contentId) {
          return module01.module_id;
        }
      }
    }
    return null;
  };

  if (action === "moduleresult") {
    return (
      <>
        <main className="w-full flex  h-fit max-h-screen overflow-auto bg-primary flex-col items-center justify-between p-1 ">
          <div className="w-full overflow-auto">
            <div className="p-3">
              <Heading pgHeading="Module Results" />
            </div>

            {/*  Recall Section */}
            <Disclosure>
              {({ open }) => (
                <>
                  <Disclosure.Button className="flex items-center justify-between w-full p-2 text-left text-white bg-textColor rounded-md">
                    <span className="flex justify-left items-center gap-2  ">
                      <RectangleGroupIcon className="w-[30px] h-[30px] text-white border-2 border-white rounded-md " />
                      Recall
                    </span>
                    <ChevronUpIcon
                      className={`${
                        open ? "rotate-180 transform" : ""
                      } h-5 w-5`}
                    />
                  </Disclosure.Button>
                  <Transition
                    enter="transition duration-150 ease-out"
                    enterFrom="transform scale-95 opacity-0"
                    enterTo="transform scale-100 opacity-100"
                    leave="transition duration-75 ease-out"
                    leaveFrom="transform scale-100 opacity-100"
                    leaveTo="transform scale-95 opacity-0"
                  >
                    <Disclosure.Panel className="p-4 text-sm bg-white">
                      <div className="w-full h-full bg-white rounded-lg   flex flex-col ">
                        <main className="w-full flex flex-col overflow-auto  rounded-lg h-[200px]">
                          <table className="min-w-full divide-y border divide-gray-200 mb-6 overflow-auto rounded-lg">
                            <thead className="bg-gray-100 text-gray-800 text-sm uppercase sticky top-0">
                              <tr>
                                <th className="p-2 text-left">Module id</th>
                                <th className="p-2 text-left">module name</th>
                                <th className="p-2 text-left">assessment Id</th>
                                <th className="p-2 text-left">attempt total</th>
                                <th className="p-2 text-left">total marks</th>
                              </tr>
                            </thead>
                            <tbody className="text-gray-500">
                              {programDataforModuleResult &&
                              Object.keys(programDataforModuleResult).length >
                                0 ? (
                                Object.keys(programDataforModuleResult).map(
                                  (key) =>
                                    programDataforModuleResult[key]
                                      .filter(
                                        (data) =>
                                          data.question_category === "Recall"
                                      )
                                      .map((data, index) => {
                                        const moduleId = findModuleIdForContent(
                                          data.module_id
                                        );
                                        return (
                                          <tr
                                            key={index}
                                            className="bg-white border-b dark:bg-gray-800 dark:border-gray-700"
                                          >
                                            <td className="p-2 text-left">
                                              {data.module_id}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.module_name}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.assessment_id}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.attempt_total}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.total_marks}
                                            </td>
                                          </tr>
                                        );
                                      })
                                )
                              ) : (
                                <tr>
                                  <td colSpan={5} className="text-center p-4">
                                    <Nothing
                                      title="No Data Available"
                                      para="There are currently no data to display. Please check back later."
                                    />
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </main>
                      </div>
                    </Disclosure.Panel>
                  </Transition>
                </>
              )}
            </Disclosure>

            {/*  Application Section */}
            <Disclosure>
              {({ open }) => (
                <>
                  <Disclosure.Button className="flex items-center justify-between w-full p-2 text-left text-white bg-textColor rounded-md mt-2">
                    <span className="flex justify-left items-center gap-2  ">
                      <WindowIcon className="w-[30px] h-[30px] text-white border-2 border-white rounded-md " />
                      Application
                    </span>
                    <ChevronUpIcon
                      className={`${
                        open ? "rotate-180 transform" : ""
                      } h-5 w-5`}
                    />
                  </Disclosure.Button>
                  <Transition
                    enter="transition duration-150 ease-out"
                    enterFrom="transform scale-95 opacity-0"
                    enterTo="transform scale-100 opacity-100"
                    leave="transition duration-75 ease-out"
                    leaveFrom="transform scale-100 opacity-100"
                    leaveTo="transform scale-95 opacity-0"
                  >
                     <Disclosure.Panel className="p-4 text-sm bg-white">
                      <div className="w-full h-full bg-white rounded-lg   flex flex-col ">
                        <main className="w-full flex flex-col overflow-auto  rounded-lg h-[200px]">
                          <table className="min-w-full divide-y border divide-gray-200 mb-6 overflow-auto rounded-lg">
                            <thead className="bg-gray-100 text-gray-800 text-sm uppercase sticky top-0">
                              <tr>
                                <th className="p-2 text-left">Module id</th>
                                <th className="p-2 text-left">module name</th>
                                <th className="p-2 text-left">assessment Id</th>
                                <th className="p-2 text-left">attempt total</th>
                                <th className="p-2 text-left">total marks</th>
                              </tr>
                            </thead>
                            <tbody className="text-gray-500">
                              {programDataforModuleResult &&
                              Object.keys(programDataforModuleResult).length >
                                0 ? (
                                Object.keys(programDataforModuleResult).map(
                                  (key) =>
                                    programDataforModuleResult[key]
                                      .filter(
                                        (data) =>
                                          data.question_category ===
                                          "Application"
                                      )
                                      .map((data, index) => {
                                        const moduleId = findModuleIdForContent(
                                          data.module_id
                                        );
                                        return (
                                          <tr
                                            key={index}
                                            className="bg-white border-b dark:bg-gray-800 dark:border-gray-700"
                                          >
                                            <td className="p-2 text-left">
                                              {data.module_id}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.module_name}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.assessment_id}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.attempt_total}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.total_marks}
                                            </td>
                                          </tr>
                                        );
                                      })
                                )
                              ) : (
                                <tr>
                                  <td colSpan={5} className="text-center p-4">
                                    <Nothing
                                      title="No Data Available"
                                      para="There are currently no data to display. Please check back later."
                                    />
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </main>
                      </div>
                    </Disclosure.Panel>
                  </Transition>
                </>
              )}
            </Disclosure>

            {/* Communication Section */}
            <Disclosure>
              {({ open }) => (
                <>
                  <Disclosure.Button className="flex items-center justify-between w-full p-2 text-left text-white bg-textColor rounded-md mt-2">
                    <span className="flex justify-left items-center gap-2  ">
                      {" "}
                      <ArrowPathRoundedSquareIcon className="w-[30px] h-[30px] text-white border-2 border-white rounded-md " />
                      Communication
                    </span>
                    <ChevronUpIcon
                      className={`${
                        open ? "rotate-180 transform" : ""
                      } h-5 w-5`}
                    />
                  </Disclosure.Button>
                  <Transition
                    enter="transition duration-150 ease-out"
                    enterFrom="transform scale-95 opacity-0"
                    enterTo="transform scale-100 opacity-100"
                    leave="transition duration-75 ease-out"
                    leaveFrom="transform scale-100 opacity-100"
                    leaveTo="transform scale-95 opacity-0"
                  >
                    <Disclosure.Panel className="p-4 text-sm bg-white">
                      <div className="w-full h-full bg-white rounded-lg   flex flex-col ">
                        <main className="w-full flex flex-col overflow-auto  rounded-lg h-[200px]">
                          <table className="min-w-full divide-y border divide-gray-200 mb-6 overflow-auto rounded-lg">
                            <thead className="bg-gray-100 text-gray-800 text-sm uppercase sticky top-0">
                              <tr>
                                <th className="p-2 text-left">Module id</th>
                                <th className="p-2 text-left">module name</th>
                                <th className="p-2 text-left">assessment Id</th>
                                <th className="p-2 text-left">attempt total</th>
                                <th className="p-2 text-left">total marks</th>
                              </tr>
                            </thead>
                            <tbody className="text-gray-500">
                              {programDataforModuleResult &&
                              Object.keys(programDataforModuleResult).length >
                                0 ? (
                                Object.keys(programDataforModuleResult).map(
                                  (key) =>
                                    programDataforModuleResult[key]
                                      .filter(
                                        (data) =>
                                          data.question_category ===
                                          "Communication"
                                      )
                                      .map((data, index) => {
                                        const moduleId = findModuleIdForContent(
                                          data.module_id
                                        );
                                        return (
                                          <tr
                                            key={index}
                                            className="bg-white border-b dark:bg-gray-800 dark:border-gray-700"
                                          >
                                            <td className="p-2 text-left">
                                              {data.module_id}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.module_name}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.assessment_id}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.attempt_total}
                                            </td>
                                            <td className="p-2 text-left">
                                              {data.total_marks}
                                            </td>
                                          </tr>
                                        );
                                      })
                                )
                              ) : (
                                <tr>
                                  <td colSpan={5} className="text-center p-4">
                                    <Nothing
                                      title="No Data Available"
                                      para="There are currently no data to display. Please check back later."
                                    />
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </main>
                      </div>
                    </Disclosure.Panel>
                  </Transition>
                </>
              )}
            </Disclosure>
          </div>
        </main>
      </>
    );
  } else if (action === "redotest") {
    return (
      <>
        <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-1">
          <div className="w-full h-[80vh] ">
            <div className="p-3">
              <Heading pgHeading="Content List" />
            </div>

            <div className="overflow-auto w-full h-full  rounded-md bg-white">
              <table className="min-w-full divide-y divide-gray-200  ">
                <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
                  <tr className="w-full ">
                    <th className="p-2 text-left">Content Id</th>
                    <th className="p-2 text-left">Content Title</th>
                    <th className="p-2 text-left">Description</th>
                    <th className="p-2 text-left">Topics</th>
                    <th className="p-2 text-left">Link</th>
                  </tr>
                </thead>
                <tbody className=" text-gray-500 h-full">
                  {redoContentArray.length > 0 ? (
                    redoContentArray.map((content, index) => {
                      const moduleId = findModuleIdForContent(
                        content.content_id
                      );
                      return (
                        <tr
                          key={index}
                          className="bg-white border-b overflow-auto dark:bg-gray-800 dark:border-gray-700 h-10"
                        >
                          <td className="p-2 text-left">
                            {content.content_id}
                          </td>
                          <td className="p-2 text-left">
                            {content.content_name}
                          </td>
                          <td className="p-2 text-left">
                            {content.content_description}
                          </td> 
                          <td className="p-2 text-left">{content.topics}</td>
                          {moduleId && (
                            <td className="p-2 text-left">
                              <Link
                                href={`/contentpanel/?content_id=${content.content_id}&group_id=${group_id}&module_id=${moduleId}`}
                                className="text-textSecondary"
                              >
                                <motion.button
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                >
                                  <ArrowRightIcon className="w-[25px] h-full" />
                                </motion.button>
                              </Link>
                            </td>
                          )}
                        </tr>
                      );
                    })
                  ) : (
                    <tr>
                      <td colSpan={6} className="text-center p-4">
                        <Nothing
                          title="No Content Available"
                          para="There are currently no content to display. Please check back later."
                        />
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </main>
      </>
    );
  } else {
    return <div>Invalid action</div>;
  }
}
