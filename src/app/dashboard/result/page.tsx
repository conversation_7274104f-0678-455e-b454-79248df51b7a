"use client";
import Heading from "@/components/ui/Heading";
import { Disclosure, Transition } from "@headlessui/react";
import Link from "next/link";
import React, { useEffect } from "react";
import {
  ClipboardDocumentCheckIcon,
  ComputerDesktopIcon
} from "@heroicons/react/20/solid";
import { ChevronUpIcon, CodeBracketIcon } from "@heroicons/react/24/outline";
import AssessmentResult from "@/components/dashboard/AssessmentResult";
import { useRouter } from "next/navigation";
import { useGetUserAssessments } from "@/hook/assessments/useGetUserAssessments";
import { getUser } from "@/api/user.localStorage";
import Nothing from "@/components/ui/Nothing";
import { useGetAllProgram } from "@/hook/dashboard/program/useGetAllProgram";
import { motion } from "framer-motion";
import { useGetModuleAttemptAssessmentResult } from "@/hook/dashboard/result/useGetModuleAttemptAssessmentResult";

const user = getUser();

// Parse the JSON string into a JavaScript object
var userData = user ? JSON.parse(JSON.stringify(user)) : {};

// Access the user_id property
var userId = userData.user_id;
console.log("user", user);
console.log("userId", userId); // Output: 3

export default function Page() {
  const router = useRouter();
  const { data: assessments, isLoading: loadingState } = useGetUserAssessments(
    userId,
    0,
    1000
  );
  //TODO: we might need to add dynamic values to offset and limit
  const currentDate = new Date();

  console.log("loadingState", loadingState);

  const previousAssessment = assessments?.filter((assessment) => {
    const endDate = new Date(assessment.end_date);
    return currentDate > endDate;
  });

  const { data: programData, isLoading, isError } = useGetAllProgram();
  const {
    data: programDataforModuleResult,
    isLoading: isLoadingForMpoduleResult,
    isError: isErrorForMpoduleResult,
  } = useGetModuleAttemptAssessmentResult();

  const colors = ["bg-blue-50", "bg-green-50", "bg-yellow-50", "bg-red-50"];

  useEffect(() => {
    if (!isLoading && !isError) {
      console.log("program data:", programData);
    }
  }, [programData, isLoading, isError]);

  useEffect(() => {
    if (!isLoadingForMpoduleResult && !isErrorForMpoduleResult) {
      console.log(
        "program data for module result:",
        programDataforModuleResult
      );
    }
  }, [
    programDataforModuleResult,
    isLoadingForMpoduleResult,
    isErrorForMpoduleResult,
  ]);

  if (isLoading) {
    return <div>Loading....</div>;
  }
  if (isError) {
    return <div>Error fetching modules</div>;
  }

  return (
    <main className="w-full flex h-fit max-h-screen overflow-auto flex-col items-center justify-between p-4">
      <div>
        <iframe
          src="http://52.172.41.51:8050/"
          style={{
            width: '80vw',
            height: '80vh',
            border: 'none',
            borderRadius: '10px',
            overflow: 'hidden',
          }}
          title="Embedded Assessment"
        ></iframe>
      </div>
    </main>
  );
}
