"use client"
import React from 'react'
import { useParams } from 'next/navigation';

import Heading from '@/components/ui/Heading';
import ModuleAssessmentTable from '@/components/dashboard/module/ModuleAssessmentTable';
import ModuleContentTable from '@/components/dashboard/module/ModuleContentTable';


export default function ModuleId() {

    const params = useParams();
    const module_id: number = parseInt(params.moduleId, 10);
    console.log("moduleId " + module_id);
    

  return (
    <div className="flex flex-col w-full h-[90vh]   ">
    {/* Use flex-grow for equal height division */}
    <div className="h-[10vh]">
      <div className="p-3">
        <Heading pgHeading="Modules" />
      </div>
      </div>
  <div className="h-[80vh] flex flex-col gap-5  bg-white   w-full  rounded-2xl p-5">
<div className='h-[40vh]'><ModuleContentTable moduleIDProp={module_id}/></div>
<div className='h-[40vh]'><ModuleAssessmentTable moduleIDProp={module_id}/> </div>
        
        
      </div>
    </div>
  
  )
}
