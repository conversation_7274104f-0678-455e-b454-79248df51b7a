"use client"

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Heading from "@/components/ui/Heading";
import Link from "next/link";


import { CodeBracketIcon } from "@heroicons/react/24/outline";
import { useGetAllProgram } from "@/hook/dashboard/program/useGetAllProgram";

export default function ModulePage() {
  const [currentPage, setCurrentPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const { data: programData, isLoading, isError } = useGetAllProgram();

  const colors = ["bg-blue-50", "bg-green-50", "bg-yellow-50", "bg-red-50"];

  useEffect(() => {
    if (!isLoading && !isError) {
      console.log("Modules data:", programData);
    }
  }, [programData, isLoading, isError]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error fetching modules</div>;
  }

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-1">
      <div className="w-full overflow-auto">
        <div className="p-3">
          <Heading pgHeading="Program List" />
        </div>
        <div className="flex flex-wrap gap-5 justify-between overflow-y-auto bg-white w-full rounded-2xl p-5 h-[90vh]">
          {programData?.map((data, index) => (
            <div
              key={data.group_id}
              className={`flex flex-col w-full md:w-[220px] lg:w-[300px] gap-1 rounded-lg border border-[#A9A9A9] text-left px-3 py-2 `}
            >

              <div className={`h-200 w-25 flex justify-center items-center p-10 ${colors[index % colors.length]}`}>
                <CodeBracketIcon className="w-20 h-20" />
              </div>
              <div className="flex flex-col w-full justify-center items-center">
              {/* <h1 className="font-bold text-lg">{data.group_name}</h1> */}
                <h1 className="font-bold text-lg">{data.group_faq}</h1>

              </div>
              <div className="flex flex-col items-center justify-center w-full gap-2">
                <p className="text-sm text-gray-800">{module.module_description}</p>
                <Link href={`module`} className="text-blue-500 underline">
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <button className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded w-40 text-sm">
                      {"Let's Start"}
                    </button>
                  </motion.button>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </main>
  );
}
