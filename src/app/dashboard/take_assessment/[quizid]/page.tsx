"use client";
import Link from "next/link";
import { useState } from "react";
import { useGetUserAssessments } from "@/hook/assessments/useGetUserAssessments";
import { useRouter } from "next/router";
import { getUser } from "@/api/user.localStorage";

const user = getUser();

// Parse the JSON string into a JavaScript object
var userData = JSON.parse(JSON.stringify(user))

// Access the user_id property
var userId = userData.user_id;
console.log("user", user);
console.log("userId", userId); // Output: 3

export default function Quiz({ params }: { params: { quizid: number } }) {

  const { data: exams } = useGetUserAssessments(userId, 0, 1000);
  //TODO: we might need to add dynamic values to offset and limit
  const [proceed, setProceed] = useState<boolean>(true);

  const examDetails = exams?.find((item) => {
    return item.assessment_id === Number(params.quizid);
  });
  console.log("Exam details:", examDetails)
  // console.log("examDetails:-",typeof(examDetails.total_time_allowed))
  if (examDetails) {
    return (
      <div className="w-full h-full px-4 pt-16 flex-col align-center justify-center">
        <div className="block mx-auto w-full max-w-xl rounded-2xl bg-white p-6">
          <h5 className="mb-2 text-lg font-medium leading-tight text-textPrimary">
            Exam Details: {" " + examDetails!.assessment_name}
          </h5>
          <p
            className="mb-3 text-base text-textColor "
            dangerouslySetInnerHTML={{ __html: examDetails!.instructions }}
          ></p>
          <p className="mb-1 text-lg font-medium leading-tight text-neutral-800 ">
            Exam Duration : {(examDetails!.total_time_allowed / 60).toFixed(2)} minutes
          </p>
          <div className="mt-4">
            <input
              className="mb-6"
              type="checkbox"
              id="proceed-checkbox"
              data-TestId="proceed"
              onClick={() => setProceed(!proceed)}
            />
            <label className="text-sm font-medium pl-1" htmlFor="proceed-checkbox">
              {" "}

              I have understood the instructions and would like to proceed.
            </label>
          </div>
          <Link
            href={{
              pathname: "/quiz",
              query: {
                exam_id: examDetails!.assessment_id,
                time_allowed: ((examDetails!.total_time_allowed) / 60),
                user_assessment_id: examDetails!.user_assessment_id
              },
            }}
          >
            <button
              disabled={proceed}
              className="flex w-full justify-center rounded-md bg-secondary px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75  disabled:bg-lightBlue"
            >
              Start Quiz
            </button>
          </Link>
        </div>
      </div>
    );
  } else {
    return null; // Handle case where examDetails is not yet available
  }
  // return (
  //   <div className='flex  flex-col gap-3 w-full h-[90vh] justify-center items-center'>

  //     <h1 className="text-lg ">Assessment Not Assign</h1>
  //     <p className="text-sm text-gray-500">"The assessment has not been assigned to you yet. Please check back later or contact the administrator."</p>

  //   </div>
  // );
}
