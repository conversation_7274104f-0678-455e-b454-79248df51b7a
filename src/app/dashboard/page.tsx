"use client";
import Heading from "@/components/ui/Heading";
import { Disclosure, Transition } from "@headlessui/react";
import React from "react";
import {
  ClipboardDocumentCheckIcon,
  ComputerDesktopIcon,
  SquaresPlusIcon,
} from "@heroicons/react/20/solid";
import { ChevronUpIcon } from "@heroicons/react/24/outline";
import AssesmentResultMainComponent from "@/components/dashboard/userResult/AssessmentResultMainComponent";
import RedoTestMainComponent from "@/components/dashboard/userResult/RedoTestMainComponent";
import AssessmentResult from "@/components/dashboard/AssessmentResult";
import { useRouter } from "next/navigation";
import { useGetUserAssessments } from "@/hook/assessments/useGetUserAssessments";
import { getUser } from "@/api/user.localStorage";



const user = getUser();

// Parse the JSON string into a JavaScript object
var userData = user ? JSON.parse(JSON.stringify(user)) : {}

// Access the user_id property
var userId = userData.user_id;
console.log("user", user);
console.log("userId", userId); // Output: 3

export default function Page() {

  const router = useRouter();
  const { data: assessments, isLoading: loadingState } = useGetUserAssessments(userId, 0 ,1000);
  //TODO: we might need to add dynamic values to offset and limit
  const currentDate = new Date();

  console.log("loadingState",loadingState)

  const previousAssessment = assessments?.filter((assessment) => {
    const endDate = new Date(assessment.end_date);
    return currentDate > endDate;
  });

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-1">
      <div className="w-full overflow-auto">
        <div className="p-3">
          <Heading pgHeading="Results" />
        </div>

        {/* Assessment Result Section */}
        <Disclosure>
          {({ open }) => (
            <>
              <Disclosure.Button className="flex items-center justify-between w-full p-2 text-left text-white bg-textColor rounded-md">
                <span className="flex justify-left items-center gap-2  ">
                  <ComputerDesktopIcon className="w-[30px] h-[30px] text-white border-2 border-white rounded-md " />
                  Assessment Result
                </span>
                <ChevronUpIcon
                  className={`${open ? "rotate-180 transform" : ""} h-5 w-5`}
                />
              </Disclosure.Button>
              <Transition
                enter="transition duration-150 ease-out"
                enterFrom="transform scale-95 opacity-0"
                enterTo="transform scale-100 opacity-100"
                leave="transition duration-75 ease-out"
                leaveFrom="transform scale-100 opacity-100"
                leaveTo="transform scale-95 opacity-0"
              >
                <Disclosure.Panel className="p-4 text-sm text-gray-500 overflow-auto">
                  <div className="w-full h-full bg-white rounded-lg border-2 border-black  flex flex-col ">
                    <main className="w-full flex flex-col overflow-auto ">
                     
                        <AssessmentResult
                          assessments={previousAssessment}
                          loadingState={loadingState}
                        />
                      
                    </main>
                  </div>
                </Disclosure.Panel>
              </Transition>
            </>
          )}
        </Disclosure>

        {/* Module Result Section */}
        <Disclosure>
          {({ open }) => (
            <>
              <Disclosure.Button className="flex items-center justify-between w-full p-2 text-left text-white bg-textColor rounded-md mt-2">
                <span className="flex justify-left items-center gap-2  ">
                  <SquaresPlusIcon className="w-[30px] h-[30px] text-white border-2 border-white rounded-md " />
                  Module Result
                </span>
                <ChevronUpIcon
                  className={`${open ? "rotate-180 transform" : ""} h-5 w-5`}
                />
              </Disclosure.Button>
              <Transition
                enter="transition duration-150 ease-out"
                enterFrom="transform scale-95 opacity-0"
                enterTo="transform scale-100 opacity-100"
                leave="transition duration-75 ease-out"
                leaveFrom="transform scale-100 opacity-100"
                leaveTo="transform scale-95 opacity-0"
              >
                <Disclosure.Panel className="p-4 text-sm text-gray-500">
                  <div className="w-full  h-full bg-white rounded-lg border-2 border-black p-2 flex flex-col justify-around items-center">
                    <p>coming soon</p>
                  </div>
                </Disclosure.Panel>
              </Transition>
            </>
          )}
        </Disclosure>

        {/* Re-do Test Section */}
        <Disclosure>
          {({ open }) => (
            <>
              <Disclosure.Button className="flex items-center justify-between w-full p-2 text-left text-white bg-textColor rounded-md mt-2">
                <span className="flex justify-left items-center gap-2  ">
                  {" "}
                  <ClipboardDocumentCheckIcon className="w-[30px] h-[30px] text-white border-2 border-white rounded-md " />
                  Re-do Test
                </span>
                <ChevronUpIcon
                  className={`${open ? "rotate-180 transform" : ""} h-5 w-5`}
                />
              </Disclosure.Button>
              <Transition
                enter="transition duration-150 ease-out"
                enterFrom="transform scale-95 opacity-0"
                enterTo="transform scale-100 opacity-100"
                leave="transition duration-75 ease-out"
                leaveFrom="transform scale-100 opacity-100"
                leaveTo="transform scale-95 opacity-0"
              >
                <Disclosure.Panel className="p-4 text-sm text-gray-500">
                  <div className="w-full h-full bg-white  rounded-lg border-2 border-black p-2 flex flex-col justify-around items-center">
                    <RedoTestMainComponent />
                  </div>
                </Disclosure.Panel>
              </Transition>
            </>
          )}
        </Disclosure>
      </div>
    </main>
  );
}
