import React, { useState } from "react";

const Page = () => {
  const questions = [
    {
      id: "QR001",
      answerId: "AN001",
      question: "In HTML, which attribute is used to define inline styles?",
      submittedAnswer:
        "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.",
    },
    {
      id: "QR002",
      answerId: "AN002",
      question: "What is the capital city of France?",
      submittedAnswer: "Paris",
    },
    {
      id: "QR003",
      answerId: "AN003",
      question: "What is the largest mammal on Earth?",
      submittedAnswer: "Blue whale",
    },
    {
      id: "QR004",
      answerId: "AN004",
      question: "Who wrote '<PERSON> and Juliet'?",
      submittedAnswer: "William Shakespeare",
    },
    {
      id: "QR005",
      answerId: "AN005",
      question: "What is the chemical symbol for gold?",
      submittedAnswer: "Au",
    },
    {
      id: "QR006",
      answerId: "AN006",
      question: "What is the largest planet in our solar system?",
      submittedAnswer: "Jupiter",
    },
    {
      id: "QR007",
      answerId: "AN007",
      question:
        "Which programming language is known for its simplicity and readability?",
      submittedAnswer: "Python",
    },
    {
      id: "QR008",
      answerId: "AN008",
      question: "Who painted the Mona Lisa?",
      submittedAnswer: "Leonardo da Vinci",
    },
    {
      id: "QR009",
      answerId: "AN009",
      question: "What is the square root of 64?",
      submittedAnswer: "8",
    },
    {
      id: "QR010",
      answerId: "AN010",
      question: "Which continent is known as the 'Land of the Rising Sun'?",
      submittedAnswer: "Asia",
    },
  ];
  return (
    <div className="flex flex-col gap-2 w-full px-3 ">
      <h2 className="flex py-2 font-semibold text-[12px] md:text-[15px] lg:text-[1.5em] text-textColor">
        Evaluate
      </h2>
      <div className="flex justify-between gap-2 w-full border-[1px] border-grey-900 bg-white rounded-lg shadow divide-x divide-grey-900  flex-wrap">
        <div className="flex-1 flex flex-col justify-around p-2">
          <h2 className="flex font-semibold  text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
            Status
          </h2>
          <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em] text-textColor">
            Submitted
          </h2>
        </div>
        <div className="flex-1 flex flex-col justify-around p-3 ">
          <h2 className="flex font-semibold text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
            User Id
          </h2>
          <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em]  text-textColor">
            ID001
          </h2>
        </div>

        <div className="flex-1 flex flex-col justify-around p-3">
          <h2 className="flex font-semibold  text-[5px] lg:text-[10px]  md:text-[7px] text-grey-100">
            Marks
          </h2>
          <h2 className="flex font-semibold  text-[10px] md:text-[12px] lg:text-[1em] text-textColor">
            100
          </h2>
        </div>
      </div>
      <div className=" h-[500px]  overflow-y-auto">
        {questions.map((q, index) => (
          <div
            key={index}
            className="flex  flex-col justify-between gap-2 w-full border-[1px] border-white  shadow bg-white p-2 "
          >
            <h2 className="flex  font-semibold  text-[10px] md:text-[12px] lg:text-[15px] text-grey-100 p-1">
              {q.question}
            </h2>
            <div className="flex justify-start gap-3  w-full font-semibold  text-[7px] lg:text-[12px]  md:text-[10px] text-blue-900">
              <h2 className="flex ">Question ID: {q.id}</h2>

              <h2 className="flex ">Answer ID: {q.answerId}</h2>
              <h2 className="flex ">Submitted Answer: {q.answerId}</h2>
              <h2 className="flex">Correct Answer: {q.answerId}</h2>
            </div>
            <h2 className="flex font-semibold  text-[7px] md:text-[10px] lg:text-[12px] text-grey-100 ">
              Explained Answer:
            </h2>
            <p className="text-[7px] md:text-[10px] lg:text-[12px] text-grey-100 p-1">
              {q.submittedAnswer}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Page;
