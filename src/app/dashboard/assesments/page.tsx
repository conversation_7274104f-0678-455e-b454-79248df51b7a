"use client";
import React , {useState} from "react";
import { useRouter } from "next/navigation";
import AssessmentTimeListing from "@/components/dashboard/assessment/AssessmentTimeListing";
import { useGetUserAssessments } from "@/hook/assessments/useGetUserAssessments";

import { getUser } from "@/api/user.localStorage";

function filterAndSortAssessments(assessments, currentDate: Date, type: string) {
  return assessments
    ?.filter(assessment => {
      const endDate = new Date(assessment.end_date);
      const startDate = new Date(assessment.start_date);
      switch (type) {
        case "previous":
          return currentDate > endDate;
        case "present":
          return currentDate < endDate && currentDate > startDate;
        case "upcoming":
          return currentDate < startDate;
        default:
          return false;
      }
    })
    ?.sort((a:any, b:any) => {
      return new Date(a.start_date) - new Date(b.start_date);
    });
}

const user = getUser();

// Parse the JSON string into a JavaScript object
var userData = user ? JSON.parse(JSON.stringify(user)) : {}

// Access the user_id property
var userId = userData.user_id;
console.log("user", user);
console.log("userId", userId); // Output: 3

export default function Dashboard() {

  
  const [limit, setLimit] = useState<number>(10)
  const [offSet, setOffSet] = useState<number>(limit-10)
  const [isPreviousButtonActive, setIsPreviousButtonActive] = useState<boolean>(false)
  const [isNextButtonActive, setisNextButtonActive] = useState<boolean>(true)

  const router = useRouter();
  const { data: assessments } = useGetUserAssessments(userId, offSet, limit);
  console.log("assessments length",assessments?.length)
  // console.log("offset", offset)
  console.log("limit", limit)
  console.log("offSet", offSet)

  const currentDate = new Date();

  const previousAssessment = filterAndSortAssessments(assessments, currentDate, "previous");
  const presentAssessment = filterAndSortAssessments(assessments, currentDate, "present");
  const UpcomingAssessment = filterAndSortAssessments(assessments, currentDate, "upcoming");

  console.log("Previous Assessments:", previousAssessment);
  console.log("Present Assessments:", presentAssessment);
  console.log("Upcoming Assessments:", UpcomingAssessment);

  const handleNext = () => {
    if (limit >= 10 && limit <= assessments?.length) {
      setIsPreviousButtonActive(true);
      setLimit(prevLimit => prevLimit + 10); // Increment limit by 10
      setOffSet(prevOffset => prevOffset + 10); // Increment offset by 10
    }
    if(limit > assessments?.length){
      setisNextButtonActive(false)
    }
  };
  

  const handlePrevious = () =>{
    if (limit > 10 && limit <= 1000){
      setisNextButtonActive(true)
      setLimit(prevLimit => prevLimit - 10);
      setOffSet(prevOffset => prevOffset - 10);
      setIsPreviousButtonActive(false)
    }
  }


  return (
    <main className="w-full h-fit max-h-screen flex flex-col  py-2 overflow-auto">
      <AssessmentTimeListing assessment_type="present" assessments={presentAssessment} />
      <AssessmentTimeListing assessment_type="upcoming" assessments={UpcomingAssessment} />
      <AssessmentTimeListing assessment_type="previous" assessments={previousAssessment} />
      <div className="flex gap-8 h-fit sm:gap-0 flex-row sm:flex-row justify-around p-4 lg:p-0 lg:mx-4 w-full lg:w-[97%]">
        <div className="flex justify-center">
          <button
            type="submit"
            className={`px-6 py-2 flex gap-2 ${isPreviousButtonActive ? "bg-textColor" : "bg-blue-500"} text-white rounded-md text-[15px]`}
            onClick={handlePrevious}
          >

            Previous
          </button>
        </div>
        <div className="flex justify-center">
          <button
            type="submit"
            className={`px-6 py-2 flex gap-2 ${isNextButtonActive ? "bg-textColor" : "bg-blue-500"} text-white rounded-md text-[15px]`}
            onClick={handleNext}
          >
            Next
          </button>
        </div>
      </div>

    </main>
  );
}
