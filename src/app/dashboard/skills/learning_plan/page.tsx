"use client";
import React, { useEffect, useState } from "react";
import Heading from "@/components/ui/Heading";
import LearningPath from "@/components/dashboard/skills/learning_plan/LearningPath";
import { useGetAllModulesForUser } from "@/hook/dashboard/program/useGetAllModulesForUser";

export default function LearningPathPage() {
  const [dummyData, setDummyData] = useState([]);

  const sample_data = [
    {
      module_id: 94,
      group_id: 1,
      sequence: 1,
      module_name: "Introduction to Basic Programming",
      module_headline: "Understanding Basic Programming Concepts",
      module_description:
        "This module covers the basics of programming, including variables, data types, and control structures.",
      group_name: "Beginner Level Programming",
      isactive: 1,
      group_admin_user_id: 2,
      created_by: "<EMAIL>",
      completed: "not_started", // Corrected to match expected value
      additional_info:
        "This module is designed for beginners. It includes hands-on exercises and quizzes to reinforce learning.",
    },
    {
      module_id: 95,
      group_id: 2,
      sequence: 2,
      module_name: "Advanced Algorithms",
      module_headline: "Delving Deeper into Algorithm Design",
      module_description:
        "This module focuses on advanced algorithmic concepts such as sorting, searching, and optimization techniques.",
      group_name: "Advanced Programming",
      isactive: 0,
      group_admin_user_id: 3,
      created_by: "<EMAIL>",
      completed: "in_Progress", // Corrected to match expected value
      additional_info:
        "This module is for intermediate learners. It includes real-world projects and case studies.",
    },
    {
      module_id: 96,
      group_id: 3,
      sequence: 3,
      module_name: "Machine Learning Foundations",
      module_headline: "Intro to Machine Learning Concepts and Models",
      module_description:
        "This module introduces the foundational concepts of machine learning, including supervised and unsupervised learning, and basic algorithms.",
      group_name: "Expert Level Programming",
      isactive: 1,
      group_admin_user_id: 4,
      created_by: "<EMAIL>",
      completed: "in_Progress", // Corrected to match expected value
      additional_info:
        "This module covers key ML algorithms and their applications. It includes coding exercises and datasets.",
    },
    {
      module_id: 97,
      group_id: 4,
      sequence: 4,
      module_name: "Web Development Basics",
      module_headline: "Building Your First Website with HTML, CSS, and JavaScript",
      module_description:
        "Learn the fundamentals of web development, including HTML, CSS, and JavaScript for creating basic websites.",
      group_name: "Beginner Web Development",
      isactive: 1,
      group_admin_user_id: 5,
      created_by: "<EMAIL>",
      completed: "completed", // Corrected to match expected value
      additional_info:
        "This module is perfect for beginners. It includes step-by-step tutorials and project-based learning.",
    },
    {
      module_id: 98,
      group_id: 5,
      sequence: 5,
      module_name: "Intermediate Web Development",
      module_headline: "Enhancing Websites with Advanced Techniques",
      module_description:
        "This module covers more advanced web development topics such as responsive design, CSS frameworks, and JavaScript frameworks.",
      group_name: "Intermediate Web Development",
      isactive: 1,
      group_admin_user_id: 6,
      created_by: "<EMAIL>",
      completed: "completed", // Corrected to match expected value
      additional_info:
        "This module focuses on modern web development practices. It includes hands-on projects and code reviews.",
    },
    {
      module_id: 99,
      group_id: 6,
      sequence: 6,
      module_name: "Full-Stack Development",
      module_headline: "Building End-to-End Applications with Modern Tools",
      module_description:
        "Learn to develop full-stack applications by mastering both front-end and back-end technologies like React, Node.js, and MongoDB.",
      group_name: "Expert Full-Stack Development",
      isactive: 1,
      group_admin_user_id: 7,
      created_by: "<EMAIL>",
      completed: "completed", // Corrected to match expected value
      additional_info:
        "This module is for advanced learners. It includes building a full-stack project from scratch.",
    },
  ];

  const {
    data: modulesDatas,
    isLoading: isLoadingModule,
    isError: isErrorModule,
  } = useGetAllModulesForUser(1);

  useEffect(() => {
    if (Array.isArray(modulesDatas)) {
      // Merge API data with sample data
      setDummyData([...modulesDatas, ...sample_data]);
    } else {
      // Use only sample data if API data is not available
      setDummyData(sample_data);
    }
  }, [modulesDatas]);

  if (isLoadingModule) {
    return <div>Loading...</div>;
  }

  if (isErrorModule) {
    return <div>Error fetching modules</div>;
  }

  if (!dummyData || dummyData.length === 0) {
    return <div>No modules found.</div>;
  }

  return (
    <main
      className="w-full flex min-h-full bg-white flex-col items-center justify-between p-4 rounded-2xl"
      aria-label="Learning Path Page"
    >
      <div className="w-full">
        <div className="p-3">
          <Heading pgHeading="Learning Plan" />
        </div>
        <div className="grid grid-cols-1 gap-5 bg-white w-full h-[75vh] p-6 overflow-auto border-y-8 border-white">
          {dummyData.map((modulesDetails) => (
            <LearningPath
              key={modulesDetails.module_id}
              moduleData={modulesDetails}
            />
          ))}
        </div>
      </div>
    </main>
  );
}
