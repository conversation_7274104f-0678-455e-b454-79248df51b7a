"use client";
import React, { useState } from "react";
import Heading from "@/components/ui/Heading";
import Button001 from "@/components/ui/Button001";
import { XMarkIcon } from "@heroicons/react/24/solid"; // Import the X icon from Heroicons
import { useRouter } from 'next/navigation';

export default function ModulePage() {
  const [showAssessment, setShowAssessment] = useState(true);
  const router = useRouter();

  // Dummy data for assessment results
  const assessmentResults = [
    { id: 1, subject: "Mathematics", score: 85, grade: "A" },
    { id: 2, subject: "Science", score: 78, grade: "B" },
    { id: 3, subject: "History", score: 92, grade: "A+" },
    { id: 4, subject: "English", score: 88, grade: "A" },
  ];

  // Skills data
  const skills_data = [
    { "Skill": "Business Intelligence", "Score": 80, "Expected": 100, "Gap": 20, "Sub-Skills": "KPI Development: 10%" },
    { "Skill": "Data Visualization", "Score": 82, "Expected": 100, "Gap": 18, "Sub-Skills": "Interactive Dashboards: 8%" },
    { "Skill": "Big Data Processing", "Score": 78, "Expected": 100, "Gap": 22, "Sub-Skills": "Distributed Computing: 7%" },
    { "Skill": "Scikit-learn (ML)", "Score": 83, "Expected": 100, "Gap": 17, "Sub-Skills": "Feature Engineering: 6%" },
    { "Skill": "Tableau", "Score": 85, "Expected": 100, "Gap": 15, "Sub-Skills": "" },
    { "Skill": "Machine Learning", "Score": 79, "Expected": 100, "Gap": 21, "Sub-Skills": "Regression Analysis: 7%" },
    { "Skill": "Data Management", "Score": 80, "Expected": 100, "Gap": 20, "Sub-Skills": "Data Cleaning: 10%, Missing Value Imputation: 5%, Data Transformation: 5%" },
    { "Skill": "Statistical Analysis", "Score": 81, "Expected": 100, "Gap": 19, "Sub-Skills": "Hypothesis Testing: 9%" },
    { "Skill": "Predictive Analytics", "Score": 79, "Expected": 100, "Gap": 21, "Sub-Skills": "Regression Analysis: 6%" },
    { "Skill": "Business Skills", "Score": 84, "Expected": 100, "Gap": 16, "Sub-Skills": "Domain Expertise: 4%, Communication Skills: 4%, Project Management: 4%, Ethics and Governance: 4%" },
  ];

  // Transform skills_data to match skillGapAnalysis structure
  const skillGapAnalysis = skills_data.map((skill, index) => ({
    id: index + 1,
    skill: skill.Skill,
    hasSkill: skill.Score >= 80, // Assuming a score of 80 or above means the skill is acquired
    currentLevel: `${skill.Score}%`,
    desiredLevel: `${skill.Expected}%`,
    gap: `${skill.Gap}%`,
    subSkills: skill["Sub-Skills"], // Adding sub-skills to the object
  }));

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-4">
      <div className="w-full overflow-auto">
        <div className="p-3">
          <Heading pgHeading="Results" />
        </div>
        <div className="flex flex-wrap gap-1 justify-center bg-white w-full rounded-2xl p-4 h-[70vh] overflow-y-auto">
          <div className="w-full h-[80%]">
            {showAssessment ? (
              // Assessment Results Table
              <table className="w-full table-auto bg-white border border-gray-200 rounded-lg">
                <thead className="bg-gray-200">
                  <tr>
                    <th className="py-2 px-4 text-left">Subject</th>
                    <th className="py-2 px-4 text-left">Score</th>
                    <th className="py-2 px-4 text-left">Grade</th>
                  </tr>
                </thead>
                <tbody>
                  {assessmentResults.map((result) => (
                    <tr key={result.id}>
                      <td className="py-2 px-4 border-b">{result.subject}</td>
                      <td className="py-2 px-4 border-b">{result.score}</td>
                      <td className="py-2 px-4 border-b">{result.grade}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (

              <div>
                <iframe
                  src="http://52.172.41.51:8050/"
                  style={{
                    width: '100%',
                    height: '52vh',
                    border: 'none',
                    borderRadius: '10px',
                    overflow: 'hidden',
                  }}
                  title="Embedded Assessment"
                ></iframe>
              </div>
            )}
          </div>

          {/* Toggle Button */}
          <div className="w-full flex justify-center mt-8 gap-20">
            <Button001
              buttonName={showAssessment ? "Show Skill Gap Analysis" : "Show Assessment Results"}
              onClick={() => setShowAssessment(!showAssessment)}
            />
            <Button001
              buttonName="Create Learning Path"
              href="/dashboard/roles/learning_plan"
              onClick={() => setShowAssessment(!showAssessment)}
            />
          </div>
        </div>
      </div>
    </main>
  );
}