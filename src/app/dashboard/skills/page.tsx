"use client";
import React, { useState } from "react";
import Heading from "@/components/ui/Heading";
import Button001 from "@/components/ui/Button001";
import { XMarkIcon } from "@heroicons/react/24/solid"; // Import the X icon from Heroicons
import { useRouter } from 'next/navigation';
import InstructionModal from "@/components/ui/InstructionModal";

export default function ModulePage() {
  const router = useRouter();
  const [files, setFiles] = useState([]); // State to store multiple files
  const [uploadStatus, setUploadStatus] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  // Handle file selection
  const handleFileChange = (event) => {
    const selectedFiles = Array.from(event.target.files); // Convert FileList to an array
    if (selectedFiles.length > 0) {
      setFiles((prevFiles) => [...prevFiles, ...selectedFiles]); // Add new files to the existing list
      setUploadStatus(`${selectedFiles.length} file(s) selected.`);
    }
  };

  // Handle file removal
  const handleRemoveFile = (index) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index)); // Remove the file at the specified index
    setUploadStatus(`File removed. ${files.length - 1} file(s) remaining.`);
  };

  // Handle file upload
  const handleUpload = () => {
    if (files.length > 0) {
      setUploadStatus("Uploading...");
      // Simulate file upload (replace with actual API call)
      setTimeout(() => {
        setUploadStatus("Upload successful!");
        setFiles([]); // Clear files after upload

        setTimeout(() => {
          router.push('/dashboard/skills/skill_level');
        }, 1000);
      }, 2000);
    } else {
      setUploadStatus("Please select at least one file.");
    }
  };

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-4 relative">
      {isModalOpen && <InstructionModal type = "pdf" title="Sample File" closeModal={closeModal}/>}
      <div className="w-full overflow-auto">
        
        <div className="p-3">
          <Heading pgHeading="Skills" />
        </div>
        <div className="flex flex-wrap gap-1 justify-center bg-white w-full rounded-2xl p-4 h-[75vh] overflow-y-auto">
          <p>
            Please upload a document that lists the skills required for a particular role or person. The document can be in a Job Description type format, or a list of required skills and levels of expertise for each skill.{" "}
            <button
              className="text-blue-800"
              onClick={() => openModal()}>(See sample here)​</button>
          </p>

          {/* File Upload Section */}
          <div className="flex flex-wrap gap-5 justify-center w-full">
            <label className="flex-1 min-w-[250px] h-48 p-6 rounded-lg flex flex-col justify-center items-center text-2xl text-center border-2 border-blue-800 transition-colors cursor-pointer hover:bg-blue-50">
              <span>Upload Skill Requirement Document(s) here</span>
              <input
                type="file"
                className="hidden"
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx"
                multiple // Allow multiple files
              />
            </label>
          </div>

          {/* Display Selected Files */}
          {files.length > 0 && (
            <div className="w-full mt-6">
              <h3 className="text-lg font-semibold mb-4">Selected Files:</h3>
              <div className="space-y-2">
                {files.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-100 rounded-lg"
                  >
                    <span className="text-sm">{file.name}</span>
                    <button
                      onClick={() => handleRemoveFile(index)}
                      className="text-red-400 hover:text-red-800 duration-300"
                    >
                      <XMarkIcon className="h-5 w-5" /> {/* Heroicon X icon */}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Upload Status */}
          {uploadStatus && <p className="text-center mt-4">{uploadStatus}</p>}

          {/* Generate Button */}
          <div className="w-full flex justify-center mt-8">
            <Button001 onClick={handleUpload} buttonName="Upload & Generate" />
          </div>
        </div>
      </div>
    </main>
  );
}