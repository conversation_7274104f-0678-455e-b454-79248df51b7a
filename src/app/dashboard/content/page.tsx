"use client"
import React, { useState, useEffect } from "react";
import { Disclosure, Transition } from "@headlessui/react";
import { ChevronUpIcon } from "@heroicons/react/20/solid";
import { useGetAllModules } from '@/hook/admin/module/useGetAllModules';
 
import Heading from "@/components/ui/Heading";

import Link from "next/link";

const Content = () => {
    const [currentPage, setCurrentPage] = useState(0);
    const [limit, setLimit] = useState(10);
    const { data: modulesData} = useGetAllModules(0, 100);
  
    console.log("moduledata",modulesData)
  
    return (
      <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-1">
        <div className="w-full overflow-auto">
          <div className="p-3">
            <Heading pgHeading="Modules List" />
          </div>
          <div className="flex flex-col gap-3 overflow-y-auto bg-white  w-full rounded-2xl p-5 h-[30rem] py-[10px]">
            <div className="flex flex-col gap-2">
            {modulesData?.map((module) => (
              <div
                key={module.module_id}
                className="flex flex-col w-full justify-between gap-1 rounded-lg border border-[#A9A9A9] text-left p-3"
              >
                <h1 className="font-bold text-lg">{module.module_name}</h1>
            
                <div className="flex w-full justify-between">
                  <div className="text-base text-gray-800 font-semibold">
                    {module.module_headline}
                  </div>
                  <div className="text-sm text-gray-800 font-semibold px-1">
                    By- {module.created_by}
                  </div>
                </div>
                <div className="flex w-full justify-between">
                  <p className="text-sm text-gray-800">{module.module_description}</p>
                  <Link
                        href={`module/${module.module_id}`}
                        className="text-blue-500 underline"
                      >
                  <button
                    className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded w-40 text-sm"
                  >
                    {"Let's Start"}
                  </button></Link>
                </div>
              </div>
            ))}
            </div>
          </div>
        </div>
      </main>
    );
}

export default Content;