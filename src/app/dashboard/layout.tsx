// src/app/dashboard/layout.tsx

"use client";
import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import Breadcrumbs from '@/components/ui/Breadcrumbs';
import { generateBreadcrumbs } from '@/utils/generateBreadcrumbs';
import Sidebar from '@/components/ui/header/Sidebar';
import { XMarkIcon, Bars3Icon } from '@heroicons/react/24/outline';

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const crumbs = generateBreadcrumbs(pathname);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(true); // Default to expanded
  const [isMobile, setIsMobile] = useState(false);

  // Check if we're on mobile screen
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
      if (window.innerWidth < 768) {
        setIsSidebarExpanded(false); // Auto-collapse on small screens
      }
    };
    
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // On first load, check localStorage for sidebar preference
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarExpanded');
    if (savedState !== null) {
      setIsSidebarExpanded(savedState === 'true');
    }
  }, []);

  // Toggle sidebar expanded state
  const toggleSidebar = () => {
    const newState = !isSidebarExpanded;
    setIsSidebarExpanded(newState);
    localStorage.setItem('sidebarExpanded', String(newState));
  };

  return (
    <div className="flex h-screen bg-gray-50 overflow-hidden">
      {/* Mobile header with menu button */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-40 flex items-center justify-between bg-white p-4 shadow-sm">
        <button 
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="text-gray-700 hover:text-blue-600"
        >
          {isMobileMenuOpen ? 
            <XMarkIcon className="h-6 w-6" /> : 
            <Bars3Icon className="h-6 w-6" />
          }
        </button>
        <h1 className="text-lg font-semibold">Skilling.ai</h1>
        <div className="w-6"></div> {/* Spacer for alignment */}
      </div>

      {/* Sidebar container */}
      <aside 
        className={`
          fixed z-30 h-full
          ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'} 
          lg:translate-x-0 lg:relative 
          transition-transform duration-300 ease-in-out
        `}
      >
        <Sidebar 
          isExpanded={isSidebarExpanded} 
          toggleSidebar={toggleSidebar} 
          onItemClick={() => isMobile && setIsMobileMenuOpen(false)} 
        />
      </aside>

      {/* Backdrop for mobile */}
      {isMobile && isMobileMenuOpen && (
        <div 
          className="fixed inset-0 z-20 bg-black/50 lg:hidden" 
          onClick={() => setIsMobileMenuOpen(false)}
        ></div>
      )}

      {/* Main content area */}
      <main className="flex-1 overflow-y-auto">
        {/* Add padding-top on mobile for the header */}
        <div className="pt-16 lg:pt-0">
          {/* Breadcrumbs */}
          <div className="bg-white border-b px-6 py-3">
            <Breadcrumbs crumbs={crumbs} />
          </div>
          
          {/* Page content */}
          <div className="p-1 md:p-6">
            <div className="bg-white rounded-lg shadow-sm p-1 md:p-6">
              {children}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}