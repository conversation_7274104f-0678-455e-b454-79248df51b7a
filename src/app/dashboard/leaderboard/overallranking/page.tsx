"use client";
import React, { useState } from "react";
import { ChevronUpIcon, ChevronDownIcon } from "@heroicons/react/20/solid";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";
import Nothing from "@/components/ui/Nothing";
import Image from "next/image";

const participants = [
  {
  name: "<PERSON>",
  classRank: 1,
  codeQuality: {
    Pgr1: {
      assessment1: "Optimal",
      assessment2: "Effective",
      assessment3: "Works",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "5s",
      assessment2: "15s",
    },
    Pgr2: {
      assessment1: "5s",
    },
  },
  progression: {
    currentLevel: 1,
    maxLevel: 10,
  },
},
{
  name: "<PERSON>",
  classRank: 2,
  codeQuality: {
    Pgr1: {
      assessment1: "Effective",
      assessment2: "Works",
      assessment3: "Needs Improvement",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "10s",
      assessment2: "20s",
    },
    Pgr2: {
      assessment1: "12s",
    },
  },
  progression: {
    currentLevel: 2,
    maxLevel: 10,
  },
},
{
  name: "<PERSON>",
  classRank: 3,
  codeQuality: {
    Pgr1: {
      assessment1: "Optimal",
      assessment2: "Effective",
      assessment3: "Works",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "7s",
      assessment2: "17s",
    },
    Pgr2: {
      assessment1: "8s",
    },
  },
  progression: {
    currentLevel: 1,
    maxLevel: 10,
  },
},
{
  name: "Emma Davis",
  classRank: 4,
  codeQuality: {
    Pgr1: {
      assessment1: "Works",
      assessment2: "Needs Improvement",
      assessment3: "Needs Improvement",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "9s",
      assessment2: "18s",
    },
    Pgr2: {
      assessment1: "10s",
    },
  },
  progression: {
    currentLevel: 2,
    maxLevel: 10,
  },
},
{
  name: "Noah Wilson",
  classRank: 5,
  codeQuality: {
    Pgr1: {
      assessment1: "Optimal",
      assessment2: "Effective",
      assessment3: "Works",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "6s",
      assessment2: "14s",
    },
    Pgr2: {
      assessment1: "9s",
    },
  },
  progression: {
    currentLevel: 3,
    maxLevel: 10,
  },
},
{
  name: "Ava Martinez",
  classRank: 6,
  codeQuality: {
    Pgr1: {
      assessment1: "Effective",
      assessment2: "Works",
      assessment3: "Optimal",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "8s",
      assessment2: "16s",
    },
    Pgr2: {
      assessment1: "7s",
    },
  },
  progression: {
    currentLevel: 1,
    maxLevel: 10,
  },
},
{
  name: "Ethan Garcia",
  classRank: 7,
  codeQuality: {
    Pgr1: {
      assessment1: "Needs Improvement",
      assessment2: "Works",
      assessment3: "Effective",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "10s",
      assessment2: "20s",
    },
    Pgr2: {
      assessment1: "12s",
    },
  },
  progression: {
    currentLevel: 2,
    maxLevel: 10,
  },
},
{
  name: "Sophia Taylor",
  classRank: 8,
  codeQuality: {
    Pgr1: {
      assessment1: "Works",
      assessment2: "Needs Improvement",
      assessment3: "Effective",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "11s",
      assessment2: "19s",
    },
    Pgr2: {
      assessment1: "10s",
    },
  },
  progression: {
    currentLevel: 1,
    maxLevel: 10,
  },
},
{
  name: "Mason Anderson",
  classRank: 9,
  codeQuality: {
    Pgr1: {
      assessment1: "Effective",
      assessment2: "Optimal",
      assessment3: "Works",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "6s",
      assessment2: "13s",
    },
    Pgr2: {
      assessment1: "9s",
    },
  },
  progression: {
    currentLevel: 3,
    maxLevel: 10,
  },
},
{
  name: "Isabella Thomas",
  classRank: 10,
  codeQuality: {
    Pgr1: {
      assessment1: "Needs Improvement",
      assessment2: "Works",
      assessment3: "Effective",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "10s",
      assessment2: "18s",
    },
    Pgr2: {
      assessment1: "11s",
    },
  },
  progression: {
    currentLevel: 2,
    maxLevel: 10,
  },
},
{
  name: "Alexander Harris",
  classRank: 11,
  codeQuality: {
    Pgr1: {
      assessment1: "Works",
      assessment2: "Optimal",
      assessment3: "Effective",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "9s",
      assessment2: "15s",
    },
    Pgr2: {
      assessment1: "10s",
    },
  },
  progression: {
    currentLevel: 4,
    maxLevel: 10,
  },
},
{
  name: "Mia Clark",
  classRank: 12,
  codeQuality: {
    Pgr1: {
      assessment1: "Effective",
      assessment2: "Works",
      assessment3: "Needs Improvement",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "11s",
      assessment2: "19s",
    },
    Pgr2: {
      assessment1: "12s",
    },
  },
  progression: {
    currentLevel: 2,
    maxLevel: 10,
  },
},
{
  name: "Jacob Lewis",
  classRank: 13,
  codeQuality: {
    Pgr1: {
      assessment1: "Optimal",
      assessment2: "Effective",
      assessment3: "Works",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "7s",
      assessment2: "14s",
    },
    Pgr2: {
      assessment1: "8s",
    },
  },
  progression: {
    currentLevel: 3,
    maxLevel: 10,
  },
},
{
  name: "Charlotte Walker",
  classRank: 14,
  codeQuality: {
    Pgr1: {
      assessment1: "Needs Improvement",
      assessment2: "Works",
      assessment3: "Effective",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "10s",
      assessment2: "18s",
    },
    Pgr2: {
      assessment1: "11s",
    },
  },
  progression: {
    currentLevel: 4,
    maxLevel: 10,
  },
},
{
  name: "William Scott",
  classRank: 15,
  codeQuality: {
    Pgr1: {
      assessment1: "Works",
      assessment2: "Effective",
      assessment3: "Optimal",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "9s",
      assessment2: "17s",
    },
    Pgr2: {
      assessment1: "10s",
    },
  },
  progression: {
    currentLevel: 1,
    maxLevel: 10,
  },
},
{
  name: "Amelia Adams",
  classRank: 16,
  codeQuality: {
    Pgr1: {
      assessment1: "Optimal",
      assessment2: "Effective",
      assessment3: "Works",
    },
  },
  speed: {
    Pgr1: {
      assessment1: "6s",
      assessment2: "13s",
    },
    Pgr2: {
      assessment1: "7s",
    },
  },
  progression: {
    currentLevel: 2,
    maxLevel: 10,
  },
}
];

const Page = () => {
  const [expandedRow, setExpandedRow] = useState(null);

  const handleRowClick = (index) => {
    setExpandedRow(expandedRow === index ? null : index);
  };

  return (
    <main className="h-full w-screen flex flex-col justify-normal gap-6 items-center px-4 pt-4 pb-10">
      {/* Header */}
      <div className="w-11/12 h-[10%] bg-white rounded-2xl shadow-lg flex justify-between px-6 py-4 gap-2 lg:gap-20">
        <div className="flex justify-start flex-1">
          <Image
            src="/logo.png"
            width={60}
            height={60}
            alt="Company Logo"
          />
        </div>
        <div className="flex-1 flex justify-center items-center">
          <Heading pgHeading={"Leaderboard Ranking"} />
        </div>
        <div className="flex flex-1 justify-end items-center">
          <span className="text-sm lg:text-lg font-mono">Total Participants: 50</span>
        </div>
      </div>

      {/* Table */}
      <div className="shadow-lg border-y-8 border-white mx-auto w-11/12 rounded-2xl bg-white p-4 h-4/6 relative">
        {participants.length > 0 ? (
          <div className="overflow-y-auto w-full h-[600px] relative ">
            <table className="min-w-full bg-white border rounded-lg">
              <thead className="sticky top-0 bg-secondary text-white shadow-md z-40">
                <tr>
                  <th className="py-3 px-6 text-left">Name</th>
                  <th className="py-3 px-6 text-left">Rank</th>
                  <th className="py-3 px-6 text-left">Progression</th>
                  <th className="py-3 px-6 text-left">Expand</th>
                </tr>
              </thead>
              <tbody>
                {participants.map((participant, index) => (
                  <React.Fragment key={index}>
                    <tr
                      onClick={() => handleRowClick(index)}
                      className="cursor-pointer transition duration-300 ease-in-out hover:bg-gray-100"
                    >
                      <td className="py-3 px-6 text-left">{participant.name}</td>
                      <td className="py-3 px-6 text-left">{participant.classRank}</td>
                      <td className="py-3 px-6 text-left">
                        <div className="relative h-4 w-full bg-gray-200 rounded-full">
                          <div
                            className="absolute top-0 left-0 h-full bg-green-500 rounded-full transition-all duration-500"
                            style={{
                              width: `${(participant.progression.currentLevel / participant.progression.maxLevel) * 100}%`,
                            }}
                          />
                          <span className="absolute inset-0 flex justify-center items-center text-xs font-bold">
                            Level {participant.progression.currentLevel} / {participant.progression.maxLevel}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-6 text-left">
                        {expandedRow === index ? (
                          <ChevronUpIcon className="w-5 h-5 transition-transform duration-500" />
                        ) : (
                          <ChevronDownIcon className="w-5 h-5 transition-transform duration-500" />
                        )}
                      </td>
                    </tr>
                    {expandedRow === index && (
                      <tr>
                        <td colSpan={4} className="py-4 px-6 text-left bg-gray-50">
                          <div className="flex gap-10 justify-start items-start animate-slideDown">
                            <div className="flex flex-col">
                              <h3 className="font-bold">Code Quality:</h3>
                              <ul>
                                {Object.keys(participant.codeQuality.Pgr1).map((assessment, i) => (
                                  <li key={i}>
                                    {assessment}: {participant.codeQuality.Pgr1[assessment]}
                                  </li>
                                ))}
                              </ul>
                            </div>
                            <div className="flex flex-col">
                              <h3 className="font-bold">Speed:</h3>
                              <ul>
                                {Object.keys(participant.speed.Pgr1).map((assessment, i) => (
                                  <li key={i}>
                                    {assessment}: {participant.speed.Pgr1[assessment]}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <Nothing
            title="No Participants Available"
            para="There are currently no participants to display. Please check back later."
            btnName="Add Participant"
            btnIcon={DocumentTextIcon}
            onClickFunctionForBtn={() => {
              window.location.href = `/admin/participants/createparticipant`;
            }}
          />
        )}
      </div>
    </main>
  );
};

export default Page;
