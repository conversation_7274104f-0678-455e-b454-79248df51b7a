import Heading from "@/components/ui/Heading";
import Link from "next/link";
import { ArrowTrendingUpIcon, Bars4Icon, UserIcon } from "@heroicons/react/20/solid";

export default function Page() {
  return (
    <main className="w-full flex h-full bg-white rounded-xl flex-col items-center justify-between p-1">
      <div className="w-full overflow-auto">
        <div className="p-3">
          <Heading pgHeading="LeaderBoard" />
        </div>
        <div className="w-full gap-5 flex justify-between p-2">
          <div className="w-full h-[300px] bg-white rounded-lg border-2 border-black p-2 flex flex-col justify-around items-center">
            <ArrowTrendingUpIcon className="w-40 h-40  text-textColor border-4 border-textColor rounded-full p-5" />
            <Link
              className="w-full px-2 py-2 bg-textColor text-white rounded-md text-center text-[10px] md:text-[15px] lg:text-[18px] flex justify-center"
              href={"/dashboard/leaderboard/myprogress"}>
              My Progress
            </Link>
          </div>
          <div className="w-full h-[300px] bg-white  rounded-lg border-2 border-black p-2 flex flex-col justify-around items-center">
            <Bars4Icon className="w-40 h-40  text-textColor border-4 border-textColor rounded-full p-5" />
            <Link
              className="w-full px-2 py-2 bg-textColor text-white rounded-md text-center text-[10px] md:text-[15px] lg:text-[18px] flex justify-center"
              href={"/dashboard/group"}>My Cohort Progress</Link>
          </div>
          <div className="w-full h-[300px] bg-white  rounded-lg border-2 border-black p-2 flex flex-col justify-around items-center">
            <UserIcon className="w-40 h-40  text-textColor border-4 border-textColor rounded-full p-5" />
            <Link
              className="w-full px-2 py-2 bg-textColor text-white rounded-md text-center text-[10px] md:text-[15px] lg:text-[18px] flex justify-center"
              href={"/dashboard/userattempts"}>User Attempts</Link>
          </div>
        </div>
      </div>
    </main>
  );
}
