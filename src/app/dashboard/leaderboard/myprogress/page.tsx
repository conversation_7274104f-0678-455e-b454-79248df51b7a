import Heading from "@/components/ui/Heading";
import { CommandLineIcon, CubeTransparentIcon, Squares2X2Icon } from "@heroicons/react/20/solid";
import Link from "next/link";

export default function Page() {
    return (
        <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-1">
            <div className="w-full overflow-auto">
                <div className="p-3">
                    <Heading pgHeading="My Progress" />
                </div>
                <div className="w-full gap-5 flex  justify-between p-2 ">
                    <div className="w-full h-[300px] bg-white rounded-lg border-2 border-black p-2 flex flex-col justify-around items-center">
                        <CubeTransparentIcon className="w-40 h-40 text-textColor" />

                        <h1 className="text-md "> OverView Of  Competition  </h1>


                        <Link
                            href={"/dashboard/leaderboard/myprogress/overall"}
                            className="w-full px-2 py-2 bg-textColor text-white rounded-md text-center text-[10px] md:text-[15px] lg:text-[18px] flex justify-center"
                        >
                            Overall Competition
                        </Link>

                    </div>

                    <div className="w-full h-[300px] bg-white rounded-lg border-2 border-black p-2 flex flex-col justify-around items-center">
                        <CommandLineIcon className="w-40 h-40  text-textColor" />

                        <h1> Take The Test Again</h1>

                        <Link href={"/dashboard/result"}
                            className="w-full px-2 py-2 bg-textColor text-white rounded-md text-center text-[10px] md:text-[15px] lg:text-[18px] flex justify-center">
                            Retest Due
                        </Link>
                    </div>

                    <div className='w-full h-[300px] bg-white rounded-lg border-2 border-black p-2 flex flex-col justify-around items-center'>
                        <Squares2X2Icon className='w-40 h-40  text-textColor' />

                        <h1> Ckeck All The Module By Score </h1>


                        <Link href={"/dashboard/result"}
                            className="w-full px-2 py-2 bg-textColor text-white rounded-md text-center text-[10px] md:text-[15px] lg:text-[18px] flex justify-center">
                            Scores By Module
                        </Link>

                    </div>

                </div>
            </div>
        </main>
    );
}
