"use client";
import Heading from '@/components/ui/Heading';
import Nothing from '@/components/ui/Nothing';
import { useGetAllUserGroup } from '@/hook/dashboard/leaderborad/useGetAllUserGroup';
import { useGetOverall } from '@/hook/dashboard/leaderborad/useGetOverall';

export default function Page() {
  const { data: OverallData, isLoading: isLoadingOverallData, isError: isErrorOverallData } = useGetOverall();
  const { data: groupData, isLoading: isLoadinGroup, isError: isErrorGroup } = useGetAllUserGroup();

  if (isLoadingOverallData || isLoadinGroup) {
    return <div>Loading...</div>;
  }

  if (isErrorOverallData || isErrorGroup) {
    return <div>Error fetching modules</div>;
  }

  const overallDataEntries = OverallData ? Object.entries(OverallData) : [];

  const getGroupName = (key) => {
    const group = groupData.find(group => group.group_id === parseInt(key));
    return group ? group.group_name : 'Unknown Group';
  };

  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-1">
      <div className="w-full overflow-auto">
        <div className="p-3">
          <Heading pgHeading="Overall Complete" />
        </div>
        <div className="flex flex-wrap gap-1 justify-start bg-white w-full rounded-2xl p-1 h-fit overflow-auto">
          {overallDataEntries.length === 0 ? (
            <div className="w-full flex justify-center items-center h-full">
              <Nothing
                title="No Data"
                para="There are currently no data to display. Please check back later."
              />
            </div>
          ) : (
            overallDataEntries.map(([key, value]) => (
              <div
                key={key}
                className={`flex flex-col w-full gap-1 rounded-lg border h-fit border-[#A9A9A9] p-2`}
              >
                <div className="flex flex-col w-full justify-center items-center">
                  <h1 className="font-bold text-lg">
                    {getGroupName(key)}
                  </h1>
                  <p>Status: {value}</p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </main>
  );
}
