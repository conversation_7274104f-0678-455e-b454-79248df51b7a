// "use client"
// import React, { useState, useEffect } from "react";
// import QuizHeader from "@/components/dashboard/quiz/QuizHeader";
// import { useRouter, useSearchParams } from "next/navigation";
// // Import mock data for development
// import {
//   mockAssessmentQuestions,
//   mockUserAssessmentDetails,
//   mockLiveStateCheck,
//   mockSelectedAnswers,
//   mockSubmitAssessmentAnswers
// } from '@/utils/dummyData/quizPageData';
// //API Hooks
// //Comment out real development hooks 
// //import { useGetAssessmentQuestions } from "@/hook/assessments/useGetAssessmentQuestions";
// //import { UseSubmitAssessmentAnswers } from "@/hook/user/UseSubmitAssessmentAnswers";
// //import { useStartUserAssessments } from "@/hook/assessment_attempts/useStartUserAssessments";
// import { SubmitAttempt } from "@/types/LMSTypes"
// import QuestionUserBox from "@/components/dashboard/quiz/QuestionUserBox";
// import SummaryModal from "@/components/dashboard/quiz/SummaryModal";
// import SubmitModal from "@/components/dashboard/quiz/SubmitModal";
// import { useTimer } from "react-timer-hook";
// import { XMarkIcon } from "@heroicons/react/24/outline";
// import { useUserRefreshToken } from "@/hook/useUserRefreshToken";
// interface storedAnswer {
//   assessment_question_id: number,
//   submitted_answer_option: number,
//   answer_text: string
// }

// const QuizPage = () => {
//   const searchParams = useSearchParams();
//   const router = useRouter();
//   // Use mock data instead of fetching from API
//   const examId = searchParams.get("exam_id") || "EXAM001";
//   const user_assessment_id = searchParams.get("user_assessment_id") || "UA12345";
    
//   // Use mock questions data
//   const questions = mockAssessmentQuestions;
//   console.log("Development questions:", questions);
    
//   // Use mock user assessment details
//   const userAssesmentDetails = mockUserAssessmentDetails;
    
//   const time = new Date();
//     time.setSeconds(
//       time.getSeconds() + 60 * (Number(searchParams.get("time_allowed")) || 60)
//   );
    
//   // Initialize with mock data
//   const [liveStateCheck, setLiveStateCheck] = useState<Array<any>>(mockLiveStateCheck);
  
//   console.log("Development live value:", liveStateCheck)
  
//   // Uncommnent it for real data
//   // const examId = searchParams.get("exam_id");
//   // const user_assessment_id = searchParams.get("user_assessment_id");
//   // const { data: questions } = useGetAssessmentQuestions(examId);
//   // console.log("questinn", questions);
//   // const { data: userAssesmentDetails } = useStartUserAssessments(user_assessment_id);
//   // const time = new Date();
//   // time.setSeconds(
//   //   time.getSeconds() + 60 * Number(searchParams.get("time_allowed")!)
//   // );
//   // const [liveStateCheck, setLiveStateCheck] = useState<Array<any>>([]);

//   // console.log("live value is coming as", liveStateCheck)

//   const [submitText, setSubmitText] = useState<string>("Submit")
//   const [arrayOfObjects, setArrayOfObjects] = useState<SubmitAttempt[]>([]);
//   const [activeQuestion, setActiveQuestion] = useState<number>(0);
//   const [selectedAnswers, setSelectedAnswer] = useState<storedAnswer[]>(Array(questions?.length).fill({
//     assessment_question_id: 0,
//     submitted_answer_option: 0,
//     answer_text: null
//   }));
//   const [summaryModal, setSummaryModal] = useState(false)
//   const [allQuestionsCompleted, setAllQuestionsCompleted] = useState(false)
//   const [submitModal, setSubmitModal] = useState(false)
//   const [switchCount, setSwitchCount] = useState(0);
//   const [backButtonCounter, setBackButtonCounter] = useState(0);
//   const [optionChangeCount, setOptionChangeCount] = useState<number[]>(questions ? Array(questions.length).fill(0) : []);
//   const [questionStartTime, setQuestionStartTime] = useState<Date | null>(new Date);
//   const [timeTakenPerQuestion, setTimeTakenPerQuestion] = useState<number[]>(Array(questions?.length).fill(0));
//   const [text, setText] = useState('');
//   let answers = {};
//   const [pageCounter, setPageCounter] = useState<number>(0)
//   const [submitAssessmentAnswers, setSubmitAssessmentAnswers] = useState([])
//   const [allQuestionSubmitted, setAllQuestionSubmitted] = useState(false)

//   console.log("allQuestionSubmitted", allQuestionSubmitted)

//   console.log("tmme", time)
//   useEffect(() => {
//     const handleVisibilityChange = () => {
//       if (document.visibilityState === 'visible') {
//         setSwitchCount(prevCount => prevCount + 1);
//       }
//     };

//     document.addEventListener('visibilitychange', handleVisibilityChange);

//     return () => {
//       document.removeEventListener('visibilitychange', handleVisibilityChange);
//     };
//   }, []);

//   function countNumbers(arr: number[]) {
//     // Initialize an object to store the counts
//     let counts = {
//       "1": 0,
//       "2": 0,
//       "3": 0,
//       "4": 0
//     };
//     // Loop through the array and increment counts for each number
//     arr.forEach(num => {
//       if (counts[num] !== undefined) {
//         counts[num]++;
//       }
//     });

//     // Return the counts object
//     return counts;
//   }

//   const tempValues = countNumbers(liveStateCheck)

//   function checkValues(data: { [key: string]: number }): boolean {
//     return data["1"] === 0 && data["2"] === 0 && data["4"] === 0;
//   }

//   useEffect(() => {
//     setAllQuestionSubmitted(checkValues(tempValues));
//   }, [liveStateCheck]);

//   useEffect(() => {
//     const handleVisibilityChange = () => {
//       if (document.visibilityState === 'visible') {
//         setBackButtonCounter(prevCount => prevCount + 1);
//       }
//     };

//     const handlePopState = () => {
//       setBackButtonCounter(prevCount => prevCount + 1);
//     };

//     document.addEventListener('visibilitychange', handleVisibilityChange);
//     window.addEventListener('popstate', handlePopState);

//     return () => {
//       document.removeEventListener('visibilitychange', handleVisibilityChange);
//       window.removeEventListener('popstate', handlePopState);
//     };
//   }, []);

//   useEffect(() => {
//     // Initialize the time taken per question array with the correct length
//     if (questions) {
//       setTimeTakenPerQuestion(Array(questions.length).fill(0));
//       setOptionChangeCount(Array(questions.length).fill(0)); // Update optionChangeCount when questions change
//     }
//   }, [questions]);

//   const getTimeTakenPerQuestion = () => { //might need some work as it calculate time as a whole instead of per question
//     const endTime = new Date();
//     const timeDifferenceInSeconds = Math.floor((endTime.getTime() - (questionStartTime?.getTime() || endTime.getTime())) / 1000);

//     setTimeTakenPerQuestion(prevTimeTaken => {
//       // Update the time taken for the current question
//       const updatedTimeTaken = [...prevTimeTaken];
//       updatedTimeTaken[activeQuestion] = timeDifferenceInSeconds;
//       return updatedTimeTaken;
//     });
//   }

//   // Calculate score and increment to next question
//   const nextQuestion = () => {
//     if (pageCounter < questions?.length) {
//       getTimeTakenPerQuestion()
//       setActiveQuestion(prev => prev + 1);
//     }
//   };


//   const AddingAnswersToArray = () => {
//     const finalAnswerTemp = [...selectedAnswers, answers]
//     setSelectedAnswer(finalAnswerTemp);
//   }

//   const previousQuestion = () => {
//     getTimeTakenPerQuestion()
//     setActiveQuestion((prev) => prev - 1);
//   };

//   const createSubmitBody = () => {
//     setArrayOfObjects(submitAssessmentAnswers)

//   }
//   // Mock function for API submission - For development
//   const addDataForSubmit = {
//     mutate: async () => {
//         console.log("Development: Mock submitting answers", arrayOfObjects);
//         // Simulate API delay
//         await new Promise(resolve => setTimeout(resolve, 1000));
//         return { success: true };
//       }
//   };
//   //Uncomment for production
//   //const addDataForSubmit = UseSubmitAssessmentAnswers(userAssesmentDetails?.user_assessment_attempt_id, arrayOfObjects)


//   const prepareSubmit = () => {
//     createSubmitBody()
//     AddingAnswersToArray()
//     setSubmitText("Submitting...")
//     setSummaryModal(true)
//   }

//   const submitAnswerValue = async () => {
//     await addDataForSubmit.mutate()
//     setSubmitModal(true)
//     setSubmitText("Submitted")
//   }

//   const submitAnswerValueForTimeUp = async () => {
//     await addDataForSubmit.mutate()
//     //setSubmitModal(true) may add modified modal if times up!
//     setSubmitText("Submitted")
//   }

//   const { seconds, minutes, hours } = useTimer({ time });

//   // useEffect(() => {

//   //   if (time && (minutes <= 10 && hours == 0)){
//   //     alert(
//   //       "10 minutes are remaining, please hurry up !!"
//   //     )
//   //   }else if(time && (minutes <= 5 && hours == 0)){
//   //     alert(
//   //       "5 minutes are remaining, please hurry up !!"
//   //     )
//   //   }
//   //   else if (time && (seconds == 0 && minutes == 0 && hours == 0)){
//   //     alert(
//   //       "Thank you for taking the test, we will check your answers. Best of luck for the results."
//   //     );
//   //     submitAnswerValueForTimeUp();
//   //   }


//   // }, [minutes])


//   if (userAssesmentDetails?.hasOwnProperty('detail')) {
//     return (
//       <div className="modal flex justify-center items-center fixed inset-0 bg-black bg-opacity-40 overflow-auto z-10">
//         <div className="flex flex-col gap-3 modal-content bg-white m-auto p-4 border border-gray-300 rounded-md w-6/12 h-3/6">
//           <div className="flex justify-end">
//             <button className="block h-6 w-6" onClick={() => router.push('/dashboard/result')}>
//               <XMarkIcon aria-hidden="true" />
//             </button>
//           </div>
//           <p className="text-center md:text-2xl items-center pt-10 lg:pt-32">
//             {userAssesmentDetails.detail}
//           </p>
//         </div>
//       </div>

//     )
//   }



//   return (
//     <main className='h-screen sm:h-svh w-screen flex flex-col items-center '>
//       <div className="w-[100%]">
//         <QuizHeader
//           examId={userAssesmentDetails?.assessment_name}
//           expiryTimestamp={time}
//           submit={prepareSubmit}
//           submitTextValue={submitText}
//         />
//       </div>
//       <div className="flex flex-col items-center justify-center w-[95%] h-[90%]">
//         {questions && userAssesmentDetails && addDataForSubmit && (
//           <QuestionUserBox
//             submit={prepareSubmit}
//             getAssessmentQuestions={questions}
//             setliveStateCheck={setLiveStateCheck}
//             liveStateCheck={liveStateCheck}
//             startUserAssessment={userAssesmentDetails}
//             allQuestionSubmitted={allQuestionSubmitted}
//             setSubmitAssessmentAnswers={setSubmitAssessmentAnswers}
//           />
//         )}
//       </div>

//       {summaryModal &&
//         <SummaryModal
//           submitAnswerValue={submitAnswerValue}
//           liveStateCheck={liveStateCheck}
//           modalName={"SummaryModal"}
//           modalText={"modalText"}
//           onClose={() => setSummaryModal(false)}
//           setAllQuestionsCompleted={setAllQuestionsCompleted}
//         />}

//       {submitModal &&
//         <SubmitModal modalName={"Assessment Submitted"} modalText={"Thank you for taking the test, we will check your answers. Best of luck for the results"} onClose={() => setSubmitModal(false)} />}
//     </main>
//   )
// }

// export default QuizPage;

"use client"
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { useTimer } from "react-timer-hook";

// Import components
import QuizHeader from "@/components/dashboard/quiz/QuizHeader";
import QuestionUserBox from "@/components/dashboard/quiz/QuestionUserBox";
import SummaryModal from "@/components/dashboard/quiz/SummaryModal";
import SubmitModal from "@/components/dashboard/quiz/SubmitModal";

// Import mock data (for development)
// In production, replace with actual API hooks
import { 
  mockAssessmentQuestions, 
  mockUserAssessmentDetails,
  mockLiveStateCheck,
  mockSelectedAnswers,
  mockSubmitAssessmentAnswers,
  mockCompleteAnswers,
  mockUseSubmitAssessmentAnswers
} from '@/utils/dummyData/quizPageData';

// Types
interface storedAnswer {
  assessment_question_id: number,
  submitted_answer_option: number,
  answer_text: string | null
}

interface SubmitAttempt {
  user_assessment_attempt_id: string,
  assessment_question_id: number,
  submitted_answer_option: number | null,
  answer_text: string | null,
  submitted_answer_marks: number,
  submitted_answer_status: string,
  evaluation_comments: string,
  evaluated_by_email: string,
  time_take_to_answer_in_sec: number,
  number_of_screen_switch: number,
  used_back_button: number,
  changed_answer: number
}

const QuizPage = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  // Get URL parameters with fallbacks to mock data
  const examId = searchParams.get("exam_id") || "EXAM001";
  const user_assessment_id = searchParams.get("user_assessment_id") || "UA12345";
  
  // Use mock data for development
  // In production, use actual API hooks
  const questions = mockAssessmentQuestions;
  const userAssesmentDetails = mockUserAssessmentDetails;
  
  // Set timer
  const time = new Date();
  time.setSeconds(
    time.getSeconds() + 60 * (Number(searchParams.get("time_allowed")) || userAssesmentDetails.time_allowed)
  );

  // State management
  const [liveStateCheck, setLiveStateCheck] = useState<Array<string>>(mockLiveStateCheck);
  const [submitText, setSubmitText] = useState<string>("Submit");
  const [arrayOfObjects, setArrayOfObjects] = useState<SubmitAttempt[]>([]);
  const [activeQuestion, setActiveQuestion] = useState<number>(0);
  const [selectedAnswers, setSelectedAnswer] = useState<storedAnswer[]>(mockSelectedAnswers);
  const [summaryModal, setSummaryModal] = useState<boolean>(false);
  const [allQuestionsCompleted, setAllQuestionsCompleted] = useState<boolean>(false);
  const [submitModal, setSubmitModal] = useState<boolean>(false);
  const [switchCount, setSwitchCount] = useState<number>(0);
  const [backButtonCounter, setBackButtonCounter] = useState<number>(0);
  const [optionChangeCount, setOptionChangeCount] = useState<number[]>(
    Array(questions?.length).fill(0)
  );
  const [questionStartTime, setQuestionStartTime] = useState<Date>(new Date());
  const [timeTakenPerQuestion, setTimeTakenPerQuestion] = useState<number[]>(
    Array(questions?.length).fill(0)
  );
  const [pageCounter, setPageCounter] = useState<number>(0);
  const [submitAssessmentAnswers, setSubmitAssessmentAnswers] = useState<any[]>([]);
  const [allQuestionSubmitted, setAllQuestionSubmitted] = useState<boolean>(false);

  // Mocked API submission function
  const addDataForSubmit = mockUseSubmitAssessmentAnswers(
    userAssesmentDetails?.user_assessment_attempt_id, 
    arrayOfObjects
  );

  // Check visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        setSwitchCount(prevCount => prevCount + 1);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Update submission data when complete answers change
  useEffect(() => {
    // Add null check to prevent TypeError
    if (submitAssessmentAnswers && Array.isArray(submitAssessmentAnswers) && submitAssessmentAnswers.length > 0) {
      setArrayOfObjects(submitAssessmentAnswers);
    }
  }, [submitAssessmentAnswers]);

  // Track back button usage
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        setBackButtonCounter(prevCount => prevCount + 1);
      }
    };

    const handlePopState = () => {
      setBackButtonCounter(prevCount => prevCount + 1);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('popstate', handlePopState);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Initialize time tracking arrays when questions load
  useEffect(() => {
    if (questions?.length) {
      setTimeTakenPerQuestion(Array(questions.length).fill(0));
      setOptionChangeCount(Array(questions.length).fill(0));
    }
  }, [questions]);

  // Calculate time spent on current question
  const getTimeTakenPerQuestion = () => {
    const endTime = new Date();
    const timeDifferenceInSeconds = Math.floor(
      (endTime.getTime() - questionStartTime.getTime()) / 1000
    );

    setTimeTakenPerQuestion(prevTimeTaken => {
      const updatedTimeTaken = [...prevTimeTaken];
      updatedTimeTaken[activeQuestion] = timeDifferenceInSeconds;
      return updatedTimeTaken;
    });
  };

  // Navigate to next question
  const nextQuestion = () => {
    if (activeQuestion < questions?.length - 1) {
      getTimeTakenPerQuestion();
      setActiveQuestion(prev => prev + 1);
      setQuestionStartTime(new Date());
    }
  };

  // Navigate to previous question
  const previousQuestion = () => {
    if (activeQuestion > 0) {
      getTimeTakenPerQuestion();
      setActiveQuestion(prev => prev - 1);
      setQuestionStartTime(new Date());
    }
  };

  // Prepare for submission
  const prepareSubmit = () => {
    setSubmitText("Submitting...");
    setSummaryModal(true);
  };

  // Submit answers
  const submitAnswerValue = async () => {
    await addDataForSubmit.mutate();
    setSummaryModal(false);
    setSubmitModal(true);
    setSubmitText("Submitted");
  };

  // Submit answers when time is up
  const submitAnswerValueForTimeUp = async () => {
    await addDataForSubmit.mutate();
    setSubmitText("Submitted");
    setSubmitModal(true);
  };

  // Check if user may have left the page or pressed back
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      const message = "Are you sure you want to leave? Your progress may be lost.";
      e.returnValue = message;
      return message;
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Handle timer expiration
  const { seconds, minutes, hours } = useTimer({ 
    expiryTimestamp: time,
    onExpire: () => {
      alert("Time's up! Your quiz will be submitted automatically.");
      submitAnswerValueForTimeUp();
    }
  });

  // Time warnings
  useEffect(() => {
    if (minutes === 10 && hours === 0 && seconds === 0) {
      alert("10 minutes remaining. Please finish your quiz soon!");
    } else if (minutes === 5 && hours === 0 && seconds === 0) {
      alert("Only 5 minutes remaining!");
    }
  }, [minutes, seconds, hours]);

  // If user assessment details are not available
  if (userAssesmentDetails?.hasOwnProperty('detail')) {
    return (
      <div className="modal flex justify-center items-center fixed inset-0 bg-black bg-opacity-40 overflow-auto z-10">
        <div className="flex flex-col gap-3 modal-content bg-white m-auto p-6 border border-gray-300 rounded-lg w-11/12 max-w-2xl">
          <div className="flex justify-end">
            <button 
              className="p-1 rounded-full hover:bg-gray-100" 
              onClick={() => router.push('/dashboard/result')}
            >
              <XMarkIcon className="h-6 w-6 text-gray-500" aria-hidden="true" />
            </button>
          </div>
          <p className="text-center text-xl md:text-2xl py-8">
            {userAssesmentDetails.detail || "User assessment details not available"}
          </p>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen w-full bg-gray-50 flex flex-col">
      {/* Header with timer */}
      <div className="w-full bg-white shadow-sm">
        <QuizHeader
          examId={userAssesmentDetails?.assessment_name}
          expiryTimestamp={time}
          submit={prepareSubmit}
          submitTextValue={submitText}
        />
      </div>
      
      {/* Main quiz content */}
      <div className="flex-grow flex items-center justify-center p-4">
        <div className="w-full max-w-7xl">
          {questions && userAssesmentDetails && addDataForSubmit && (
            <QuestionUserBox
              submit={prepareSubmit}
              getAssessmentQuestions={questions}
              setliveStateCheck={setLiveStateCheck}
              liveStateCheck={liveStateCheck}
              startUserAssessment={userAssesmentDetails}
              allQuestionSubmitted={allQuestionSubmitted}
              setSubmitAssessmentAnswers={setSubmitAssessmentAnswers}
            />
          )}
        </div>
      </div>

      {/* Modals */}
      {summaryModal && (
        <SummaryModal
          submitAnswerValue={submitAnswerValue}
          liveStateCheck={liveStateCheck}
          modalName={"Quiz Summary"}
          modalText={"Please review your answers before final submission"}
          onClose={() => setSummaryModal(false)}
          setAllQuestionsCompleted={setAllQuestionsCompleted}
        />
      )}

      {submitModal && (
        <SubmitModal 
          modalName={"Assessment Submitted"} 
          modalText={"Thank you for taking the test. We will check your answers. Best of luck for the results!"} 
          onClose={() => {
            setSubmitModal(false);
            router.push('/dashboard/result');
          }}
          okclickPath="/dashboard/result"
        />
      )}
    </main>
  );
};

export default QuizPage;