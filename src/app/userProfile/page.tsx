"use client";
import NavigationHeader from "@/components/ui/header/NavigationHeader";
import { useState } from "react";
import GeneralInfo from "@/components/dashboard/userProfile/GeneralInfo";
import EduQualification from "@/components/dashboard/userProfile/EduQualification";
import SocialLinks from "@/components/dashboard/userProfile/SocialLinks";
import ItSkills from "@/components/dashboard/userProfile/ItSkills";
import PersonalInfo from "@/components/dashboard/userProfile/PersonalInfo";
import Notifications from "@/components/dashboard/userProfile/Notifications";


const UserProfile = () => {
  const [selectedComponent, setSelectedComponent] = useState('GeneralInfo');

  const renderComponent = () => {
    switch (selectedComponent) {
      case 'GeneralInfo':
        return <GeneralInfo />;
      case 'EduQualification':
        return <EduQualification />;
      case 'SocialLinks':
        return <SocialLinks />;
      case 'ItSkills':
        return <ItSkills />;
      case 'PersonalInfo':
        return <PersonalInfo />;
      case 'Notifications':
        return <Notifications />;
      default:
        return <GeneralInfo />;
    }
  };


  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:');
  };

  return (
    <>
      <NavigationHeader />
      <div className="flex flex-col lg:flex-row gap-6 min-h-screen">
        <aside className="shadow-md sticky top-0 lg:sticky lg:top-0 h-full lg:h-screen lg:w-64 z-10 bg-gray-900 text-white">
          <nav className="mt-9 flex flex-col space-y-3">
            <button onClick={() => setSelectedComponent('GeneralInfo')} className="py-1 px-4 text-left rounded bg-gray-700 hover:bg-gray-600">General Info</button>
            {/* <button onClick={() => setSelectedComponent('EduQualification')} className="py-1 px-4 text-left rounded bg-gray-700 hover:bg-gray-600">Educational Qualification</button>
            <button onClick={() => setSelectedComponent('PersonalInfo')} className="py-1 px-4 text-left rounded bg-gray-700 hover:bg-gray-600">Personal Info</button>
            <button onClick={() => setSelectedComponent('SocialLinks')} className="py-1 px-4 text-left rounded bg-gray-700 hover:bg-gray-600">Social Links</button>
            <button onClick={() => setSelectedComponent('ItSkills')} className="py-1 px-4 text-left rounded bg-gray-700 hover:bg-gray-600">IT Skills</button>
            <button onClick={() => setSelectedComponent('Notifications')} className="py-1 px-4 text-left rounded bg-gray-700 hover:bg-gray-600">Notifications</button> */}
          </nav>
        </aside>
        
        <form className="lg:m-9 w-full space-y-10 flex-1" onSubmit={handleSubmit}>
          {renderComponent()}
        </form>
      </div>
    </>

  );
};

export default UserProfile;
