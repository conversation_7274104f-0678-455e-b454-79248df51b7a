import { create } from "zustand";

interface SearchStoreProps {
    searchValue: boolean;
    searchCurrentValue: string;
    searchNumber: number;
    setSearchCurrentValue: (value: string) => void;
    setSearchValue: (value: boolean) => void;
    setSearchNumber: () => void;
}

export const useCount = create<SearchStoreProps>((set) => (
    {
        searchValue: false,
        searchNumber: 0,
        searchCurrentValue: "",
        setSearchCurrentValue: (value: string) => set({ searchCurrentValue: value }),
        setSearchNumber: () => set((state) => ({ searchNumber: state.searchNumber + 1 })),
        setSearchValue: (value: boolean) => set({ searchValue: value })
    }
));
