"use client";
import Nothing from "@/components/ui/Nothing";
import { useModuleContentByModuleId } from "@/hook/admin/module/usegetModuleContentByModuleId";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion"
import {
  ArrowRightIcon
} from "@heroicons/react/24/outline";

interface ModuleContentTableProps {
  moduleIDProp: number;
  groupIdProp: number
}

export default function ModuleContentTable({ moduleIDProp, groupIdProp }: any) {
  const [moduleId, setmoduleId] = useState(parseInt(moduleIDProp || ""));
  const [currentPage, setCurrentPage] = useState(1);

  console.log("module id for Table" + moduleIDProp);

  const {
    data: userValues,
    isLoading: userValuesLoading,
    isError: userValuesError,
  } = useModuleContentByModuleId(moduleId);

  useEffect(() => {
    if (!userValuesLoading && !userValuesError) {
      console.log("data:", userValues);
    }
  }, [userValues, userValuesLoading, userValuesError]);
 
  if (userValuesLoading) {
    return <div>Loading...</div>;
  }

  if (userValuesError) {
    return <div>Error fetching contents</div>;
  }

  const itemsPerPage = 3;
  const totalPages = Math.ceil(userValues?.length / itemsPerPage);

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  // Filter out duplicates based on content_id
  const uniqueUserValues = userValues?.filter(
    (value, index, self) =>
      index === self.findIndex((t) => t.content_id === value.content_id)
  );
    // Sort uniqueUserValues based on content.sequence (ascending order)
    const sortedUserValues = uniqueUserValues?.sort((a, b) => a.sequence - b.sequence);

  // const indexOfLastItem = currentPage * itemsPerPage;
  // const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  // const currentItems = userValues?.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className=" flex flex-col w-auto h-full   gap-1">
      <h2 className="text-lg font-medium leading-tight text-textPrimary mb-2">
        Topics :
      </h2>

      {/*Add Typescript Types*/}
      <div className="overflow-y-auto  w-full  h-full  rounded-lg  border">
        <table className="divide-y divide-gray-200 rounded-xl items-start w-full h-fit">
          <thead className="bg-gray-100 text-gray-700 text-sm uppercase sticky top-0 w-full">
            <tr className="w-full h-fit">
              {/* <th className="p-2 text-left">
                Content Id
              </th> */}
              <th className="p-2 text-left w-full">
                Content Title
              </th>
              {/* <th className="p-2 text-left">
                Description
              </th>
              <th className="p-2 text-left">
                Topics
              </th> */}
              <th className="p-2 text-left w-fit">
                Link
              </th>
            </tr>
          </thead>
          <tbody className="overflow-x-auto text-gray-500">
            {sortedUserValues && sortedUserValues.length > 0 ? (
              sortedUserValues.map((content, index) => (
                <tr
                  key={index}
                  className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 h-fit"
                >
                  {/* <td

                    className="p-2 text-left"
                  >
                    {content.content_id}
                  </td> */}
                  <td

                    className="p-2 text-left w-full"
                  >
                    {content.content_name}
                  </td>
                  {/* <td className="p-2 text-left">{content.content_description}</td>
                  <td className="p-2 text-left">{content.topics}</td> */}
                  <td className="p-2 text-left w-fit">
                    <Link
                      href={`/contentpanel?content_id=${content.content_id}&group_id=${groupIdProp}&module_id=${moduleIDProp}`}
                      
                    >
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="text-textSecondary border border-textSecondary px-3 py-1 rounded-xl"
                      >
                        Play
                        {/* <ArrowRightIcon
                          className="w-[25px] h-full" /> */}
                      </motion.button>
                    </Link>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="6" className="text-center p-4">
                  <Nothing
                    title="No Content Available"
                    para="There are currently content to display.Please check back later."
                  />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>


    </div>
  );
}
