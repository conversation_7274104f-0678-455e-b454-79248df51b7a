"use client";
import Nothing from "@/components/ui/Nothing";
import { useGetModuleAssessmentByModuleId } from "@/hook/admin/module/useGetModuleAssessmentByModuleID";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import {
  ArrowRightIcon
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion"

export default function ModuleAssessmentTable({ moduleIDProp }: any) {
  const [moduleId, setmoduleId] = useState(parseInt(moduleIDProp || ""));
  console.log("module id for Table" + moduleIDProp);
  const [currentPage, setCurrentPage] = useState(1);

  const {
    data: userValues,
    isLoading: userValuesLoading,
    isError: userValuesError,
  } = useGetModuleAssessmentByModuleId(moduleId);
  useEffect(() => {
    if (!userValuesLoading && !userValuesError) {
      console.log("data:", userValues);
    }
  }, [userValues, userValuesLoading, userValuesError]);

  if (userValuesLoading) {
    return <div>Loading...</div>;
  }

  if (userValuesError) {
    return <div>Error fetching assessments</div>;
  }

  const itemsPerPage = 2;
  const totalPages = Math.ceil(userValues?.length / itemsPerPage);

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };
  // const indexOfLastItem = currentPage * itemsPerPage;
  // const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  // const currentItems = userValues?.slice(indexOfFirstItem, indexOfLastItem);

  const uniqueUserValues = userValues?.filter(
    (value, index, self) =>
      index === self.findIndex((t) => t.assessment_id === value.assessment_id)
  );

  return (
    <div className=" flex flex-col w-auto h-full   gap-1">
      <h2 className="text-lg font-medium leading-tight text-textPrimary mb-2 mt-4">
        Assessments :
      </h2>

      {/*Add Typescript Types*/}
      <div className="overflow-y-auto  w-full  h-full  rounded-lg border">
        <table className="divide-y divide-gray-200 rounded-xl items-start w-full h-fit">
          <thead className="bg-gray-100  text-gray-700   text-sm uppercase sticky top-0  w-full">
            <tr className="w-full h-fit">
              {/* <th className="p-2 text-left">
                Assessment Id
              </th> */}
              <th className="p-2 text-left flex-auto">
                Assessment Title
              </th>
              <th className="p-2 text-left flex-auto">
                Total Time Allowed
              </th>
              <th className="py-2 text-left flex-none flex justify-end px-8">
                Link
              </th>
            </tr>
          </thead>
          <tbody className="overflow-x-auto text-gray-500">
            {uniqueUserValues && uniqueUserValues.length > 0 ? (
              uniqueUserValues?.map((assessment, index) => (
                <tr
                  key={index}
                  className="p-2 text-left h-fit"
                >
                  
                  <td
                    className="p-2 text-left"
                  >
                    {assessment.assessment_name}
                  </td>
                  <td
                    className="p-2 text-left"
                  >
                    {(assessment.total_time_allowed)/60} mins
                  </td>
                  {/* <td className="p-2 text-left">{assessment.instructions}</td> */}
                  <td className="p-2 py-2 text-left flex-none flex justify-end px-4">
                    <Link
                      href={`/dashboard/${assessment.assessment_id}`}
                     
                    >
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="text-textSecondary border border-textSecondary px-3 py-1 rounded-xl"
                      >Play
                        {/* <ArrowRightIcon
                          className="w-[25px] h-full" /> */}
                      </motion.button>
                    </Link>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="6" className="text-center p-4">
                  <Nothing
                    title="No Assessments Available"
                    para="There are currently no Assessments to display.
            Please check back later."
                  />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>


    </div>
  );
}
