import React from 'react';
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ClockIcon, CalendarDaysIcon } from "@heroicons/react/24/outline";
import { UpcomingUserAssessment } from '@/types/LMSTypes';

type propType = {
    assessments: UpcomingUserAssessment[]
    type: string
}

const AssessmentDetails: React.FC<propType> = ({ assessments, type }) => {
    const seeResults = () => {
        console.log("See results");
    };

    const router = useRouter();
    console.log("assessment", assessments);

    return (
        <div>
            {assessments?.length > 0 ? (
                assessments.map((assessment) => (
                    <div key={assessment.assessment_id} className="flex mx-auto mb-2 w-full max-w-4xl rounded-xl bg-white p-4">
                        <div className="w-full pl-2">
                            <h5 className="text-lg font-medium leading-tight text-textPrimary">
                                {assessment.assessment_name}
                            </h5>
                            <h5 className="pt-1 text-xs leading-tight text-slate-500">
                                Assessment Details
                            </h5>
                            <div className="flex flex-col sm:flex-row w-full justify-between">
                                <div className="flex w-full max-w-sm pt-1 justify-between">
                                    <div className="flex flex-col">
                                        <p className="text-xs text-textColor">
                                            Schedule From:
                                        </p>
                                        <div className="flex">
                                            <CalendarDaysIcon className="h-4 w-4 " />
                                            <span className="text-xs px-1">{new Date(assessment.start_date).toLocaleDateString()}</span>
                                        </div>
                                        <div className="flex pt-1">
                                            <ClockIcon color="#475569" className="h-4 w-4" />
                                            <span className="text-xs px-1">{new Date(assessment.start_date).toLocaleTimeString()}</span>
                                        </div>
                                    </div>
                                    <div className="flex flex-col">
                                        <p className="text-xs text-textColor">Till:</p>
                                        <div className="flex">
                                            <CalendarDaysIcon className="h-4 w-4" />
                                            <span className="text-xs px-1">{new Date(assessment.end_date).toLocaleDateString()}</span>
                                        </div>
                                        <div className="flex pt-1">
                                            <ClockIcon color="#475569" className="h-4 w-4" />
                                            <span className="text-xs px-1">{new Date(assessment.end_date).toLocaleTimeString()}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                    {type === "previous" ? (
                                         <Link
                                         href={{
                                           pathname: "/dashboard/seeuserresult",
                                           query: {
                                             assessment_id: assessment.assessment_id,
                                             user_id: assessment.user_id,
                                           },
                                         }}
                                       >
                                        <button
                                            
                                            className="flex w-30 h-9 mt-5 justify-center rounded-md bg-secondary px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75"
                                        >
                                            See Results
                                        </button>
                                        </Link>
                                    ) : type === "present" ? (
                                        <button
                                            onClick={() => router.push("/dashboard/" + assessment.assessment_id)}
                                            className="flex w-30 h-9 mt-5 justify-center rounded-md bg-secondary px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75"
                                        >
                                            Take Test
                                        </button>
                                    ) : (
                                        <>
                                        </>
                                    )}
                                
                            </div>
                        </div>
                    </div>
                ))
            ) : (
                <div className="flex flex-col mx-auto w-full max-w-2xl rounded-xl bg-white p-4">
                    <span className="text-md font-medium">
                        Currently, there are no assessments assigned for you.
                    </span>
                    <span className="pt-2 text-md font-medium">
                        Please check this section when new assessments are assigned to you.
                    </span>
                </div>
            )}
        </div>
    );
};

export default AssessmentDetails;
