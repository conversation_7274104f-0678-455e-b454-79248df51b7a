"use client"; // This is a client component
import React, { useState, useEffect } from "react";
import { Disclosure, Transition } from "@headlessui/react";
import { ChevronUpIcon } from "@heroicons/react/20/solid";
import { useGetAllAssessments } from "@/hook/assessments/useGetAllAssessments";
import Heading from "@/components/ui/Heading";
import Button from "@/components/ui/Button";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import Nothing from "@/components/ui/Nothing";
import AssessmentDetails from "./AssessmentDetails";

export default function AssessmentTimeListing({ assessment_type, assessments }) {
    const [loadingSelect, setLoadingSelect] = useState(false);
    return (
        <>
            {assessment_type === "previous" ? (
                <div className="p-4 lg:p-0 lg:mx-4 w-full lg:w-[97%]">
                    {(
                        <Disclosure>
                            {({ open }) => (
                                <>
                                    <Disclosure.Button className="mb-3 flex w-full sm:w-5/6 mx-auto justify-between rounded-lg text-white bg-secondary px-2 py-4 text-left text-sm font-medium transition duration-300 hover:bg-textSecondary hover:text-white focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75">
                                        <div className="flex items-center">
                                            <span className="ml-2 text-xl">
                                                Previous Assessment
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <ChevronUpIcon
                                                className={`${open ? "rotate-180 transform" : ""
                                                    } h-5 w-5 text-white`}
                                            />
                                        </div>
                                    </Disclosure.Button>
                                    <Transition
                                        enter="transition duration-150 ease-out"
                                        enterFrom="transform scale-95 opacity-0"
                                        enterTo="transform scale-100 opacity-100"
                                        leave="transition duration-75 ease-out"
                                        leaveFrom="transform scale-100 opacity-100"
                                        leaveTo="transform scale-95 opacity-0"
                                    >
                                        <Disclosure.Panel className="px-4 pt-4 pb-4 text-sm text-gray-500">
                                            <AssessmentDetails assessments={assessments} type="previous" />

                                        </Disclosure.Panel>
                                    </Transition>
                                </>
                            )}
                        </Disclosure>
                    )}
                </div>
            ) : assessment_type === "present" ? (
                <div className="p-4 lg:p-0 lg:mx-4 w-full lg:w-[97%]">
                    {/*Add Typescript Types*/}
                    {(
                        <Disclosure>
                            {({ open }) => (
                                <>
                                    <Disclosure.Button className="mb-3 flex w-full sm:w-5/6 mx-auto justify-between rounded-lg text-white bg-secondary px-2 py-4 text-left text-sm font-medium transition duration-300 hover:bg-textSecondary hover:text-white focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75">
                                        <div className="flex items-center">
                                            <span className="ml-2 text-xl">
                                                Present Assessment
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <ChevronUpIcon
                                                className={`${open ? "rotate-180 transform" : ""
                                                    } h-5 w-5 text-white`}
                                            />
                                        </div>
                                    </Disclosure.Button>
                                    <Transition
                                        enter="transition duration-150 ease-out"
                                        enterFrom="transform scale-95 opacity-0"
                                        enterTo="transform scale-100 opacity-100"
                                        leave="transition duration-75 ease-out"
                                        leaveFrom="transform scale-100 opacity-100"
                                        leaveTo="transform scale-95 opacity-0"
                                    >
                                        <Disclosure.Panel className="px-4 pt-4 pb-4 text-sm text-gray-500">
                                            <AssessmentDetails assessments={assessments} type="present" />
                                        </Disclosure.Panel>
                                    </Transition>
                                </>
                            )}
                        </Disclosure>
                    )}
                </div>
            ) : assessment_type === "upcoming" ? (
                <div className="p-4 lg:p-0 lg:mx-4 w-full lg:w-[97%]">
                    {/*Add Typescript Types*/}
                    {(
                        <Disclosure>
                            {({ open }) => (
                                <>
                                    <Disclosure.Button className="mb-3 flex w-full sm:w-5/6 mx-auto justify-between rounded-lg text-white bg-secondary px-2 py-4 text-left text-sm font-medium transition duration-300 hover:bg-textSecondary hover:text-white focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75">
                                        <div className="flex items-center">
                                            <span className="ml-2 text-xl">
                                                Upcoming Assessment
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <ChevronUpIcon
                                                className={`${open ? "rotate-180 transform" : ""
                                                    } h-5 w-5 text-white`}
                                            />
                                        </div>
                                    </Disclosure.Button>
                                    <Transition
                                        enter="transition duration-150 ease-out"
                                        enterFrom="transform scale-95 opacity-0"
                                        enterTo="transform scale-100 opacity-100"
                                        leave="transition duration-75 ease-out"
                                        leaveFrom="transform scale-100 opacity-100"
                                        leaveTo="transform scale-95 opacity-0"
                                    >
                                        <Disclosure.Panel className="px-4 pt-4 pb-4 text-sm text-gray-500">
                                        <AssessmentDetails assessments={assessments} type="Upcoming"/>
                                        </Disclosure.Panel>
                                    </Transition>
                                </>
                            )}
                        </Disclosure>
                    )}
                </div>
            ) : (
                // Default content when assessment_type doesn't match any case
                <div>No Assessments Found</div>
            )}
            <div className="overflow-y-auto p-4 w-full h-full ">
                {/* Add your Disclosure component and its contents here */}
            </div>
        </>
    );
}
