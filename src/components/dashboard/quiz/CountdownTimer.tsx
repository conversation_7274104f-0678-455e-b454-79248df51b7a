// import { useTimer } from 'react-timer-hook';

// interface QuizTimer {
//   expiryTimestamp: Date;
//   submit: Function;
// }

// export default function QuizTimer({ expiryTimestamp, submit }: QuizTimer) {
//   const { seconds, minutes, hours } = useTimer({ expiryTimestamp, onExpire: () => submit(true) });
  
//   return (
//     <div className="w-32 text-md text-textSecondary">
//       <span>{hours}h</span> : <span>{minutes}m</span> : <span>{seconds}s</span>
//     </div>
//   );
// }
import React, { useEffect, useState } from 'react';
import { useTimer } from 'react-timer-hook';

interface QuizTimerProps {
  expiryTimestamp: Date;
  submit: (timeup?: boolean) => void;
}

export default function QuizTimer({ expiryTimestamp, submit }: QuizTimerProps) {
  const { seconds, minutes, hours } = useTimer({ 
    expiryTimestamp, 
    onExpire: () => submit(true) 
  });
  
  // For warning colors when time is running out
  const [isWarning, setIsWarning] = useState(false);
  const [isDanger, setIsDanger] = useState(false);
  
  useEffect(() => {
    // Set warning state when less than 5 minutes remain
    if (hours === 0 && minutes < 5) {
      setIsWarning(true);
    }
    
    // Set danger state when less than 1 minute remains
    if (hours === 0 && minutes < 1) {
      setIsDanger(true);
    }
  }, [hours, minutes]);
  
  // Format numbers to always have two digits
  const formatNumber = (num: number) => {
    return num < 10 ? `0${num}` : num;
  };
  
  return (
    <div 
      className={`flex items-center transition-colors ${
        isDanger 
          ? 'text-red-600 animate-pulse' 
          : isWarning 
            ? 'text-amber-600' 
            : 'text-blue-800'
      }`}
    >
      {/* Improved Clock Icon */}
      <svg 
        className="w-5 h-5 mr-2" 
        viewBox="0 0 24 24" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      >
        <circle cx="12" cy="12" r="10" />
        <path d="M12 6v6l4 2" />
      </svg>
      
      <span className="tabular-nums font-medium">{formatNumber(hours)}</span>
      <span className="px-0.5">:</span>
      <span className="tabular-nums font-medium">{formatNumber(minutes)}</span>
      <span className="px-0.5">:</span>
      <span className="tabular-nums font-medium">{formatNumber(seconds)}</span>
    </div>
  );
}