
// import Heading from '@/components/ui/Heading';
// import React, { useState, useEffect, useRef } from 'react';
// import QuestionSelection from "./QuestionSelection"
// import AnswerInput from './AnswerInput';
// import { useRouter, useSearchParams } from "next/navigation";
// //API Hooks 
// import { UseSubmitAssessmentAnswers } from "@/hook/user/UseSubmitAssessmentAnswers";
// import { SubmitAttempt } from "@/types/LMSTypes"
// import Instructions from './Instructions';
// import { markQuestionRef } from './QuestionSelection';
// import { useUserRefreshToken } from '@/hook/useUserRefreshToken';

// interface storedAnswer {
//   assessment_question_id: number,
//   submitted_answer_option: number,
//   answer_text: string
// }

// interface QuestionUserBoxProps {
//   getAssessmentQuestions: Object[];
//   startUserAssessment: Object[];
//   setSubmitAssessmentAnswers?: any[];
//   setliveStateCheck: Function;
//   liveStateCheck?: any[]
//   submit?: Function;
//   allQuestionSubmitted: any;
// }



// // This component will display a set of questions in a display panel
// const QuestionUserBox = ({ setliveStateCheck, getAssessmentQuestions, startUserAssessment, setSubmitAssessmentAnswers, liveStateCheck, submit, allQuestionSubmitted }: QuestionUserBoxProps) => {
//   const searchParams = useSearchParams();
//   const [storedAnswers, setStoredAnswers] = useState([]);
//   const router = useRouter();
//   const examId = searchParams.get("exam_id");
//   const [anotherLiveState, setAnotherLiveState] = useState<number[]>([]);
//   const user_assessment_id = searchParams.get("user_assessment_id");
//   console.log("questinn", getAssessmentQuestions);
//   const time = new Date();
//   time.setSeconds(
//     time.getSeconds() + 60 * Number(searchParams.get("time_allowed")!)
//   );
//   const [arrayOfObjects, setArrayOfObjects] = useState<SubmitAttempt[]>([]);
//   const [activeQuestion, setActiveQuestion] = useState<number>(0);
//   const [currentQuestion, setCurrentQuestion] = useState(activeQuestion)
//   const [selectedAnswers, setSelectedAnswer] = useState<storedAnswer[]>(Array(getAssessmentQuestions?.length).fill({
//     assessment_question_id: 0,
//     submitted_answer_option: 0,
//     answer_text: null
//   }));
//   const [completeAnswers, setCompleteAnswers] = useState()

//   //refresh after changing question
//   const { data: tokenRefresh } = useUserRefreshToken(activeQuestion)

//   // console.log("currentQuestion", currentQuestion)
//   // console.log("activeQuestion", activeQuestion)

//   const [userAssessmentAttempt, setUserAssessmentAttempt] = useState<number>(startUserAssessment?.user_assessment_attempt_id)
//   const [switchCount, setSwitchCount] = useState(0);
//   const [backButtonCounter, setBackButtonCounter] = useState(0);
//   const [optionChangeCount, setOptionChangeCount] = useState<number[]>(getAssessmentQuestions ? Array(getAssessmentQuestions.length).fill(0) : []);
//   const [questionStartTime, setQuestionStartTime] = useState<Date | null>(new Date);
//   const [timeTakenPerQuestion, setTimeTakenPerQuestion] = useState<number[]>(Array(getAssessmentQuestions?.length).fill(0));
//   const [text, setText] = useState('');
//   let answers = {};
//   const [pageCounter, setPageCounter] = useState<number>(0)

//   const initialLiveStatus = Array(getAssessmentQuestions.length).fill('1');
//   // const [liveStatus, setLiveStatus] = useState(initialLiveStatus)
//   // let 

//   const liveStatus = useRef(initialLiveStatus);

//   const settingCurrentPage = (tempNo: number) => {
//     setActiveQuestion(tempNo)
//   }


//   useEffect(() => {
//     const handleVisibilityChange = () => {
//       if (document.visibilityState === 'visible') {
//         setSwitchCount(prevCount => prevCount + 1);
//       }
//     };

//     document.addEventListener('visibilitychange', handleVisibilityChange);

//     return () => {
//       document.removeEventListener('visibilitychange', handleVisibilityChange);
//     };
//   }, []);

//   useEffect(() => {
//     setSubmitAssessmentAnswers(completeAnswers)
//   }, [completeAnswers])


//   useEffect(() => {
//     const handleVisibilityChange = () => {
//       if (document.visibilityState === 'visible') {
//         setBackButtonCounter(prevCount => prevCount + 1);
//       }
//     };

//     const handlePopState = () => {
//       setBackButtonCounter(prevCount => prevCount + 1);
//     };

//     document.addEventListener('visibilitychange', handleVisibilityChange);
//     window.addEventListener('popstate', handlePopState);

//     return () => {
//       document.removeEventListener('visibilitychange', handleVisibilityChange);
//       window.removeEventListener('popstate', handlePopState);
//     };
//   }, []);



//   useEffect(() => {
//     // Initialize the time taken per question array with the correct length
//     if (getAssessmentQuestions) {
//       setTimeTakenPerQuestion(Array(getAssessmentQuestions.length).fill(0));
//       setOptionChangeCount(Array(getAssessmentQuestions.length).fill(0)); // Update optionChangeCount when ques change
//     }
//   }, [getAssessmentQuestions]);

//   const getTimeTakenPerQuestion = () => { //might need some work as it calculate time as a whole instead of per question
//     const endTime = new Date();
//     const timeDifferenceInSeconds = Math.floor((endTime.getTime() - (questionStartTime?.getTime() || endTime.getTime())) / 1000);

//     setTimeTakenPerQuestion(prevTimeTaken => {
//       // Update the time taken for the current question
//       const updatedTimeTaken = [...prevTimeTaken];
//       updatedTimeTaken[activeQuestion] = timeDifferenceInSeconds;
//       return updatedTimeTaken;
//     });
//   }




//   // Calculate score and increment to next question
//   const nextQuestion = () => {
//     if (activeQuestion < getAssessmentQuestions?.length - 1) {
//       // Check if there's a next question available
//       getTimeTakenPerQuestion(); // Record time taken for current question
//       setActiveQuestion(prev => prev + 1); // Move to the next question
//     }
//   };

//   const previousQuestion = () => {
//     if (activeQuestion > 0) {
//       // Check if there's a previous question available
//       getTimeTakenPerQuestion(); // Record time taken for current question
//       setActiveQuestion(prev => prev - 1); // Move to the previous question
//     }
//   };

//   const AddingAnswersToArray = () => {
//     const finalAnswerTemp = [...selectedAnswers, answers]
//     setSelectedAnswer(finalAnswerTemp);
//   }

//   const createSubmitBody = () => {
//     // const newArray = [];
//     // for (let i = 0; i < getAssessmentQuestions?.length; i++) {
//     //   const obj = {
//     //     question: getAssessmentQuestions[i].question,
//     //     user_assessment_attempt_id: startUserAssessment?.user_assessment_attempt_id,
//     //     assessment_question_id: getAssessmentQuestions[i].assessment_question_id,
//     //     submitted_answer_option: storedAnswers[i]?.submitted_answer_option,
//     //     answer_text: storedAnswers[i]?.answer_text,
//     //     submitted_answer_marks: ((getAssessmentQuestions[i].question_type == "Coding" || getAssessmentQuestions[i].question_type == "SUBJECTIVE") ? 0 : getAssessmentQuestions[i].marks),
//     //     submitted_answer_status: "SUBMITTED",
//     //     evaluation_comments: "None",
//     //     evaluated_by_email: "None",
//     //     time_take_to_answer_in_sec: timeTakenPerQuestion[i],
//     //     number_of_screen_switch: switchCount,
//     //     used_back_button: backButtonCounter,
//     //     changed_answer: optionChangeCount[i]
//     //   };
//     //   newArray.push(obj);
//     //   setArrayOfObjects(newArray)
//     //   setSubmitAssessmentAnswers(newArray)
//     // }
//     // console.log("arrayOfObjects:- ", arrayOfObjects)
//   }

//   const addDataForSubmit = UseSubmitAssessmentAnswers(startUserAssessment?.user_assessment_attempt_id, arrayOfObjects)

//   const submitAnswer = async (timeup: boolean | null) => {
//     createSubmitBody()
//     AddingAnswersToArray()
//     if (timeup)
//       alert(
//         "Time is up. Thank you for taking the test, we will check your answers. Best of luck for the results."
//       );
//     else
//       alert(
//         "Thank you for taking the test, we will check your answers. Best of luck for the results."
//       );
//     await addDataForSubmit.mutate()
//   };

//   const markQuestionRef = useRef<markQuestionRef>(null);

//   const markForReview = () => {
//     //setanotherLiveState(liveStateCheck); 
//     const newArray = [...liveStatus.current];
//     newArray[activeQuestion] = "4"
//     liveStatus.current = newArray
//     console.log("new error", newArray)
//     setliveStateCheck(newArray)
//     setAnotherLiveState(newArray)

//   }

//   // console.log("activeQuestionp", activeQuestion)


//   const [submitText, setSubmitText] = useState<string>("Submit")
//   const onSubmit = () => {

//     setSubmitText("Submitting...")
//     setTimeout(() => {
//       setSubmitText("Submit")
//     }, 500);
//   }

//   return (
//     <div className='h-[90%] w-[90%] flex flex-col lg:flex-row gap-10 rounded-lg'>
//       <div className='bg-slate-100 w-auto h-fit lg:h-[600px] lg:w-4/6 rounded-lg flex flex-col justify-between p-4'>
//         <div className='w-auto h-[90%] m-4 bg-white shadow-md overflow-y-auto'>
//           <AnswerInput questionData={getAssessmentQuestions} activeQuestion={activeQuestion} setStoredAnswers={setStoredAnswers} setCompleteAnswers={setCompleteAnswers} startUserAssessment={startUserAssessment} />
//         </div>
//         <div className='flex justify-between items-center lg:mx-4 lg:my-4 p-2'>
//           {activeQuestion == 0 ?
//             <button
//               data-testid="button-1"
//               disabled
//               className='px-3 py-2 text-xs sm:text-base lg:text-lg rounded-md text-white bg-hoverColor'
//             >
//               Previous
//             </button> :
//             <button
//               data-testid="button-2"
//               className='px-3 py-2 text-xs sm:text-base lg:text-lg rounded-md text-white bg-secondary'
//               onClick={() => {
//                 previousQuestion();
//                 createSubmitBody();
//               }}
//             >
//               Previous
//             </button>
//           }

//           <button
//             className='px-3 py-2 text-xs sm:text-base lg:text-lg rounded-md text-white bg-secondary '
//             onClick={() => { markForReview() }}
//           >
//             Mark for Review
//           </button>
//           {activeQuestion == (getAssessmentQuestions?.length - 1) ?
//             <button
//               data-testid="button-3"
//               disabled
//               className='px-3 py-2 text-xs sm:text-base lg:text-lg rounded-md text-white bg-hoverColor'
//             >
//               Next
//             </button> :
//             <button
//               data-testid="button-4"
//               className='px-3 py-2 text-xs sm:text-base lg:text-lg rounded-md text-white bg-secondary '
//               onClick={() => {
//                 nextQuestion();
//                 createSubmitBody();
//               }}
//             >
//               Next
//             </button>
//           }
//         </div>
//         <div className='w-full flex justify-center mt-1'>
//           {submit && (
//             <button
//               type="button"
//               disabled={!allQuestionSubmitted}
//               onClick={() => { submit(); onSubmit(); }}
//               className={`inline-flex w-[320px] justify-center rounded-md ${allQuestionSubmitted?"bg-green-800 animate-pulse":"bg-gray-500"} px-10 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75`}
//             >
//               {submitText}
//             </button>
//           )}
//         </div>
//       </div>
//       <div className='bg-slate-100 h-fit w-auto lg:h-[600px] lg:w-2/6 rounded-lg flex flex-col'>
//         <div className='bg-white m-4 h-fit lg:h-full shadow-md flex justify-between lg:justify-evenly flex-col sm:flex-row lg:flex-col'>
//           <div className='h-fit m-2 gap-4 flex flex-col border'>
//             <div className='flex justify-center'>
//               <Heading pgHeading='All Questions' />
//             </div>
//             <div className='h-fit w-full'>
//               <Instructions />
//             </div>
//           </div>
//           <div className='h-96 lg:h-2/6 p-4 sm:p-4 lg:px-4'>
//             <QuestionSelection ref={markQuestionRef} setliveStateCheck={setliveStateCheck} liveStateCheck={liveStateCheck} question={getAssessmentQuestions} anotherLiveState={anotherLiveState} current={settingCurrentPage} activeQuestion={activeQuestion} storedAnswers={storedAnswers} liveStatus={liveStatus} />
//           </div>
//           <div className=''>

//           </div>
//         </div>
        
//       </div>
//     </div>
//   );
// };

// export default QuestionUserBox;
import React, { useState, useEffect, useRef } from 'react';
import Heading from '@/components/ui/Heading';
import QuestionSelection from "./QuestionSelection";
import AnswerInput from './AnswerInput';
import Instructions from './Instructions';
import { markQuestionRef } from './QuestionSelection';

interface storedAnswer {
  assessment_question_id: number,
  submitted_answer_option: number,
  answer_text: string
}

interface QuestionUserBoxProps {
  getAssessmentQuestions: any[];
  startUserAssessment: any;
  setSubmitAssessmentAnswers: React.Dispatch<React.SetStateAction<any[]>>;
  setliveStateCheck: React.Dispatch<React.SetStateAction<string[]>>;
  liveStateCheck?: string[];
  submit?: () => void;
  allQuestionSubmitted: boolean;
}

const QuestionUserBox = ({
  setliveStateCheck,
  getAssessmentQuestions,
  startUserAssessment,
  setSubmitAssessmentAnswers,
  liveStateCheck,
  submit,
  allQuestionSubmitted
}: QuestionUserBoxProps) => {
  // State management
  const [storedAnswers, setStoredAnswers] = useState<any[]>([]);
  const [activeQuestion, setActiveQuestion] = useState<number>(0);
  const [anotherLiveState, setAnotherLiveState] = useState<string[]>([]);
  const [completeAnswers, setCompleteAnswers] = useState<any>();
  const [submitText, setSubmitText] = useState<string>("Submit");
  
  // Initial state for all questions (1 = Not Visited)
  const initialLiveStatus = Array(getAssessmentQuestions.length).fill('1');
  const liveStatus = useRef(initialLiveStatus);
  const markQuestionRef = useRef<markQuestionRef>(null);

  // Update submit answers when complete answers change
  useEffect(() => {
    setSubmitAssessmentAnswers(completeAnswers);
  }, [completeAnswers, setSubmitAssessmentAnswers]);

  // Set current question
  const settingCurrentPage = (questionIndex: number) => {
    setActiveQuestion(questionIndex);
  };

  // Move to next question
  const nextQuestion = () => {
    if (activeQuestion < getAssessmentQuestions?.length - 1) {
      setActiveQuestion(prev => prev + 1);
    }
  };

  // Move to previous question
  const previousQuestion = () => {
    if (activeQuestion > 0) {
      setActiveQuestion(prev => prev - 1);
    }
  };

  // Mark current question for review
  const markForReview = () => {
    const newArray = [...liveStatus.current];
    newArray[activeQuestion] = "4"; // 4 = Marked for Review
    liveStatus.current = newArray;
    setliveStateCheck(newArray);
    setAnotherLiveState(newArray);
  };

  // Handle submit button click
  const onSubmit = () => {
    setSubmitText("Submitting...");
    setTimeout(() => {
      setSubmitText("Submit");
    }, 500);
  };

  return (
    <div className="w-full flex flex-col lg:flex-row gap-4 rounded-lg">
      {/* Main content area - question and answers */}
      <div className="bg-white rounded-lg shadow-sm w-full lg:w-3/4 flex flex-col">
        {/* Question and answer area - simplified height */}
        <div className="flex-grow overflow-auto p-4 min-h-[300px]">
          <AnswerInput 
            questionData={getAssessmentQuestions} 
            activeQuestion={activeQuestion} 
            setStoredAnswers={setStoredAnswers} 
            setCompleteAnswers={setCompleteAnswers} 
            startUserAssessment={startUserAssessment} 
          />
        </div>
        
        {/* Navigation buttons */}
        <div className="px-4 py-3 border-t border-gray-100 flex items-center justify-between bg-gray-50 rounded-b-lg">
          <div className="flex space-x-3 items-center">
            <button
              data-testid="button-prev"
              disabled={activeQuestion === 0}
              className={`px-4 py-2 rounded text-sm font-medium transition-all
                        ${activeQuestion === 0 
                          ? 'bg-gray-200 text-gray-500 cursor-not-allowed' 
                          : 'bg-blue-100 text-blue-700 hover:bg-blue-200 active:scale-95'}`}
              onClick={previousQuestion}
            >
              ← Prev
            </button>
            
            <button
              data-testid="button-next"
              disabled={activeQuestion === getAssessmentQuestions?.length - 1}
              className={`px-4 py-2 rounded text-sm font-medium transition-all
                        ${activeQuestion === getAssessmentQuestions?.length - 1 
                          ? 'bg-gray-200 text-gray-500 cursor-not-allowed' 
                          : 'bg-blue-100 text-blue-700 hover:bg-blue-200 active:scale-95'}`}
              onClick={nextQuestion}
            >
              Next →
            </button>
          </div>
          
          {/* Action buttons grouped together */}
          <div className="flex space-x-3 items-center">
            <button
              className="px-4 py-2 rounded text-sm font-medium transition-all bg-purple-100 text-purple-700 hover:bg-purple-200 active:scale-95"
              onClick={markForReview}
            >
              Review
            </button>
            
            {/* Submit button */}
            {submit && (
              <button
                type="button"
                // For Developement, change it later.
                //disabled={!allQuestionSubmitted}
                onClick={() => { submit(); onSubmit(); }}
                className={`px-5 py-2 rounded text-sm font-medium transition-all
                          ${allQuestionSubmitted
                            ? 'bg-green-600 text-white hover:bg-green-700 active:scale-95'
                            : 'bg-gray-200 text-gray-500 cursor-not-allowed'}`}
              >
                {submitText}
              </button>
            )}
          </div>
        </div>
      </div>
      
      {/* Sidebar - question navigation and instructions */}
      <div className="bg-white rounded-lg shadow-sm w-full lg:w-1/4 flex flex-col">
        {/* Question navigation heading */}
        <div className="p-2 border-b">
          <h1 className="text-lg sm:text-xl font-medium text-gray-900">
            All Questions 
          </h1>
        </div>
        
        {/* Legend and instructions */}
        <div className="p-2 border-b">
          <Instructions />
        </div>
        
        {/* Question selection grid */}
        <div className="p-2 flex-grow overflow-auto">
          <QuestionSelection 
            ref={markQuestionRef} 
            setliveStateCheck={setliveStateCheck} 
            liveStateCheck={liveStateCheck} 
            question={getAssessmentQuestions} 
            anotherLiveState={anotherLiveState} 
            current={settingCurrentPage} 
            activeQuestion={activeQuestion} 
            storedAnswers={storedAnswers} 
            liveStatus={liveStatus} 
          />
        </div>
      </div>
    </div>
  );
};

export default QuestionUserBox;