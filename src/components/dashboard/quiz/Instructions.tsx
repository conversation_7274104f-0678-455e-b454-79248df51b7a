import React, { useState } from 'react';
import InstructionsModal from './InstructionsModal';

const Instructions = () => {
  const [instructionsModal, setInstructionsModal] = useState(false);
  
  // Color legend items
  const colorLegend = [
    { name: 'Not Visited', colorClass: 'bg-gray-300', textClass: 'text-gray-700' },
    { name: 'Not Answered', colorClass: 'bg-red-500', textClass: 'text-white' },
    { name: 'Answered', colorClass: 'bg-green-600', textClass: 'text-white' },
    { name: 'For Review', colorClass: 'bg-purple-500', textClass: 'text-white' }
  ];

  return (
      <div className='w-full h-full flex flex-col gap-4'>
          {/* Legend heading */}
          {/* <h3 className="text-sm font-medium text-gray-700">Questions Status</h3>

          {/* Color legend grid - styled version */}
          {/* <div className="grid grid-cols-2 gap-3 bg-gray-50 p-3 rounded-lg border border-gray-100">
              {colorLegend.map((color, index) => (
                  <div key={index} className="flex items-center space-x-2">
                      <div className={`w-6 h-6 rounded-full ${color.colorClass} border border-gray-200 shadow-sm flex items-center justify-center flex-shrink-0`}>
                          <span className={`text-xs font-medium ${color.textClass}`}>{index + 1}</span>
                      </div>
                      <span className="text-xs text-gray-700">{color.name}</span>
                  </div>
              ))}
          </div> */}

          {/* Quiz tips */}
          <div className="mt-0 text-xs text-gray-600 space-y-2 bg-blue-50 p-3 rounded-lg border border-blue-100">
              <h4 className="font-medium text-blue-800 mb-1.5">Tips:</h4>
              <div className="space-y-1.5">
                  <p className="flex items-start">
                      <svg className="w-3.5 h-3.5 text-blue-500 mr-1.5 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span>You can mark questions for later review</span>
                  </p>
                  <p className="flex items-start">
                      <svg className="w-3.5 h-3.5 text-blue-500 mr-1.5 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span>All questions carry different marks</span>
                  </p>
                  <p className="flex items-start">
                      <svg className="w-3.5 h-3.5 text-blue-500 mr-1.5 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span>Submit only when you're done with all questions</span>
                  </p>
              </div>
          </div>

          {/* Instructions button */}
          <div className='mt-0'>
              <button
                  //className='w-full py-2 px-4 text-sm rounded-md text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-colors'
                  className='w-full py-2 px-4 text-sm rounded-md text-blue-600 border border-blue-200 hover:bg-blue-100 hover:border-blue-300 transition-colors flex items-center justify-center font-medium'
                  onClick={() => setInstructionsModal(true)}
              >
                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  View Detailed Instructions
              </button>
          </div>

          {/* Instructions modal */}
          {instructionsModal && (
              <InstructionsModal
                  onClose={() => setInstructionsModal(false)}
              />
          )}
      </div>
  );
};

export default Instructions;