// import React, { useState, useEffect } from 'react';
// import { XMarkIcon, CheckBadgeIcon, XCircleIcon } from '@heroicons/react/24/outline';
// import { useRouter } from 'next/navigation';
// import { tree } from 'next/dist/build/templates/app-page';

// interface SubmitModalProps {
//     modalName?: string;
//     modalText?: string;
//     okclickPath?: any;
//     submitAnswerValue: Function;
//     setAllQuestionsCompleted: any;
//     // onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;

// }

// function countOccurrences(arr: number[]) {
//     // Initialize counters for each number
//     let count1 = 0;
//     let count2 = 0;
//     let count3 = 0;
//     let count4 = 0;

//     // Loop through the array
//     arr.forEach(num => {
//         if (num === 1) {
//             count1++;
//         } else if (num === 2) {
//             count2++;
//         } else if (num === 3) {
//             count3++;
//         } else if (num === 4) {
//             count4++;
//         }
//     });

//     // Create an object to store the counts
//     const counts = {
//         1: count1,
//         2: count2,
//         3: count3,
//         4: count4
//     };

//     return counts;
// }


// const ColorBoxes = ({ type, tempValue }: { type: number, tempValue: any }) => {
//     // Determine background color and text based on the 'type' prop
//     let bgColor = "bg-purple-500";
//     let labelText = "Marked:";

//     if (type === 1) {
//         bgColor = "bg-slate-300";
//         labelText = "Not Visited:";
//     } else if (type === 2) {
//         bgColor = "bg-red-500";
//         labelText = "Not Answered:";
//     } else if (type === 3) {
//         bgColor = "bg-green-500";
//         labelText = "Answered:";
//     }

//     return (
//         <div className={`flex justify-around items-center w-40 text-base h-10 
//         rounded-lg ${bgColor}`}>
//             <span className='flex justify-center items-center text-center'>{labelText}</span>
//             <span className=''>{tempValue}</span>
//             {/* Additional content can be added here */}
//         </div>
//     );
// };



// // Example usage:
// // <ColorBoxes type={2} numbers={[1, 2, 3]} />



// export default function SummaryModal({ modalName, modalText, liveStateCheck, onClose, okclickPath, submitAnswerValue, setAllQuestionsCompleted }: SubmitModalProps) {

//     const router = useRouter();

//     const onSubmit = () => {
//         // Save information in sessionStorage
//         sessionStorage.setItem('assessmentCompleted', 'true');
        
//         // Then, redirect after 2 seconds (if you need to show something or wait)
//         setTimeout(() => {
//             router.push('/dashboard/roles/assessment/results');
//         }, 2000);
//     }
    
    

//     function countNumbers(arr: number[]) {
//         // Initialize an object to store the counts
//         let counts = {
//             "1": 0,
//             "2": 0,
//             "3": 0,
//             "4": 0
//         };

//         // Loop through the array and increment counts for each number
//         arr.forEach(num => {
//             if (counts[num] !== undefined) {
//                 counts[num]++;
//             }
//         });

//         // Return the counts object
//         return counts;
//     }

//     function checkValues(data: { [key: string]: number }): boolean {
//         return data["1"] === 0 && data["2"] === 0 && data["4"] === 0;
//     }

//     const [submitModal, setSubmitModal] = useState(false)
//     const [allQuestionSubmitted, setAllQuestionSubmitted] = useState(false)
//     const boxesValue = [1, 2, 3, 4]
//     const tempValues = countNumbers(liveStateCheck)


//     useEffect(() => {
//         setAllQuestionSubmitted(checkValues(tempValues));
//     }, [liveStateCheck]);  // Add liveStateCheck as a dependency here.
    
//     useEffect(() => {
//         setAllQuestionsCompleted(allQuestionSubmitted);
//     }, [allQuestionSubmitted]);  // This effect runs only when allQuestionSubmitted updates.
    


//     console.log("tempValues", tempValues)

//     return (
//         <div className="modal flex justify-center items-center fixed inset-0 bg-black bg-opacity-40 overflow-auto z-10">
//             <div className="flex flex-col gap-2 modal-content bg-white p-5 border border-gray-300 rounded-md w-11/12 sm:w-[500px] h-[400px] m-2">
//                 <div className='flex justify-end w-[100%]'>
//                     <button className="flex justify-end" onClick={onClose}>
//                         <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
//                     </button>
//                 </div>
//                 <div className=' w-[100%] h-[100%]'>
//                     <div className='flex justify-center item-center w-[100%] h-4/6 flex flex-col gap-2 sm:gap-10 p-10'>
//                         <div className='flex flex-col sm:flex-row justify-between gap-2'>
//                             {boxesValue.slice(0, 2).map((item, index) => (
//                                 <div className='' key={index}> {/* Assuming each item has a unique id */}
//                                     {<ColorBoxes type={item} tempValue={tempValues[item]} />}
//                                 </div>
//                             ))}
//                         </div>
//                         <div className='flex flex-col sm:flex-row justify-between gap-2'>
//                             {boxesValue.slice(2, 4).map((item, index) => (
//                                 <div key={index}> {/* Assuming each item has a unique id */}
//                                     {<ColorBoxes type={item} tempValue={tempValues[item]} />}
//                                 </div>
//                             ))}
//                         </div>



//                     </div>
//                     <p className='flex justify-center font-sans p-4'>
//                         {allQuestionSubmitted ? " Are you sure you want to submit ?" : "You need to complete all the questions before submitting"}
//                     </p>
//                     <div className='flex justify-between gap-20 sm:gap-40'>
//                         <button
//                             className='font-sans px-3 py-2 text-base rounded-md w-full text-white bg-secondary hover:bg-hoverColor'
//                             onClick={onClose}
//                         >
//                             Back
//                         </button>
//                         <button
//                             disabled={!allQuestionSubmitted}
//                             className='font-sans px-3 py-2 text-base rounded-md w-full text-white bg-secondary hover:bg-hoverColor disabled:bg-hoverColor'
//                             onClick={() => {
//                                 submitAnswerValue();
//                                 onSubmit();
//                             }}
//                         >
//                             Submit
//                         </button>
//                     </div>


//                 </div>

//             </div>


//         </div>
//     );
// }
import React, { useState, useEffect } from 'react';
import { XMarkIcon, CheckCircleIcon, ExclamationCircleIcon, ArrowPathIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';

interface SummaryModalProps {
  modalName?: string;
  modalText?: string;
  submitAnswerValue: () => void;
  setAllQuestionsCompleted: (completed: boolean) => void;
  liveStateCheck: string[];
  onClose: () => void;
}

const SummaryModal = ({
  modalName,
  modalText,
  liveStateCheck,
  onClose,
  submitAnswerValue,
  setAllQuestionsCompleted
}: SummaryModalProps) => {
  const router = useRouter();
  const [allQuestionSubmitted, setAllQuestionSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Status types configuration with colors and icons
  const statusTypes = [
    { 
      id: 1, 
      type: "1", 
      label: "Not Visited", 
      bgColor: "bg-gray-100", 
      textColor: "text-gray-800",
      borderColor: "border-gray-300",
      icon: <QuestionMarkCircleIcon className="h-5 w-5 text-gray-400" />
    },
    { 
      id: 2, 
      type: "2", 
      label: "Not Answered", 
      bgColor: "bg-red-50", 
      textColor: "text-red-800",
      borderColor: "border-red-300",
      icon: <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
    },
    { 
      id: 3, 
      type: "3", 
      label: "Answered", 
      bgColor: "bg-green-50", 
      textColor: "text-green-800",
      borderColor: "border-green-300",
      icon: <CheckCircleIcon className="h-5 w-5 text-green-500" />
    },
    { 
      id: 4, 
      type: "4", 
      label: "Marked for Review", 
      bgColor: "bg-purple-50", 
      textColor: "text-purple-800",
      borderColor: "border-purple-300",
      icon: <ArrowPathIcon className="h-5 w-5 text-purple-500" />
    }
  ];
  
  // Count occurrences of each status
  const countStatusTypes = (arr: string[]) => {
    return {
      "1": arr.filter(status => status === "1").length,
      "2": arr.filter(status => status === "2").length,
      "3": arr.filter(status => status === "3").length,
      "4": arr.filter(status => status === "4").length
    };
  };

  // Check if all questions are attempted
  const checkAllQuestionsAttempted = (counts: { [key: string]: number }): boolean => {
    return counts["1"] === 0 && counts["2"] === 0;
  };
  
  // Get counts of each status
  const statusCounts = countStatusTypes(liveStateCheck);
  const totalQuestions = liveStateCheck.length;
  const answeredPercent = Math.round((statusCounts["3"] / totalQuestions) * 100);
  
  // Check for all questions attempted on component mount and when liveStateCheck changes
  useEffect(() => {
    const isAllAttempted = checkAllQuestionsAttempted(statusCounts);
    setAllQuestionSubmitted(isAllAttempted);
    setAllQuestionsCompleted(isAllAttempted);
  }, [liveStateCheck, setAllQuestionsCompleted, statusCounts]);
  
  // Handle submit click with loading state
  const handleSubmit = () => {
    setIsSubmitting(true);
    setTimeout(() => {
      submitAnswerValue();
    }, 500);
  };
  
  // Display warning message based on unfinished questions
  const getSubmitReadiness = () => {
    if (statusCounts["1"] > 0 || statusCounts["2"] > 0) {
      return {
        icon: <ExclamationCircleIcon className="h-6 w-6 text-yellow-500" />,
        message: "You have unanswered questions",
        bgColor: "bg-yellow-50",
        textColor: "text-yellow-800",
        borderColor: "border-yellow-200"
      };
    } else if (statusCounts["4"] > 0) {
      return {
        icon: <QuestionMarkCircleIcon className="h-6 w-6 text-blue-500" />,
        message: "You have questions marked for review",
        bgColor: "bg-blue-50",
        textColor: "text-blue-800",
        borderColor: "border-blue-200"
      };
    }
    return {
      icon: <CheckCircleIcon className="h-6 w-6 text-green-500" />,
      message: "You have answered all questions",
      bgColor: "bg-green-50",
      textColor: "text-green-800",
      borderColor: "border-green-200"
    };
  };
  
  const readinessInfo = getSubmitReadiness();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl overflow-hidden transform transition-all">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">
            {modalName || "Quiz Summary"}
          </h2>
          <button 
            onClick={onClose}
            className="text-white hover:text-gray-200 focus:outline-none transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        
        {/* Content */}
        <div className="px-6 py-5 max-w-2xl mx-auto">
          <p className="text-gray-600 mb-6">
            {modalText || "Please review your progress before submitting."}
          </p>
          
          {/* Progress bar */}
          <div className="mb-8 max-w-2xl ">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">
                Completion Progress
              </span>
              <span className="text-sm font-medium text-gray-700">
                {answeredPercent}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                className="bg-blue-600 h-2.5 rounded-full" 
                style={{ width: `${answeredPercent}%` }}
              ></div>
            </div>
          </div>
          
          {/* Status grid - more balanced layout with counts at bottom */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-8 max-w-2xl mx-auto">
            {statusTypes.map(status => (
              <div 
                key={status.id}
                className={`${status.bgColor} ${status.textColor} border ${status.borderColor} rounded-lg p-3 flex flex-col h-24`}
              >
                <div className="flex items-center">
                  {status.icon}
                  <span className="text-xs font-medium ml-1.5">{status.label}</span>
                </div>
                <div className="flex justify-center items-end flex-grow">
                  <span className="text-2xl font-bold">{statusCounts[status.type]}</span>
                </div>
              </div>
            ))}
          </div>
          
          {/* Readiness message */}
          <div className={`mb-8 p-4 rounded-lg ${readinessInfo.bgColor} ${readinessInfo.textColor} border ${readinessInfo.borderColor} flex items-start max-w-2xl`}>
            <div className="flex-shrink-0 mr-3">
              {readinessInfo.icon}
            </div>
            <div>
              <p className="font-medium">{readinessInfo.message}</p>
              {!allQuestionSubmitted && (
                <p className="mt-1 text-sm">
                  You should answer all questions before submitting for best results.
                </p>
              )}
            </div>
          </div>
        </div>
        
        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex justify-between space-x-4 border-t">
          <button
            onClick={onClose}
            className="px-5 py-2.5 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium flex items-center"
           >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 17l-5-5m0 0l5-5m-5 5h12" />
            </svg>
            Return to Quiz
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className={`px-5 py-2.5 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 flex items-center justify-center font-medium min-w-[140px] shadow-sm disabled:opacity-70 disabled:cursor-not-allowed ${
              allQuestionSubmitted 
            ? 'bg-gradient-to-r from-green-600 to-green-500 text-white hover:from-green-700 hover:to-green-600 hover:shadow-md focus:ring-green-500 active:scale-[0.98]' 
      : 'bg-gradient-to-r from-yellow-500 to-amber-500 text-white hover:from-yellow-600 hover:to-amber-600 hover:shadow-md focus:ring-yellow-500 active:scale-[0.98]'
  }`}
>
  {isSubmitting ? (
    <>
      <svg className="animate-spin mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span>Submitting...</span>
    </>
  ) : (
    <>
      {allQuestionSubmitted ? (
        <>
          <svg className="mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <span>Submit Quiz</span>
        </>
      ) : (
        <>
          <svg className="mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <span>Submit Anyway</span>
        </>
      )}
    </>
  )}
</button>
        </div>
      </div>
    </div>
  );
};

export default SummaryModal;