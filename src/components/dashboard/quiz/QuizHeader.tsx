// import React, { useState, useEffect } from 'react';
// import { ClockIcon } from '@heroicons/react/20/solid'
// import QuizTimer from '@/components/dashboard/quiz/CountdownTimer'

// interface QuizHeaderProps {
//   examId: string | null;
//   expiryTimestamp: Date;
//   submit?: Function;
// }

// export default function QuizHeader({ examId, expiryTimestamp, submit}: QuizHeaderProps) {

//   return (
//     <div className="lg:flex lg:items-center lg:justify-center bg-white p-4">
//       <div className="min-w-0 flex-1">
//         <h2 className="text-xl font-bold leading-7 text-textPrimary sm:truncate sm:text-3xl sm:tracking-tight">
//           Quiz Title : {examId}
//         </h2>
//       </div>
//       <div className="mt-2 flex justify-between lg:ml-4 lg:mt-0">
//         <div className=" flex flex-row items-center sm:mr-3">
//           <ClockIcon className="h-6 w-6 mr-1" color='#407BBF' aria-hidden="true" />
//           <QuizTimer expiryTimestamp={expiryTimestamp} submit={submit} />
//         </div>
        
//       </div>
      
//     </div>
//   )
// }

import React from 'react';
import { ClockIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import QuizTimer from './CountdownTimer';

interface QuizHeaderProps {
  examId: string | null;
  expiryTimestamp: Date;
  submit?: (timeup?: boolean) => void;
  submitTextValue?: string;
}

export default function QuizHeader({ 
  examId, 
  expiryTimestamp, 
  submit,
  submitTextValue
}: QuizHeaderProps) {
  return (
    <div className="w-full bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          {/* Quiz title */}
          <div className="mb-2 sm:mb-0">
            <div className="flex items-center">
              <DocumentTextIcon className="h-6 w-6 text-blue-600 mr-2" />
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 ">
                {examId || "Quiz"}
              </h1>
            </div>
            <p className="text-sm text-gray-500 mt-1">Complete all questions before time expires</p>
          </div>
          
          {/* Timer */}
          <div className="flex items-center justify-end">
            <div className="flex items-center space-x-1 bg-blue-50 px-3 py-2 rounded-lg">
              {/* <ClockIcon className="h-5 w-5 text-blue-800" aria-hidden="true" /> */}
              <div className="font-mono text-base sm:text-lg font-semibold text-blue-800">
                <QuizTimer expiryTimestamp={expiryTimestamp} submit={submit} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}