import React, { useEffect, useState } from 'react';
import { EditorState } from '@codemirror/state';
import { EditorView, keymap, lineNumbers, highlightActiveLine } from '@codemirror/view';
import { defaultKeymap, indentWithTab } from '@codemirror/commands';
import { javascript } from '@codemirror/lang-javascript';
import { python } from '@codemirror/lang-python';
import { java } from '@codemirror/lang-java';
import { cpp } from '@codemirror/lang-cpp';
import { oneDark } from '@codemirror/theme-one-dark';
import { indentOnInput, indentUnit } from '@codemirror/language';

interface CodeEditorProps {
  initialValue: string;
  onChange: (value: string) => void;
  language?: 'javascript' | 'python' | 'java' | 'cpp';
}

const CodeEditor: React.FC<CodeEditorProps> = ({ 
  initialValue = '', 
  onChange,
  language = 'javascript'
}) => {
  const [editorRef, setEditorRef] = useState<HTMLDivElement | null>(null);
  const [editorView, setEditorView] = useState<EditorView | null>(null);

  // Select language extension
  const getLanguageExtension = () => {
    switch (language) {
      case 'python':
        return python();
      case 'java':
        return java();
      case 'cpp':
        return cpp();
      case 'javascript':
      default:
        return javascript();
    }
  };

  useEffect(() => {
    if (!editorRef) return;
    
    // Clean up any existing view
    if (editorView) {
      editorView.destroy();
    }

    // Create editor state and view
    const startState = EditorState.create({
      doc: initialValue,
      extensions: [
        lineNumbers(),
        highlightActiveLine(),
        indentOnInput(),
        indentUnit.of("  "), // 2 spaces per indent level
        keymap.of([indentWithTab, ...defaultKeymap]), // Add tab for indentation
        getLanguageExtension(),
        oneDark,
        EditorView.updateListener.of(update => {
          if (update.changes) {
            onChange(update.state.doc.toString());
          }
        })
      ],
    });

    const view = new EditorView({
      state: startState,
      parent: editorRef
    });

    setEditorView(view);

    // Ensure the editor receives focus when mounted
    setTimeout(() => {
      view.focus();
    }, 100);

    // Cleanup
    return () => {
      view.destroy();
    };
  }, [editorRef, language]);

  // Update content if initialValue changes externally
  useEffect(() => {
    if (editorView && initialValue !== editorView.state.doc.toString()) {
      editorView.dispatch({
        changes: {
          from: 0,
          to: editorView.state.doc.length,
          insert: initialValue
        }
      });
    }
  }, [initialValue, editorView]);

  return (
    <div className="rounded-lg overflow-hidden border border-gray-300 shadow-sm">
      <div className="bg-gray-800 px-4 py-2 flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
        <span className="text-gray-400 text-xs font-mono">{language} editor</span>
      </div>
      <div 
        ref={setEditorRef} 
        className="w-full h-60 overflow-auto bg-gray-900"
        tabIndex={0}
      />
      <div className="bg-gray-800 px-4 py-2 flex items-center justify-between text-xs text-gray-400">
        <div>
          {language === 'javascript' && "// Write your JavaScript code here"}
          {language === 'python' && "# Write your Python code here"}
          {language === 'java' && "// Write your Java code here"}
          {language === 'cpp' && "// Write your C++ code here"}
        </div>
        <div className="flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span>Press Tab to indent</span>
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;