// import React, { useState, useEffect } from 'react'
// import { RadioGroup } from "@headlessui/react";
// import { CheckCircleIcon } from "@heroicons/react/20/solid";

// interface storedAnswer {
//     assessment_question_id: number,
//     submitted_answer_option: number,
//     answer_text: string
// }

// const AnswerInput = ({ questionData, activeQuestion, setStoredAnswers, setCompleteAnswers, startUserAssessment }) => {

//     const [arrayOfAnswers, setArrayOfAnswers] = useState()
//     const [selectedAnswers, setSelectedAnswer] = useState<storedAnswer[]>(Array(questionData?.length).fill({
//         assessment_question_id: 0,
//         submitted_answer_option: 0,
//         answer_text: null
//     }));
//     const [switchCount, setSwitchCount] = useState(0);
//     const [backButtonCounter, setBackButtonCounter] = useState(0);
//     const [optionChangeCount, setOptionChangeCount] = useState<number[]>(questionData ? Array(questionData.length).fill(0) : []);
//     const [questionStartTime, setQuestionStartTime] = useState<Date | null>(new Date);
//     const [timeTakenPerQuestion, setTimeTakenPerQuestion] = useState<number[]>(Array(questionData?.length).fill(0));

//     useEffect(() => {
//         const newArray = [];
//         for (let i = 0; i < questionData?.length; i++) {
//             const obj = {
//                 question: questionData[i].question,
//                 user_assessment_attempt_id: startUserAssessment?.user_assessment_attempt_id,
//                 assessment_question_id: questionData[i].assessment_question_id,
//                 submitted_answer_option: selectedAnswers[i]?.submitted_answer_option,
//                 answer_text: selectedAnswers[i]?.answer_text,
//                 submitted_answer_marks: ((questionData[i].question_type?.toLowerCase() == "coding" || questionData[i].question_type?.toLowerCase() == "subjective") ? 0 : questionData[i].marks),
//                 submitted_answer_status: "SUBMITTED",
//                 evaluation_comments: "None",
//                 evaluated_by_email: "None",
//                 time_take_to_answer_in_sec: timeTakenPerQuestion[i],
//                 number_of_screen_switch: switchCount,
//                 used_back_button: backButtonCounter,
//                 changed_answer: optionChangeCount[i]
//             };
//             newArray.push(obj);
//             //   setArrayOfObjects(newArray)
//             // setArrayOfAnswers(newArray)
//             setCompleteAnswers(newArray)
//         }
//         console.log("arrayOfObjects:- ", arrayOfAnswers)
//         console.log("kya data ban raha hai", newArray)

//     }, [selectedAnswers])

//     useEffect(() => {
//         getTimeTakenPerQuestion()
//     }, [selectedAnswers])


//     const getTimeTakenPerQuestion = () => { //might need some work as it calculate time as a whole instead of per question
//         const endTime = new Date();
//         const timeDifferenceInSeconds = Math.floor((endTime.getTime() - (questionStartTime?.getTime() || endTime.getTime())) / 1000);

//         setTimeTakenPerQuestion(prevTimeTaken => {
//             // Update the time taken for the current question
//             const updatedTimeTaken = [...prevTimeTaken];
//             updatedTimeTaken[activeQuestion] = timeDifferenceInSeconds;
//             return updatedTimeTaken;
//         });
//     }

//     useEffect(() => {
//         const handleVisibilityChange = () => {
//             if (document.visibilityState === 'visible') {
//                 setSwitchCount(prevCount => prevCount + 1);
//             }
//         };

//         document.addEventListener('visibilitychange', handleVisibilityChange);

//         return () => {
//             document.removeEventListener('visibilitychange', handleVisibilityChange);
//         };
//     }, []);

//     const OnAnswerGiven = (elem: string) => {
//         let question = questionData![activeQuestion]; //ensures that if questions is null or undefined
//         console.log("activeQuestion", activeQuestion)
//         console.log("question", question)
//         let storedAnswer = {}

//         console.log("124423", questionData)

//         if (question.question_type?.toLowerCase() == "subjective") {
//             storedAnswer = {
//                 assessment_question_id: question.assessment_question_id,
//                 submitted_answer_option: null,
//                 answer_text: elem
//             }

//             //  storeValueTemp[activeQuestion] = storedAnswer

//         } else if (question.question_type?.toLowerCase() == "coding") {
//             console.log("124423", question.question_type?.toLowerCase())
//             storedAnswer = {
//                 assessment_question_id: question.assessment_question_id,
//                 submitted_answer_option: null,
//                 answer_text: elem
//             }
//         } else if (question.question_type?.toLowerCase() == "mcq") {
//             switch (elem) {
//                 case questionData[activeQuestion].option1:
//                     storedAnswer = {
//                         assessment_question_id: question.assessment_question_id,
//                         submitted_answer_option: 1,
//                         answer_text: elem
//                     }
//                     break;
//                 case questionData[activeQuestion].option2:
//                     storedAnswer = {
//                         assessment_question_id: question.assessment_question_id,
//                         submitted_answer_option: 2,
//                         answer_text: elem
//                     }
//                     break;
//                 case questionData[activeQuestion].option3:
//                     storedAnswer = {
//                         assessment_question_id: question.assessment_question_id,
//                         submitted_answer_option: 3,
//                         answer_text: elem
//                     }
//                     break;
//                 case questionData[activeQuestion].option4:
//                     storedAnswer = {
//                         assessment_question_id: question.assessment_question_id,
//                         submitted_answer_option: 4,
//                         answer_text: elem
//                     }
//                     break;
//                 default:
//                     console.log("Going outside the switch cases:-")
//             }
//         } else if (question.question_type == "T/F") {
//             switch (elem) {
//                 case questionData[activeQuestion].option1:
//                     storedAnswer = {
//                         assessment_question_id: question.assessment_question_id,
//                         submitted_answer_option: 1,
//                         answer_text: elem
//                     }
//                     break;
//                 case questionData[activeQuestion].option2:
//                     storedAnswer = {
//                         assessment_question_id: question.assessment_question_id,
//                         submitted_answer_option: 2,
//                         answer_text: elem
//                     }
//                     break;
//                 case questionData[activeQuestion].option3:
//                     storedAnswer = {
//                         assessment_question_id: question.assessment_question_id,
//                         submitted_answer_option: 3,
//                         answer_text: elem
//                     }
//                     break;
//                 case questionData[activeQuestion].option4:
//                     storedAnswer = {
//                         assessment_question_id: question.assessment_question_id,
//                         submitted_answer_option: 4,
//                         answer_text: elem
//                     }
//                     break;
//                 default:
//                     console.log("Going outside the switch cases:-")
//             }
//         } else {
//             console.log("Wrong question type")
//             console.error("Wrong question type")
//         }

//         switch (elem) {
//             case questionData[activeQuestion].option1:
//                 storedAnswer = {
//                     assessment_question_id: question.assessment_question_id,
//                     submitted_answer_option: 1,
//                     answer_text: elem
//                 }
//                 break;
//             case questionData[activeQuestion].option2:
//                 storedAnswer = {
//                     assessment_question_id: question.assessment_question_id,
//                     submitted_answer_option: 2,
//                     answer_text: elem
//                 }
//                 break;
//             case questionData[activeQuestion].option3:
//                 storedAnswer = {
//                     assessment_question_id: question.assessment_question_id,
//                     submitted_answer_option: 3,
//                     answer_text: elem
//                 }
//                 break;
//             case questionData[activeQuestion].option4:
//                 storedAnswer = {
//                     assessment_question_id: question.assessment_question_id,
//                     submitted_answer_option: 4,
//                     answer_text: elem
//                 }
//                 break;
//             default:
//                 console.log("Going outside the switch cases:-")
//         }

//         const storeValueTemp = [...selectedAnswers]
//         storeValueTemp[activeQuestion] = storedAnswer
//         console.log("storeValueTemp", storeValueTemp);
//         setSelectedAnswer(storeValueTemp)
//         console.log("selectedAnswers", selectedAnswers);
//         setOptionChangeCount(prevCounts => {
//             const updatedCounts = [...prevCounts];
//             if (activeQuestion >= 0 && activeQuestion < questionData.length) { // Ensure activeQuestion is within bounds
//                 updatedCounts[activeQuestion]++;
//             }
//             return updatedCounts;
//         });
//         setStoredAnswers(storeValueTemp)
//     }

//     // console.log("typetesttest", type)
//     // console.log("questiontest", question)
//     // console.log("questionNumbertest", questionNumber)




//     if (questionData[activeQuestion].question_type?.toLowerCase() == "mcq") {
//         const options = [1, 2, 3, 4];
//         return (
//             <div className='p-4 w-auto h-[100%] flex flex-col gap-4'>
//                 <div className='flex flex-col gap-2'>
//                     <div className=''>
//                         <span className='text-xs text-textSecondary'>Category: {questionData[activeQuestion].question_type}</span>
//                     </div>
//                     <div className='flex flex-row gap-4'>
//                         <span className='text-xl'>Q.{activeQuestion + 1}</span>
//                         <span className='text-xl'>{questionData[activeQuestion].question ? questionData[activeQuestion].question : "Question"}</span>
//                     </div>

//                 </div>
//                 <div className='h-[90%] bg-white p-8'>
//                     {selectedAnswers[activeQuestion].answer_text ? (<RadioGroup
//                         value={
//                             selectedAnswers[activeQuestion].answer_text
//                         }
//                         onChange={(elem) => OnAnswerGiven(elem)}
//                     >
//                         <RadioGroup.Label className="text-md text-textSecondary mb-2">
//                             Options :
//                         </RadioGroup.Label>
//                         <div className="space-y-2">
//                             {options.map((key) => (
//                                 <RadioGroup.Option
//                                     key={key}
//                                     value={
//                                         key == 1
//                                             ? questionData[activeQuestion].option1
//                                             : key == 2
//                                                 ? questionData[activeQuestion].option2
//                                                 : key == 3
//                                                     ? questionData[activeQuestion].option3
//                                                     : questionData[activeQuestion].option4
//                                     }
//                                     className={({ active, checked }) =>
//                                         `${active
//                                             ? "ring-2 ring-white ring-opacity-60 ring-offset-2 ring-offset-sky-300"
//                                             : ""
//                                         }
//                   ${checked
//                                             ? "bg-textSecondary bg-opacity-75 text-white"
//                                             : "bg-white"
//                                         }
//                     relative flex cursor-pointer rounded-lg px-5 py-4 shadow-md focus:outline-none`
//                                     }
//                                 >
//                                     {({ checked }) => (
//                                         <>
//                                             <div className="flex w-full items-center justify-between">
//                                                 <div className="flex items-center">
//                                                     <div className="text-sm">
//                                                         <RadioGroup.Label
//                                                             as="p"
//                                                             className={`font-medium  ${checked ? "text-white" : "text-textColor"
//                                                                 }`}
//                                                         >
//                                                             {key == 1
//                                                                 ? questionData[activeQuestion].option1
//                                                                 : key == 2
//                                                                     ? questionData[activeQuestion].option2
//                                                                     : key == 3
//                                                                         ? questionData[activeQuestion].option3
//                                                                         : questionData[activeQuestion].option4}
//                                                         </RadioGroup.Label>
//                                                     </div>
//                                                 </div>
//                                                 {checked && (
//                                                     <div className="shrink-0 text-white">
//                                                         <CheckCircleIcon className="h-6 w-6" />
//                                                     </div>
//                                                 )}
//                                             </div>
//                                         </>
//                                     )}
//                                 </RadioGroup.Option>
//                             ))}
//                         </div>
//                     </RadioGroup>) : (<RadioGroup
//                         value={
//                             questionData && questionData[activeQuestion] && selectedAnswers && selectedAnswers[questionData[activeQuestion].submitted_answer]
//                         }
//                         onChange={(elem) => OnAnswerGiven(elem)}
//                     >
//                         <RadioGroup.Label className="text-md text-textSecondary mb-2">
//                             Options :
//                         </RadioGroup.Label>
//                         <div className="space-y-2">
//                             {options.map((key) => (
//                                 <RadioGroup.Option
//                                     key={key}
//                                     value={
//                                         key == 1
//                                             ? questionData[activeQuestion].option1
//                                             : key == 2
//                                                 ? questionData[activeQuestion].option2
//                                                 : key == 3
//                                                     ? questionData[activeQuestion].option3
//                                                     : questionData[activeQuestion].option4
//                                     }
//                                     className={({ active, checked }) =>
//                                         `${active
//                                             ? "ring-2 ring-white ring-opacity-60 ring-offset-2 ring-offset-sky-300"
//                                             : ""
//                                         }
//                   ${checked
//                                             ? "bg-textSecondary bg-opacity-75 text-white"
//                                             : "bg-white"
//                                         }
//                     relative flex cursor-pointer rounded-lg px-5 py-4 shadow-md focus:outline-none`
//                                     }
//                                 >
//                                     {({ checked }) => (
//                                         <>
//                                             <div className="flex w-full items-center justify-between">
//                                                 <div className="flex items-center">
//                                                     <div className="text-sm">
//                                                         <RadioGroup.Label
//                                                             as="p"
//                                                             className={`font-medium  ${checked ? "text-white" : "text-textColor"
//                                                                 }`}
//                                                         >
//                                                             {key == 1
//                                                                 ? questionData[activeQuestion].option1
//                                                                 : key == 2
//                                                                     ? questionData[activeQuestion].option2
//                                                                     : key == 3
//                                                                         ? questionData[activeQuestion].option3
//                                                                         : questionData[activeQuestion].option4}
//                                                         </RadioGroup.Label>
//                                                     </div>
//                                                 </div>
//                                                 {checked && (
//                                                     <div className="shrink-0 text-white">
//                                                         <CheckCircleIcon className="h-6 w-6" />
//                                                     </div>
//                                                 )}
//                                             </div>
//                                         </>
//                                     )}
//                                 </RadioGroup.Option>
//                             ))}
//                         </div>
//                     </RadioGroup>)}
//                 </div>
//             </div>
//         )
//     } else if (questionData[activeQuestion].question_type == "T/F") {
//         const options = [1, 2, 3, 4];
//         return (
//             <div className='p-4 w-auto h-[100%] flex flex-col gap-4'>
//                 <div className='flex flex-col gap-2'>
//                     <div className=''>
//                         <span className='text-xs text-textSecondary'>Category: {questionData[activeQuestion].question_type}</span>
//                     </div>
//                     <div className='flex flex-row gap-4'>
//                         <span className='text-xl'>Q.{activeQuestion + 1}</span>
//                         <span className='text-xl'>{questionData[activeQuestion].question ? questionData[activeQuestion].question : "Question"}</span>
//                     </div>
//                 </div>
//                 <div className='h-[90%] bg-white p-8'>
//                     {selectedAnswers[activeQuestion].answer_text ? (<RadioGroup
//                         value={
//                             selectedAnswers[activeQuestion].answer_text
//                         }
//                         onChange={(elem) => OnAnswerGiven(elem)}
//                     >
//                         <RadioGroup.Label className="text-md text-textSecondary mb-2">
//                             Options:
//                         </RadioGroup.Label>
//                         <div className="space-y-2">
//                             {options.slice(0, 2).map((key) => (
//                                 <RadioGroup.Option
//                                     key={key}
//                                     value={
//                                         key == 1
//                                             ? questionData[activeQuestion].option1
//                                             : questionData[activeQuestion].option2
//                                     }
//                                     className={({ active, checked }) =>
//                                         `${active
//                                             ? "ring-2 ring-white ring-opacity-60 ring-offset-2 ring-offset-sky-300"
//                                             : ""
//                                         }
//                 ${checked
//                                             ? "bg-textSecondary bg-opacity-75 text-white"
//                                             : "bg-white"
//                                         }
//                   relative flex cursor-pointer rounded-lg px-5 py-4 shadow-md focus:outline-none`
//                                     }
//                                 >
//                                     {({ checked }) => (
//                                         <>
//                                             <div className="flex w-full items-center justify-between">
//                                                 <div className="flex items-center">
//                                                     <div className="text-sm">
//                                                         <RadioGroup.Label
//                                                             as="p"
//                                                             className={`font-medium  ${checked ? "text-white" : "text-textColor"
//                                                                 }`}
//                                                         >
//                                                             {key == 1
//                                                                 ? questionData[activeQuestion].option1
//                                                                 : questionData[activeQuestion].option2}
//                                                         </RadioGroup.Label>
//                                                     </div>
//                                                 </div>
//                                                 {checked && (
//                                                     <div className="shrink-0 text-white">
//                                                         <CheckCircleIcon className="h-6 w-6" />
//                                                     </div>
//                                                 )}
//                                             </div>
//                                         </>
//                                     )}
//                                 </RadioGroup.Option>
//                             ))}
//                         </div>
//                     </RadioGroup>) : (<RadioGroup
//                         value={
//                             questionData && questionData[activeQuestion] && selectedAnswers && selectedAnswers[questionData[activeQuestion].submitted_answer]
//                         }
//                         onChange={(elem) => OnAnswerGiven(elem)}
//                     >
//                         <RadioGroup.Label className="text-md text-textSecondary mb-2">
//                             Options:
//                         </RadioGroup.Label>
//                         <div className="space-y-2">
//                             {options.slice(0, 2).map((key) => (
//                                 <RadioGroup.Option
//                                     key={key}
//                                     value={
//                                         key == 1
//                                             ? questionData[activeQuestion].option1
//                                             : questionData[activeQuestion].option2
//                                     }
//                                     className={({ active, checked }) =>
//                                         `${active
//                                             ? "ring-2 ring-white ring-opacity-60 ring-offset-2 ring-offset-sky-300"
//                                             : ""
//                                         }
//                 ${checked
//                                             ? "bg-textSecondary bg-opacity-75 text-white"
//                                             : "bg-white"
//                                         }
//                   relative flex cursor-pointer rounded-lg px-5 py-4 shadow-md focus:outline-none`
//                                     }
//                                 >
//                                     {({ checked }) => (
//                                         <>
//                                             <div className="flex w-full items-center justify-between">
//                                                 <div className="flex items-center">
//                                                     <div className="text-sm">
//                                                         <RadioGroup.Label
//                                                             as="p"
//                                                             className={`font-medium  ${checked ? "text-white" : "text-textColor"
//                                                                 }`}
//                                                         >
//                                                             {key == 1
//                                                                 ? questionData[activeQuestion].option1
//                                                                 : questionData[activeQuestion].option2}
//                                                         </RadioGroup.Label>
//                                                     </div>
//                                                 </div>
//                                                 {checked && (
//                                                     <div className="shrink-0 text-white">
//                                                         <CheckCircleIcon className="h-6 w-6" />
//                                                     </div>
//                                                 )}
//                                             </div>
//                                         </>
//                                     )}
//                                 </RadioGroup.Option>
//                             ))}
//                         </div>
//                     </RadioGroup>)}
//                 </div>
//             </div>
//         )
//     } else if (questionData[activeQuestion].question_type?.toLowerCase() == "coding") {
//         return (
//             <div className='p-4 w-auto h-[100%] flex flex-col gap-4'>
//                 <div className='flex flex-col gap-2'>
//                     <div className=''>
//                         <span className='text-xs text-textSecondary'>Category: {questionData[activeQuestion].question_type}</span>
//                     </div>
//                     <div className='flex flex-row gap-4'>
//                         <span className='text-xl'>Q.{activeQuestion + 1}</span>
//                         <span className='text-xl'>{questionData[activeQuestion].question ? questionData[activeQuestion].question : "Question"}</span>
//                     </div>
//                 </div>
//                 <div className='h-[90%] bg-white'>
//                     <textarea
//                         className="w-full h-96 lg:h-[100%] p-4 text-slate-700 font-sans text-base"
//                         style={{ whiteSpace: 'pre-wrap' }}
//                         value={selectedAnswers[activeQuestion]?.answer_text || ''}
//                         onChange={(event) => OnAnswerGiven(event.target.value)}
//                         placeholder="Type the code here..."
//                     />

//                 </div>
//             </div>
//         )
//     } else if (questionData[activeQuestion].question_type?.toLowerCase() == "subjective") {
//         return (
//             <div className='p-4 w-[100%] lg:w-auto h-[100%] flex flex-col gap-4'>
//                 <div className='flex flex-col gap-2'>
//                     <div className=''>
//                         <span className='text-xs text-textSecondary'>Category: {questionData[activeQuestion].question_type}</span>
//                     </div>
//                     <div className='flex flex-row gap-4'>
//                         <span className='text-xl'>Q.{activeQuestion + 1}</span>
//                         <span className='text-xl'>{questionData[activeQuestion].question ? questionData[activeQuestion].question : "Question"}</span>
//                     </div>
//                 </div>
//                 <div className='h-[90%] w-auto bg-white'>
//                     <textarea
//                         className="w-11/12 lg:w-full h-96 lg:h-[100%] p-4 text-slate-700 font-sans text-base"
//                         style={{ whiteSpace: 'pre-wrap' }}
//                         value={selectedAnswers[activeQuestion]?.answer_text || ''}
//                         onChange={(event) => OnAnswerGiven(event.target.value)}
//                         placeholder="Type the answer here..."
//                     />
//                 </div>
//             </div>
//         )
//     } else {
//         return (
//             <div>
//                 !! Wrong Type !!
//             </div>
//         )
//     }

// }

// export default AnswerInput
import React, { useState, useEffect } from 'react';
import { RadioGroup } from "@headlessui/react";
import { CheckCircleIcon } from "@heroicons/react/20/solid";
import CodeEditor from './CodeEditor'; // Import the CodeEditor component

interface storedAnswer {
  assessment_question_id: number,
  submitted_answer_option: number,
  answer_text: string | null,
  language?: string
}

interface AnswerInputProps {
  questionData: any[];
  activeQuestion: number;
  setStoredAnswers: React.Dispatch<React.SetStateAction<any[]>>;
  setCompleteAnswers: React.Dispatch<React.SetStateAction<any>>;
  startUserAssessment: any;
}

const AnswerInput = ({ 
  questionData, 
  activeQuestion, 
  setStoredAnswers, 
  setCompleteAnswers, 
  startUserAssessment 
}: AnswerInputProps) => {
  const [selectedAnswers, setSelectedAnswer] = useState<storedAnswer[]>(
    Array(questionData?.length).fill({
      assessment_question_id: 0,
      submitted_answer_option: 0,
      answer_text: null,
      language: 'javascript'
    })
  );
  
  const [selectedLanguage, setSelectedLanguage] = useState<string>('javascript');
  const [switchCount, setSwitchCount] = useState(0);
  const [backButtonCounter, setBackButtonCounter] = useState(0);
  const [optionChangeCount, setOptionChangeCount] = useState<number[]>(
    questionData ? Array(questionData.length).fill(0) : []
  );
  const [questionStartTime, setQuestionStartTime] = useState<Date>(new Date());
  const [timeTakenPerQuestion, setTimeTakenPerQuestion] = useState<number[]>(
    Array(questionData?.length).fill(0)
  );

  // Set initial language based on stored answer when question changes
  useEffect(() => {
    if (selectedAnswers[activeQuestion]?.language) {
      setSelectedLanguage(selectedAnswers[activeQuestion].language);
    } else {
      setSelectedLanguage('javascript');
    }
  }, [activeQuestion, selectedAnswers]);

  // Track screen visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        setSwitchCount(prevCount => prevCount + 1);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Build answer objects for submission
  useEffect(() => {
    if (!questionData || !startUserAssessment) return;
    
    const newArray = [];
    for (let i = 0; i < questionData.length; i++) {
      const obj = {
        question: questionData[i].question,
        user_assessment_attempt_id: startUserAssessment?.user_assessment_attempt_id,
        assessment_question_id: questionData[i].assessment_question_id,
        submitted_answer_option: selectedAnswers[i]?.submitted_answer_option,
        answer_text: selectedAnswers[i]?.answer_text,
        language: selectedAnswers[i]?.language,
        submitted_answer_marks: ((questionData[i].question_type?.toLowerCase() === "coding" || 
                                  questionData[i].question_type?.toLowerCase() === "subjective") 
                                  ? 0 : questionData[i].marks),
        submitted_answer_status: "SUBMITTED",
        evaluation_comments: "None",
        evaluated_by_email: "None",
        time_take_to_answer_in_sec: timeTakenPerQuestion[i],
        number_of_screen_switch: switchCount,
        used_back_button: backButtonCounter,
        changed_answer: optionChangeCount[i]
      };
      newArray.push(obj);
    }
    
    setCompleteAnswers(newArray);
  }, [selectedAnswers, questionData, startUserAssessment, timeTakenPerQuestion, switchCount, backButtonCounter, optionChangeCount]);

  // Calculate time taken per question
  useEffect(() => {
    getTimeTakenPerQuestion();
  }, [selectedAnswers, activeQuestion]);

  const getTimeTakenPerQuestion = () => {
    const endTime = new Date();
    const timeDifferenceInSeconds = Math.floor((endTime.getTime() - questionStartTime.getTime()) / 1000);

    setTimeTakenPerQuestion(prevTimeTaken => {
      const updatedTimeTaken = [...prevTimeTaken];
      updatedTimeTaken[activeQuestion] = timeDifferenceInSeconds;
      return updatedTimeTaken;
    });
  };

  // Handle answer selection
  const onAnswerGiven = (elem: string) => {
    if (!questionData || !questionData[activeQuestion]) return;
    
    const question = questionData[activeQuestion];
    let storedAnswer: storedAnswer = {
      assessment_question_id: question.assessment_question_id,
      submitted_answer_option: 0,
      answer_text: null,
      language: selectedLanguage
    };

    // Process answer based on question type
    if (question.question_type?.toLowerCase() === "subjective" || 
        question.question_type?.toLowerCase() === "coding") {
      storedAnswer = {
        assessment_question_id: question.assessment_question_id,
        submitted_answer_option: null,
        answer_text: elem,
        language: selectedLanguage
      };
    } else if (question.question_type?.toLowerCase() === "mcq" || 
               question.question_type === "T/F") {
      // Map selected option text to option number
      let optionNumber = 0;
      for (let i = 1; i <= 4; i++) {
        if (elem === question[`option${i}`]) {
          optionNumber = i;
          break;
        }
      }
      
      storedAnswer = {
        assessment_question_id: question.assessment_question_id,
        submitted_answer_option: optionNumber,
        answer_text: elem
      };
    }

    // Update stored answers
    const storeValueTemp = [...selectedAnswers];
    storeValueTemp[activeQuestion] = storedAnswer;
    setSelectedAnswer(storeValueTemp);
    setStoredAnswers(storeValueTemp);
    
    // Track option changes
    setOptionChangeCount(prevCounts => {
      const updatedCounts = [...prevCounts];
      updatedCounts[activeQuestion]++;
      return updatedCounts;
    });
  };

  // Handle language change
  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
    
    // Update the stored answer with the new language
    const storeValueTemp = [...selectedAnswers];
    storeValueTemp[activeQuestion] = {
      ...storeValueTemp[activeQuestion],
      language: language
    };
    
    setSelectedAnswer(storeValueTemp);
    setStoredAnswers(storeValueTemp);
  };

  // If no question data is available
  if (!questionData || !questionData[activeQuestion]) {
    return <div className="p-6 text-center text-gray-500">No question data available</div>;
  }

  // Render MCQ question
  if (questionData[activeQuestion].question_type?.toLowerCase() === "mcq") {
    const options = [1, 2, 3, 4];
    return (
      <div className='w-full h-full flex flex-col'>
        {/* Question header */}
        <div className='flex flex-col mb-4'>
          <div className='mb-2'>
            <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
              {questionData[activeQuestion].question_type} • {questionData[activeQuestion].marks} marks
            </span>
          </div>
          <div className='flex flex-row gap-2'>
            <span className='text-lg font-bold text-blue-700'>Q.{activeQuestion + 1}</span>
            <span className='text-lg'>{questionData[activeQuestion].question || "Question"}</span>
          </div>
        </div>
        
        {/* Options */}
        <div className='flex-grow'>
          <RadioGroup
            value={selectedAnswers[activeQuestion]?.answer_text || ''}
            onChange={(elem) => onAnswerGiven(elem)}
            className="space-y-3"
          >
            <RadioGroup.Label className="text-sm font-medium text-gray-700 mb-2 block">
              Select one option:
            </RadioGroup.Label>
            
            <div className="space-y-2 max-w-2xl mx-auto">
              {options.map((key) => {
                const optionText = questionData[activeQuestion][`option${key}`];
                if (!optionText) return null;
                
                return (
                  <RadioGroup.Option
                    key={key}
                    value={optionText}
                    className={({ active, checked }) => `
                      relative flex cursor-pointer rounded-lg p-3 focus:outline-none transition-all duration-200
                      ${checked 
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-300 shadow-sm transform translate-y-px' 
                        : 'bg-white border border-gray-200 hover:border-blue-200 hover:bg-blue-50/30'}
                      max-w-xl
                    `}
                  >
                    {({ checked }) => (
                      <div className="flex items-center">
                        <div className={`
                          h-5 w-5 rounded-full flex items-center justify-center mr-3 shrink-0
                          ${checked ? 'bg-blue-600 text-white' : 'border-2 border-gray-300 bg-white'}
                        `}>
                          {checked && (
                            <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <div>
                          <RadioGroup.Label
                            as="p"
                            className={`text-sm ${checked ? 'font-medium text-blue-900' : 'font-normal text-gray-800'}`}
                          >
                            {optionText}
                          </RadioGroup.Label>
                        </div>
                      </div>
                    )}
                  </RadioGroup.Option>
                );
              })}
            </div>
          </RadioGroup>
        </div>
      </div>
    );
  } 
  // Render True/False question
  else if (questionData[activeQuestion].question_type === "T/F") {
    const options = [1, 2];
    return (
      <div className='w-full h-full flex flex-col'>
        {/* Question header */}
        <div className='flex flex-col mb-4'>
          <div className='mb-2'>
            <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
              {questionData[activeQuestion].question_type} • {questionData[activeQuestion].marks} marks
            </span>
          </div>
          <div className='flex flex-row gap-2'>
            <span className='text-lg font-bold text-blue-700'>Q.{activeQuestion + 1}</span>
            <span className='text-lg'>{questionData[activeQuestion].question || "Question"}</span>
          </div>
        </div>
        
        {/* Options */}
        <div className='flex-grow'>
          <RadioGroup
            value={selectedAnswers[activeQuestion]?.answer_text || ''}
            onChange={(elem) => onAnswerGiven(elem)}
            className="space-y-3"
          >
            <RadioGroup.Label className="text-sm font-medium text-gray-700 mb-2 block">
              Select one option:
            </RadioGroup.Label>
            
            <div className="flex flex-col sm:flex-row gap-3 max-w-md">
              {options.map((key) => {
                const optionText = questionData[activeQuestion][`option${key}`];
                if (!optionText) return null;
                
                return (
                  <RadioGroup.Option
                    key={key}
                    value={optionText}
                    className={({ active, checked }) => `
                      relative flex-1 cursor-pointer rounded-lg p-3 focus:outline-none transition-all duration-200
                      ${checked 
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-300 shadow-sm transform translate-y-px' 
                        : 'bg-white border border-gray-200 hover:border-blue-200 hover:bg-blue-50/30'}
                      min-w-[120px] flex items-center justify-center
                    `}
                  >
                    {({ checked }) => (
                      <div className="flex items-center justify-center">
                        <div className={`
                          h-5 w-5 rounded-full flex items-center justify-center mr-2
                          ${checked ? 'bg-blue-600 text-white' : 'border-2 border-gray-300 bg-white'}
                        `}>
                          {checked && (
                            <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <span className={`text-sm ${checked ? 'font-medium text-blue-900' : 'font-normal text-gray-700'}`}>
                          {optionText}
                        </span>
                      </div>
                    )}
                  </RadioGroup.Option>
                );
              })}
            </div>
          </RadioGroup>
        </div>
      </div>
    );
  } 
  // Render Coding question with CodeEditor and language selector
  else if (questionData[activeQuestion].question_type?.toLowerCase() === "coding") {
    return (
      <div className='w-full h-full flex flex-col'>
        {/* Question header */}
        <div className='flex flex-col mb-4'>
          <div className='mb-2'>
            <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800'>
              {questionData[activeQuestion].question_type} • {questionData[activeQuestion].marks} marks
            </span>
          </div>
          <div className='flex flex-row gap-2'>
            <span className='text-lg font-bold text-purple-700'>Q.{activeQuestion + 1}</span>
            <span className='text-lg'>{questionData[activeQuestion].question || "Question"}</span>
          </div>
        </div>
        
        {/* Language selector */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Select language:</label>
          <div className="flex flex-wrap gap-2">
            {['javascript', 'python', 'java', 'cpp'].map((lang) => (
              <button
                key={lang}
                type="button"
                onClick={() => handleLanguageChange(lang)}
                className={`px-3 py-1.5 text-xs font-medium rounded-md transition-colors
                  ${selectedLanguage === lang
                    ? 'bg-purple-600 text-white ring-2 ring-purple-600 ring-offset-1'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }
                `}
              >
                {lang === 'javascript' && 'JavaScript'}
                {lang === 'python' && 'Python'}
                {lang === 'java' && 'Java'}
                {lang === 'cpp' && 'C++'}
              </button>
            ))}
          </div>
        </div>
        
        {/* Code editor */}
        <div className='flex-grow'>
          <CodeEditor
            initialValue={selectedAnswers[activeQuestion]?.answer_text || ''}
            onChange={(code) => onAnswerGiven(code)}
            language={selectedLanguage}
          />
          <div className="mt-2 flex justify-between items-center text-xs text-gray-500">
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>Need help? Check the question description for hints</span>
            </div>
            <span>
              {selectedAnswers[activeQuestion]?.answer_text?.length || 0} characters
            </span>
          </div>
        </div>
      </div>
    );
  } 
  // Render Subjective question
  else if (questionData[activeQuestion].question_type?.toLowerCase() === "subjective") {
    return (
      <div className='w-full h-full flex flex-col'>
        {/* Question header */}
        <div className='flex flex-col mb-4'>
          <div className='mb-2'>
            <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800'>
              {questionData[activeQuestion].question_type} • {questionData[activeQuestion].marks} marks
            </span>
          </div>
          <div className='flex flex-row gap-2'>
            <span className='text-lg font-bold text-amber-700'>Q.{activeQuestion + 1}</span>
            <span className='text-lg'>{questionData[activeQuestion].question || "Question"}</span>
          </div>
        </div>
        
        {/* Answer textarea */}
        <div className='flex-grow'>
          <textarea
            className="w-full h-60 min-h-60 p-4 text-gray-800 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent resize-y"
            style={{ whiteSpace: 'pre-wrap', lineHeight: '1.5' }}
            value={selectedAnswers[activeQuestion]?.answer_text || ''}
            onChange={(event) => onAnswerGiven(event.target.value)}
            placeholder="Type your answer here..."
          />
          <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>Be concise and clear in your explanation</span>
            </div>
            <span>
              {selectedAnswers[activeQuestion]?.answer_text?.length || 0} characters
            </span>
          </div>
        </div>
      </div>
    );
  } 
  // Fallback for unknown question type
  else {
    return (
      <div className="p-6 bg-red-50 text-red-700 rounded-lg border border-red-200">
        <div className="font-bold mb-2">Unknown Question Type</div>
        <p>The question type "{questionData[activeQuestion].question_type}" is not supported.</p>
      </div>
    );
  }
};

export default AnswerInput;