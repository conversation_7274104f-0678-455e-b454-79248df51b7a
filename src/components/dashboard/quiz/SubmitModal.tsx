// import React, {useState, useEffect } from 'react';
// import { XMarkIcon, CheckBadgeIcon, XCircleIcon } from '@heroicons/react/24/outline';
// import Button from '@/components/ui/Button';
// import Link from 'next/link';
// import { useRouter } from 'next/navigation';

// interface SubmitModalProps {
//   modalName?: string;
//   modalText?: string;
//   okclickPath?:any;
//   timeset?: number; // in seconds only
//   onClose?: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
// }

// export default function SubmitModal({ modalName, modalText, onClose, timeset, okclickPath }: SubmitModalProps) {
//   const [time, setTime] = useState(timeset ? (timeset * 1000) : 4000)

//   const router = useRouter(); // Get router instance
//   useEffect(() => {
   
//   }, [onClose]); 

//   if(timeset){
//     setTimeout(() => {
//       onClose
//     }, time);
//   }

  
  
//   return (
//     <div className="modal flex justify-center items-center fixed inset-0 bg-black bg-opacity-40 overflow-auto z-10">
//       <div className="flex flex-col gap-5 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md w-[400px] h-[300px]">
//         <button className="flex justify-end" onClick={onClose}>
//           <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
//         </button>
        
//        {modalName === "Error" ? (
//       <div className="flex flex-col gap-5 justify-center items-center text-center">
//        <XCircleIcon className="h-[50px] w-[50px] text-red-500" />
//           <h1 className='text-lg'>{modalName}</h1>
//           {modalText?<p>{modalText}</p>:<p>{`The ${modalName} is submit.`}</p>}
//           </div>
//           ) : (
//             <div className="flex flex-col gap-5 justify-center items-center text-center">
//           <CheckBadgeIcon className="h-[50px] w-[50px] text-green-500" />
//           <h1 className='text-lg'>{modalName}</h1>
//           {modalText?<p>{modalText}</p>:<p>{`The ${modalName} is submit.`}</p>}
//           {okclickPath?
//           <Link href={okclickPath}>
//           <Button btnName='OK'/> 
//           </Link> :null}
//           </div>
//           )}        
        
//       </div>
//     </div>
//   );
// }

import React, { useState, useEffect } from 'react';
import { XMarkIcon, CheckBadgeIcon, XCircleIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface SubmitModalProps {
  modalName?: string;
  modalText?: string;
  okclickPath?: string;
  timeset?: number; // in seconds only
  onClose?: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}

export default function SubmitModal({ modalName, modalText, onClose, timeset, okclickPath }: SubmitModalProps) {
  const [time, setTime] = useState(timeset ? (timeset * 1000) : 4000);
  const [countdown, setCountdown] = useState(5);
  const [showConfetti, setShowConfetti] = useState(true);
  const router = useRouter();
  
  // Auto-close modal after timeset if provided
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (timeset && onClose) {
      timer = setTimeout(() => {
        onClose();
      }, time);
    }
    
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [onClose, time, timeset]);
  
  // Countdown for auto-redirect
  useEffect(() => {
    if (okclickPath && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
      
      return () => clearTimeout(timer);
    } else if (okclickPath && countdown === 0) {
      router.push(okclickPath);
    }
  }, [countdown, okclickPath, router]);
  
  // Hide confetti after a few seconds to reduce visual noise
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowConfetti(false);
    }, 2500);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      {/* Confetti effect for success message */}
      {showConfetti && modalName !== "Error" && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {[...Array(50)].map((_, i) => {
            const size = Math.random() * 10 + 5;
            const color = [
              'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 
              'bg-purple-500', 'bg-pink-500', 'bg-indigo-500'
            ][Math.floor(Math.random() * 7)];
            const left = `${Math.random() * 100}%`;
            const animationDuration = `${Math.random() * 3 + 2}s`;
            const animationDelay = `${Math.random() * 0.5}s`;
            
            return (
              <div 
                key={i}
                className={`absolute ${color} rounded-sm opacity-70`}
                style={{
                  left,
                  top: '-10px',
                  width: `${size}px`,
                  height: `${size * 0.6}px`,
                  transform: `rotate(${Math.random() * 360}deg)`,
                  animation: `confetti ${animationDuration} ease-in-out ${animationDelay} forwards`
                }}
              />
            );
          })}
        </div>
      )}
      
      <style jsx global>{`
        @keyframes confetti {
          0% { transform: translateY(-10px) rotate(0deg); opacity: 1; }
          100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
        }
      `}</style>
      
      <div className="bg-white rounded-xl shadow-2xl w-[90%] sm:w-[85%] md:w-[450px] max-w-lg min-h-[320px] flex flex-col overflow-hidden transform transition-all">
        {/* Header */}
        <div className={`${modalName === "Error" ? 'bg-red-600' : 'bg-green-600'} px-6 py-4 flex justify-between items-center`}>
          <h2 className="text-xl font-bold text-white">
            {modalName || (modalName === "Error" ? "Error" : "Success")}
          </h2>
          {onClose && (
            <button 
              className="text-white hover:text-gray-200 focus:outline-none transition-colors"
              onClick={onClose}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          )}
        </div>
        
        {/* Content */}
        <div className="px-6 py-8 flex-grow flex flex-col items-center justify-center text-center">
          {modalName === "Error" ? (
            <div className="mb-4 p-3 rounded-full bg-red-100">
              <XCircleIcon className="h-16 w-16 text-red-600" />
            </div>
          ) : (
            <div className="mb-4 p-3 rounded-full bg-green-100">
              <CheckBadgeIcon className="h-16 w-16 text-green-600" />
            </div>
          )}
          
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {modalName === "Error" ? "Something went wrong" : "Quiz Submitted Successfully!"}
          </h3>
          
          <p className="text-gray-600 mb-6">
            {modalText || (modalName === "Error" 
              ? "There was an error processing your request. Please try again."
              : "Thank you for completing the quiz. Your answers have been recorded."
            )}
          </p>
          
          {okclickPath && (
            <div className="text-sm text-gray-500 mb-4">
              You will be redirected in <span className="font-bold">{countdown}</span> seconds...
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex justify-center border-t">
          {okclickPath ? (
            <Link href={okclickPath}>
              <button className={`px-6 py-2 rounded-md font-medium transition-all ${
                modalName === "Error" 
                  ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
                  : 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
              } focus:outline-none focus:ring-2 focus:ring-offset-2`}>
                {modalName === "Error" ? "Try Again" : "View Results"}
              </button>
            </Link>
          ) : (
            <button 
              onClick={onClose}
              className={`px-6 py-2 rounded-md font-medium transition-all ${
                modalName === "Error" 
                  ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
                  : 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
              } focus:outline-none focus:ring-2 focus:ring-offset-2`}
            >
              {modalName === "Error" ? "Close" : "OK"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}