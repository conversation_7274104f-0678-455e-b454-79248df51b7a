// import React, { useState, useRef, Ref, useEffect, forwardRef, useImperativeHandle } from 'react'


// export type markQuestionRef = {
//   updateValues: () => void
// }
// interface QuestionSelectionProps {
//   question: Object[];
//   startUserAssessment: Object[];
//   current?: Function;
//   ref: Ref<markQuestionRef>
//   setliveStateCheck: Function

// }

// const QuestionSelection = ({ question, current, activeQuestion, storedAnswers, setliveStateCheck, anotherLiveState, liveStatus }: QuestionSelectionProps) => {

//   const initialLiveStatus = Array(question.length).fill('1');

//   useEffect(() => {
//     if (liveStatus.current[activeQuestion] == "1") {
//       const newArray = [...liveStatus.current];
//       newArray[activeQuestion] = "2"
//       // setLiveStatus(newArray)
//       liveStatus.current = newArray
//     }
//   }, [activeQuestion])

//   useEffect(() => {
//     if ((liveStatus.current[activeQuestion] == "2" || liveStatus.current[activeQuestion] == "4") && storedAnswers[activeQuestion]?.answer_text) {
//       const newArray = [...liveStatus.current];
//       newArray[activeQuestion] = "3"
//       liveStatus.current = newArray
//     }
//   }, [storedAnswers])


//   useEffect(() => {
//     setliveStateCheck(liveStatus.current)

//   }, [initialLiveStatus])

//   // useEffect(() => {
//   //   liveStatus.current = anotherLiveState
//   // }, [anotherLiveState])

//   return (
//     <div className='w-auto h-[100%] border'>
//       <div className="grid grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 2xl:grid-cols-8 gap-2 p-4 h-[100%] flex justify-start item-start overflow-y-auto">
//         {question.map((item, index) => (
//           <button
//           aria-label='getbutton'
//             className={`flex items-center justify-center w-8 h-8 rounded-full ${liveStatus.current[index] == "1" ? "bg-slate-300 text-black" : liveStatus.current[index] == "2" ? "bg-red-500 text-white" : liveStatus.current[index] == "3" ? "bg-textSecondary text-white" : "bg-purple-500 text-white"}  focus:outline-none`}
//             key={index} // Use index as key since `item.id` isn't provided
//             onClick={() => { current(index); }}
//           >
//             <span className="">{index + 1}</span>
//           </button>
//         ))}
//       </div>
//       {/* <div className='flex justify-center px-10 m-4'>
//         <button
//           className='px-3 w-40 py-2 text-lg rounded-md w-full text-white bg-secondary hover:bg-hoverColor '
//           onClick={() => (updateValues("mfr"))}>Mark for review
//         </button>
//       </div> */}
//     </div>
//   );
// }

// export default QuestionSelection;

import React, { useEffect, forwardRef, useImperativeHandle } from 'react';

export type markQuestionRef = {
  updateValues: () => void
}

interface QuestionSelectionProps {
  question: any[];
  current: (index: number) => void;
  activeQuestion: number;
  storedAnswers: any[];
  setliveStateCheck: (statuses: string[]) => void;
  anotherLiveState: string[];
  liveStatus: React.RefObject<string[]>;
  liveStateCheck?: string[];
}

const QuestionSelection = forwardRef<markQuestionRef, QuestionSelectionProps>(({
  question,
  current,
  activeQuestion,
  storedAnswers,
  setliveStateCheck,
  anotherLiveState,
  liveStatus
}, ref) => {
  
  // Update question status when active question changes
  useEffect(() => {
    if (liveStatus.current[activeQuestion] === "1") {
      const newArray = [...liveStatus.current];
      newArray[activeQuestion] = "2"; // Not Answered
      liveStatus.current = newArray;
      setliveStateCheck(newArray);
    }
  }, [activeQuestion, liveStatus, setliveStateCheck]);

  // Update question status when an answer is provided
  useEffect(() => {
    if ((liveStatus.current[activeQuestion] === "2" || liveStatus.current[activeQuestion] === "4") 
        && storedAnswers && storedAnswers[activeQuestion]?.answer_text) {
      const newArray = [...liveStatus.current];
      newArray[activeQuestion] = "3"; // Answered
      liveStatus.current = newArray;
      setliveStateCheck(newArray);
    }
  }, [activeQuestion, storedAnswers, liveStatus, setliveStateCheck]);

  // Expose method to update values to parent component
  useImperativeHandle(ref, () => ({
    updateValues: () => {
      setliveStateCheck(liveStatus.current);
    }
  }));
  
  // Get status style for question button
  const getStatusStyles = (status: string, isActive: boolean) => {
    let bgColor, textColor, borderStyle, hoverStyles, innerCircleColor;
    
    switch(status) {
      case "1": // Not Visited
        bgColor = "bg-gray-200";
        textColor = "text-gray-700";
        borderStyle = "border border-gray-300";
        hoverStyles = "hover:bg-gray-300 hover:border-gray-400";
        innerCircleColor = "bg-gray-700"; 
        break;
      case "2": // Not Answered
        bgColor = "bg-red-100";
        textColor = "text-red-800";
        borderStyle = "border border-red-300";
        hoverStyles = "hover:bg-red-200 hover:border-red-400";
        innerCircleColor = "bg-red-600";
        break;
      case "3": // Answered
        bgColor = "bg-green-100";
        textColor = "text-green-800";
        borderStyle = "border border-green-300";
        hoverStyles = "hover:bg-green-200 hover:border-green-400";
        innerCircleColor = "bg-green-600";
        break;
      case "4": // Marked for Review
        bgColor = "bg-purple-100";
        textColor = "text-purple-800";
        borderStyle = "border border-purple-300";
        hoverStyles = "hover:bg-purple-200 hover:border-purple-400";
        innerCircleColor = "bg-purple-600";
        break;
      default:
        bgColor = "bg-gray-200";
        textColor = "text-gray-700";
        borderStyle = "border border-gray-300";
        hoverStyles = "hover:bg-gray-300 hover:border-gray-400";
        innerCircleColor = "bg-gray-700";
    }
    
    // Add active styles if this is the active question
    const activeStyles = isActive ? `ring-2 ring-offset-1 ${
      status === "1" ? "ring-gray-500" : 
      status === "2" ? "ring-red-500" : 
      status === "3" ? "ring-green-500" : 
      "ring-purple-500"
    }` : "";
    
    return {
      container: `${bgColor} ${textColor} ${borderStyle} ${hoverStyles} ${activeStyles}`,
      innerCircle: innerCircleColor
    };
  };
  
  // Determine the optimal number of columns based on question count
  const getGridColumns = () => {
    const totalQuestions = question.length;
    
    // For responsive layouts
    const baseColumns = "grid-cols-5";
    const smColumns = totalQuestions <= 30 ? "sm:grid-cols-6" : "sm:grid-cols-6";
    const mdColumns = totalQuestions <= 20 ? "md:grid-cols-5" : "md:grid-cols-6";
    const lgColumns = totalQuestions <= 50 ? "lg:grid-cols-5" : "lg:grid-cols-6";
    const xlColumns = totalQuestions <= 100 ? "xl:grid-cols-6" : "xl:grid-cols-7";
    
    return `${baseColumns} ${smColumns} ${mdColumns} ${lgColumns} ${xlColumns}`;
  };

  // Count questions with each status
  const getStatusCounts = () => {
    return {
      notVisited: liveStatus.current.filter(s => s === "1").length,
      notAnswered: liveStatus.current.filter(s => s === "2").length,
      answered: liveStatus.current.filter(s => s === "3").length,
      review: liveStatus.current.filter(s => s === "4").length,
    };
  };

  const statusCounts = getStatusCounts();

  // Render grid of question buttons
  return (
    <div className='w-full'>
      <h3 className="text-sm font-medium text-gray-700 mb-2">Questions:</h3>
      <div className={`grid ${getGridColumns()} gap-2 overflow-y-auto max-h-[220px] p-1 rounded-lg`}>
        {question.map((item, index) => {
          const isActive = activeQuestion === index;
          const statusStyles = getStatusStyles(liveStatus.current[index], isActive);
          
          return (
            <button
              aria-label={`Question ${index + 1}`}
              className={`
                relative flex items-center justify-center w-8 h-8 rounded-full
                ${statusStyles.container}
                shadow-sm transition-all hover:shadow-md focus:outline-none
                text-xs font-medium cursor-pointer
              `}
              key={index}
              onClick={() => { current(index); }}
            >
              <span className="relative z-10">{index + 1}</span>
              {isActive && (
                <span className="absolute inset-0 w-full h-full rounded-full animate-pulse opacity-30"></span>
              )}
            </button>
          );
        })}
      </div>
      
      {/* Question status summary */}
      <div className="mt-4 grid grid-cols-2 gap-3">
        <div className="bg-gray-50 p-2 rounded-md flex flex-col items-center">
          <div className="flex items-center mb-1">
            <div className="w-3 h-3 rounded-full bg-gray-200 border border-gray-300 mr-1"></div>
            <span className="text-xs text-gray-600">Not Visited</span>
          </div>
          <span className="text-lg font-semibold text-gray-700">{statusCounts.notVisited}</span>
        </div>
        
        <div className="bg-gray-50 p-2 rounded-md flex flex-col items-center">
          <div className="flex items-center mb-1">
            <div className="w-3 h-3 rounded-full bg-red-100 border border-red-300 mr-1"></div>
            <span className="text-xs text-gray-600">Not Answered</span>
          </div>
          <span className="text-lg font-semibold text-red-700">{statusCounts.notAnswered}</span>
        </div>
        
        <div className="bg-gray-50 p-2 rounded-md flex flex-col items-center">
          <div className="flex items-center mb-1">
            <div className="w-3 h-3 rounded-full bg-green-100 border border-green-300 mr-1"></div>
            <span className="text-xs text-gray-600">Answered</span>
          </div>
          <span className="text-lg font-semibold text-green-700">{statusCounts.answered}</span>
        </div>
        
        <div className="bg-gray-50 p-2 rounded-md flex flex-col items-center">
          <div className="flex items-center mb-1">
            <div className="w-3 h-3 rounded-full bg-purple-100 border border-purple-300 mr-1"></div>
            <span className="text-xs text-gray-600">For Review</span>
          </div>
          <span className="text-lg font-semibold text-purple-700">{statusCounts.review}</span>
        </div>
      </div>
    </div>
  );
});

QuestionSelection.displayName = "QuestionSelection";

export default QuestionSelection;