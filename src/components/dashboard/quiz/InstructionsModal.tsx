// import React, { useEffect } from 'react';
// import { XMarkIcon, QuestionMarkCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
// import Button from '@/components/ui/Button';
// import Link from 'next/link';

// interface InstructionsModalProps {
//   okclickPath?: any;
//   onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
// }

// // const tempModalName = "";
// const tempModalText = [
//   "MCQs: Provide a clear question stem with 3-5 options, including one correct answer. Instruct test-takers to choose the best option.",
//   "Coding: Describe the problem, input/output format, and submission method. Specify scoring criteria.",
//   "Subjective: Offer a clear prompt, response format, and scoring rubric.",
//   "True/False: Present clear statements and mark true/false options for selection."
// ];

// export default function InstructionsModal({ onClose, okclickPath }: InstructionsModalProps) {
//   useEffect(() => {

//   }, [onClose]);

//   return (
//     <div className="modal flex justify-center items-center fixed inset-0 bg-black bg-opacity-40 overflow-auto z-10">
//       <div className="flex flex-col gap-5 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md w-[400px] lg:w-[800px] h-[700px] lg:h-[450px]">
//         <button className="flex justify-end" onClick={onClose} data-testid="close-button">
//           <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
//         </button>

//         <div className="flex flex-col gap-2 justify-center items-center text-center m-4">
//           <QuestionMarkCircleIcon className="h-[50px] w-[50px] text-yellow-300" />
//           <div className='w-full flex justify-start'>
//           </div>
//           <div className='w-full flex justify-start flex-col'>
//             {tempModalText.map((item, index) => {
//               if (index === 0) {
//                 <br />
//                 return item; // Keeping the first item unchanged
//               } else {
//                 return (

//                   <React.Fragment key={index}>
//                     <br />
//                     • {item}
//                     <br /> {/* Adding a new line after each point */}
//                   </React.Fragment>
//                 );
//               }
//             })}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }

import React, { useEffect } from 'react';
import { XMarkIcon, AcademicCapIcon, DocumentCheckIcon, CodeBracketIcon, CheckIcon } from '@heroicons/react/24/outline';

interface InstructionsModalProps {
  okclickPath?: string;
  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}

export default function InstructionsModal({ onClose, okclickPath }: InstructionsModalProps) {
  // Instructions categorized by question type
  const instructions = [
    {
      type: "MCQs",
      icon: <DocumentCheckIcon className="h-5 w-5 text-blue-500" />,
      description: "Provide a clear question stem with 3-5 options, including one correct answer. Instruct test-takers to choose the best option.",
      color: "blue"
    },
    {
      type: "Coding",
      icon: <CodeBracketIcon className="h-5 w-5 text-purple-500" />,
      description: "Describe the problem, input/output format, and submission method. Specify scoring criteria.",
      color: "purple"
    },
    {
      type: "Subjective",
      icon: <AcademicCapIcon className="h-5 w-5 text-amber-500" />,
      description: "Offer a clear prompt, response format, and scoring rubric.",
      color: "amber"
    },
    {
      type: "True/False",
      icon: <CheckIcon className="h-5 w-5 text-green-500" />,
      description: "Present clear statements and mark true/false options for selection.",
      color: "green"
    }
  ];

  // General exam guidelines
  const generalGuidelines = [
    "Read all questions carefully before answering",
    "You cannot return to a question once you've moved to the next section",
    "Your responses are automatically saved when you navigate to a new question",
    "You can mark questions for review and come back to them later",
    "The timer in the top-right shows your remaining time",
    "The exam will be automatically submitted when time expires"
  ];

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white flex items-center">
            <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Quiz Instructions
          </h2>
          <button 
            onClick={onClose}
            className="text-white hover:text-gray-200 focus:outline-none transition-colors"
            data-testid="close-button"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        
        {/* Content */}
        <div className="px-6 py-4 max-h-[70vh] overflow-y-auto">
          {/* General guidelines */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">General Guidelines</h3>
            <ul className="space-y-2">
              {generalGuidelines.map((guideline, index) => (
                <li key={index} className="flex items-start">
                  <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-blue-100 text-blue-600 mr-2 mt-0.5 flex-shrink-0">
                    <span className="text-xs font-bold">{index + 1}</span>
                  </span>
                  <span className="text-gray-700">{guideline}</span>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Question types */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Question Types</h3>
            <div className="space-y-4">
              {instructions.map((instruction, index) => (
                <div 
                  key={index} 
                  className={`rounded-lg border border-${instruction.color}-200 bg-${instruction.color}-50 p-4`}
                >
                  <div className="flex items-center mb-2">
                    {instruction.icon}
                    <h4 className={`ml-2 text-${instruction.color}-700 font-medium`}>{instruction.type}</h4>
                  </div>
                  <p className="text-gray-700">{instruction.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex justify-end border-t">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Got it
          </button>
        </div>
      </div>
    </div>
  );
}