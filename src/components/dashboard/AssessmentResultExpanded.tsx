import React from "react";
import { UserAssessmentAttempt } from "@/types/LMSTypes";
import { ClockIcon, CalendarDaysIcon } from "@heroicons/react/24/outline";

type PropType = {
  AttemptsData: UserAssessmentAttempt | undefined;
}

const AssessmentResultExpanded: React.FC<PropType> = ({ AttemptsData }) => {
  console.log("Upcoming attempt data", AttemptsData)

  
  // Helper function to format time
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    seconds = seconds % 60;
    if (minutes > 0) {
      return `${minutes} mins ${seconds < 10 ? '0' : ''}${seconds} sec`;
    } else {
      return `${seconds} sec`;
    }
  };


  return (
    <div className="w-full h-[90vh] flex flex-col px-4 overflow-auto  ">
      {AttemptsData?.length > 0 ? (
        AttemptsData?.map((attempt) => (
          <div
            key={attempt.user_assessment_attempt_id}
            className="flex flex-col-reverse md:flex-row justify-start my-3 w-full  rounded-xl bg-white p-4 h-full md:h-[160px] "
          >
            <div className=" flex  flex-col justify-between w-full ">
              <div>
                <p className="text-xs text-textColor">Assessment:</p>
                <h5 className="text-lg font-medium leading-tight text-textPrimary my-1">
                  {attempt.assessment_name}
                </h5>
              </div>
              <div className="flex flex-col ">
                <p className="text-xs text-textColor">Assessment Scheduled:</p>
                <div className="flex flex-col ">
                  <div className="flex  w-full  my-1">
                    <CalendarDaysIcon className="h-6 w-6 mr-2 " />
                    Start date :
                    <span className="text-md px-1">
                      {new Date(attempt.start_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex  w-full  my-1">
                    <CalendarDaysIcon className="h-6 w-6  mr-2" />
                    End date :
                    <span className="text-md px-1">
                      {new Date(attempt.end_date).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className=" flex  flex-col justify-between w-full pl-2">
              <div>
                <p className="text-xs text-textColor">Evaluation:</p>
                <h5 className="text-lg font-medium leading-tight text-textPrimary my-1">
                  {attempt.attempt_evaluation}
                </h5>
              </div>
              <div>
                <p className="text-xs text-textColor">Total Marks:</p>
                <h5 className="text-lg font-medium leading-tight text-textPrimary my-1">
                  {attempt.total_marks}
                </h5>
              </div>
              <div>
                <p className="text-xs text-textColor">
                  Attempt Number/Total Attempt Number:
                </p>
                <h5 className="text-lg font-medium leading-tight text-textPrimary my-1">
                  {attempt.attempt_number}/{attempt.max_attempts}
                </h5>
              </div>
            </div>
            <div className=" flex  flex-col justify-between w-full pl-2">
              <div className="flex flex-col ">
                <div className="flex flex-col ">
                  <p className="text-xs text-textColor">
                    Attempt Time/Total Time Allowed:
                  </p>
                  <h5 className="text-lg font-medium leading-tight text-textPrimary my-1">
                  {formatTime(attempt.attempt_total_time)} / {(attempt.total_time_allowed/60)} mins
                  </h5>
                </div>
              </div>
              <div className="flex flex-col ">
                <div className="flex flex-col">
                  <p className="text-xs text-textColor">Attempt Status:</p>
                  <h5 className="text-lg font-medium leading-tight text-textPrimary my-1">
                    {attempt.attempt_status === 0 ? "NOT_STARTED" :
                      attempt.attempt_status === 1 ? "STARTED" :
                        attempt.attempt_status === 2 ? "SUBMITTED" :
                          attempt.attempt_status === 3 ? "UNDER_EVALUATION" :
                            attempt.attempt_status === 4 ? "EVALUATED" :
                              attempt.attempt_status === 5 ? "TIME_OUT" :
                                attempt.attempt_status === 99 ? "EXPIRED" :
                                  (Array.isArray(attempt.attempt_status) && attempt.attempt_status.includes(1)) ? "SUBMIT_ABLE" :
                                    (Array.isArray(attempt.attempt_status) && attempt.attempt_status.includes(2)) ? "EVALUATE_ABLE" : null
                    }
                  </h5>
                </div>

              </div>
            </div>
          </div>
        ))
      ) : (
        <div className="flex flex-col m-5  mx-auto w-full max-w-2xl rounded-xl bg-white p-4">
          <span className="flex text-md font-medium justify-center items-center">
            Currently, there are no assessment results for you.{" "}
          </span>
        </div>
      )}
    </div>
  );
};

export default AssessmentResultExpanded;
