"use client";
import { ChangeEvent, useEffect, useState } from "react";
import Button from "@/components/ui/Button";
import Link from "next/link";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface Country {
    name: string;
    code: string;
  }

  
const PersonalInfo = () => {
    const [selectedDate, setselectedDate] = useState(null);
    const [countries, setCountries] = useState<Country[]>([]);
    const [selectedCountry, setSelectedCountry] = useState<string>("");

    useEffect(() => {
        fetch("/countries.json")
            .then((response) => response.json())
            .then((data) => setCountries(data))
            .catch((error) => console.error("Error fetching countries:", error));
        }, []);
        
        const handleCountryChange = (event: ChangeEvent<HTMLSelectElement>) => {
            setSelectedCountry(event.target.value);
        };

    return (
        <div className="w-full lg:max-w-4xl md:max-w-2xl sm:max-w-xl px-4">
            <div className="flex w-full justify-between px-1">
                <div>
                <h2 className="text-2xl font-semibold leading-7 text-gray-900">
                    Personal Information:
                </h2>
                </div>
                <Link
                className="h-10 flex justify-center items-center"
                href="/dashboard/assesments"
                >
                <Button btnName="Back Home" />
                </Link>
            </div>

            <div className="col-span-full mt-5">
                <label
                htmlFor="bio"
                className="block text-sm font-medium leading-6 text-gray-900"
                >
                Bio:
                </label>
                <div className="mt-2">
                <textarea
                    id="bio"
                    name="bio"
                    rows={3}
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    defaultValue={""}
                />
                </div>
                <p className="mt-3 text-sm leading-6 text-gray-600">
                Write a few sentences about your Job Profile.
                </p>
            </div>

            <div className="mt-10 grid gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-3 flex flex-col gap-2">
                <label
                    htmlFor="dateofbirth"
                    className="block text-sm font-medium leading-6 text-gray-900"
                >
                    Date Of Birth:
                </label>
                <DatePicker
                    id = "dateofbirth"
                    className="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    selected={selectedDate}
                    onChange={(date) => setselectedDate(date)}
                    dateFormat="dd/MM/yyyy"
                    placeholderText="   Select your DOB."
                    showYearDropdown
                    showIcon
                    required
                />
                </div>

                <div className="sm:col-span-3">
                <label
                    htmlFor="phone"
                    className="block text-sm font-medium leading-6 text-gray-900"
                >
                    Phone:
                </label>
                <div className="mt-2">
                    <input
                    id = "phone"
                    required
                    className="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                </div>
                </div>

                <div className="sm:col-span-3">
                <label
                    htmlFor="gender"
                    className="block text-sm font-medium leading-6 text-gray-900"
                >
                    Gender:
                </label>
                <div className="mt-2 sm:col-span-5">
                    <select
                    id="gender"
                    name="gender"
                    autoComplete="gender-name"
                    required
                    className="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    >
                    <option value="" disabled selected>
                        Select your Gender
                    </option>
                    <option>Male</option>
                    <option>Female</option>
                    <option>Others</option>
                    </select>
                </div>
                </div>

                <div className="sm:col-span-3">
                <label
                    htmlFor="country"
                    className="block text-sm font-medium leading-6 text-gray-900"
                >
                    Country:
                </label>
                <div className="mt-2 col-span-2">
                    <select
                    className="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    id="country"
                    required
                    value={selectedCountry}
                    onChange={handleCountryChange}
                    >
                    <option value="" disabled>
                        Select your country
                    </option>
                    {countries.map((country) => (
                        <option key={country.code} value={country.code}>
                        {country.name}
                        </option>
                    ))}
                    </select>
                </div>
                </div>

                <div className="col-span-full">
                <label
                    htmlFor="street-address"
                    className="block text-sm font-medium leading-6 text-gray-900"
                >
                    Permanent Address:
                </label>
                <div className="mt-2">
                    <input
                    type="text"
                    name="street-address"
                    id="street-address"
                    autoComplete="street-address"
                    required
                    className="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                </div>
                </div>

                <div className="sm:col-span-2">
                <label
                    htmlFor="city"
                    className="block text-sm font-medium leading-6 text-gray-900"
                >
                    City:
                </label>
                <div className="mt-2">
                    <input
                    type="text"
                    name="city"
                    id="city"
                    autoComplete="address-level2"
                    required
                    className="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                </div>
                </div>

                <div className="sm:col-span-2">
                <label
                    htmlFor="region"
                    className="block text-sm font-medium leading-6 text-gray-900"
                >
                    State / Province:
                </label>
                <div className="mt-2">
                    <input
                    type="text"
                    name="region"
                    id="region"
                    required
                    autoComplete="address-level1"
                    className="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                </div>
                </div>

                <div className="sm:col-span-2">
                <label
                    htmlFor="postal-code"
                    className="block text-sm font-medium leading-6 text-gray-900"
                >
                    ZIP / Postal code:
                </label>
                <div className="mt-2">
                    <input
                    type="text"
                    name="postal-code"
                    id="postal-code"
                    autoComplete="postal-code"
                    required
                    className="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                </div>
                </div>
            </div>

            <div className="mt-6 flex justify-end gap-x-6 p-2">
                <button
                type="button"
                className="text-sm font-semibold leading-6 text-gray-900"
                >
                Cancel
                </button>
                <button type="submit" className="flex gap-2 py-2 px-4 bg-textColor text-white rounded-md text-xs md:text-sm lg:text-base">
                Save Changes
                </button>
            </div>
            </div>

        );
    };
    
    export default PersonalInfo;