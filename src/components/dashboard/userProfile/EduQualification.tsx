"use client";
import { ChangeEvent, useEffect, useState } from "react";
import Button from "@/components/ui/Button";
import Link from "next/link";


const EduQualification = () => {
    const [courses, setCourses] = useState({});
    const [selectedCourse, setSelectedCourse] = useState('');
    

    useEffect(() => {
        // Fetch the JSON data
        fetch('/courses.json')
            .then((response) => response.json())
            .then((data) => setCourses(data))
            .catch((error) => console.error('Error fetching courses:', error));
        }, []);
        
        const handleCourseChange = (event: ChangeEvent<HTMLSelectElement>) => {
            setSelectedCourse(event.target.value);
            console.log('Selected value:', event.target.value);
            console.log('Selected Course:', event.target.value);
        };
        
    return (
        <div className="w-full lg:max-w-4xl md:max-w-2xl sm:max-w-xl px-4">
            <div className="flex w-full justify-between px-1">
                <div>
                    <h2 className="text-2xl font-semibold leading-7 text-gray-900">
                        Educational Qualification:
                    </h2>
                    <p className="mt-1 text-sm leading-6 text-gray-600">
                        Fill your highest qualification here.
                    </p>
                </div>
                <Link className="h-10 flex justify-center items-center" href="/dashboard/assesments">
                    <Button btnName="Back Home" />
                </Link>
            </div>

            <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-2 lg:grid-cols-3">
                <div>
                    <label htmlFor="courseDropdown" className="block text-sm font-medium leading-6 text-gray-900">
                        Degree / Course:
                    </label>
                    <div className="mt-2">
                        <select
                            id="courseDropdown"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            value={selectedCourse}
                            onChange={handleCourseChange}
                            data-testid="courseDropdown"
                        >
                            <option value="" disabled>Select your course</option>
                            {Object.keys(courses).map((category) => (
                                <optgroup key={category} label={category}>
                                    {Object.entries(courses[category]).map(([courseName, courseShortForm]) => (
                                        <option key={courseShortForm} value={courseShortForm}>
                                            {courseName} ({courseShortForm})
                                        </option>
                                    ))}
                                </optgroup>
                            ))}
                        </select>
                    </div>
                </div>

                <div>
                    <label htmlFor="university-name" className="block text-sm font-medium leading-6 text-gray-900">
                        University Name:
                    </label>
                    <div className="mt-2">
                        <input
                            id="university-name"
                            name="university-name"
                            autoComplete="university-name"
                            required
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div>
                    <label htmlFor="college-name" className="block text-sm font-medium leading-6 text-gray-900">
                        College Name:
                    </label>
                    <div className="mt-2">
                        <input
                            id="college-name"
                            name="college-name"
                            autoComplete="college-name"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div>
                    <label htmlFor="specialisation-name" className="block text-sm font-medium leading-6 text-gray-900">
                        Specialisation:
                    </label>
                    <div className="mt-2">
                        <input
                            id="specialisation-name"
                            name="specialisation-name"
                            autoComplete="specialisation-name"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div>
                    <label htmlFor="year" className="block text-sm font-medium leading-6 text-gray-900">
                        Year of Passing:
                    </label>
                    <div className="mt-2">
                        <input
                            id="year"
                            name="year"
                            autoComplete="year"
                            required
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div>
                    <label htmlFor="cgpa" className="block text-sm font-medium leading-6 text-gray-900">
                        CGPA or Percentage:
                    </label>
                    <div className="mt-2">
                        <input
                            id="cgpa"
                            name="cgpa"
                            autoComplete="cgpa"
                            required
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>
            </div>

            <div className="mt-6 flex justify-end gap-x-6 p-2">
                <button type="button" className="text-sm font-semibold leading-6 text-gray-900">
                    Cancel
                </button>
                <button type="submit" className="flex gap-2 py-2 px-4 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]">
                    Save Changes
                </button>
            </div>
        </div>

        );
    };
    
    export default EduQualification;