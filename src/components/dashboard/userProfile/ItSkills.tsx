"use client"
import Button from "@/components/ui/Button";
import <PERSON> from "next/link";

const ItSkills = () => {
    return (
        <div className="w-full lg:max-w-4xl md:max-w-2xl sm:max-w-xl px-4">
            <div className="flex w-full justify-between px-1">
                <div>
                <h2 className="text-xl font-semibold leading-7 text-gray-900">IT Skills:</h2>
                <p className="mt-1 text-sm leading-6 text-gray-600">Add Your Professional IT Skills Here.</p>
                </div>
                <Link className="h-10 flex justify-center items-center" href="/dashboard/assessments">
                <Button btnName="Back Home" />
                </Link>
            </div>

            <div className="col-span-full mt-5">
                <label htmlFor="about" className="block text-sm font-medium leading-6 text-gray-900">
                IT Skills:
                </label>
                <div className="mt-2">
                <textarea
                    id="about"
                    required
                    name="about"
                    rows={3}
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    defaultValue={""}
                />
                </div>
            </div>

            <div className="mt-6 flex justify-end gap-x-6 p-2">
                <button type="button" className="text-sm font-semibold leading-6 text-gray-900">
                Cancel
                </button>
                <button type="submit" className="flex gap-2 py-2 px-4 bg-textColor text-white rounded-md text-xs sm:text-sm lg:text-base">
                Save Changes
                </button>
            </div>
            </div>

        );
    };
    
    export default ItSkills;