"use client";
import Button from "@/components/ui/Button";
import Link from "next/link";


const SocialLinks = () => {
    return (
        <div className="w-full lg:max-w-4xl md:max-w-2xl sm:max-w-xl px-4">
            <div className="flex w-full justify-between px-1">
                <div>
                    <h2 className="text-2xl font-semibold leading-7 text-gray-900">
                        Social Links:
                    </h2>
                    <p className="mt-1 text-sm leading-6 text-gray-600">
                        You can also connect your social profiles with us to stay updated anywhere and everywhere.
                    </p>
                </div>
                <Link className="h-10 flex justify-center items-center" href="/dashboard/assesments">
                    <Button btnName="Back Home" />
                </Link >
            </div>

            <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-3">
                    <label htmlFor="github" className="block text-sm font-medium leading-6 text-gray-900">
                        Github
                    </label>
                    <div className="mt-2">
                        <input
                            type="text"
                            name="github"
                            id="github"
                            autoComplete="github"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>
                <div className="sm:col-span-3">
                    <label htmlFor="linkedin" className="block text-sm font-medium leading-6 text-gray-900">
                        LinkedIn
                    </label>
                    <div className="mt-2">
                        <input
                            type="text"
                            name="linkedin"
                            id="linkedin"
                            autoComplete="linkedin"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>
                <div className="sm:col-span-3">
                    <label htmlFor="twitter" className="block text-sm font-medium leading-6 text-gray-900">
                        Twitter
                    </label>
                    <div className="mt-2">
                        <input
                            type="text"
                            name="twitter"
                            id="twitter"
                            autoComplete="twitter"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>
                <div className="sm:col-span-3">
                    <label htmlFor="naukri" className="block text-sm font-medium leading-6 text-gray-900">
                        Naukri.com
                    </label>
                    <div className="mt-2">
                        <input
                            type="text"
                            name="naukri"
                            id="naukri"
                            autoComplete="naukri"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>
            </div>
            <div className="mt-6 flex justify-end gap-x-6 p-2">
                <button type="button" className="text-sm font-semibold leading-6 text-gray-900">
                    Cancel
                </button>
                <button type="submit" className="flex gap-2 py-2 px-4 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]">
                    Save Changes
                </button>
            </div>
        </div>

        );
    };
    
    export default SocialLinks;