"use client";
import <PERSON>ton from "@/components/ui/Button";
import Link from "next/link";
import { PhotoIcon, UserCircleIcon } from "@heroicons/react/24/outline";
import { useState } from "react";
import { getUser } from "@/api/user.localStorage";


const GeneralInfo = () => {
    const user = getUser();
    const [selectedFile, setSelectedFile] = useState(null);

    const handlephotofile = (event) => {
        setSelectedFile(event.target.files[0]);

        const formData = new FormData();
        formData.append('file', event.target.files[0]);
        const blob = new Blob([event.target.files[0]]);

        console.log("photo uploaded", blob);

    };

    const [email, setEmail] = useState("");
    const [emailError, setEmailError] = useState("");

    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleEmailChange = (e) => {
        const newEmail = e.target.value;
        setEmail(newEmail);

        if (!validateEmail(newEmail)) {
        setEmailError("Please enter a valid email address.");
        } else {
        setEmailError("");
        }
        console.log("Email is validated");
    };


    return (
        <div className="w-full  lg:max-w-4xl md:max-w-2xl sm:max-w-xl px-4">
            <div className="flex w-full justify-between px-1">
                <div className="mt-1">
                    <h2 className="text-3xl font-bold leading-7 text-gray-900 underline underline-offset-4">
                        User Profile Info
                    </h2>
                </div>

                <Link
                    className="h-10 flex justify-center items-center"
                    href="/dashboard/assesments"
                >
                    <Button btnName="Back Home" />
                </Link>
            </div>

            <div className="col-span-full mt-8">
                <div className="mt-2 flex items-center gap-x-3">
                    <UserCircleIcon
                        className="h-14 w-14 text-gray-500"
                        aria-hidden="true"
                    />
                    <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                    >
                        <span>Upload Photo</span>
                        <input
                            id="file-upload"
                            name="file-upload"
                            type="file"
                            className="sr-only"
                            onChange={(e) => handlephotofile(e)}
                        />
                    </label>
                </div>
            </div>

            <div className="mt-8 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-3">
                    <label
                        htmlFor="username"
                        className="block text-sm font-medium leading-6 text-gray-900"
                    >
                        Username:
                    </label>
                    <div className="mt-2">
                        <div className="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 px-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                            <input
                                type="text"
                                name="username"
                                id="username"
                                autoComplete="username"
                                className="block flex-1 border-0 bg-transparent py-1.5 pl-1 text-gray-500 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                                placeholder="janesmith"
                               value={user?.user_full_name}
                            />
                        </div>
                    </div>
                </div>

                <div className="sm:col-span-6">
                    <label
                        htmlFor="email"
                        className="block text-sm font-medium leading-6 text-gray-900"
                    >
                        Email address:
                    </label>
                    <div className="mt-2">
                        <input
                            type="email"
                            id="email"
                            value={user?.email}
                            onChange={handleEmailChange}
                            disabled={true}
                            className={`block w-full rounded-md border-0 py-1.5 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 px-2`}
                        />
                        {emailError && (<p className="text-red-500 text-sm mt-1">{emailError}</p>)}
                    </div>
                </div>

                <div className="sm:col-span-3">
                    <label
                        htmlFor="first-name"
                        className="block text-sm font-medium leading-6 text-gray-900"
                    >
                        First Name:
                    </label>
                    <div className="mt-2">
                        <input
                            type="text"
                            name="first-name"
                            id="first-name"
                            autoComplete="given-name"
                            required
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div className="sm:col-span-3">
                    <label
                        htmlFor="last-name"
                        className="block text-sm font-medium leading-6 text-gray-900"
                    >
                        Last Name:
                    </label>
                    <div className="mt-2">
                        <input
                            type="text"
                            name="last-name"
                            id="last-name"
                            autoComplete="family-name"
                            required
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div className="sm:col-span-3">
                    <label
                        htmlFor="jobprofile"
                        className="block text-sm font-medium leading-6 text-gray-900"
                    >
                        Job Profile:
                    </label>
                    <div className="mt-2">
                        <input
                            id="jobprofile"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            required
                        />
                    </div>
                </div>

                <div className="sm:col-span-3">
                    <label
                        htmlFor="company-name"
                        className="block text-sm font-medium leading-6 text-gray-900"
                    >
                        Company Name:
                    </label>
                    <div className="mt-2">
                        <input
                            id = "company-name"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            required
                        />
                    </div>
                </div>

                <div className="sm:col-span-3">
                    <label
                        htmlFor="experience"
                        className="block text-sm font-medium leading-6 text-gray-900"
                    >
                        Work Experience (in yrs):
                    </label>
                    <div className="mt-2">
                        <input
                            type="number"
                            id="experience"
                            aria-describedby="helper-text-explanation"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 px-2"
                            placeholder="0"
                            required
                        />
                    </div>
                </div>

                <div className="col-span-full">
                    <label
                        htmlFor="cover-photo"
                        className="block text-sm font-medium leading-6 text-gray-900"
                    >
                        Submit Resume:
                    </label>
                    <div className="mt-2 flex justify-center rounded-lg border border-dashed border-2 border-gray-900/25 px-6 py-10">
                        <div className="text-center">
                            <PhotoIcon
                                className="mx-auto h-12 w-12 text-gray-300"
                                aria-hidden="true"
                            />
                            <div className="mt-4 flex text-sm leading-6 text-gray-600">
                                <label
                                    htmlFor="resume-upload"
                                    className="relative cursor-pointer rounded-md bg-white font-semibold text-indigo-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 hover:text-indigo-500"
                                >
                                    <span>Upload a file</span>
                                    <input
                                        id="resume-upload"
                                        name="resume-upload"
                                        type="file"
                                        className="sr-only"
                                    />
                                </label>
                                <p className="pl-1">or drag and drop</p>
                            </div>
                            <p className="text-xs leading-5 text-gray-600">
                                doc, docx, and PDF up to 10MB
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div className="mt-6 flex justify-end gap-x-6 p-2">
                <button
                    type="button"
                    className="text-sm font-semibold leading-6 text-gray-900"
                >
                    Cancel
                </button>
                <button
                    type="submit"
                    className="flex gap-2 py-2 px-4 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                >
                    Save Changes
                </button>
            </div>

            <div className="border-b border-gray-900/10 pb-10"></div>
        </div>

            
        );
    };
    
    export default GeneralInfo;