import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, BookOpenIcon, CheckCircleIcon, ClockIcon } from '@heroicons/react/24/outline';

interface ModuleData {
  module_id: number;
  sequence: number;
  module_name: string;
  module_headline: string;
  module_description: string;
  group_name: string;
  completed: "completed" | "in_Progress" | "not_completed";
}

interface LearningPathProps {
  moduleData: ModuleData;
}

/**
 * LearningPath component shows a single learning module with expandable details
 */
const LearningPath: React.FC<LearningPathProps> = ({ moduleData }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Toggle expansion state
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Get status icon based on completion state
  const getStatusIcon = () => {
    switch (moduleData.completed) {
      case 'completed':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'in_Progress':
        return <ClockIcon className="h-6 w-6 text-yellow-500" />;
      default:
        return <BookOpenIcon className="h-6 w-6 text-gray-400" />;
    }
  };

  // Get status text and color based on completion state
  const getStatusInfo = () => {
    switch (moduleData.completed) {
      case 'completed':
        return {
          text: 'Completed',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800'
        };
      case 'in_Progress':
        return {
          text: 'In Progress',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800'
        };
      default:
        return {
          text: 'Not Started',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-700'
        };
    }
  };

  const statusInfo = getStatusInfo();

  // Sample overview content for each module
  const getOverviewContent = () => {
    switch (moduleData.module_id) {
      case 94:
        return (
          <div className="space-y-3">
            <p className="text-gray-700">This introductory module will help you establish a strong foundation in programming concepts.</p>
            <ul className="list-disc pl-5 space-y-1 text-gray-600">
              <li>Understanding variables and data types</li>
              <li>Working with control structures (if/else, loops)</li>
              <li>Basic functions and parameters</li>
              <li>Introduction to algorithms</li>
            </ul>
            <p className="text-gray-700">Estimated completion time: 4-6 hours</p>
          </div>
        );
      case 95:
        return (
          <div className="space-y-3">
            <p className="text-gray-700">This advanced module covers complex algorithmic concepts for more efficient problem-solving.</p>
            <ul className="list-disc pl-5 space-y-1 text-gray-600">
              <li>Time and space complexity analysis</li>
              <li>Sorting algorithms (quicksort, mergesort, heapsort)</li>
              <li>Search algorithms (binary search, depth-first, breadth-first)</li>
              <li>Dynamic programming techniques</li>
            </ul>
            <p className="text-gray-700">Estimated completion time: 8-10 hours</p>
          </div>
        );
      default:
        return (
          <div className="space-y-3">
            <p className="text-gray-700">This module will help you develop essential skills in {moduleData.module_name.toLowerCase()}.</p>
            <ul className="list-disc pl-5 space-y-1 text-gray-600">
              <li>Key concepts and principles</li>
              <li>Practical application techniques</li>
              <li>Best practices and patterns</li>
              <li>Problem-solving strategies</li>
            </ul>
            <p className="text-gray-700">Estimated completion time: 5-8 hours</p>
          </div>
        );
    }
  };

  return (
    <div className="bg-white overflow-hidden transition-all duration-200 hover:bg-gray-50">
      <div className="px-3 py-4 sm:px-6">
        {/* Mobile View (Status and Sequence) */}
        <div className="flex items-center justify-between mb-3 sm:hidden">
          <div className="flex items-center">
            {getStatusIcon()}
            <span className={`ml-2 px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.textColor}`}>
              {statusInfo.text}
            </span>
          </div>
          <div className="flex items-center">
            <span className="text-sm text-gray-500">Sequence: {moduleData.sequence}</span>
          </div>
        </div>

        {/* Desktop View Header Row */}
        <div className="hidden sm:flex sm:items-start sm:justify-between">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              {getStatusIcon()}
              <span className={`ml-2 px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.textColor}`}>
                {statusInfo.text}
              </span>
              <span className="ml-2 text-sm text-gray-500">Sequence: {moduleData.sequence}</span>
            </div>
          </div>
        </div>
        
        {/* Content (Both Mobile and Desktop) */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900">{moduleData.module_name}</h3>
            <p className="mt-1 text-sm text-gray-600">{moduleData.module_headline}</p>
            <p className="mt-2 text-sm text-gray-700">{moduleData.module_description}</p>
          </div>
          
          {/* Action Buttons */}
          <div className="mt-4 sm:mt-0 sm:ml-4 sm:flex-shrink-0 flex flex-col sm:flex-row gap-2">
            <button
              onClick={toggleExpand}
              className="inline-flex items-center justify-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {isExpanded ? 'Hide Overview' : 'Overview'}
              {isExpanded ? (
                <ChevronUpIcon className="ml-1.5 h-4 w-4" />
              ) : (
                <ChevronDownIcon className="ml-1.5 h-4 w-4" />
              )}
            </button>
            
            <button className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              {moduleData.completed === 'completed' ? 'Review' : 'Start'}
            </button>
          </div>
        </div>
        
        {/* Expandable Overview Section */}
        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Module Overview</h4>
            {getOverviewContent()}
          </div>
        )}
      </div>
    </div>
  );
};

export default LearningPath;