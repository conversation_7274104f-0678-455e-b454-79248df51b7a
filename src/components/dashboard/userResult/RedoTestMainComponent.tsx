

"use client";
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Heading from "@/components/ui/Heading";
import Link from "next/link";

import { CodeBracketIcon } from "@heroicons/react/24/outline";
import { useGetAllProgram } from "@/hook/dashboard/program/useGetAllProgram";
import Nothing from "@/components/ui/Nothing";

export default function RedoTestMainComponent() {
  const [currentPage, setCurrentPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const { data: programData, isLoading, isError } = useGetAllProgram();

  const colors = ["bg-blue-50", "bg-green-50", "bg-yellow-50", "bg-red-50"];

  useEffect(() => {
    if (!isLoading && !isError) {
      console.log("program data:", programData);
    }
  }, [programData, isLoading, isError]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error fetching modules</div>;
  }

  return (
    <main className="w-full flex h-full bg-white flex-col items-center justify-between p-2 overflow-auto">
      
        <div className="p-3">
          <Heading pgHeading="Program List" />
        </div>
        <div className="flex flex-col gap-2 justify-center w-full rounded-2xl p-1 h-full overflow-y-auto">
          {(!programData || programData.length === 0) ? (
            <div className="w-full flex justify-center items-center h-full">
              <Nothing
                title="No Program Available"
                para="There are currently no Program to display.
                          Please check back later."
              />
            </div>
          ) : (
            programData.map((data, index) => (
              <div
                key={data.group_id}
                className="flex  w-full justify-between gap-3 rounded-lg border border-[#A9A9A9] px-2 py-1"
              >
                <div className="flex w-full gap-1 justify-start items-center">
                <div className={`h-8 rounded-full flex justify-center items-center p-2 ${colors[index % colors.length]}`}>
                  <CodeBracketIcon className="w-5 h-5" />
                </div>

                <div className="flex w-full text-left items-center">
                  <h1 className=" text-black text-md text-center">{data.group_name}</h1>
                </div>
                </div>
                <div className=" w-full gap-2 flex justify-end items-center  ">
                  <Link href={`result/${data.group_id}`} >
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="self-end border-1   border-textSecondary text-textSecondary  p-2 rounded text-sm w-90 text-right"
                    >
                      {"Let's Start"}
                    </motion.button>
                  </Link>
                </div>
              </div>
            ))
          )}
        </div>
     
    </main>
  );
}

