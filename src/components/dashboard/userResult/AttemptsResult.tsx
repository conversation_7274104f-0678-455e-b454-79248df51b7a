import React from 'react'
import { CalendarDaysIcon } from '@heroicons/react/24/outline'
import { ClockIcon } from '@heroicons/react/20/solid'
const AttemptsResult = ({AttemptData}) => {
  return (
    <div className="w-full flex  ">
      {AttemptData?.length > 0 ? (
        AttemptData?.map((attempt) => (
          <div
            key={attempt.user_assessment_attempt_id}
            className="flex justify-start mx-auto  my-3 w-full  rounded-xl bg-white p-4 h-[180px]"

          >
            <div className=" flex  flex-col justify-between w-full pl-2">
              <div className="flex justify-between">
                <div>
                  <p className="text-xs text-textColor">User Id:</p>
                  <h5 className="text-lg font-medium leading-tight text-textPrimary">
                    {console.log("abc: ",attempt.user_id)} 37
                  </h5>
                </div>
                <div>

                  <p className="text-xs text-textColor">Assessment:</p>

                  <h5 className="text-lg font-medium leading-tight text-textPrimary">
                    {attempt.assessment_name}
                  </h5>
                </div>
                <div>
                  <p className="text-xs text-textColor">Attempt:</p>
                  <h5 className="text-lg font-medium leading-tight text-textPrimary">
                    {attempt.attempt_number}
                  </h5>
                </div>
                <div>
                  <p className="text-xs text-textColor">Total Attempt:</p>
                  <h5 className="text-lg font-medium leading-tight text-textPrimary">
                    {attempt.attempt_total}
                  </h5>
                </div>
              </div>
              <div className="flex flex-col">

                <p className="text-xs text-textColor">
                  Assessment Was Scheduled:
                </p>
                <div className="flex w-full justify-between my-2">
                  <div className="flex">
                    <CalendarDaysIcon className="h-6 w-6 mr-2 " />
                    Start date :
                    <span className="text-md px-1">
                      {new Date(attempt.start_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex ">
                    <ClockIcon color="#475569" className="h-6 w-6  mr-2" />
                    End date :
                    <span className="text-md px-1">
                      {new Date(attempt.start_date).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))
      ) : (
        <div className="flex flex-col mx-auto w-full max-w-2xl rounded-xl bg-white p-4">
          <span className="text-md font-medium">
            {" "}
            Currently, there are no attempt results for you.{" "}
          </span>
        </div>
      )}
    </div>
  )
}

export default AttemptsResult