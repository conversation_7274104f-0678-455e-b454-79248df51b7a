import React from 'react';
import { <PERSON>riefcaseIcon, ArrowRightIcon } from "@heroicons/react/24/outline";

// Define the Job type for the job prop
interface Job {
  job_title_id: number;
  job_title: string;
  job_description?: string;
}

// Define the props for the JobCard component
interface JobCardProps {
  job: Job;
  index: number;
  colors: string[];
  selected: boolean;
  onClick?: (job_title_id: number) => void;
  onSectorSelect: () => void;
  onViewDetails: () => void;
  hasSectorsSelected: boolean;
  // Updated props for sector and domain selection
  selectedSectorNames?: string[];
  selectedDomainsByTitle?: {[sectorName: string]: string[]};
}

/**
 * JobCard component displays job information in a card format with sector and domain selection
 */
const JobCard: React.FC<JobCardProps> = ({ 
  job, 
  index, 
  colors, 
  selected, 
  onClick, 
  onSectorSelect,
  onViewDetails,
  hasSectorsSelected,
  selectedSectorNames = [],
  selectedDomainsByTitle = {}
}) => {
  const jobDescription = job.job_description || "No description available.";
  const colorClass = colors[index % colors.length];
  
  const handleCardClick = () => {
    if (onClick) {
      onClick(job.job_title_id);
    }
  };

  const handleSectorSelectClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSectorSelect();
  };

  const handleViewDetailsClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onViewDetails();
  };

  // Check if sectors have domains selected
  const hasSectorsWithDomains = () => {
    return Object.keys(selectedDomainsByTitle).length > 0;
  };

  const cardStyle = {
    height: 'auto',
    alignSelf: 'flex-start',
    display: 'block'
  };

  return (
    <div 
      style={cardStyle}
      className={`
        relative w-full bg-white rounded-lg overflow-hidden shadow-md
        ${selected ? "ring-2 ring-blue-500" : "hover:shadow-lg"}
        transition-all duration-200 cursor-pointer
      `}
      onClick={handleCardClick}
    >
      {/* Card header with colored background */}
      <div className={`${colorClass} h-32 w-full relative`}>
        <div className="absolute inset-0 flex items-center justify-center">
          <BriefcaseIcon className="h-16 w-16 text-gray-800/80" />
        </div>
        
        {/* Badge to show if sectors or domains are selected */}
        {hasSectorsSelected && (
          <div className="absolute top-3 left-3 bg-green-100 rounded-full px-2 py-0.5 text-xs font-medium text-green-800">
            {hasSectorsWithDomains() ? 'Sectors & Domains Selected' : 'Sectors Selected'}
          </div>
        )}
      </div>
      
      {/* Card content */}
      <div className="p-4">
        {/* Job title */}
        <h2 className="text-xl font-bold text-gray-800 mb-2">
          {job.job_title}
        </h2>
        
        {/* Job description*/}
        <div className="mb-4">
          <p className="text-gray-600 text-sm leading-relaxed">
            {jobDescription}
          </p>
        </div>
        
        {/* Selected sectors and domains display - only renders when content exists */}
        {hasSectorsSelected && selectedSectorNames.length > 0 && (
          <div className="mb-4">
            <p className="text-xs font-medium text-gray-500 mb-2">Selected Sectors & Domains:</p>
            
            {selectedSectorNames.map((sectorName, idx) => (
              <div key={idx} className="mb-2">
                <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700 border border-blue-100">
                  {sectorName}
                </span>
                
                {/* Show domains for this sector if any are selected */}
                {selectedDomainsByTitle[sectorName] && selectedDomainsByTitle[sectorName].length > 0 && (
                  <div className="pl-2 flex flex-wrap gap-1 mt-1">
                    {selectedDomainsByTitle[sectorName].map((domainName, domainIdx) => (
                      <span 
                        key={domainIdx} 
                        className="inline-flex items-center rounded-full bg-indigo-50 px-2 py-0.5 text-xs font-medium text-indigo-700 border border-indigo-100"
                      >
                        {domainName}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
        
        {/* Action buttons */}
        <div className={`${hasSectorsSelected ? "space-y-2" : ""}`}>
          <button
            onClick={handleSectorSelectClick}
            className={`w-full py-2 px-4 rounded-md font-medium text-sm flex items-center justify-center transition-colors duration-200 ${
              hasSectorsSelected 
                ? "bg-gray-100 text-gray-700 hover:bg-gray-200" 
                : "bg-blue-50 text-blue-600 hover:bg-blue-100"
            }`}
          >
            {hasSectorsSelected ? 'Modify Sectors & Domains' : 'Select Sectors & Domains'}
          </button>
          
          {/* View Skills button only shows when sectors are selected */}
          {hasSectorsSelected && (
            <button
              onClick={handleViewDetailsClick}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium text-sm rounded-md 
                flex items-center justify-center transition-colors duration-200 mt-2"
            >
              View Skills
              <ArrowRightIcon className="ml-2 h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default JobCard;