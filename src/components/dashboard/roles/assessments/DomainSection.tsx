import React from 'react';
import { AcademicCapIcon } from "@heroicons/react/24/outline";
import AssessmentCard from './AssessmentCard';

// Assessment interface
interface Assessment {
  id: number;
  title: string;
  description: string;
  sector: string;
  domain: string;
  duration: number;
  questionCount: number;
  difficulty: "beginner" | "intermediate" | "advanced";
  requiredSkills: string[];
}

interface DomainSectionProps {
  domain: string;
  assessments: Assessment[];
  onSelectAssessment: (id: number) => void;
}

const DomainSection: React.FC<DomainSectionProps> = ({ domain, assessments, onSelectAssessment }) => {
  return (
    <div className="p-6 border-t border-gray-200">
      <div className="flex items-center mb-6">
        <AcademicCapIcon className="h-5 w-5 text-blue-600 mr-2" />
        <h3 className="text-lg font-medium text-gray-800">{domain}</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {assessments.map((assessment) => (
          <AssessmentCard 
            key={assessment.id} 
            assessment={assessment} 
            onSelect={onSelectAssessment} 
          />
        ))}
      </div>
    </div>
  );
};

export default DomainSection;