import React from 'react';
import DomainSection from './DomainSection';

// Assessment interface
interface Assessment {
  id: number;
  title: string;
  description: string;
  sector: string;
  domain: string;
  duration: number;
  questionCount: number;
  difficulty: "beginner" | "intermediate" | "advanced";
  requiredSkills: string[];
}

interface SectorSectionProps {
  sector: string;
  domains: string[];
  assessments: Assessment[];
  onSelectAssessment: (id: number) => void;
}

const SectorSection: React.FC<SectorSectionProps> = ({ sector, domains, assessments, onSelectAssessment }) => {
  // Get assessments for a specific domain
  const getDomainAssessments = (domain: string) => {
    return assessments.filter(a => a.domain === domain);
  };
  
  // Get icon for sector
  const getSectorIcon = () => {
    switch (sector) {
      case "Technology":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      case "Healthcare":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
          </svg>
        );
      case "Finance":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        );
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Sector header */}
      <div className="bg-gray-50 border-b border-gray-200 p-6">
        <h2 className="text-xl font-medium text-gray-800 flex items-center">
          {getSectorIcon()}
          {sector}
        </h2>
      </div>
      
      {/* Domain groups within this sector */}
      <div>
        {domains.map(domain => (
          <DomainSection
            key={`${sector}-${domain}`}
            domain={domain}
            assessments={getDomainAssessments(domain)}
            onSelectAssessment={onSelectAssessment}
          />
        ))}
      </div>
    </div>
  );
};

export default SectorSection;