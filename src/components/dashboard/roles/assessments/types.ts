export interface Assessment {
    id: number;
    title: string;
    description: string;
    sector: string;
    domain: string;
    duration: number; // in minutes
    questionCount: number;
    difficulty: "beginner" | "intermediate" | "advanced";
    requiredSkills: string[];
  }
  
  export interface AssessmentFilters {
    sector: string;
    domain: string;
    difficulty: string;
    searchQuery: string;
  }