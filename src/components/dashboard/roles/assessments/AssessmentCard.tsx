import React from 'react';
import { 
  AcademicCapIcon, 
  ClockIcon, 
  DocumentTextIcon 
} from "@heroicons/react/24/outline";

// Assessment interface
interface Assessment {
  id: number;
  title: string;
  description: string;
  sector: string;
  domain: string;
  duration: number;
  questionCount: number;
  difficulty: "beginner" | "intermediate" | "advanced";
  requiredSkills: string[];
}

interface AssessmentCardProps {
  assessment: Assessment;
  onSelect: (id: number) => void;
}

// Get difficulty badge styling
const getDifficultyBadge = (difficulty: string) => {
  switch (difficulty) {
    case 'beginner':
      return 'bg-green-100 text-green-800 border border-green-200';
    case 'intermediate':
      return 'bg-blue-100 text-blue-800 border border-blue-200';
    case 'advanced':
      return 'bg-purple-100 text-purple-800 border border-purple-200';
    default:
      return 'bg-gray-100 text-gray-800 border border-gray-200';
  }
};

const AssessmentCard: React.FC<AssessmentCardProps> = ({ assessment, onSelect }) => {
  return (
    <div 
      className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 hover:shadow-md transition-all duration-200"
    >
      <div className="p-5">
        {/* Header with title and difficulty badge */}
        <div className="flex items-start justify-between mb-3">
          <h3 className="font-medium text-lg text-gray-900 flex-1">{assessment.title}</h3>
          <span className={`whitespace-nowrap ml-2 px-2.5 py-0.5 rounded-full text-xs font-medium ${getDifficultyBadge(assessment.difficulty)}`}>
            {assessment.difficulty}
          </span>
        </div>
        
        {/* Description */}
        <p className="text-gray-600 mb-4">{assessment.description}</p>
        
        {/* Assessment metadata */}
        <div className="flex gap-4 mb-4">
          <div className="flex items-center text-gray-600">
            <ClockIcon className="h-4 w-4 mr-2 text-gray-500" />
            <span>{assessment.duration} minutes</span>
          </div>
          <div className="flex items-center text-gray-600">
            <DocumentTextIcon className="h-4 w-4 mr-2 text-gray-500" />
            <span>{assessment.questionCount} questions</span>
          </div>
        </div>
        
        {/* Skills tags */}
        <div className="mb-5">
          <p className="text-xs font-medium text-gray-500 mb-2">SKILLS:</p>
          <div className="flex flex-wrap gap-2">
            {assessment.requiredSkills.map((skill, index) => (
              <span 
                key={index}
                className="px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-md"
              >
                {skill}
              </span>
            ))}
            {assessment.requiredSkills.length > 4 && assessment.requiredSkills.slice(4).length > 0 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-500 text-sm rounded-md">
                +{assessment.requiredSkills.slice(4).length} more
              </span>
            )}
          </div>
        </div>
        
        {/* Take assessment button */}
        <button
          onClick={() => onSelect(assessment.id)}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors flex items-center justify-center"
        >
          <AcademicCapIcon className="h-5 w-5 mr-2" />
          Take Assessment
        </button>
      </div>
    </div>
  );
};

export default AssessmentCard;