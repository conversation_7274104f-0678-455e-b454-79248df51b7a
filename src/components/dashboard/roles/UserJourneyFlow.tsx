import React from 'react';
import { 
  BriefcaseIcon, 
  BuildingLibraryIcon, 
  ClipboardDocumentCheckIcon, 
  PresentationChartLineIcon, 
  AcademicCapIcon,
  ArrowRightIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

/**
 * UserJourneyFlow component displays steps for the user to follow in the roles dashboard
 */
interface UserJourneyFlowProps {
  isExpanded: boolean;
  onToggle: () => void;
}

const UserJourneyFlow = (props: UserJourneyFlowProps) => {
  return (
    <div className="bg-white rounded-xl shadow-sm p-5 mb-6 border-l-4 border-blue-500">
      <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center justify-between">
        <div className="flex items-center">
          <span className="bg-blue-100 text-blue-700 p-1 rounded-md mr-2">
            <AcademicCapIcon className="h-5 w-5" />
          </span>
          How It Works: Your Skills Journey
        </div>
        {/* Toggle button moved to header */}
        <button
          onClick={props.onToggle}
          className="text-blue-600 hover:bg-blue-50 transition-colors flex items-center gap-1 px-3 py-1 rounded-md text-sm"
        >
          {props.isExpanded ? "Hide Details" : "Show Details"}
        </button>
      </h2>
      
      {props.isExpanded && (
        <>
          <div className="flex flex-col md:flex-row justify-between gap-4 py-2">
            {/* Step 1 - Select Role */}
            <div className="text-center">
              <div className="bg-blue-50 rounded-full p-4 h-16 w-16 mx-auto flex items-center justify-center mb-2">
                <BriefcaseIcon className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="font-medium text-gray-800">1. Select Role</h3>
              <p className="text-sm text-gray-600 mt-1">Choose a job role that interests you</p>
            </div>
            
            {/* Arrow */}
            <div className="hidden md:flex text-gray-400 items-center">
              <ArrowRightIcon className="h-5 w-5" />
            </div>
            <div className="flex md:hidden text-gray-400 justify-center my-1">
              <ChevronDownIcon className="h-5 w-5" />
            </div>
            
            {/* Step 2 - Choose Sector */}
            <div className="text-center">
              <div className="bg-purple-50 rounded-full p-4 h-16 w-16 mx-auto flex items-center justify-center mb-2">
                <BuildingLibraryIcon className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="font-medium text-gray-800">2. Choose Sector & Domain</h3>
              <p className="text-sm text-gray-600 mt-1">Specify your area of interest</p>
            </div>
            
            {/* Arrow */}
            <div className="hidden md:flex text-gray-400 items-center">
              <ArrowRightIcon className="h-5 w-5" />
            </div>
            <div className="flex md:hidden text-gray-400 justify-center my-1">
              <ChevronDownIcon className="h-5 w-5" />
            </div>
            
            {/* Step 3 - Take Assessment */}
            <div className="text-center">
              <div className="bg-green-50 rounded-full p-4 h-16 w-16 mx-auto flex items-center justify-center mb-2">
                <ClipboardDocumentCheckIcon className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="font-medium text-gray-800">3. Take Assessment</h3>
              <p className="text-sm text-gray-600 mt-1">Evaluate your current skills</p>
            </div>
            
            {/* Arrow */}
            <div className="hidden md:flex text-gray-400 items-center">
              <ArrowRightIcon className="h-5 w-5" />
            </div>
            <div className="flex md:hidden text-gray-400 justify-center my-1">
              <ChevronDownIcon className="h-5 w-5" />
            </div>
            
            {/* Step 4 - View Gap Analysis */}
            <div className="text-center">
              <div className="bg-amber-50 rounded-full p-4 h-16 w-16 mx-auto flex items-center justify-center mb-2">
                <PresentationChartLineIcon className="h-8 w-8 text-amber-600" />
              </div>
              <h3 className="font-medium text-gray-800">4. View Gap Analysis</h3>
              <p className="text-sm text-gray-600 mt-1">See your skill gaps and start learning</p>
            </div>
          </div>
          
          {/* Additional explanatory text */}
          <div className="mt-5 bg-blue-50 p-4 rounded-lg text-sm text-blue-800 border border-blue-100">
            <ol className="list-decimal pl-5 space-y-2">
              <li><strong>Select a role</strong> from the available job titles below that matches your career goals.</li>
              <li><strong>Specify your sector and domain</strong> by clicking the "Select Sector" button on the job card.</li>
              <li><strong>View the skills master</strong> to see what skills are required for your selected role.</li>
              <li><strong>Take an assessment</strong> to evaluate your current skill level compared to the role requirements.</li>
              <li><strong>Review your results</strong> to identify skill gaps, then follow the personalized learning plan.</li>
            </ol>
            <p className="mt-3 italic">This step-by-step process helps you identify and bridge the gap between your current skills and those needed for your desired role.</p>
          </div>
        </>
      )}
    </div>
  );
};

export default UserJourneyFlow;