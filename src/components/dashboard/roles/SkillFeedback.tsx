import React, { useState } from 'react';
import { 
  PencilIcon, 
  PlusIcon, 
  MinusIcon, 
  XMarkIcon, 
  ExclamationCircleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

const SkillFeedbackComponent = ({ skillName = "React.js", onSubmit }) => {
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [feedbackType, setFeedbackType] = useState("limited"); // "unknown" or "limited"
  const [feedbackDetails, setFeedbackDetails] = useState("");
  const [alternativeSkills, setAlternativeSkills] = useState([]);
  const [newSkill, setNewSkill] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  
  // Open feedback form with specified type
  const openFeedback = (type) => {
    setFeedbackType(type);
    setShowFeedbackForm(true);
  };
  
  // Add alternative skill
  const addAlternativeSkill = () => {
    if (newSkill.trim() !== "") {
      setAlternativeSkills([...alternativeSkills, newSkill.trim()]);
      setNewSkill("");
    }
  };
  
  // Remove alternative skill
  const removeAlternativeSkill = (index) => {
    setAlternativeSkills(alternativeSkills.filter((_, i) => i !== index));
  };
  
  // Submit feedback
  const handleSubmit = () => {
    // Create feedback object
    const feedback = {
      skillName,
      feedbackType,
      feedbackDetails,
      alternativeSkills
    };
    
    // Call onSubmit prop if provided
    if (onSubmit) {
      onSubmit(feedback);
    }
    
    // Show success message
    setSuccessMessage("Your feedback has been submitted successfully!");
    
    // Reset form after short delay
    setTimeout(() => {
      setShowFeedbackForm(false);
      setFeedbackDetails("");
      setAlternativeSkills([]);
      setSuccessMessage("");
    }, 3000);
  };

  return (
    <div className="w-full">
      {!showFeedbackForm ? (
        <div className="flex flex-wrap gap-2 items-center justify-end mt-2">
          <button
            onClick={() => openFeedback("unknown")}
            className="flex items-center text-sm text-gray-500 hover:text-blue-600 transition-colors py-1 px-2 rounded hover:bg-blue-50"
          >
            <ExclamationCircleIcon className="h-4 w-4 mr-1" />
            <span>I don't know this skill</span>
          </button>
          <button
            onClick={() => openFeedback("limited")}
            className="flex items-center text-sm text-gray-500 hover:text-blue-600 transition-colors py-1 px-2 rounded hover:bg-blue-50"
          >
            <InformationCircleIcon className="h-4 w-4 mr-1" />
            <span>I have limited knowledge</span>
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-4 mt-3 relative">
          {/* Close button */}
          <button 
            onClick={() => setShowFeedbackForm(false)}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
          
          {/* Success message */}
          {successMessage && (
            <div className="bg-green-50 border border-green-100 rounded-md p-3 mb-4 flex items-start">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <p className="text-green-700">{successMessage}</p>
            </div>
          )}
          
          {/* Form title */}
          <h3 className="text-lg font-medium text-gray-900 mb-3">
            {feedbackType === "unknown" 
              ? `I don't know ${skillName}` 
              : `I have limited knowledge of ${skillName}`}
          </h3>
          
          {/* Feedback details */}
          <div className="mb-4">
            <label htmlFor="feedback-details" className="block text-sm font-medium text-gray-700 mb-1">
              Additional details (optional)
            </label>
            <textarea
              id="feedback-details"
              rows={3}
              placeholder={feedbackType === "unknown" 
                ? "Share why you haven't learned this skill yet or any challenges you've faced..." 
                : "Describe what aspects of this skill you're familiar with and where you need improvement..."
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              value={feedbackDetails}
              onChange={(e) => setFeedbackDetails(e.target.value)}
            />
          </div>
          
          {/* Alternative skills section */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Alternative skills I possess (optional)
            </label>
            
            {/* Skills list */}
            <div className="flex flex-wrap gap-2 mb-3">
              {alternativeSkills.length === 0 ? (
                <span className="text-sm text-gray-500 italic">Add skills you have that might be relevant alternatives</span>
              ) : (
                alternativeSkills.map((skill, index) => (
                  <div key={index} className="bg-blue-50 text-blue-700 text-sm px-3 py-1 rounded-full flex items-center">
                    {skill}
                    <button 
                      onClick={() => removeAlternativeSkill(index)}
                      className="ml-1 text-blue-500 hover:text-blue-700"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                ))
              )}
            </div>
            
            {/* Add skill input */}
            <div className="flex">
              <input
                type="text"
                placeholder="Type a skill and press Add"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                value={newSkill}
                onChange={(e) => setNewSkill(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addAlternativeSkill()}
              />
              <button
                onClick={addAlternativeSkill}
                className="bg-blue-600 text-white px-3 py-2 rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
              >
                <PlusIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          {/* Submit button */}
          <div className="flex justify-end">
            <button
              onClick={handleSubmit}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm font-medium"
            >
              Submit Feedback
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SkillFeedbackComponent;