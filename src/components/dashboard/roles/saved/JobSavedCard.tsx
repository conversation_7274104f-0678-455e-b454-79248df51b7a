import React, { useState } from 'react';
import { CheckIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

interface JobSavedCardProps {
  jobDescription: string;
  jobId: string;
  onSelect: (jobId: string, isSelected: boolean) => void;
  onClick?: () => void;
}

/**
 * JobSavedCard displays a saved job with description and selection capability
 * 
 * @param jobDescription - Text description of the job
 * @param jobId - Unique identifier for the job
 * @param onSelect - Callback when job selection changes
 * @param onClick - Optional callback when card is clicked (for details view)
 */
const JobSavedCard: React.FC<JobSavedCardProps> = ({
  jobDescription,
  jobId,
  onSelect,
  onClick
}) => {
  const [selected, setSelected] = useState(false);

  // Handle selection toggle
  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event if there's an onClick handler
    
    const newSelectedState = !selected;
    setSelected(newSelectedState);
    onSelect(jobId, newSelectedState);
  };

  // Handle card click for details view
  const handleCardClick = () => {
    if (onClick) {
      onClick();
    }
  };

  // Get a preview of the job description
  const getDescriptionPreview = () => {
    // If description is short, use it as is
    if (jobDescription.length <= 300) {
      return jobDescription;
    }
    // Otherwise, truncate it and add ellipsis
    return jobDescription.substring(0, 300) + '...';
  };

  return (
    <div 
      className={`
        flex flex-col bg-white rounded-lg shadow-md overflow-hidden border-2 transition-all duration-200
        ${selected ? 'border-green-500 ring-1 ring-green-500' : 'border-gray-200 hover:border-blue-300'}
        h-full
      `}
      onClick={handleCardClick}
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      {/* Card Header */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <DocumentTextIcon className="h-5 w-5 mr-2 text-blue-600" />
          Job Description
        </h3>
      </div>
      
      {/* Card Content */}
      <div className="p-4 flex flex-col flex-grow">
        {/* Description with no fixed height and limited line count */}
        <div className="flex-grow mb-4 p-3 bg-gray-50 rounded-md border border-gray-200">
          <p className="text-gray-700 line-clamp-6 sm:line-clamp-8 md:line-clamp-[10]">{jobDescription}</p>
          
          {/* Show a "View more" button if text is very long */}
          {jobDescription.length > 300 && (
            <button 
              onClick={(e) => {
                e.stopPropagation();
                onClick && onClick();
              }}
              className="text-blue-600 hover:text-blue-800 text-sm mt-2 font-medium"
            >
              View details...
            </button>
          )}
        </div>
        
        {/* Action Button - at the bottom */}
        <button
          onClick={handleSelect}
          className={`
            w-full py-2 px-4 rounded-md font-medium transition-colors duration-200 mt-auto
            flex items-center justify-center
            ${selected 
              ? 'bg-green-100 text-green-700 border border-green-500' 
              : 'bg-white text-blue-600 border border-blue-600 hover:bg-blue-50'
            }
          `}
        >
          {selected ? (
            <>
              <CheckIcon className="h-5 w-5 mr-2" />
              Selected
            </>
          ) : (
            'Select Job'
          )}
        </button>
      </div>
    </div>
  );
};

export default JobSavedCard;