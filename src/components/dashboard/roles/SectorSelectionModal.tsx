import React, { useState } from 'react';
import { XMarkIcon, ArrowLeftIcon, CheckIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import { 
  BeakerIcon, 
  BriefcaseIcon, 
  BuildingOffice2Icon,
  ComputerDesktopIcon,
  CurrencyDollarIcon,
  TruckIcon,
  ShieldCheckIcon,
  WrenchScrewdriverIcon,
  AcademicCapIcon,
  HeartIcon
} from '@heroicons/react/24/outline';

interface SectorDomain {
  id: string;
  name: string;
}

interface Sector {
  id: string;
  name: string;
  icon: React.ReactNode;
  domains: SectorDomain[];
}

interface SectorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSectorSelect: (sectorId: string) => void;
  onDomainSelect: (sectorId: string, domainId: string) => void;
  selectedSectors: string[];
  selectedDomains: {[sectorId: string]: string[]};
  jobTitle: string;
}

const SectorSelectionModal: React.FC<SectorModalProps> = ({
  isOpen,
  onClose,
  onSectorSelect,
  onDomainSelect,
  selectedSectors,
  selectedDomains,
  jobTitle
}) => {
  // State to track current view (sectors or domains)
  const [currentView, setCurrentView] = useState<'sectors' | 'domains'>('sectors');
  // State to track the currently selected sector for domain selection
  const [activeSector, setActiveSector] = useState<Sector | null>(null);
  // State to track if instructions tooltip is visible
  const [showTooltip, setShowTooltip] = useState(true);

  // Sectors data with domains
  const sectors: Sector[] = [
    { 
      id: '1', 
      name: 'Healthcare', 
      icon: <HeartIcon className="h-6 w-6" />,
      domains: [
        { id: '101', name: 'Clinical Research' },
        { id: '102', name: 'Healthcare Administration' },
        { id: '103', name: 'Medical Practice' },
        { id: '104', name: 'Public Health' },
        { id: '105', name: 'Pharmaceuticals' }
      ]
    },
    { 
      id: '2', 
      name: 'Technology', 
      icon: <ComputerDesktopIcon className="h-6 w-6" />,
      domains: [
        { id: '201', name: 'Software Development' },
        { id: '202', name: 'Data Science' },
        { id: '203', name: 'Cybersecurity' },
        { id: '204', name: 'Cloud Computing' },
        { id: '205', name: 'Artificial Intelligence' }
      ]
    },
    { 
      id: '3', 
      name: 'Finance', 
      icon: <CurrencyDollarIcon className="h-6 w-6" />,
      domains: [
        { id: '301', name: 'Investment Banking' },
        { id: '302', name: 'Financial Analysis' },
        { id: '303', name: 'Risk Management' },
        { id: '304', name: 'Asset Management' },
        { id: '305', name: 'Corporate Finance' }
      ]
    },
    { 
      id: '4', 
      name: 'Manufacturing', 
      icon: <WrenchScrewdriverIcon className="h-6 w-6" />,
      domains: [
        { id: '401', name: 'Process Engineering' },
        { id: '402', name: 'Quality Control' },
        { id: '403', name: 'Supply Chain' },
        { id: '404', name: 'Production Planning' },
        { id: '405', name: 'Industrial Design' }
      ]
    },
    { 
      id: '5', 
      name: 'Education', 
      icon: <AcademicCapIcon className="h-6 w-6" />,
      domains: [
        { id: '501', name: 'K-12 Education' },
        { id: '502', name: 'Higher Education' },
        { id: '503', name: 'Educational Technology' },
        { id: '504', name: 'Special Education' },
        { id: '505', name: 'Educational Administration' }
      ]
    },
    { 
      id: '6', 
      name: 'Logistics', 
      icon: <TruckIcon className="h-6 w-6" />,
      domains: [
        { id: '601', name: 'Transportation' },
        { id: '602', name: 'Warehouse Management' },
        { id: '603', name: 'Inventory Control' },
        { id: '604', name: 'Supply Chain Optimization' },
        { id: '605', name: 'Freight Forwarding' }
      ]
    },
    { 
      id: '7', 
      name: 'Government', 
      icon: <BuildingOffice2Icon className="h-6 w-6" />,
      domains: [
        { id: '701', name: 'Public Administration' },
        { id: '702', name: 'Policy Development' },
        { id: '703', name: 'Public Safety' },
        { id: '704', name: 'Municipal Services' },
        { id: '705', name: 'Regulatory Affairs' }
      ]
    },
    { 
      id: '8', 
      name: 'Research', 
      icon: <BeakerIcon className="h-6 w-6" />,
      domains: [
        { id: '801', name: 'Academic Research' },
        { id: '802', name: 'R&D' },
        { id: '803', name: 'Market Research' },
        { id: '804', name: 'Scientific Research' },
        { id: '805', name: 'Social Science Research' }
      ]
    },
    { 
      id: '9', 
      name: 'Security', 
      icon: <ShieldCheckIcon className="h-6 w-6" />,
      domains: [
        { id: '901', name: 'Physical Security' },
        { id: '902', name: 'Information Security' },
        { id: '903', name: 'Risk Assessment' },
        { id: '904', name: 'Security Operations' },
        { id: '905', name: 'Compliance' }
      ]
    },
    { 
      id: '10', 
      name: 'Business', 
      icon: <BriefcaseIcon className="h-6 w-6" />,
      domains: [
        { id: '1001', name: 'Marketing' },
        { id: '1002', name: 'Human Resources' },
        { id: '1003', name: 'Operations' },
        { id: '1004', name: 'Strategic Management' },
        { id: '1005', name: 'Business Development' }
      ]
    },
  ];

  if (!isOpen) return null;

  const handleSectorClick = (sector: Sector) => {
    // First select/deselect the sector
    onSectorSelect(sector.id);
    
    // If selecting (not deselecting), go to domains view
    if (!selectedSectors.includes(sector.id)) {
      setActiveSector(sector);
      setCurrentView('domains');
    }
  };

  const handleDomainClick = (domainId: string) => {
    if (activeSector) {
      onDomainSelect(activeSector.id, domainId);
    }
  };

  const handleBackToSectors = () => {
    setCurrentView('sectors');
    setActiveSector(null);
  };

  const handleSelectAllDomains = () => {
    if (!activeSector) return;
    
    // Check if all domains are already selected
    const sectorDomains = activeSector.domains.map(d => d.id);
    const currentlySelected = selectedDomains[activeSector.id] || [];
    const allSelected = sectorDomains.every(id => currentlySelected.includes(id));
    
    // If all selected, deselect all; otherwise select all
    if (allSelected) {
      // Deselect all domains for this sector
      sectorDomains.forEach(domainId => {
        onDomainSelect(activeSector.id, domainId);
      });
    } else {
      // Select all domains that aren't already selected
      sectorDomains.forEach(domainId => {
        if (!currentlySelected.includes(domainId)) {
          onDomainSelect(activeSector.id, domainId);
        }
      });
    }
  };

  const isSectorSelected = (id: string) => selectedSectors.includes(id);
  
  const isDomainSelected = (sectorId: string, domainId: string) => {
    return selectedDomains[sectorId]?.includes(domainId) || false;
  };

  // Count total selections
  const totalSelectedSectors = selectedSectors.length;
  const totalSelectedDomains = Object.values(selectedDomains).reduce(
    (sum, domains) => sum + domains.length, 0
  );

  // Helper to get sector name by ID
  const getSectorName = (id: string): string => {
    const sector = sectors.find(s => s.id === id);
    return sector ? sector.name : '';
  };

  // Get domain count for a sector
  const getSelectedDomainCount = (sectorId: string): number => {
    return selectedDomains[sectorId]?.length || 0;
  };

  // Format sector selections for summary
  const formatSectorSelections = () => {
    return selectedSectors.map(id => {
      const name = getSectorName(id);
      const domainCount = getSelectedDomainCount(id);
      return { id, name, domainCount };
    });
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-lg max-h-[90vh] flex flex-col overflow-hidden">
        {/* Header - fixed */}
        <div className="flex justify-between items-center p-5 border-b border-gray-200">
          {currentView === 'domains' && activeSector ? (
            <div className="flex items-center">
              <button 
                onClick={handleBackToSectors}
                className="mr-2 flex items-center text-blue-600 hover:text-blue-800 p-1 rounded transition-colors"
                aria-label="Back to sectors"
              >
                <ArrowLeftIcon className="h-5 w-5 mr-1" />
                <span>Back to Sectors</span>
              </button>
            </div>
          ) : (
            <h2 className="text-xl font-bold text-gray-900">Select Sectors for {jobTitle}</h2>
          )}
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors"
            aria-label="Close"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        
        {/* Selection info - shows current count */}
        <div className="bg-blue-50 p-3 flex justify-between items-center border-b border-blue-100">
          <div className="flex items-center">
            <InformationCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
            <p className="text-blue-800 text-sm">
              You can select multiple {currentView === 'sectors' ? 'sectors for this role' : `domains within ${activeSector?.name}`}
            </p>
          </div>
          <div className="text-sm font-medium text-blue-800">
            {totalSelectedSectors} {totalSelectedSectors === 1 ? 'sector' : 'sectors'}, {totalSelectedDomains} {totalSelectedDomains === 1 ? 'domain' : 'domains'}
          </div>
        </div>
        
        {/* Modal body - scrollable */}
        <div className="p-5 overflow-y-auto flex-grow">
          {currentView === 'sectors' ? (
            <>
              {/* Sector heading */}
              <div className="flex justify-between items-center mb-5">
                <h3 className="text-lg font-medium text-gray-900">Available Sectors</h3>
                
                {/* Show selections summary if any */}
                {totalSelectedSectors > 0 && (
                  <button 
                    onClick={() => setShowTooltip(!showTooltip)}
                    className="text-blue-600 text-sm hover:text-blue-800 flex items-center"
                  >
                    {showTooltip ? 'Hide' : 'Show'} selections
                  </button>
                )}
              </div>
              
              {/* Selections summary */}
              {showTooltip && totalSelectedSectors > 0 && (
                <div className="mb-5 bg-gray-50 rounded-lg p-3 border border-gray-200">
                  <h4 className="font-medium text-gray-700 mb-2">Current Selections:</h4>
                  <ul className="space-y-1">
                    {formatSectorSelections().map(sector => (
                      <li key={sector.id} className="flex justify-between text-sm">
                        <span>{sector.name}</span>
                        {sector.domainCount > 0 && (
                          <span className="text-blue-600">
                            {sector.domainCount} {sector.domainCount === 1 ? 'domain' : 'domains'}
                          </span>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {/* Sectors grid */}
              <div className="grid grid-cols-2 gap-4">
                {sectors.map((sector) => (
                  <button
                    key={sector.id}
                    onClick={() => handleSectorClick(sector)}
                    className={`flex items-center gap-3 p-4 rounded-lg border ${
                      isSectorSelected(sector.id)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    } transition-all duration-150`}
                  >
                    <div className={`rounded-full p-3 ${
                      isSectorSelected(sector.id) 
                        ? 'bg-blue-100 text-blue-600' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {sector.icon}
                    </div>
                    <div className="flex flex-col items-start">
                      <span className={`text-base ${
                        isSectorSelected(sector.id) ? 'text-blue-700 font-medium' : 'text-gray-700'
                      }`}>
                        {sector.name}
                      </span>
                      {isSectorSelected(sector.id) && getSelectedDomainCount(sector.id) > 0 && (
                        <span className="text-xs text-blue-600 mt-1">
                          {getSelectedDomainCount(sector.id)} domains selected
                        </span>
                      )}
                    </div>
                    {isSectorSelected(sector.id) && (
                      <CheckIcon className="h-5 w-5 text-blue-500 ml-auto" />
                    )}
                  </button>
                ))}
              </div>
            </>
          ) : (
            activeSector && (
              <>
                {/* Domain selection header */}
                <div className="flex justify-between items-center mb-5">
                  <h3 className="text-lg font-medium text-gray-900">{activeSector.name} Domains</h3>
                  
                  {/* Select all button */}
                  <button
                    onClick={handleSelectAllDomains}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    {activeSector.domains.every(d => 
                      isDomainSelected(activeSector.id, d.id)) 
                      ? 'Deselect All' 
                      : 'Select All'}
                  </button>
                </div>
                
                {/* Domains list with checkboxes */}
                <div className="space-y-2">
                  {activeSector.domains.map((domain) => (
                    <button
                      key={domain.id}
                      onClick={() => handleDomainClick(domain.id)}
                      className={`w-full flex items-center p-3 rounded-lg border ${
                        isDomainSelected(activeSector.id, domain.id)
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                      } transition-all duration-150`}
                    >
                      <div className={`h-5 w-5 flex-shrink-0 border rounded mr-3 flex items-center justify-center ${
                        isDomainSelected(activeSector.id, domain.id)
                          ? 'bg-blue-500 border-blue-500'
                          : 'border-gray-300'
                      }`}>
                        {isDomainSelected(activeSector.id, domain.id) && (
                          <CheckIcon className="h-4 w-4 text-white" />
                        )}
                      </div>
                      <span className="text-base flex-grow text-left">
                        {domain.name}
                      </span>
                    </button>
                  ))}
                </div>
                
                {/* Return to sectors button */}
                <button
                  onClick={handleBackToSectors}
                  className="mt-6 w-full flex items-center justify-center text-blue-600 hover:text-blue-800 border border-blue-200 p-2 rounded-md hover:bg-blue-50"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Select Another Sector
                </button>
              </>
            )
          )}
        </div>
        
        {/* Footer with action buttons - fixed */}
        <div className="p-5 border-t border-gray-200 bg-gray-50 flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onClose}
            disabled={selectedSectors.length === 0}
            className={`px-5 py-2 rounded-md font-medium transition-all duration-200 ${
              selectedSectors.length > 0 
                ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md' 
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {selectedSectors.length > 0 ? 'Save Selections' : 'Select at least one sector'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SectorSelectionModal;