import React, { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';

const QuestionaireModal = ({ isOpen, onClose, handleSubmit }) => {
  const [answers, setAnswers] = useState({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  const questions = [
    {
      type: 'radio',
      text: 'What is your highest level of education?',
      options: ['High School', 'Bachelor\'s Degree', 'Master\'s Degree', 'Doctorate'],
      id: 'education'
    },
    {
      type: 'text',
      text: 'What is your current salary expectation?',
      id: 'salary'
    },
    {
      type: 'radio',
      text: 'What is your preferred work location?',
      options: ['Remote', 'Hybrid', 'On-site'],
      id: 'location'
    },
    {
      type: 'textarea', // Changed to textarea for longer answers
      text: 'Tell us about your relevant work experience.',
      id: 'experience'
    },
    // Add more questions here...
  ];

  const currentQuestion = questions[currentQuestionIndex];

  const handleInputChange = (id, value) => {
    setAnswers({ ...answers, [id]: value });
  };

  const handleRadioChange = (id, event) => {
    setAnswers({ ...answers, [id]: event.target.value });
  };

  const handleFormSubmit = () => {
    handleSubmit(answers);
    onClose();
    setAnswers({});
    setCurrentQuestionIndex(0); // Reset to the first question
  };

  const handleNextQuestion = () => {
    setCurrentQuestionIndex(prevIndex => Math.min(prevIndex + 1, questions.length - 1));
  };

  const handlePreviousQuestion = () => {
    setCurrentQuestionIndex(prevIndex => Math.max(prevIndex - 1, 0));
  };


  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/30 flex justify-center items-center z-30 overflow-auto">
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        drag 
        transition={{
          duration: 0.4,
          scale: { type: "spring", visualDuration: 0.4, bounce: 0.5 },
        }}
        className="bg-white p-6 rounded-lg w-full lg:w-[600px] h-fit max-h-[90vh] overflow-y-auto"
      >

        {/* Added max-h and overflow for long content */}
        <div className='w-full flex justify-between'>
          <h2 className="text-2xl font-semibold mb-4">Job Application Questionnaire</h2>
          <button onClick={onClose} className="w-fit h-fit">
            <XMarkIcon className='w-7 h-7' />
          </button> {/* Cancel button at the bottom */}
        </div>


        <div> {/* Container for the current question */}
          <h3 className="text-md font-medium mb-2">{currentQuestion.text}</h3>
          {currentQuestion.type === 'radio' && (
            <div>
              {currentQuestion.options.map((option) => (
                <label key={option} className="block mb-1">
                  <input
                    type="radio"
                    name={currentQuestion.id}
                    value={option}
                    checked={answers[currentQuestion.id] === option}
                    onChange={(event) => handleRadioChange(currentQuestion.id, event)}
                  />
                  {option}
                </label>
              ))}
            </div>
          )}
          {currentQuestion.type === 'text' && (
            <input
              type="text"
              className="border border-gray-300 rounded px-3 py-2 w-full"
              value={answers[currentQuestion.id] || ''}
              onChange={(event) => handleInputChange(currentQuestion.id, event.target.value)}
            />
          )}
          {currentQuestion.type === 'textarea' && ( // Added textarea input
            <textarea
              className="border border-gray-300 rounded px-3 py-2 w-full h-24" // Added height for textarea
              value={answers[currentQuestion.id] || ''}
              onChange={(event) => handleInputChange(currentQuestion.id, event.target.value)}
            />
          )}
        </div>

        <div className="flex justify-between mt-4"> {/* Navigation buttons */}
          <button
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
            className="self-end border-2 border-textSecondary disabled:border-gray-600 text-textSecondary font-bold py-2 rounded text-sm w-fit px-4 disabled:bg-gray-100 disabled:text-gray-600"
          >
            Previous
          </button>
          <div>
            <button
              onClick={currentQuestionIndex === questions.length - 1 ? handleFormSubmit : handleNextQuestion}
              // disabled={currentQuestionIndex === questions.length - 1}
              className="self-end border-2 border-textSecondary text-textSecondary font-bold py-2 rounded text-sm w-full px-4"
            >
              {currentQuestionIndex === questions.length - 1 ? "Submit" : "Next"}
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default QuestionaireModal;