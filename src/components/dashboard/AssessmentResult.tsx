import React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ClockIcon, CalendarDaysIcon } from "@heroicons/react/24/outline";
import { getUser } from "@/api/user.localStorage";
import { UpcomingUserAssessment } from "@/types/LMSTypes";

type propType = {
  assessments: UpcomingUserAssessment[];
  loadingState: boolean;
}
const AssessmentResult: React.FC<propType> = ({ assessments, loadingState }) => {
  const user = getUser();



  console.log("user", user)
  const router = useRouter();
  console.log("assessment", assessments);

  if (loadingState) {
    return (
      <div className="flex flex-col mx-auto w-full max-w-2xl rounded-xl bg-white p-4">
        <span className="text-md font-medium">
          {" "}
          Loading...{" "}
        </span>
      </div>
    )

  } else {
    return (
      <div >
        {assessments?.length > 0 ? (
          assessments.map((assessment) => (
            <div
              key={assessment}
              className="flex w-full rounded-lg bg-white border-b-2 border-gray-700 p-4"
            >
              {/* <div className="w-16 h-24 bg-[#CFDFF6] rounded-lg"></div> */}
              <div className="w-full ">
                <h5 className="text-lg font-medium leading-tight text-textPrimary ">
                  {assessment.assessment_name}
                </h5>
                <h5 className="pt-1 text-xs leading-tight text-slate-500 " id="test1">
                  Previous assesment
                </h5>
                <div className="flex flex-col sm:flex-row w-full justify-between">
                  <div className="flex w-full max-w-sm pt-1 justify-between">
                    <div className="flex flex-col">
                      <p className="text-xs text-textColor">
                        Assessmment Was Schedule:
                      </p>
                      <div className="flex">
                        <CalendarDaysIcon className="h-4 w-4 " />
                        <span className="text-xs px-1 ">
                          {new Date(assessment.start_date).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex pt-1">
                        <ClockIcon color="#475569" className="h-4 w-4" />
                        <span className="text-xs px-1">
                          {new Date(assessment.start_date).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                    {/* <div className="flex flex-col">
                      <p className="text-xs text-textColor">Till:</p>
                      <div className="flex">
                        <CalendarDaysIcon className="h-4 w-4" />
                        <span className="text-xs px-1">
                          {new Date(assessment.end_date).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex pt-1">
                        <ClockIcon color="#475569" className="h-4 w-4" />
                        <span className="text-xs px-1">
                          {new Date(assessment.end_date).toLocaleTimeString()}
                        </span>
                      </div>
                    </div> */}
                  </div>
                  <Link
                    href={{
                      pathname: "/dashboard/seeuserresult",
                      query: {
                        assessment_id: assessment.assessment_id,
                        user_id: assessment.user_id,
                      },
                    }}
                  >
                    <button
                      className="flex w-30 h-9 mt-5 justify-center rounded-md bg-secondary px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75"
                    >
                      See Results
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="flex flex-col mx-auto w-full max-w-2xl rounded-xl bg-white p-4">
            <span className="text-md font-medium">
              {" "}
              Currently, there are no asessments result for you.{" "}
            </span>
          </div>
        )}
      </div>
    );
  }


};

export default AssessmentResult;
