import { FunctionComponent } from 'react';

interface YoutubeRendererProps {
  url: string;
  height: number;
  width: number;
}

export const YoutubeRenderer: FunctionComponent<YoutubeRendererProps> = ({
  url, height, width
}) => {
  return (
    <div className="mt-2 flex justify-center">
      <iframe
        width={width}
        className="h-[80vh]"
        height={height}
        src={url}
        title="YouTube video player"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowFullScreen
      ></iframe>
    </div>
  );
};
