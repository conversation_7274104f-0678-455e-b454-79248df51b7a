'use client';
import React, { useEffect, useRef, FunctionComponent, useState } from 'react';
import videojs from 'video.js';
import Player from 'video.js/dist/types/player';
import 'video.js/dist/video-js.css';
import 'videojs-contrib-eme';
import 'videojs-mobile-ui/dist/videojs-mobile-ui.css';
import 'videojs-seek-buttons/dist/videojs-seek-buttons.css';
import 'videojs-mobile-ui';
import 'videojs-sprite-thumbnails';
import 'videojs-seek-buttons';
import { handleMarkAsCompleted } from '@/utils/utils';
import { useSearchParams } from 'next/navigation';
import { YoutubeRenderer } from './YoutubeRenderer';
import { useVideoContextCount } from '@/utils/videoContext';

// todo correct types
interface VideoPlayerProps {
  inputUrl: any;
  inputWidth: number;
  inputHeight: number;
  setQuality: React.Dispatch<React.SetStateAction<string>>;
  options: any;
  onReady?: (player: Player) => void;
  subtitles?: string;
  contentId: number;
  onVideoEnd: () => void;
  handleVideoSave: () => void;
  
}

const PLAYBACK_RATES: number[] = [0.5, 1, 1.25, 1.5, 1.75, 2];
const VOLUME_LEVELS: number[] = [0, 0.2, 0.4, 0.6, 0.8, 1.0];

export const VideoPlayer: FunctionComponent<VideoPlayerProps> = ({
  inputUrl,
  inputWidth,
  inputHeight,
  setQuality,
  options,
  contentId,
  onReady,
  subtitles,
  onVideoEnd,
  setvideoProgress,
  videoProgress,
  handleVideoSave,
}) => {
  const videoRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<Player | null>(null);
  const [player, setPlayer] = useState<any>(null);
  const searchParams = useSearchParams();
  // const { 
  //   // videoProgress, 
  //   setvideoProgress } = useVideoContextCount()

  useEffect(() => {
    if (player && videoProgress !== undefined) {
      player.currentTime(videoProgress);
    }
  }, [player, videoProgress]);

  const progressRef = useRef(0);

  useEffect(() => {
    if (!player || !setvideoProgress) {
      return;
    }

    const handleTimeUpdate = () => {
      const currentTime = player.currentTime();
      progressRef.current = currentTime; // Store in ref
      // handleVideoSave()
    };

    const handlePauseOrSeek = () => {
      setvideoProgress(progressRef.current); // Update state only when paused or seeked
      handleVideoSave()
    };

    setInterval(() => {
      setvideoProgress(progressRef.current);
    }, 120000);

    // Save progress when the component unmounts
    const saveProgressOnUnmount = () => {
      setvideoProgress(progressRef.current); // Save progress before unmounting
    };

    // Attach event listeners
    player.on('timeupdate', handleTimeUpdate);
    player.on('pause', handlePauseOrSeek);
    player.on('seeked', handlePauseOrSeek);
    // player.on('ended', handleVideoEnded)
    // Cleanup function
    return () => {
      // Save progress when the component unmounts
      saveProgressOnUnmount();

      // Remove event listeners
      player.off('timeupdate', handleTimeUpdate);
      player.off('pause', handlePauseOrSeek);
      player.off('seeked', handlePauseOrSeek);
      // player.off('ended', handleVideoEnded)
    };
  }, [player, setvideoProgress]);

  useEffect(() => {
    if (!player) {
      return;
    }
    let volumeSetTimeout: ReturnType<typeof setInterval> | null = null;
    const handleKeyPress = (event: KeyboardEvent) => {
      const isShiftPressed = event.shiftKey;
      const isModifierPressed = event.metaKey || event.ctrlKey || event.altKey;
      const activeElement = document.activeElement;

      const tracks: TextTrackList = player.textTracks();

      if (
        activeElement?.tagName.toLowerCase() === 'input' ||
        activeElement?.tagName.toLowerCase() === 'textarea' ||
        isModifierPressed
      ) {
        return; // Do nothing if the active element is an input or textarea
      }
      if (event.code === 'KeyT') {
        player.playbackRate(2);
      }
      if (isShiftPressed) {
        const currentIndexPeriod: number = PLAYBACK_RATES.indexOf(
          player.playbackRate(),
        );
        const newIndexPeriod: number =
          currentIndexPeriod !== PLAYBACK_RATES.length - 1
            ? currentIndexPeriod + 1
            : currentIndexPeriod;
        const currentIndexComma = PLAYBACK_RATES.indexOf(player.playbackRate());
        const newIndexComma =
          currentIndexComma !== 0 ? currentIndexComma - 1 : currentIndexComma;
        const currentIndexUp = VOLUME_LEVELS.indexOf(player.volume());
        const newIndexUp =
          currentIndexUp !== VOLUME_LEVELS.length - 1
            ? currentIndexUp + 1
            : currentIndexUp;
        const currentIndexDown = VOLUME_LEVELS.indexOf(player.volume());
        const newIndexDown =
          currentIndexDown !== 0 ? currentIndexDown - 1 : currentIndexDown;
        switch (event.code) {
          case 'Period': // Increase playback speed
            player.playbackRate(PLAYBACK_RATES[newIndexPeriod]);
            event.stopPropagation();
            break;
          case 'Comma': // Decrease playback speed
            player.playbackRate(PLAYBACK_RATES[newIndexComma]);
            event.stopPropagation();
            break;
          case 'ArrowUp': // Increase volume
            videoRef.current?.children[0].children[6].children[3].classList.add(
              'vjs-hover',
            );
            if (volumeSetTimeout !== null) clearTimeout(volumeSetTimeout);
            volumeSetTimeout = setTimeout(() => {
              videoRef.current?.children[0].children[6].children[3].classList.remove(
                'vjs-hover',
              );
            }, 1000);
            player.volume(VOLUME_LEVELS[newIndexUp]);
            event.stopPropagation();
            break;
          case 'ArrowDown': // Decrease volume
            videoRef.current?.children[0].children[6].children[3].classList.add(
              'vjs-hover',
            );
            if (volumeSetTimeout !== null) clearTimeout(volumeSetTimeout);
            volumeSetTimeout = setTimeout(() => {
              videoRef.current?.children[0].children[6].children[3].classList.remove(
                'vjs-hover',
              );
            }, 1000);
            player.volume(VOLUME_LEVELS[newIndexDown]);
            event.stopPropagation();
            break;
        }
        return;
      }

      switch (event.code) {
        case 'Space': // Space bar for play/pause
          if (player.paused()) {
            player.play();
            event.stopPropagation();
          } else {
            player.pause();
            event.stopPropagation();
          }
          event.preventDefault();
          break;
        case 'ArrowRight': // Right arrow for seeking forward 5 seconds
          player.currentTime(player.currentTime() + 5);
          event.stopPropagation();
          break;
        case 'ArrowLeft': // Left arrow for seeking backward 5 seconds
          player.currentTime(player.currentTime() - 5);
          event.stopPropagation();
          break;
        case 'ArrowUp': // Arrow up for increasing volume
          event.preventDefault();
          player.volume(player.volume() + 0.1);
          event.stopPropagation();
          break;
        case 'ArrowDown': // Arow dowwn for decreasing volume
          event.preventDefault();
          player.volume(player.volume() - 0.1);
          event.stopPropagation();
          break;
        case 'KeyF': // F key for fullscreen
          if (player.isFullscreen_) document.exitFullscreen();
          else player.requestFullscreen();
          event.stopPropagation();
          break;
        case 'KeyR': // 'R' key to restart playback from the beginning
          player.currentTime(0);
          event.stopPropagation();
          break;
        case 'KeyM': // 'M' key to toggle mute/unmute
          if (player.volume() === 0) {
            player.volume(1);
          } else {
            player.volume(0);
          }
          event.stopPropagation();
          break;
        case 'KeyK': // 'K' key for play/pause toggle
          if (player.paused()) {
            player.play();
          } else {
            player.pause();
          }
          event.stopPropagation();
          break;
        case 'KeyJ': // 'J' key for seeking backward 10 seconds multiplied by the playback rate
          player.currentTime(player.currentTime() - 10 * player.playbackRate());
          event.stopPropagation();
          break;
        case 'KeyL': // 'L' key for seeking forward 10 seconds multiplied by the playback rate
          player.currentTime(player.currentTime() + 10 * player.playbackRate());
          event.stopPropagation();
          break;
        case 'KeyC':
          for (let i = 0; i < tracks.length; i++) {
            const track = tracks[i];

            if (track.kind === 'subtitles' && track.language === 'en') {
              if (track.mode === 'hidden') {
                track.mode = 'showing';
              } else {
                track.mode = 'hidden';
              }
            }
          }
          event.stopPropagation();
          break;
        case 'Digit1':
          player.currentTime(player.duration() * 0.1);
          event.stopPropagation();
          break;
        case 'Digit2':
          player.currentTime(player.duration() * 0.2);
          event.stopPropagation();
          break;
        case 'Digit3':
          player.currentTime(player.duration() * 0.3);
          event.stopPropagation();
          break;
        case 'Digit4':
          player.currentTime(player.duration() * 0.4);
          event.stopPropagation();
          break;
        case 'Digit5':
          player.currentTime(player.duration() * 0.5);
          event.stopPropagation();
          break;
        case 'Digit6':
          player.currentTime(player.duration() * 0.6);
          event.stopPropagation();
          break;
        case 'Digit7':
          player.currentTime(player.duration() * 0.7);
          event.stopPropagation();
          break;
        case 'Digit8':
          player.currentTime(player.duration() * 0.8);
          event.stopPropagation();
          break;
        case 'Digit9':
          player.currentTime(player.duration() * 0.9);
          event.stopPropagation();
          break;
        case 'Digit0':
          player.currentTime(0);
          event.stopPropagation();
          break;
      }
    };
    const handleKeyUp = (event: any) => {
      if (event.code === 'KeyT') {
        player.playbackRate(1);
      }
    };
    document.addEventListener('keydown', handleKeyPress);
    document.addEventListener('keyup', handleKeyUp);
    // Cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [player]);
  useEffect(() => {
    if (!player) {
      return;
    }
    let interval = 0;
    const handleVideoProgress = () => {
      if (!player) {
        return;
      }
      interval = window.setInterval(
        async () => {
          if (!player) {
            return;
          }
          //@ts-ignore
          if (player?.paused()) {
            return;
          }
          const currentTime = player.currentTime();
          if (currentTime <= 20) {
            return;
          }
          await fetch('/api/course/videoProgress', {
            body: JSON.stringify({
              currentTimestamp: currentTime,
              contentId,
            }),
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          });
        },
        Math.ceil((100 * 1000) / player.playbackRate()),
      );
    };
    const handleVideoEnded = (interval: number) => {
      handleMarkAsCompleted(true, contentId);
      window.clearInterval(interval);
      onVideoEnd();
    };

    player.on('play', handleVideoProgress);
    player.on('ended', () => handleVideoEnded(interval));
    return () => {
      window.clearInterval(interval);
    };
  }, [player, contentId]);

  useEffect(() => {
    if (!playerRef.current && videoRef.current) {
      const videoElement = document.createElement('video-js');
      videoElement.classList.add('vjs-big-play-centered');

      // Apply width and height to the video element
      videoElement.style.width = `${inputWidth}px`;
      videoElement.style.height = `${inputHeight}px`;

      videoRef.current.appendChild(videoElement);


      if (subtitles) {
        const subtitlesEl = document.createElement('track');
        subtitlesEl.setAttribute('kind', 'subtitles');

        subtitlesEl.setAttribute('label', 'English');
        subtitlesEl.setAttribute('srcLang', 'en');
        subtitlesEl.setAttribute('src', subtitles);

        videoElement.append(subtitlesEl);
      }
      videoRef.current.appendChild(videoElement);
      const player: any = (playerRef.current = videojs(
        videoElement,
        {
          ...options,
          playbackRates: [0.5, 1, 1.25, 1.5, 1.75, 2],
        },
        () => {
          player.mobileUi(); // mobile ui #https://github.com/mister-ben/videojs-mobile-ui
          player.eme(); // Initialize EME
          player.seekButtons({
            forward: 15,
            back: 15,
          });

          player.qualitySelector = setQuality;
          // const qualitySelector = player.controlBar.addChild(
          //   'QualitySelectorControllBar',
          // );
          const controlBar = player.getChild('controlBar');
          const fullscreenToggle = controlBar.getChild('fullscreenToggle');

          controlBar
            .el(), fullscreenToggle.el();
          setPlayer(player);
          if (options.isComposite) {
            player.spriteThumbnails({
              interval: options.delta,
              url: options.thumbnail.secure_url,
              width: inputWidth,
              height: inputHeight,
            });
          }
          player.on('loadedmetadata', () => {
            if (onReady) {
              onReady(player);
            }
          });
          // Focus the video player when toggling fullscreen
          player.on('fullscreenchange', () => {
            videoElement.focus();
          });
        },
      ));

      if (
        inputUrl
      ) {
        player.src(inputUrl);
      }
    }
  }, [options, onReady]);

  useEffect(() => {
    if (player) {
      const currentTime = player.currentTime();
      player.src(inputUrl);
      player.currentTime(currentTime);
    }
  }, [inputUrl]);

  useEffect(() => {
    const player = playerRef.current;
    return () => {
      if (player && !player.isDisposed()) {
        player.dispose();
        playerRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    const t = searchParams.get('timestamp');

    if (player && t) {
      player.currentTime(parseInt(t, 10));
    }
  }, [searchParams, player]);

  const isYoutubeUrl = (url: string) => {
    const regex = /^https:\/\/www\.youtube\.com\/embed\/[a-zA-Z0-9_-]+/;
    return regex.test(url);
  };

  if (isYoutubeUrl(inputUrl)) {
    return <YoutubeRenderer
      url={inputUrl}
      height={inputHeight}
      width={inputWidth}
    />;
  }

  return (
    <div data-vjs-player className="mx-auto">
      <div ref={videoRef} />
    </div>
  );
};

export default VideoPlayer;