
import { useState } from "react"
import Collapsible from "./Collapsible";
import Link from "next/link";

interface SidebarProps {
  height: number;
  width: number;
  contentPanelData: any;
  expanded: any;
  incomingContentId: any;
}

export default function Sidebar({ height, width, contentPanelData, expanded, incomingContentId }: SidebarProps) {
  // const [expanded, setExpanded] = useState(false)
  const [count, setCount] = useState(1)


  console.log("contentPanelData22", contentPanelData) 
  // console.log("collapsibleData22", collapsibleData)

  return (
    <aside className="h-screen">
      <nav className={`h-[80%] lg:pb-10 flex flex-col ${expanded ? "border-r shadow-sm" : ""}`}>
        <div className="flex flex-col justify-center items-center gap-6">
         
          {expanded && <div className="w-fit h-fit py-4">
            <Link
              className="bg-secondary text-white text-lg font-bold px-8 py-2 border-[2px] border-slate-300 rounded-lg hover:bg-blue-900 duration-150"
              href="/dashboard/group">Dashboard</Link>
          </div>}
          {expanded && <div
            className={`flex flex-col h-[520px] lg:h-[480px] gap-[8px] w-full items-start overflow-auto transition-all ${expanded ? "w-72 p-3" : "w-0"}`}
          >
            {contentPanelData?.map((item: any, index: number) => (
              <Collapsible
                data={item}
                key={index}
                number={index + 1}
                setCount={setCount}
                group_id={contentPanelData[0]['group_id']}
                incomingContentId={incomingContentId}
              />
            ))}
          </div>}

        </div>
      </nav>
    </aside>
  )
}
