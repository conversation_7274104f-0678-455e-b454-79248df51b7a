import { useState, useEffect } from 'react';
import { useModuleContentStore } from '@/utils/dummyData/ModuleContentContext';
import { ChevronLeftIcon, ChevronRightIcon, InformationCircleIcon } from "@heroicons/react/24/outline";

// Import our custom components
import NewHeader from './NewHeader';
import NewSidebar from './NewSidebar';
import NewInfoPanel from './NewInfoPanel';
import { 
  VideoPlayer, 
  DocumentViewer, 
  YoutubeEmbed, 
  WebEmbed, 
  PresentationViewer, 
  extractVideoId, 
  formatDate 
} from './NewContentViewers';

// Import dummy data
import { courseData, dummyUserProgress } from '@/utils/dummyData/courseData';

const NewContentPanel = () => {
  // Get and set state from context
  const { content_id, module_id, group_id, setContent_id, setModule_id, setGroup_id } = useModuleContentStore();
  
  // State Variables
  const [activeModule, setActiveModule] = useState(module_id);
  const [activeContent, setActiveContent] = useState(content_id);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showDetails, setShowDetails] = useState(false);
  const [expandedModules, setExpandedModules] = useState([module_id]);
  const [activeTab, setActiveTab] = useState('content');
  
  // Track completed content
  const [completedItems, setCompletedItems] = useState(dummyUserProgress.completed_content_ids || [1]); 
  
  // Use our course data
  const [contentData, setContentData] = useState(() => {
    return courseData.map(module => ({
      ...module,
      contents: module.contents.map(content => ({
        ...content,
        completed: dummyUserProgress.completed_content_ids.includes(content.content_id)
      }))
    }));
  });

  // Update local state when context changes
  useEffect(() => {
    setActiveModule(module_id);
    setActiveContent(content_id);
    
    // Ensure the module is expanded when switching to it
    if (!expandedModules.includes(module_id)) {
      setExpandedModules(prev => [...prev, module_id]);
    }
  }, [module_id, content_id, expandedModules]);

  // Handle module toggle
  const toggleModule = (moduleId) => {
    setExpandedModules(prev => 
      prev.includes(moduleId) 
        ? prev.filter(id => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  // Select content - this is the main function that makes the sidebar functional
  const handleContentChange = (moduleId, contentId) => {
    setActiveModule(moduleId);
    setActiveContent(contentId);
    // Update the global context for consistency
    setModule_id(moduleId);
    setContent_id(contentId);
  };

  // Find next/previous content
  const findNextContent = () => {
    // Find all content items flattened into a single array
    const allContent = contentData.flatMap(module => 
      module.contents.map(content => ({
        moduleId: module.module_id,
        contentId: content.content_id,
        sequence: content.sequence
      }))
    ).sort((a, b) => {
      // First compare by moduleId
      if (a.moduleId !== b.moduleId) return a.moduleId - b.moduleId;
      // Then by sequence within the module
      return a.sequence - b.sequence;
    });
    
    // Find current content index
    const currentIndex = allContent.findIndex(item => 
      item.moduleId === activeModule && item.contentId === activeContent
    );
    
    // Find next content if available
    if (currentIndex < allContent.length - 1) {
      const nextContent = allContent[currentIndex + 1];
      return nextContent;
    }
    
    return null;
  };
  
  const findPrevContent = () => {
    // Find all content items flattened into a single array
    const allContent = contentData.flatMap(module => 
      module.contents.map(content => ({
        moduleId: module.module_id,
        contentId: content.content_id,
        sequence: content.sequence
      }))
    ).sort((a, b) => {
      // First compare by moduleId
      if (a.moduleId !== b.moduleId) return a.moduleId - b.moduleId;
      // Then by sequence within the module
      return a.sequence - b.sequence;
    });
    
    // Find current content index
    const currentIndex = allContent.findIndex(item => 
      item.moduleId === activeModule && item.contentId === activeContent
    );
    
    // Find previous content if available
    if (currentIndex > 0) {
      const prevContent = allContent[currentIndex - 1];
      return prevContent;
    }
    
    return null;
  };

  // Navigate to next/previous content
  const goToNextContent = () => {
    const nextContent = findNextContent();
    if (nextContent) {
      handleContentChange(nextContent.moduleId, nextContent.contentId);
      // Ensure the module is expanded
      if (!expandedModules.includes(nextContent.moduleId)) {
        setExpandedModules(prev => [...prev, nextContent.moduleId]);
      }
    }
  };
  
  const goToPrevContent = () => {
    const prevContent = findPrevContent();
    if (prevContent) {
      handleContentChange(prevContent.moduleId, prevContent.contentId);
      // Ensure the module is expanded
      if (!expandedModules.includes(prevContent.moduleId)) {
        setExpandedModules(prev => [...prev, prevContent.moduleId]);
      }
    }
  };

  // Mark content as completed
  const markAsCompleted = () => {
    if (!completedItems.includes(activeContent)) {
      setCompletedItems(prev => [...prev, activeContent]);
      
      // Update the content data to reflect completion
      setContentData(prevData => prevData.map(module => ({
        ...module,
        contents: module.contents.map(content => 
          content.content_id === activeContent 
            ? { ...content, completed: true } 
            : content
        )
      })));
      
      // Automatically go to next content
      goToNextContent();
    }
  };

  // Calculate progress
  const totalContents = contentData?.reduce((sum, module) => sum + module.contents.length, 0) || 0;
  const completedContents = contentData?.reduce((sum, module) => 
    sum + module.contents.filter(content => content.completed).length, 0) || 0;
  const progressPercentage = totalContents ? (completedContents / totalContents) * 100 : 0;

  // Find active module and content
  const currentModule = contentData?.find(m => m.module_id === activeModule);
  const currentContent = currentModule?.contents?.find(c => c.content_id === activeContent);

  // Render the appropriate content viewer based on content type
  const renderContentViewer = () => {
    if (!currentContent) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center p-8">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
            <h3 className="mt-2 text-sm font-medium">No content selected</h3>
            <p className="mt-1 text-sm text-gray-500">Please select a content item from the sidebar.</p>
          </div>
        </div>
      );
    }
  
    if (activeTab === 'description') {
      return <DescriptionViewer content={currentContent} />;
    }
  
    // Handle video content (YouTube or regular video)
    if (currentContent.file_type === 'None' && currentContent.link_path?.includes('youtube')) {
      return (
        <div className="content-viewer youtube-viewer">
          <YoutubeEmbed videoId={extractVideoId(currentContent.link_path)} />
        </div>
      );
    }
    
    // Handle PDF content
    if (currentContent.file_type?.includes('pdf')) {
      return (
        <div className="content-viewer pdf-viewer">
          <DocumentViewer filePath={currentContent.file_path || "/sample-pdf.pdf"} />
        </div>
      );
    }
    
    // Handle PowerPoint content
    if (currentContent.file_type?.includes('powerpoint')) {
      return (
        <div className="content-viewer powerpoint-viewer">
          <PresentationViewer filename={currentContent.file_path?.split('/').pop() || 'presentation.pptx'} />
        </div>
      );
    }
    
    // Handle external website content
    if (currentContent.file_type === 'None' && currentContent.link_path && !currentContent.link_path?.includes('youtube')) {
      return (
        <div className="content-viewer web-embed">
          <WebEmbed url={currentContent.link_path} />
        </div>
      );
    }
    
    // Fallback
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <div className="text-center p-8">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium">Unsupported content type</h3>
          <p className="mt-1 text-sm text-gray-500">This content type cannot be displayed.</p>
        </div>
      </div>
    );
  };
  

  return (
    <div className="flex h-screen bg-gray-50 text-gray-800 overflow-hidden flex-col">
        <NewHeader />
        
        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar */}
          <div 
            className="bg-white border-r border-gray-200 flex flex-col transition-all duration-300 overflow-hidden"
            style={{ width: showSidebar ? '280px' : '0px' }}
          >
            {showSidebar && (
              <NewSidebar 
                contentData={contentData}
                activeModule={activeModule}
                activeContent={activeContent}
                expandedModules={expandedModules}
                toggleModule={toggleModule}
                handleContentChange={handleContentChange}
                completedContents={completedContents}
                totalContents={totalContents}
                progressPercentage={progressPercentage}
              />
            )}
          </div>
          
          {/* Sidebar toggle */}
          <button
            className="absolute left-0 top-16 bg-white border border-gray-200 shadow-sm rounded-r-md p-1.5 z-10"
            style={{ left: showSidebar ? '280px' : '0px' }}
            onClick={() => setShowSidebar(!showSidebar)}
          >
            <svg 
              className="w-4 h-4 text-gray-500" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                d={showSidebar ? "M15 19l-7-7 7-7" : "M9 5l7 7-7 7"}
              />
            </svg>
          </button>
          
          {/* Main content area */}
          <div className="flex-1 flex flex-col h-full overflow-hidden relative">
            {/* Content viewer */}
            <div 
              className="flex-1 overflow-hidden"
              style={{ marginRight: showDetails ? '320px' : '0', transition: 'margin-right 0.3s ease-out' }}
            >
              <div className="bg-white h-full flex flex-col overflow-hidden">
                <ContentStructure 
                  currentContent={currentContent}
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                  renderContent={() => renderContentViewer()}
                  findPrevContent={findPrevContent}
                  findNextContent={findNextContent}
                  goToPrevContent={goToPrevContent}
                  goToNextContent={goToNextContent}
                  markAsCompleted={markAsCompleted}
                />
              </div>
            </div>
            
            {/* Info panel toggle */}
            <button
              className="absolute right-0 top-4 bg-white border border-gray-200 shadow-sm rounded-l-md px-2 py-3 flex items-center z-20"
              style={{ right: showDetails ? '320px' : '0' }}
              onClick={() => setShowDetails(!showDetails)}
            >
              <InformationCircleIcon className="w-4 h-4 text-blue-600 mr-1" />
              <span className="text-xs font-medium text-gray-700">Info</span>
              <ChevronLeftIcon 
                className={`w-4 h-4 text-gray-500 ml-1 transition-transform ${showDetails ? 'rotate-180' : ''}`} 
              />
            </button>
    
            {/* Info panel */}
            <div 
              className="absolute right-0 top-0 bottom-0 bg-white border-l border-gray-200 transition-all duration-300 overflow-hidden z-10"
              style={{ width: '320px', transform: showDetails ? 'translateX(0)' : 'translateX(100%)' }}
            >
              <NewInfoPanel 
                currentModule={currentModule} 
                currentContent={currentContent} 
              />
            </div>
          </div>
        </div>
    </div>
  );

};
const ContentStructure = ({ 
  currentContent, 
  activeTab, 
  setActiveTab, 
  renderContent, 
  findPrevContent,
  findNextContent,
  goToPrevContent,
  goToNextContent,
  markAsCompleted 
}) => {
  return (
    <div className="flex flex-col h-full">
      {/* Content header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-white">
        <h1 className="text-lg font-medium text-gray-900">{currentContent?.content_name || 'Select a content'}</h1>
        {currentContent && (
          <div className="flex items-center mt-1 text-xs text-gray-500">
            <span>Last updated: {formatDate(currentContent.last_update)}</span>
            <span className="mx-2">•</span>
            <span>
              {currentContent.topics.split(',').map((topic, i) => (
                <span key={i} className="inline-block px-2 py-0.5 bg-gray-100 rounded mr-1 mb-1">
                  {topic.trim()}
                </span>
              ))}
            </span>
          </div>
        )}
      </div>

      {/* Content tabs */}
      {currentContent && (
        <div className="px-4 pt-4 border-b border-gray-200 bg-white">
          <div className="flex space-x-1">
            <button
              className={`px-3 py-1.5 text-xs font-medium rounded-t-md transition-colors ${
                activeTab === 'content' 
                  ? 'bg-white border-t border-l border-r border-gray-200 text-blue-700' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              onClick={() => setActiveTab('content')}
            >
              Content
            </button>
            <button
              className={`px-3 py-1.5 text-xs font-medium rounded-t-md transition-colors ${
                activeTab === 'description' 
                  ? 'bg-white border-t border-l border-r border-gray-200 text-blue-700' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              onClick={() => setActiveTab('description')}
            >
              Description
            </button>
          </div>
        </div>
      )}
      
      {/* Content body - scrollable area */}
      <div className="flex-1 overflow-auto p-6">
        {renderContent()}
      </div>
      
      {/* Content footer - sticky at bottom */}
      <div className="sticky bottom-0 px-6 py-3 border-t border-gray-200 flex justify-between items-center bg-white shadow-md z-10">
        <button 
          className={`px-3 py-1.5 ${findPrevContent() ? 'bg-gray-100 hover:bg-gray-200 text-gray-700' : 'bg-gray-50 text-gray-400 cursor-not-allowed'} rounded text-xs font-medium flex items-center`}
          onClick={goToPrevContent}
          disabled={!findPrevContent()}
        >
          <ChevronLeftIcon className="h-3 w-3 mr-1" />
          Previous
        </button>
        
        {currentContent && (
          <div className="text-xs">
            {currentContent.completed ? (
              <span className="text-green-600 font-medium">✓ Completed</span>
            ) : (
              <button 
                className="px-3 py-1.5 bg-green-100 hover:bg-green-200 text-green-800 rounded text-xs font-medium"
                onClick={markAsCompleted}
              >
                Mark as completed
              </button>
            )}
          </div>
        )}
        
        <button 
          className={`px-3 py-1.5 ${findNextContent() ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-100 text-blue-400 cursor-not-allowed'} rounded text-xs font-medium flex items-center`}
          onClick={goToNextContent}
          disabled={!findNextContent()}
        >
          Next
          <ChevronRightIcon className="h-3 w-3 ml-1" />
        </button>
      </div>
    </div>
  );
};

export default NewContentPanel;