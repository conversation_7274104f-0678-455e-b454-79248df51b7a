"use client";
import { Fragment, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Menu, Transition } from "@headlessui/react";
import {
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from "@heroicons/react/24/outline";

// Navigation items
const navigation = [
  { name: "Home", href: "/dashboard/roles" },
  { name: "Assessments", href: "/dashboard/roles/assessment" },
  { name: "Learning Plan", href: "/dashboard/roles/learning_plan" }
];

function classNames(...classes: any) {
  return classes.filter(Boolean).join(" ");
}

export default function NewHeader() {
  const [activeItem, setActiveItem] = useState("Learning Plan");
  const router = useRouter();

  const handleLogout = () => {
    // Your logout logic here
    router.replace("/login");
  };

  const handleItemClick = (itemName: string, href: string, e: React.MouseEvent) => {
    e.preventDefault();
    setActiveItem(itemName);
    router.push(href);
  };

  return (
    <header className="bg-white border-b border-gray-200">
      <div className="mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-14 items-center justify-between">
          {/* Logo and Nav Links */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Image
                className="h-8 w-auto"
                src="/logo_colour.png"
                alt="Company Logo"
                width={40}
                height={40}
              />
            </div>
            <nav className="ml-6 hidden md:flex space-x-2">
              {navigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  onClick={(e) => handleItemClick(item.name, item.href, e)}
                  className={classNames(
                    item.name === activeItem
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                    'px-3 py-2 rounded-md text-sm font-medium transition-colors'
                  )}
                >
                  {item.name}
                </a>
              ))}
            </nav>
          </div>

          {/* User Menu */}
          <div className="flex items-center">
            <Menu as="div" className="relative ml-3">
              <div>
                <Menu.Button className="flex rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <UserCircleIcon className="h-8 w-8 text-gray-500" />
                </Menu.Button>
              </div>
              <Transition
                as={Fragment}
                enter="transition ease-out duration-200"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <Menu.Item>
                    {({ active }) => (
                      <a
                        href="/userProfile"
                        className={classNames(
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-4 py-2 text-sm text-gray-700'
                        )}
                      >
                        <UserCircleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                        Your Profile
                      </a>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <a
                        href="/settings"
                        className={classNames(
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-4 py-2 text-sm text-gray-700'
                        )}
                      >
                        <Cog6ToothIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                        Settings
                      </a>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={handleLogout}
                        className={classNames(
                          active ? 'bg-gray-100' : '',
                          'flex w-full items-center px-4 py-2 text-sm text-gray-700'
                        )}
                      >
                        <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                        Sign out
                      </button>
                    )}
                  </Menu.Item>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>
    </header>
  );
}