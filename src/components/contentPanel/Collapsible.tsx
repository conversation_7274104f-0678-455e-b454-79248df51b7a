import { useState, useEffect } from 'react';
import {
    ChevronDownIcon,
    ChevronUpIcon,
    VideoCameraIcon,
    DocumentTextIcon,
    DocumentChartBarIcon,
    TableCellsIcon,
    PresentationChartBarIcon,
    QuestionMarkCircleIcon, 
    LinkIcon,
    CheckIcon
} from "@heroicons/react/24/outline";
import { motion } from 'framer-motion';
import { useModuleContentCounterStore } from '@/utils/moduleContent';

// Simple progress indicator for modules
const ModuleProgressIndicator = ({ percentage }) => {
    return (
        <div className="flex items-center space-x-1">
            <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                <div 
                    className="h-full bg-blue-500 rounded-full" 
                    style={{ width: `${percentage}%` }}
                />
            </div>
            <span className="text-xs text-gray-500">{Math.round(percentage)}%</span>
        </div>
    );
};

// Compact status indicator for content items
const ContentStatusIndicator = ({ completed, sequence }) => {
    return completed ? (
        <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
            <CheckIcon className="h-3 w-3 text-white" />
        </div>
    ) : (
        <div className="w-5 h-5 border border-gray-300 rounded-full flex items-center justify-center">
            <span className="text-xs text-gray-500">{sequence}</span>
        </div>
    );
};

const Collapsible = ({ data, number, incomingContentId }) => {
    const [isOpen, setIsOpen] = useState(false);
    const { content_id, setContent_id } = useModuleContentCounterStore();
    const [selectedContentID, setSelectedContentID] = useState(
        Number.isNaN(content_id) ? incomingContentId : content_id
    );

    useEffect(() => {
        if (!(Number.isNaN(content_id))) {
            setSelectedContentID(content_id);
        }
    }, [content_id]);

    const handleContentChange = (contentId) => {
        console.log("Selected content ID:", contentId);
        setContent_id(contentId);
    };

    // Calculate module completion percentage
    const calculateCompletionPercentage = (data) => {
        const totalItems = data.contents.length;
        const completedCount = data.contents.filter(content => content.completed).length;
        return totalItems > 0 ? (completedCount / totalItems) * 100 : 0;
    };

    // Sort the contents by sequence in ascending order
    const sortedContents = data?.contents.sort((a, b) => a.sequence - b.sequence);
    
    // Get completion percentage for this module
    const completionPercentage = calculateCompletionPercentage(data);

    return (
        <div className="mb-2 overflow-hidden rounded-md border border-gray-200 bg-white">
            <div
                className={`flex items-center px-3 py-2 cursor-pointer transition-colors
                    ${isOpen ? 'bg-blue-50 border-b border-gray-200' : 'hover:bg-gray-50'}`}
                onClick={() => setIsOpen(!isOpen)}
            >
                <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-800">{data?.module_name}</h3>
                    <div className="mt-1">
                        <ModuleProgressIndicator percentage={completionPercentage} />
                    </div>
                </div>
                <span className="ml-2 text-gray-500">
                    {isOpen ? (
                        <ChevronUpIcon className="h-4 w-4" />
                    ) : (
                        <ChevronDownIcon className="h-4 w-4" />
                    )}
                </span>
            </div>
            
            <motion.div
                initial="closed"
                animate={isOpen ? 'open' : 'closed'}
                variants={{ 
                    open: { opacity: 1, height: 'auto' },
                    closed: { opacity: 0, height: 0 } 
                }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden bg-gray-50"
            >
                {sortedContents?.map((item) => (
                    <button
                        key={item.content_id}
                        className={`flex items-center w-full py-2 px-3 text-left transition-colors
                            ${selectedContentID === item.content_id 
                                ? "bg-blue-100 text-blue-800" 
                                : "hover:bg-gray-100 text-gray-700"}`}
                        onClick={() => handleContentChange(item.content_id)}
                    >
                        <ContentStatusIndicator 
                            completed={item?.completed} 
                            sequence={item.sequence} 
                        />
                        
                        <div className="ml-3 flex-1">
                            <span className="text-xs font-medium truncate block">{item.content_name}</span>
                        </div>
                        
                        <div className="flex items-center ml-2">
                            {item?.file_type?.includes("pdf") && 
                                <DocumentTextIcon className="h-3 w-3 text-gray-500" />}
                            {item?.file_type?.includes("mp4") && 
                                <VideoCameraIcon className="h-3 w-3 text-gray-500" />}
                            {item?.file_type?.includes("vnd.ms-powerpoint") && 
                                <PresentationChartBarIcon className="h-3 w-3 text-gray-500" />}
                            {item?.file_type?.includes("sheet") && 
                                <TableCellsIcon className="h-3 w-3 text-gray-500" />}
                            {(item?.file_type?.includes("None") && item?.link_path?.includes("youtube")) && (
                                <VideoCameraIcon className="h-3 w-3 text-gray-500" />
                            )}
                            {(item?.file_type?.includes("None") && !item?.link_path?.includes("youtube")) && (
                                <LinkIcon className="h-3 w-3 text-gray-500" />
                            )}
                            {!item?.file_type && 
                                <QuestionMarkCircleIcon className="h-3 w-3 text-gray-500" />}
                        </div>
                    </button>
                ))}
            </motion.div>
        </div>
    );
};

export default Collapsible;