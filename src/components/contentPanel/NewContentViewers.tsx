import { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

// Document viewer for PDF content types - optimized for scrollable content area
export const DocumentViewer = ({ filePath = "/sample-pdf.pdf" }) => (
  <div className="h-full flex flex-col overflow-hidden">
    <div className="bg-gray-100 border-b border-gray-200 px-3 py-2 flex items-center">
      <svg className="h-4 w-4 text-gray-500 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
      <span className="text-xs text-gray-700">{filePath.split('/').pop()}</span>
    </div>
    <div className="flex-1 bg-gray-100">
      <iframe 
        src={filePath}
        className="w-full h-full border-0"
        title="PDF Document"
      />
    </div>
  </div>
);

// Youtube embed component - optimized for scrollable content area
export const YoutubeEmbed = ({ videoId }) => (
  <div className="h-full flex flex-col">
    <div className="bg-gray-100 border-b border-gray-200 px-3 py-2">
      <span className="text-xs text-gray-700">YouTube Video</span>
    </div>
    <div className="flex-1" style={{ minHeight: '500px', height: '500px' }}>
      <iframe 
        className="w-full h-full"
        src={`https://www.youtube.com/embed/${videoId}?rel=0`}
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        title="YouTube Video"
      ></iframe>
    </div>
  </div>
);

// Web embed for external website links - optimized for scrollable content area
export const WebEmbed = ({ url }) => (
  <div className="h-full flex flex-col">
    <div className="bg-gray-100 border-b border-gray-200 px-3 py-2 flex items-center justify-between">
      <span className="text-xs text-gray-700">External Website</span>
      <a 
        href={url} 
        target="_blank" 
        rel="noopener noreferrer"
        className="text-xs text-blue-600 hover:underline"
      >
        Open in new tab
      </a>
    </div>
    <div className="flex-1">
      <iframe 
        src={url} 
        className="w-full h-full border-0"
        title="External Website"
        sandbox="allow-scripts allow-same-origin allow-forms"
      ></iframe>
    </div>
  </div>
);

// Presentation viewer for PowerPoint files - optimized for scrollable content area
export const PresentationViewer = ({ filename = "presentation.pptx" }) => {
  const [slideIndex, setSlideIndex] = useState(1);
  const totalSlides = 10; // Mock value - in a real app would be determined from the file
  
  const handlePrevSlide = () => {
    setSlideIndex(prev => Math.max(1, prev - 1));
  };
  
  const handleNextSlide = () => {
    setSlideIndex(prev => Math.min(totalSlides, prev + 1));
  };
  
  // Constructing a URL for the Office Online viewer
  // In a real app, you would need to use a publicly accessible URL
  const officeOnlineUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent('https://www.cmu.edu/teaching/designteach/teach/instructionalstrategies/lectures/sampleslides.ppt')}`;
  
  return (
    <div className="h-full flex flex-col">
      <div className="bg-gray-100 border-b border-gray-200 px-3 py-2 flex items-center justify-between">
        <span className="text-xs text-gray-700">{filename}</span>
        <div className="flex items-center space-x-2 text-xs text-gray-600">
          <span>Slide {slideIndex} of {totalSlides}</span>
        </div>
      </div>
      <div className="flex-1 relative bg-gray-50">
        <iframe 
          src={officeOnlineUrl}
          className="w-full h-full border-0"
          title="PowerPoint Presentation"
        ></iframe>
        
        {/* Navigation controls overlaid on the presentation */}
        <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-4">
          <button 
            className="px-3 py-1.5 bg-gray-800 bg-opacity-70 hover:bg-opacity-90 text-white rounded text-xs font-medium backdrop-blur-sm flex items-center"
            onClick={handlePrevSlide}
            disabled={slideIndex <= 1}
          >
            <ChevronLeftIcon className="h-3 w-3 mr-1" /> Previous
          </button>
          <button 
            className="px-3 py-1.5 bg-gray-800 bg-opacity-70 hover:bg-opacity-90 text-white rounded text-xs font-medium backdrop-blur-sm flex items-center"
            onClick={handleNextSlide}
            disabled={slideIndex >= totalSlides}
          >
            Next <ChevronRightIcon className="h-3 w-3 ml-1" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Description viewer - for showing content descriptions
export const DescriptionViewer = ({ content }) => (
  <div className="prose max-w-none">
    <p className="text-gray-700 mb-4">
      {content.content_description}
    </p>
    
    {content.link_path && (
      <div className="p-4 bg-blue-50 rounded-lg border border-blue-100">
        <h3 className="text-sm font-medium text-blue-800 mb-2">External Resource</h3>
        <a 
          href={content.link_path} 
          target="_blank" 
          rel="noopener noreferrer" 
          className="text-blue-600 hover:underline break-all"
        >
          {content.link_path}
        </a>
      </div>
    )}
  </div>
);

// Helper function to extract YouTube video ID
export const extractVideoId = (url) => {
  if (!url) return null;
  const regex = /(?:https?:\/\/(?:www\.)?youtube\.com\/(?:v|watch)\?v=|(?:https?:\/\/(?:www\.)?youtu\.be\/))([a-zA-Z0-9_-]{11})/;
  const match = url?.match(regex);
  return match ? match[1] : null;
};

// Format date for display
export const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};