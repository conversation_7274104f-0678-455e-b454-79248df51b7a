import { ChevronRightIcon, CheckIcon } from "@heroicons/react/24/outline";

// Helper function to get file type icon
const getFileTypeIcon = (fileType) => {
  if (!fileType || fileType === "None") {
    return (
      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path strokeLinecap="round" strokeLinejoin="round" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M10.172 13.828a4 4 0 005.656 0l4-4a4 4 0 10-5.656-5.656l-1.102 1.101" />
      </svg>
    );
  }
  
  if (fileType.includes("pdf")) {
    return (
      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    );
  }
  
  if (fileType.includes("video")) {
    return (
      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
      </svg>
    );
  }
  
  if (fileType.includes("powerpoint")) {
    return (
      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 13v-1m4 1v-3m4 3V8M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
      </svg>
    );
  }
  
  return (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  );
};

const NewSidebar = ({ 
  contentData, 
  activeModule, 
  activeContent, 
  expandedModules, 
  toggleModule, 
  handleContentChange, 
  completedContents, 
  totalContents, 
  progressPercentage 
}) => {
  return (
    <>
      <div className="p-3 border-b border-gray-200 flex items-center justify-between">
        <h2 className="text-sm font-medium text-gray-800">{contentData?.[0]?.group_name || 'Course'} Modules</h2>
        <div className="flex items-center space-x-2">
          <div className="w-24 h-1.5 bg-gray-200 rounded-full overflow-hidden">
            <div className="h-full bg-blue-500 rounded-full" style={{ width: `${progressPercentage}%` }}></div>
          </div>
          <span className="text-xs text-gray-500">{completedContents}/{totalContents}</span>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto p-2">
        {contentData?.map((module) => (
          <div key={module.module_id} className="mb-2 overflow-hidden rounded-md border border-gray-200 bg-white">
            <div
              className={`flex items-center px-3 py-2 cursor-pointer transition-colors
                  ${module.module_id === activeModule ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}`}
              onClick={() => toggleModule(module.module_id)}
            >
              <div className="flex-1">
                <h3 className="text-sm font-medium">{module.module_name}</h3>
                <p className="text-xs text-gray-500 mt-0.5 truncate">{module.module_headline}</p>
              </div>
              <ChevronRightIcon 
                className={`w-4 h-4 text-gray-500 transform transition-transform ${expandedModules.includes(module.module_id) ? 'rotate-90' : ''}`}
              />
            </div>
            
            {expandedModules.includes(module.module_id) && (
              <div className="bg-gray-50">
                {module.contents.map((content) => (
                  <button
                    key={content.content_id}
                    className={`w-full text-left px-3 py-2 flex items-center text-xs ${
                      content.content_id === activeContent 
                        ? "bg-blue-100 text-blue-800" 
                        : "hover:bg-gray-100 text-gray-700"
                    }`}
                    onClick={() => handleContentChange(module.module_id, content.content_id)}
                  >
                    <div className="w-5 h-5 mr-3 flex-shrink-0">
                      {content.completed ? (
                        <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                          <CheckIcon className="w-3 h-3 text-white" />
                        </div>
                      ) : (
                        <div className="w-5 h-5 border border-gray-300 rounded-full flex items-center justify-center">
                          <span className="text-xs text-gray-500">{content.sequence}</span>
                        </div>
                      )}
                    </div>
                    
                    <span className="flex-1 truncate">{content.content_name}</span>
                    
                    <div className="ml-2 text-gray-500">
                      {getFileTypeIcon(content.file_type)}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
      
      <div className="p-3 border-t border-gray-200">
        <button
          className="w-full bg-blue-600 text-white text-xs font-medium py-2 px-4 rounded hover:bg-blue-700 transition-colors"
          onClick={() => console.log("Navigate to dashboard")}
        >
          Back to Dashboard
        </button>
      </div>
    </>
  );
};

export default NewSidebar;