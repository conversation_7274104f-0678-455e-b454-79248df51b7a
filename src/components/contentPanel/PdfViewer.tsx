import React, { useState, useEffect, useRef } from 'react';
import PDFObject from 'pdfobject';

const PdfViewer = ({ file }) => {
    const [pageNumber, setPageNumber] = useState(1); // Start at page 1
    const [width, setWidth] = useState(window.innerWidth / 1.6);
    const [height, setHeight] = useState(400); // Initial height
    const [fileURL, setFileURL] = useState(null); // URL for the file
    const pdfContainerRef = useRef(null);

    useEffect(() => {
        // Generate a URL for the file
        if (file) {
            const url = URL.createObjectURL(file);
            setFileURL(url);

            // Clean up the URL when the component unmounts or the file changes
            return () => {
                URL.revokeObjectURL(url);
            };
        }
    }, [file]);

    useEffect(() => {
        if (fileURL) {
            // Embed the PDF using pdfobject.js
            PDFObject.embed(fileURL, pdfContainerRef.current, {
                width: `${width}px`,
                height: `${height}px`,
                pdfOpenParams: {
                    page: pageNumber,
                },
            });
        }

        // Update width on window resize
        const handleResize = () => {
            setWidth(window.innerWidth);
        };

        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [fileURL, width, height, pageNumber]);

    const handleNextPage = () => {
        setPageNumber(prevPage => prevPage + 1);
    };

    const handlePrevPage = () => {
        setPageNumber(prevPage => Math.max(1, prevPage - 1)); // Prevent going below page 1
    };

    const handleZoomIn = () => {
        setHeight(prevHeight => prevHeight * 1.1); // Increase height by 10%
    };

    const handleZoomOut = () => {
        setHeight(prevHeight => Math.max(100, prevHeight * 0.9)); // Decrease, but keep a minimum
    };

    return (
        <div className="w-full h-full flex flex-col justify-center items-center">
            <div ref={pdfContainerRef} style={{ width: `${width}px`, height: `${height}px` }}></div>
            <div className="mt-4">
                <button onClick={handlePrevPage} className="mr-2 p-2 bg-blue-500 text-white rounded">
                    Previous Page
                </button>
                <button onClick={handleNextPage} className="p-2 bg-blue-500 text-white rounded">
                    Next Page
                </button>
                <button onClick={handleZoomIn} className="ml-2 p-2 bg-green-500 text-white rounded">
                    Zoom In
                </button>
                <button onClick={handleZoomOut} className="ml-2 p-2 bg-red-500 text-white rounded">
                    Zoom Out
                </button>
            </div>
        </div>
    );
};

export default PdfViewer;