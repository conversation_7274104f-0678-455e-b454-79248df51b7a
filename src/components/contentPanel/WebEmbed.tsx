// import React from "react";
// import PropTypes from "prop-types";

// const WebEmbed = ({ url, height, width }) => {
//   const handleIframeLoad = () => {
//     console.log("Iframe loaded successfully");
//   };

//   const handleIframeError = () => {
//     console.error("Error loading iframe content");
//   };

//   return (
//     <div className="web-embed-responsive">
//       <iframe
//         id="test"
//         style={{
//           width: width,  // Corrected: width prop for width
//           height: height, // Corrected: height prop for height
//         }}
//         src={url}
//         onLoad={handleIframeLoad}
//         onError={handleIframeError}
//       />
//     </div>
//   );
// };

// WebEmbed.propTypes = {
//   url: PropTypes.string.isRequired,
//   height: PropTypes.string,
//   width: PropTypes.string,
// };

// WebEmbed.defaultProps = {
//   height: "500px",
//   width: "100%",
// };

// export default WebEmbed;

import React from "react";
import PropTypes from "prop-types";

const WebEmbed = ({ url, height, width }) => {
  const handleIframeLoad = () => {
    console.log("Iframe loaded successfully");
  };

  const handleIframeError = () => {
    console.error("Error loading iframe content");
  };

  return (
    <div className="rounded-lg overflow-hidden shadow-sm border border-gray-200">
      <iframe
        style={{
          width: width,
          height: height,
        }}
        src={url}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        className="w-full h-full"
      />
    </div>
  );
};

WebEmbed.propTypes = {
  url: PropTypes.string.isRequired,
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

WebEmbed.defaultProps = {
  height: 500,
  width: "100%",
};

export default WebEmbed;