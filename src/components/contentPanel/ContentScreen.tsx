import { useState, useEffect } from 'react';
import { useModuleContentCounterStore } from '@/utils/moduleContent';
import { simulateGetAllContentInSlide, dummyUserProgress } from '@/utils/dummyData/contentPanelData';
import WebEmbed from './WebEmbed';
import YoutubeEmbed from './youtubeEmbed';
import { PlayIcon, DocumentTextIcon, ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

// Mock VideoPlayer component
const VideoPlayer = ({ inputUrl, inputWidth, inputHeight, options, onVideoEnd, setvideoProgress, videoProgress, handleVideoSave }) => (
  <div className="flex flex-col h-full w-full">
    <div className="relative aspect-video bg-gray-900 rounded-lg overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900"></div>
      <div className="absolute inset-0 flex items-center justify-center">
        <button className="bg-white/20 backdrop-blur-sm w-16 h-16 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors">
          <PlayIcon className="h-8 w-8 text-white" />
        </button>
      </div>
      <div className="absolute bottom-0 left-0 right-0 bg-black/50 backdrop-blur-sm text-white text-xs p-2">
        <div className="mb-1">Video URL: {inputUrl}</div>
        <div className="w-full bg-gray-700 rounded-full h-1 mb-2">
          <div className="bg-blue-500 h-1 rounded-full" style={{ width: `${(videoProgress/100)*100}%` }}></div>
        </div>
        <div className="flex justify-between">
          <div>Current progress: {videoProgress}s</div>
          <div>Duration: 100s</div>
        </div>
      </div>
    </div>
    <div className="flex space-x-2 mt-3">
      <button className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded text-xs font-medium" onClick={() => setvideoProgress(Math.max(0, videoProgress - 10))}>
        -10s
      </button>
      <button className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded text-xs font-medium" onClick={() => setvideoProgress(videoProgress + 10)}>
        +10s
      </button>
      <button className="flex-1 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 rounded text-xs font-medium text-white" onClick={handleVideoSave}>
        Save Progress
      </button>
      <button className="px-3 py-1.5 bg-green-600 hover:bg-green-700 rounded text-xs font-medium text-white" onClick={onVideoEnd}>
        Complete Video
      </button>
    </div>
  </div>
);

// Mock PDF Viewer
const DocViewer = ({ pluginRenderers, documents, style }) => (
  <div className="h-full flex flex-col">
    <div className="bg-gray-100 border-b border-gray-200 px-3 py-2 flex items-center">
      <DocumentTextIcon className="h-4 w-4 text-gray-500 mr-2" />
      <span className="text-xs text-gray-700">
        {documents[0]?.uri.split('/').pop() || 'Document'}
      </span>
    </div>
    <div className="flex-1 bg-gray-50 p-4">
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm h-full flex flex-col">
        <div className="p-6">
          <div className="w-full h-8 bg-gray-200 rounded mb-4"></div>
          <div className="w-3/4 h-4 bg-gray-200 rounded mb-2"></div>
          <div className="w-full h-4 bg-gray-200 rounded mb-2"></div>
          <div className="w-5/6 h-4 bg-gray-200 rounded mb-4"></div>
          <div className="w-full h-36 bg-gray-200 rounded mb-4"></div>
          <div className="w-full h-4 bg-gray-200 rounded mb-2"></div>
          <div className="w-4/5 h-4 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
    <div className="bg-gray-100 border-t border-gray-200 px-4 py-2 flex justify-between">
      <button className="px-3 py-1.5 bg-gray-200 hover:bg-gray-300 rounded text-xs font-medium">
        <ChevronLeftIcon className="h-3 w-3 inline mr-1" /> Previous
      </button>
      <span className="text-xs flex items-center text-gray-600">Page 1 of 10</span>
      <button className="px-3 py-1.5 bg-gray-200 hover:bg-gray-300 rounded text-xs font-medium">
        Next <ChevronRightIcon className="h-3 w-3 inline ml-1" />
      </button>
    </div>
  </div>
);

const ContentScreen2 = ({ height, width, incomingContentId, incomingModuleId, incomingGroupId }) => {
  const { content_id } = useModuleContentCounterStore();
  const [contentId, setContentId] = useState(Number.isNaN(content_id) ? incomingContentId : content_id);
  
  // Use the dummy data function instead of API call
  const contentDetails = simulateGetAllContentInSlide(contentId);
  
  // Mock user progress data
  const getUserProgress = {
    video_progress: dummyUserProgress.video_progress
  };

  const [videoProgress, setvideoProgress] = useState(getUserProgress?.video_progress);
  const [docs, setDocs] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isVideoCompleted, setIsVideoCompleted] = useState(false);
  const [isVideoPlayerReady, setIsVideoPlayerReady] = useState(true);
  const [activeTab, setActiveTab] = useState('content');

  useEffect(() => {
    setvideoProgress(getUserProgress?.video_progress);
  }, [contentId]);

  // Mock user information
  const user = {
    user_id: 3
  };

  const [userProgress, setUserProgress] = useState({
    user_id: user?.user_id,
    group_id: parseInt(incomingGroupId),
    module_id: parseInt(incomingModuleId),
    content_id: parseInt(contentId),
    completed: (contentDetails?.file_type === "video/mp4" ? isVideoCompleted : true),
    page_number: 0,
    video_progress: parseFloat(videoProgress),
    total_progress: new Date().toISOString(),
  });

  // Mock progress update function
  const handlePostProgress = async () => {
    console.log("Saving progress...", userProgress);
    // In a real app, this would make an API call
  };

  useEffect(() => {
    const updatedUserProgress = {
      user_id: user?.user_id,
      group_id: parseInt(incomingGroupId),
      module_id: parseInt(incomingModuleId),
      content_id: parseInt(contentId),
      completed: (contentDetails?.file_type === "video/mp4" ? isVideoCompleted : 1),
      page_number: 0,
      video_progress: parseFloat(contentDetails?.file_type === "video/mp4" ? videoProgress : 0),
      total_progress: new Date().toISOString(),
    };

    setUserProgress(updatedUserProgress);
  }, [videoProgress, isVideoCompleted, contentDetails?.file_type, user?.user_id, incomingGroupId, incomingModuleId, contentId]);

  useEffect(() => {
    if (contentDetails?.file_type !== "video/mp4") {
      handlePostProgress();
    }
  }, [userProgress]);

  useEffect(() => {
    if (contentDetails?.file_type !== "video/mp4") {
      handlePostProgress();
    }
  }, [content_id]);

  useEffect(() => {
    if (contentDetails?.file_type === 'application/pdf') {
      // Simulate loading a PDF
      setLoading(true);
      
      setTimeout(() => {
        const mockPdfUrl = "mock://example.pdf";
        setDocs([{ uri: mockPdfUrl, fileType: 'pdf' }]);
        setLoading(false);
      }, 500);
    }
  }, [contentDetails?.file_path]);

  const handleVideoEnded = () => {
    setIsVideoCompleted(true);
    setvideoProgress(0);
    handlePostProgress();
  };

  const handleVideoSave = () => {
    console.log("Video progress saved:", videoProgress);
    handlePostProgress();
  };

  const extractVideoId = (url) => {
    if (!url) return null;
    const regex = /(?:https?:\/\/(?:www\.)?youtube\.com\/(?:v|watch)\?v=|(?:https?:\/\/(?:www\.)?youtu\.be\/))([a-zA-Z0-9_-]{11})/;
    const match = url?.match(regex);
    return match ? match[1] : null;
  };

  const renderYoutubeEmbed = () => {
    const videoId = extractVideoId(contentDetails?.link_path || '');
    return (
      <div className="w-full h-full flex flex-col">
        <div className="bg-gray-100 border-b border-gray-200 px-3 py-2">
          <span className="text-xs text-gray-700">YouTube Video</span>
        </div>
        <div className="flex-1 flex items-center justify-center p-4">
          <YoutubeEmbed embedId={videoId} height={height/1.5} width={width/1.5} />
        </div>
      </div>
    );
  };

  const renderWebEmbed = () => (
    <div className="w-full h-full flex flex-col">
      <div className="bg-gray-100 border-b border-gray-200 px-3 py-2">
        <span className="text-xs text-gray-700">External Website</span>
      </div>
      <div className="flex-1 flex items-center justify-center p-4">
        <WebEmbed url={contentDetails?.link_path || ''} height={height/1.5} width={width/1.5} />
      </div>
    </div>
  );

  const fileType = contentDetails?.file_type;
  
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (!fileType) return (
    <div className="h-full w-full flex items-center justify-center">
      <div className="text-gray-500 animate-pulse">Loading content...</div>
    </div>
  );

  const renderContentTabs = () => (
    <div className="px-4 pt-4 border-b border-gray-200">
      <div className="flex space-x-1">
        <button
          className={`px-3 py-1.5 text-xs font-medium rounded-t-md transition-colors ${
            activeTab === 'content' 
              ? 'bg-white border-t border-l border-r border-gray-200 text-blue-700' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => setActiveTab('content')}
        >
          Content
        </button>
        <button
          className={`px-3 py-1.5 text-xs font-medium rounded-t-md transition-colors ${
            activeTab === 'description' 
              ? 'bg-white border-t border-l border-r border-gray-200 text-blue-700' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => setActiveTab('description')}
        >
          Description
        </button>
      </div>
    </div>
  );

  return (
    <div className="h-full w-full flex flex-col">
      {/* Content header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h1 className="text-lg font-medium text-gray-900">{contentDetails?.content_name}</h1>
        <div className="flex items-center mt-1 text-xs text-gray-500">
          <span>Last updated: {formatDate(contentDetails?.last_update)}</span>
          <span className="mx-2">•</span>
          <span>
            {contentDetails?.topics?.split(',').map((topic, i) => (
              <span key={i} className="inline-block px-2 py-0.5 bg-gray-100 rounded mr-1 mb-1">
                {topic.trim()}
              </span>
            ))}
          </span>
        </div>
      </div>

      {renderContentTabs()}
      
      {/* Content body */}
      <div className="flex-1 p-6 overflow-auto">
        {activeTab === 'content' ? (
          <>
            {fileType === 'video/mp4' && (
              <div className="h-full w-full">
                {isVideoPlayerReady ? (
                  <VideoPlayer
                    inputUrl={`/api/file_content/${contentDetails.file_path?.replace(/uploads[\/\\]/, '')}`}
                    inputWidth={width / 1.2}
                    inputHeight={height / 1.2}
                    options={{ controls: true, autoplay: false }}
                    onVideoEnd={handleVideoEnded}
                    setvideoProgress={setvideoProgress}
                    videoProgress={videoProgress}
                    handleVideoSave={handleVideoSave}
                  />
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-3"></div>
                      <span className="text-gray-600 text-sm">Loading video player...</span>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {fileType === 'application/pdf' && (
              <div className="h-full w-full">
                {loading ? (
                  <div className="h-full flex items-center justify-center">
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-3"></div>
                      <span className="text-gray-600 text-sm">Loading PDF...</span>
                    </div>
                  </div>
                ) : error ? (
                  <div className="text-red-500 p-4">{error}</div>
                ) : (
                  <DocViewer
                    pluginRenderers={[]}
                    documents={docs}
                    style={{
                      width: width / 1.01,
                      height: height / 1.01,
                      overflow: 'auto'
                    }}
                  />
                )}
              </div>
            )}
            
            {fileType?.includes('None') && contentDetails?.link_path?.includes('youtube') && renderYoutubeEmbed()}
            
            {fileType?.includes('None') && !contentDetails?.link_path?.includes('youtube') && renderWebEmbed()}
            
            {fileType?.includes('presentation') && (
              <div className="h-full w-full flex items-center justify-center">
                <div className="bg-gray-50 p-6 rounded-lg text-center max-w-md">
                  <div className="inline-block p-4 rounded-full bg-gray-100 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">PowerPoint Presentation</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    {contentDetails.file_path?.split('/').pop() || 'Presentation.pptx'}
                  </p>
                  <div className="flex justify-center space-x-2">
                    <button className="px-3 py-1.5 bg-blue-100 text-blue-700 hover:bg-blue-200 rounded text-xs font-medium">
                      <ChevronLeftIcon className="h-3 w-3 inline mr-1" /> Previous Slide
                    </button>
                    <button className="px-3 py-1.5 bg-blue-100 text-blue-700 hover:bg-blue-200 rounded text-xs font-medium">
                      Next Slide <ChevronRightIcon className="h-3 w-3 inline ml-1" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="prose max-w-none text-sm">
            <p className="text-gray-700">
              {contentDetails?.content_description || 'No description available for this content.'}
            </p>
            
            {contentDetails?.link_path && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-100">
                <h3 className="text-sm font-medium text-blue-800 mb-1">External Resource</h3>
                <a 
                  href={contentDetails.link_path} 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="text-blue-600 hover:underline text-sm break-all"
                >
                  {contentDetails.link_path}
                </a>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Content footer */}
      <div className="px-6 py-3 border-t border-gray-200 flex justify-between items-center">
        <button className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded text-xs font-medium">
          <ChevronLeftIcon className="h-3 w-3 inline mr-1" /> Previous
        </button>
        <div className="text-xs text-gray-500">
          {contentDetails?.completed ? 
            <span className="text-green-600 font-medium">✓ Completed</span> : 
            <span>In progress</span>
          }
        </div>
        <button className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs font-medium">
          Next <ChevronRightIcon className="h-3 w-3 inline ml-1" />
        </button>
      </div>
    </div>
  );
};

export default ContentScreen2;