// import React from "react";
// import PropTypes from "prop-types";

// const YoutubeEmbed = ({ embedId, height, width }) => (
//   <div className="video-responsive">
//     <iframe
//        style={{  // Directly apply the height
//         width: width / 1.2,    // Apply the width as well
//         height: height / 1.2
//       }}
//       src={`https://www.youtube.com/embed/${embedId}`}
//       frameBorder="0"
//       allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
//       allowFullScreen
//       title="Embedded youtube"
//     />
//   </div>
// );

// YoutubeEmbed.propTypes = {
//   embedId: PropTypes.string.isRequired
// };

// export default YoutubeEmbed;

import React from "react";
import PropTypes from "prop-types";

const YoutubeEmbed = ({ embedId, height, width }) => (
  <div className="rounded-lg overflow-hidden shadow-sm border border-gray-200 bg-black">
    <iframe
      className="w-full aspect-video"
      style={{
        maxWidth: width,
        maxHeight: height,
      }}
      src={`https://www.youtube.com/embed/${embedId}`}
      frameBorder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowFullScreen
      title="Embedded youtube"
    />
  </div>
);

YoutubeEmbed.propTypes = {
  embedId: PropTypes.string.isRequired
};

export default YoutubeEmbed;