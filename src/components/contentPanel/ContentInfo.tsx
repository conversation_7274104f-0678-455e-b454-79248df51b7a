import { useState, useEffect } from 'react';
import { useModuleContentCounterStore } from '@/utils/moduleContent';
import {
  IdentificationIcon,
  InformationCircleIcon,
  CalendarIcon,
  DocumentTextIcon,
  TagIcon
} from "@heroicons/react/24/outline";

interface ContentInfoProps {
  contentInfo: any;
}

const ContentInfo = ({ contentInfo }: ContentInfoProps) => {
  const [selectedTab, setSelectedTab] = useState('1');
  const { content_id, module_id } = useModuleContentCounterStore();
  const [extracted, setExtracted] = useState("");

  const handleTabClick = (tab: string) => {
    setSelectedTab(tab);
  };

  const filterDataForContentInfo = (data: any, moduleId: number, contentId: number) => {
    return data
      ?.filter(module => module.module_id === moduleId)
      .map(module => ({
        ...module,
        contents: module.contents.filter(content => content.content_id === contentId)
      }));
  };

  const contentInfoData = filterDataForContentInfo(contentInfo, module_id > 1 ? module_id : 1, content_id > 1 ? content_id : 1);

  const makeDateReadable = (dateValue: string) => {
    let date = new Date(dateValue);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const extractYouTubeLink = (inputString: string) => {
    if (!inputString) return null;
    const regex = /(https?:\/\/www\.youtube\.com\/watch\?v=[\w-]+)/;
    const match = inputString.match(regex);
    return match ? match[0] : null;
  };

  useEffect(() => {
    if (contentInfoData) {
      contentInfoData.forEach(module => {
        module.contents.forEach(content => {
          const link = extractYouTubeLink(content.content_description);
          if (link) {
            setExtracted(link);
          }
        });
      });
    }
  }, [contentInfoData]);

  return (
    <div className="h-full w-full flex flex-col">
      {/* Tabs */}
      <div className="flex space-x-1 mb-4">
        <button
          className={`flex items-center gap-1 px-3 py-1.5 rounded-md text-xs font-medium transition-colors
            ${selectedTab === '1' 
              ? 'bg-blue-100 text-blue-800' 
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
          onClick={() => handleTabClick('1')}
        >
          <IdentificationIcon className="h-3 w-3" /> Overview
        </button>
        <button
          className={`flex items-center gap-1 px-3 py-1.5 rounded-md text-xs font-medium transition-colors
            ${selectedTab === '2' 
              ? 'bg-blue-100 text-blue-800' 
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
          onClick={() => handleTabClick('2')}
        >
          <InformationCircleIcon className="h-3 w-3" /> Content Info
        </button>
      </div>
      
      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto text-sm space-y-4">
        {selectedTab === '1' && contentInfoData?.length > 0 ? (
          contentInfoData.map(item => (
            <div key={item.module_id}>
              <div className="bg-gray-50 rounded-md p-3 border border-gray-200">
                <h3 className="text-xs font-medium text-gray-700 mb-2">Group & Module Information</h3>
                <div className="space-y-1.5">
                  <div className="flex">
                    <span className="text-xs font-medium text-gray-500 w-28">Group Name:</span>
                    <span className="text-xs text-gray-800">{item.group_name}</span>
                  </div>
                  <div className="flex">
                    <span className="text-xs font-medium text-gray-500 w-28">Module Name:</span>
                    <span className="text-xs text-gray-800">{item.module_name}</span>
                  </div>
                  <div className="flex">
                    <span className="text-xs font-medium text-gray-500 w-28">Module Heading:</span>
                    <span className="text-xs text-gray-800">{item.module_headline}</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-md p-3 border border-gray-200">
                <h3 className="text-xs font-medium text-gray-700 mb-2">Module Description</h3>
                <p className="text-xs text-gray-700">{item.module_description}</p>
              </div>
            </div>
          ))
        ) : selectedTab === '1' ? (
          <div className="flex justify-center items-center h-40">
            <p className="text-gray-500 text-xs">No module information available</p>
          </div>
        ) : null}

        {selectedTab === '2' && contentInfoData?.length > 0 ? (
          contentInfoData.map(item => (
            <div key={item.module_id}>
              {item.contents?.map(content => (
                <div key={content.content_id} className="space-y-4">
                  <div className="bg-gray-50 rounded-md p-3 border border-gray-200">
                    <h3 className="text-xs font-medium text-gray-700 mb-2">Content Details</h3>
                    <div className="space-y-1.5">
                      <div className="flex">
                        <span className="text-xs font-medium text-gray-500 w-28">Content Name:</span>
                        <span className="text-xs text-gray-800">{content.content_name}</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-xs font-medium text-gray-500 w-28">Description:</span>
                        <span className="text-xs text-gray-800">{content.content_description}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 rounded-md p-3 border border-gray-200">
                    <h3 className="text-xs font-medium text-gray-700 mb-2">Technical Information</h3>
                    <div className="space-y-1.5">
                      <div className="flex items-center">
                        <TagIcon className="h-3 w-3 text-gray-500 mr-2" />
                        <span className="text-xs font-medium text-gray-500 w-24">Topic:</span>
                        <span className="text-xs text-gray-800">{content.topics}</span>
                      </div>
                      <div className="flex items-center">
                        <DocumentTextIcon className="h-3 w-3 text-gray-500 mr-2" />
                        <span className="text-xs font-medium text-gray-500 w-24">File Type:</span>
                        <span className="text-xs text-gray-800">{content.file_type || 'N/A'}</span>
                      </div>
                      <div className="flex items-center">
                        <CalendarIcon className="h-3 w-3 text-gray-500 mr-2" />
                        <span className="text-xs font-medium text-gray-500 w-24">Last Update:</span>
                        <span className="text-xs text-gray-800">{makeDateReadable(content.last_update)}</span>
                      </div>
                    </div>
                  </div>
                  
                  {extracted && (
                    <div className="bg-blue-50 rounded-md p-3 border border-blue-200">
                      <h3 className="text-xs font-medium text-blue-700 mb-2">External Resource</h3>
                      <a 
                        href={extracted} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 hover:underline"
                      >
                        YouTube Video
                      </a>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ))
        ) : selectedTab === '2' ? (
          <div className="flex justify-center items-center h-40">
            <p className="text-gray-500 text-xs">No content information available</p>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default ContentInfo;