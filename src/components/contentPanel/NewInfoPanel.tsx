import { useState } from 'react';

// Helper function to format dates
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const NewInfoPanel = ({ currentModule, currentContent }) => {
  const [activeTab, setActiveTab] = useState('overview');
  
  if (!currentModule || !currentContent) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-sm text-gray-500">Select content to view details</p>
      </div>
    );
  }
  
  return (
    <div className="h-full p-4 overflow-y-auto">
      {/* Tabs */}
      <div className="flex space-x-1 mb-4">
        <button
          className={`px-3 py-1.5 text-xs font-medium rounded-md transition-colors ${
            activeTab === 'overview' 
              ? 'bg-blue-100 text-blue-800' 
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`px-3 py-1.5 text-xs font-medium rounded-md transition-colors ${
            activeTab === 'details' 
              ? 'bg-blue-100 text-blue-800' 
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
          onClick={() => setActiveTab('details')}
        >
          Content Details
        </button>
      </div>
      
      {/* Tab Content */}
      <div className="space-y-4">
        {activeTab === 'overview' ? (
          <>
            <div className="bg-gray-50 rounded-md p-3 border border-gray-200">
              <h3 className="text-xs font-medium text-gray-700 mb-2">Module Information</h3>
              <div className="space-y-1">
                <div className="flex">
                  <span className="text-xs font-medium text-gray-500 w-24">Module Name:</span>
                  <span className="text-xs text-gray-700">{currentModule.module_name}</span>
                </div>
                <div className="flex">
                  <span className="text-xs font-medium text-gray-500 w-24">Headline:</span>
                  <span className="text-xs text-gray-700">{currentModule.module_headline}</span>
                </div>
              </div>
              
              <h4 className="text-xs font-medium text-gray-700 mt-3 mb-1">Description</h4>
              <p className="text-xs text-gray-600">{currentModule.module_description}</p>
            </div>
            
            <div className="bg-blue-50 rounded-md p-3 border border-blue-100">
              <h3 className="text-xs font-medium text-blue-800 mb-2">Content Progress</h3>
              <div className="space-y-2">
                {currentModule.contents.map((content) => (
                  <div 
                    key={content.content_id} 
                    className={`flex items-center px-2 py-1 rounded ${
                      content.content_id === currentContent.content_id 
                        ? 'bg-blue-100' 
                        : ''
                    }`}
                  >
                    <div className="w-4 h-4 mr-2">
                      {content.completed ? (
                        <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                          <svg className="w-2 h-2 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                      ) : (
                        <div className="w-4 h-4 border border-gray-300 rounded-full" />
                      )}
                    </div>
                    <span className="text-xs text-blue-800 truncate">{content.content_name}</span>
                  </div>
                ))}
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="bg-gray-50 rounded-md p-3 border border-gray-200">
              <h3 className="text-xs font-medium text-gray-700 mb-2">Content Details</h3>
              <div className="space-y-1">
                <div className="flex">
                  <span className="text-xs font-medium text-gray-500 w-24">Content Name:</span>
                  <span className="text-xs text-gray-700">{currentContent.content_name}</span>
                </div>
                <div className="flex">
                  <span className="text-xs font-medium text-gray-500 w-24">Last Updated:</span>
                  <span className="text-xs text-gray-700">{formatDate(currentContent.last_update)}</span>
                </div>
                <div className="flex">
                  <span className="text-xs font-medium text-gray-500 w-24">Type:</span>
                  <span className="text-xs text-gray-700">
                    {currentContent.file_type === 'None' 
                      ? 'External Link' 
                      : currentContent.file_type?.split('/')[1] || 'Unknown'}
                  </span>
                </div>
                <div className="flex">
                  <span className="text-xs font-medium text-gray-500 w-24">Status:</span>
                  <span className={`text-xs ${currentContent.completed ? 'text-green-600' : 'text-yellow-600'}`}>
                    {currentContent.completed ? 'Completed' : 'In Progress'}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-md p-3 border border-gray-200">
              <h3 className="text-xs font-medium text-gray-700 mb-2">Description</h3>
              <p className="text-xs text-gray-600">{currentContent.content_description}</p>
            </div>
            
            <div className="bg-gray-50 rounded-md p-3 border border-gray-200">
              <h3 className="text-xs font-medium text-gray-700 mb-2">Topics</h3>
              <div className="flex flex-wrap gap-1">
                {currentContent.topics.split(',').map((topic, i) => (
                  <span 
                    key={i} 
                    className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded text-xs"
                  >
                    {topic.trim()}
                  </span>
                ))}
              </div>
            </div>
            
            {currentContent.link_path && (
              <div className="bg-blue-50 rounded-md p-3 border border-blue-100">
                <h3 className="text-xs font-medium text-blue-800 mb-2">External Resource</h3>
                <a 
                  href={currentContent.link_path} 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="text-xs text-blue-600 hover:underline break-all"
                >
                  {currentContent.link_path}
                </a>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default NewInfoPanel;