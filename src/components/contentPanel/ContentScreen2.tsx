// import { useState, useEffect } from 'react';
// import { useModuleContentCounterStore } from '@/utils/moduleContent';
// import { useGetAllContentInSlide } from '@/hook/content/useGetAllContentInSlide';
// import { getUser } from '@/api/user.localStorage';
// import { usePostTheUserProgress } from '@/hook/content/usePostTheUserProgress';
// import WebEmbed from './WebEmbed';
// import YoutubeEmbed from '@/components/contentPanel/youtubeEmbed';
// import { API_URL } from '@/utils/constants';
// import VideoPlayer from '@/components/videoPlayer/VideoPlayer';
// import { PostUserProgress } from '@/types/LMSTypes';
// import DocViewer, { DocViewerRenderers, DocViewerRef } from '@cyntler/react-doc-viewer';
// import '@cyntler/react-doc-viewer/dist/index.css';
// import { useGetTheUserProgress } from '@/hook/content/useGetTheUserProgress';

// const ContentScreen2 = ({ height, width, incomingContentId, incomingModuleId, incomingGroupId }) => {
//   const { content_id } = useModuleContentCounterStore();
//   const user = getUser();


//   const [contentId, setContentId] = useState(Number.isNaN(content_id) ? incomingContentId : content_id);
//   const { data: contentDetails } = useGetAllContentInSlide(contentId);
//   const { data: getUserProgress } = useGetTheUserProgress(incomingModuleId, incomingGroupId, user?.user_id, contentId)

//   const [videoProgress, setvideoProgress] = useState(getUserProgress?.video_progress)
//   const [docs, setDocs] = useState<{ uri: string; fileType: string }[]>([]);
//   const [error, setError] = useState<string | null>(null);
//   const [loading, setLoading] = useState<boolean>(true);
//   const [isVideoCompleted, setIsVideoCompleted] = useState(false);
//   const [isVideoPlayerReady, setIsVideoPlayerReady] = useState(false); // New state variable

//   console.log("contentIdasa",contentId, content_id)
//   useEffect(() => {
//     setvideoProgress(0)

//     setvideoProgress(getUserProgress?.video_progress)

//   }, [contentId])


//   //To prevent video component unnecessary rerender
//   useEffect(() => {
//     // Set a timeout to update the state after 1 second
//     const timer = setTimeout(() => {
//       setIsVideoPlayerReady(true);
//     }, 700);

//     // Cleanup the timer if the component unmounts
//     return () => clearTimeout(timer);
//   }, []);

//   const fileType = contentDetails?.file_type;

//   const [userProgress, setUserProgress] = useState<PostUserProgress>({
//     user_id: user?.user_id,
//     group_id: parseInt(incomingGroupId),
//     module_id: parseInt(incomingModuleId),
//     content_id: parseInt(contentId),
//     completed: (contentDetails?.file_type === "video/mp4" ? isVideoCompleted : true),
//     page_number: 0,
//     video_progress: parseFloat(videoProgress),
//     total_progress: new Date().toISOString(),
//   });

//   const addUserProgress = usePostTheUserProgress(userProgress);

//   useEffect(() => {
//     const updatedUserProgress = {
//       user_id: user?.user_id,
//       group_id: parseInt(incomingGroupId),
//       module_id: parseInt(incomingModuleId),
//       content_id: parseInt(contentId),
//       completed: (contentDetails?.file_type === "video/mp4" ? isVideoCompleted : 1),
//       page_number: 0,
//       video_progress: parseFloat(contentDetails?.file_type === "video/mp4" ? videoProgress : 0),
//       total_progress: new Date().toISOString(),
//     };

//     setUserProgress(updatedUserProgress);
//   }, [videoProgress, isVideoCompleted, contentDetails?.file_type, user?.user_id, incomingGroupId, incomingModuleId, contentId]);

//   useEffect(() => {
//     if (contentDetails?.file_type !== "video/mp4") {
//       handlePostProgress();
//     }
//   }, [userProgress]);

//   const handlePostProgress = async () => {
//     console.log("saving progress...");
//     if (!contentId) return;

//     try {
//       await addUserProgress.mutate(userProgress);
//     } catch (err) {
//       console.error('Error while submitting progress:', err);
//       setError('Failed to save progress. Please try again.');
//     }
//   };

//   useEffect(() => {
//     // setvideoProgress(0)
//     // setUserProgress({});
//     console.log("saving progress...1");
//     if (contentDetails?.file_type !== "video/mp4") {
//       handlePostProgress();
//     }
//     // setContentId(contentId);
//     if (!Number.isNaN(content_id)) {
//       setContentId(content_id);
//       console.log("saving progress...2");
//     }
//   }, [content_id]);

//   useEffect(() => {
//     if (contentDetails?.file_type === 'application/pdf') {
//       const abortController = new AbortController();

//       const fetchDocument = async () => {
//         setLoading(true);
//         setError(null);

//         try {
//           const modifiedFilePathPDF = contentDetails.file_path.replace(/uploads[\/\\]/, '');
//           const urlPDF = `${API_URL}file_content/${modifiedFilePathPDF}`;

//           const response = await fetch(urlPDF, { signal: abortController.signal });
//           if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

//           const blob = await response.blob();
//           const url = URL.createObjectURL(blob);

//           setDocs([{ uri: url, fileType: 'pdf' }]);
//         } catch (err) {
//           if (err.name !== 'AbortError') {
//             setError('Failed to load the document. Please try again.');
//             console.error('Error fetching document:', err);
//           }
//         } finally {
//           setLoading(false);
//         }
//       };

//       fetchDocument();

//       return () => {
//         abortController.abort();
//         if (docs.length > 0) URL.revokeObjectURL(docs[0].uri);
//       };
//     }
//   }, [contentDetails?.file_path]);

//   const handleVideoEnded = () => {

//     //when video ends, it should be mark as completed and  the progress should reset to 0
//     setIsVideoCompleted(true); 
//     setVideo_progress(0) 
//     handlePostProgress();
//   };

//   const handleVideoSave = () => {
//     console.log("Video getting saved")
//     handlePostProgress();
//   }

//   const extractVideoId = (url: string) => {
//     const regex = /(?:https?:\/\/(?:www\.)?youtube\.com\/(?:v|watch)\?v=|(?:https?:\/\/(?:www\.)?youtu\.be\/))([a-zA-Z0-9_-]{11})/;
//     const match = url.match(regex);
//     return match ? match[1] : null;
//   };

//   const renderYoutubeEmbed = () => {
//     const videoId = extractVideoId(contentDetails?.link_path || '');
//     return (
//       <div className="w-full h-full mx-[10px] p-1 flex justify-center items-center">
//         <YoutubeEmbed embedId={videoId} height={height} width={width} />
//       </div>
//     );
//   };

//   const renderWebEmbed = () => (
//     <div className="w-full h-full mx-[10px] p-1 flex justify-center items-center">
//       <WebEmbed url={contentDetails?.link_path || ''} height={window.innerHeight / 1.9} width={window.innerWidth / 1.8} />
//     </div>
//   );

//   if (!fileType) return <div>Loading...</div>;

//   switch (true) {
//     case fileType.includes('pdf'):
//       return (
//         <div>
//           {loading ? (
//             <div>Loading PDF...</div>
//           ) : error ? (
//             <div>{error}</div>
//           ) : (
//             <DocViewer
//               pluginRenderers={DocViewerRenderers}
//               documents={docs}
//               style={{
//                 width: width / 1.01,
//                 height: height / 1.01,
//                 overflow: 'auto'
//               }}
//             />
//           )}
//         </div>
//       );

//     case fileType.includes('mp4'):
//       const modifiedFilePath = contentDetails.file_path.replace(/uploads[\/\\]/, '');
//       return (
//         <>
//           {isVideoPlayerReady ? (
//             <VideoPlayer
//               inputUrl={`${API_URL}file_content/${modifiedFilePath}`}
//               inputWidth={width / 1.01}
//               inputHeight={height / 1.01}
//               options={{ controls: true, autoplay: false }}
//               onVideoEnd={handleVideoEnded}
//               setvideoProgress={setvideoProgress}
//               videoProgress={getUserProgress?.video_progress}
//               handleVideoSave={handleVideoSave}
//             // videoProgress={getUserProgress?.videoProgress}
//             />
//           ) : (
//             <div>Loading video player...</div>
//           )}
//         </>
//       );

//     case fileType.includes('presentation'):
//       return <div>Presentation files are not supported.</div>;

//     case fileType?.includes('None') && (contentDetails?.link_path?.includes('youtube') || contentDetails?.link_path?.includes('youtu.be')):
//       return renderYoutubeEmbed();

//     case fileType?.includes('None') && !contentDetails?.link_path?.includes('youtube'):
//       return renderWebEmbed();

//     default:
//       return <div>Unsupported file type.</div>;
//   }
// };

// export default ContentScreen2;

import { useState, useEffect } from 'react';
import { useModuleContentCounterStore } from '@/utils/moduleContent';
// Import dummy data instead of using API hooks
import { simulateGetAllContentInSlide, dummyUserProgress } from '@/utils/dummyData/contentPanelData';
import WebEmbed from './WebEmbed';
import YoutubeEmbed from './youtubeEmbed';

// Mock components to avoid dependency issues
const VideoPlayer = ({ inputUrl, inputWidth, inputHeight, options, onVideoEnd, setvideoProgress, videoProgress, handleVideoSave }) => (
  <div className="video-player-mock" style={{ width: inputWidth, height: inputHeight, backgroundColor: '#f0f0f0', borderRadius: '8px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
    <div className="video-controls" style={{ textAlign: 'center' }}>
      <h3 className="text-lg font-semibold mb-2">Video Player</h3>
      <p className="text-sm mb-4">URL: {inputUrl}</p>
      <div className="flex justify-center space-x-3">
        <button className="bg-blue-500 text-white px-4 py-2 rounded-md" onClick={() => setvideoProgress(videoProgress + 10)}>Skip +10s</button>
        <button className="bg-green-500 text-white px-4 py-2 rounded-md" onClick={handleVideoSave}>Save Progress</button>
        <button className="bg-red-500 text-white px-4 py-2 rounded-md" onClick={onVideoEnd}>Complete Video</button>
      </div>
      <div className="mt-4 text-sm">Current progress: {videoProgress}s</div>
    </div>
  </div>
);

// Mock PDF Viewer
const DocViewer = ({ pluginRenderers, documents, style }) => (
  <div style={style} className="pdf-viewer-mock bg-white border border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center">
    <h3 className="text-lg font-semibold mb-4">PDF Document Viewer</h3>
    <p className="text-sm mb-2">Document URI: {documents[0]?.uri || 'No document loaded'}</p>
    <div className="flex flex-col items-center">
      <div className="w-full bg-gray-200 h-64 flex items-center justify-center rounded-md mb-4">
        <p className="text-gray-600">PDF Content Would Display Here</p>
      </div>
      <div className="flex space-x-3">
        <button className="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md">Previous Page</button>
        <button className="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md">Next Page</button>
      </div>
    </div>
  </div>
);

const ContentScreen2 = ({ height, width, incomingContentId, incomingModuleId, incomingGroupId }) => {
  const { content_id } = useModuleContentCounterStore();
  const [contentId, setContentId] = useState(Number.isNaN(content_id) ? incomingContentId : content_id);
  
  // Use the dummy data function instead of API call
  const contentDetails = simulateGetAllContentInSlide(contentId);
  
  // Mock user progress data
  const getUserProgress = {
    video_progress: dummyUserProgress.video_progress
  };

  const [videoProgress, setvideoProgress] = useState(getUserProgress?.video_progress);
  const [docs, setDocs] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isVideoCompleted, setIsVideoCompleted] = useState(false);
  const [isVideoPlayerReady, setIsVideoPlayerReady] = useState(true);

  useEffect(() => {
    setvideoProgress(getUserProgress?.video_progress);
  }, [contentId]);

  // Mock user information
  const user = {
    user_id: 3
  };

  const [userProgress, setUserProgress] = useState({
    user_id: user?.user_id,
    group_id: parseInt(incomingGroupId),
    module_id: parseInt(incomingModuleId),
    content_id: parseInt(contentId),
    completed: (contentDetails?.file_type === "video/mp4" ? isVideoCompleted : true),
    page_number: 0,
    video_progress: parseFloat(videoProgress),
    total_progress: new Date().toISOString(),
  });

  // Mock progress update function
  const handlePostProgress = async () => {
    console.log("Saving progress...", userProgress);
    // In a real app, this would make an API call
  };

  useEffect(() => {
    const updatedUserProgress = {
      user_id: user?.user_id,
      group_id: parseInt(incomingGroupId),
      module_id: parseInt(incomingModuleId),
      content_id: parseInt(contentId),
      completed: (contentDetails?.file_type === "video/mp4" ? isVideoCompleted : 1),
      page_number: 0,
      video_progress: parseFloat(contentDetails?.file_type === "video/mp4" ? videoProgress : 0),
      total_progress: new Date().toISOString(),
    };

    setUserProgress(updatedUserProgress);
  }, [videoProgress, isVideoCompleted, contentDetails?.file_type, user?.user_id, incomingGroupId, incomingModuleId, contentId]);

  useEffect(() => {
    if (contentDetails?.file_type !== "video/mp4") {
      handlePostProgress();
    }
  }, [userProgress]);

  useEffect(() => {
    if (contentDetails?.file_type !== "video/mp4") {
      handlePostProgress();
    }
  }, [content_id]);

  useEffect(() => {
    if (contentDetails?.file_type === 'application/pdf') {
      // Simulate loading a PDF
      setLoading(true);
      
      setTimeout(() => {
        const mockPdfUrl = "mock://example.pdf";
        setDocs([{ uri: mockPdfUrl, fileType: 'pdf' }]);
        setLoading(false);
      }, 500);
    }
  }, [contentDetails?.file_path]);

  const handleVideoEnded = () => {
    setIsVideoCompleted(true);
    setvideoProgress(0);
    handlePostProgress();
  };

  const handleVideoSave = () => {
    console.log("Video progress saved:", videoProgress);
    handlePostProgress();
  };

  const extractVideoId = (url) => {
    if (!url) return null;
    const regex = /(?:https?:\/\/(?:www\.)?youtube\.com\/(?:v|watch)\?v=|(?:https?:\/\/(?:www\.)?youtu\.be\/))([a-zA-Z0-9_-]{11})/;
    const match = url?.match(regex);
    return match ? match[1] : null;
  };

  const renderYoutubeEmbed = () => {
    const videoId = extractVideoId(contentDetails?.link_path || '');
    return (
      <div className="w-full h-full flex justify-center items-center">
        <YoutubeEmbed embedId={videoId} height={height} width={width} />
      </div>
    );
  };

  const renderWebEmbed = () => (
    <div className="w-full h-full flex justify-center items-center">
      <WebEmbed url={contentDetails?.link_path || ''} height={height/1.5} width={width/1.5} />
    </div>
  );

  const fileType = contentDetails?.file_type;

  if (!fileType) return <div className="flex h-full w-full items-center justify-center text-lg font-medium text-gray-500">Loading content...</div>;

  return (
    <div className="bg-white rounded-2xl shadow-md p-8 w-full h-full flex flex-col gap-8">
      {/* Heading */}
      <h2 className="text-2xl font-bold text-slate-800 mb-4">{contentDetails?.content_name || 'Content'}</h2>
      {/* Video or Document Embed */}
      {fileType === 'video/mp4' && (
        <div className="rounded-xl shadow-lg overflow-hidden mb-6">
          {isVideoPlayerReady ? (
            <VideoPlayer
              inputUrl={`/api/file_content/${contentDetails.file_path.replace(/uploads[\/\\]/, '')}`}
              inputWidth={width / 1.2}
              inputHeight={height / 1.2}
              options={{ controls: true, autoplay: false }}
              onVideoEnd={handleVideoEnded}
              setvideoProgress={setvideoProgress}
              videoProgress={videoProgress}
              handleVideoSave={handleVideoSave}
            />
          ) : (
            <div className="loading-video flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mr-2"></div>
              <span>Loading video player...</span>
            </div>
          )}
        </div>
      )}
      {fileType === 'application/pdf' && (
        <div className="rounded-xl shadow-lg overflow-hidden mb-6">
          {loading ? (
            <div className="loading-animation flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              <span className="ml-3">Loading PDF...</span>
            </div>
          ) : error ? (
            <div className="error-message text-red-500">{error}</div>
          ) : (
            <DocViewer
              pluginRenderers={[]}
              documents={docs}
              style={{
                width: width / 1.01,
                height: height / 1.01,
                overflow: 'auto'
              }}
            />
          )}
        </div>
      )}
      {/* Main Content */}
      <div className="prose max-w-none text-base text-slate-700">
        {/* Render main content here, e.g., description, etc. */}
      </div>
      {fileType?.includes('None') && (contentDetails?.link_path?.includes('youtube') || contentDetails?.link_path?.includes('youtu.be')) && (
        renderYoutubeEmbed()
      )}
      {fileType?.includes('None') && !contentDetails?.link_path?.includes('youtube') && (
        renderWebEmbed()
      )}
      {fileType?.includes('presentation') && (
        <div className="flex flex-col justify-center items-center h-full w-full">
          <div className="bg-gray-100 rounded-lg p-8 text-center max-w-lg">
            <h3 className="text-xl font-semibold mb-4">PowerPoint Presentation</h3>
            <p className="mb-6 text-gray-600">File: {contentDetails.file_path?.split('/').pop() || 'Presentation'}</p>
            <div className="w-full h-64 bg-white border border-gray-300 rounded flex items-center justify-center mb-6">
              <p className="text-gray-500">Presentation content would display here</p>
            </div>
            <div className="flex justify-center space-x-4">
              <button className="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded">Previous Slide</button>
              <button className="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded">Next Slide</button>
            </div>
          </div>
        </div>
      )}
      {fileType && !fileType.includes('pdf') && !fileType.includes('mp4') && !fileType.includes('presentation') && !fileType.includes('None') && (
        <div className="flex justify-center items-center h-full w-full">
          <div className="bg-gray-100 rounded-lg p-8 text-center">
            <h3 className="text-xl font-semibold mb-2">Unsupported File Type</h3>
            <p className="text-gray-600">The file type "{fileType}" is not supported by the content viewer.</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentScreen2;