import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, InformationCircleIcon } from "@heroicons/react/20/solid";
import { motion } from "framer-motion"

import { useGetTheUserProgress } from "@/hook/content/useGetTheUserProgress"

interface YourProgressProps {
    percentage: number;
}

const blocks = 10;


const YourProgress = ({ percentage }: YourProgressProps) => {
    // Calculate how many blocks should be filled
    const filledBlocks = Math.floor((percentage / 100) * blocks);
    const [expanded, setExpanded] = useState(false)

    const temp1 = {
        "group_id": 2,
        "user_id": 3
    }

    const { data : tempData } = useGetTheUserProgress(temp1)

    console.log("tempData",tempData)

    return (
        <main className='w-36 h-fit flex flex-col justify-between gap-1'
            onClick={() => (expanded ? setExpanded(!expanded) : "")}
            onBlur={() => setExpanded(false)}
            onMouseLeave={() => setExpanded(false)}
            onMouseEnter={() => setExpanded(true)}
            tabIndex={0} // Make the main element focusable
        >
            <div className='w-full h-10 bg-gray-100 rounded-lg p-2 flex'>
                {
                    // Generate the blocks
                    Array.from({ length: blocks }).map((_, index) => (
                        <div
                            key={index}
                            className={`flex-1 h-full ${index < filledBlocks
                                ? index < 2
                                    ? 'bg-red-400'
                                    : index < 4
                                        ? 'bg-orange-400'
                                        : index < 6
                                            ? 'bg-yellow-400'
                                            : index < 8
                                                ? 'bg-green-400'
                                                : 'bg-green-700'
                                : 'bg-gray-300'} mx-0.5`}
                        />
                    ))
                }
                <div onClick={() => setExpanded(!expanded)} className='h-6 w-6 ml-2'>
                    {/* {expanded ? <ChevronDownIcon /> : <ChevronUpIcon />} */}
                    {<InformationCircleIcon />}
                </div>
            </div>
            {expanded &&
                <motion.div
                    transition={{
                        ease: "linear",
                        duration: 2,
                        x: { duration: 1 }
                    }}
                >
                    <div className='absolute mt-4 w-full h-1/6 rounded-lg p-2 flex'>
                        <div className='absolute w-9/12 h-fit bg-gray-100 rounded-lg p-2 flex border'>
                            <span className='flex justify-center w-full'>
                                {percentage} is completed
                            </span>
                        </div>

                    </div>
                </motion.div>
            }


        </main>
    );
}

export default YourProgress;
