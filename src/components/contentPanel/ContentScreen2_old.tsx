"use client"
import React, { useState, useEffect, useRef } from "react";
import * as XLSX from "xlsx";
import 'video.js/dist/video-js.css';
import 'videojs-contrib-eme';
import 'videojs-mobile-ui/dist/videojs-mobile-ui.css';
import 'videojs-seek-buttons/dist/videojs-seek-buttons.css';
import 'videojs-mobile-ui';
import 'videojs-sprite-thumbnails';
import 'videojs-seek-buttons';
import VideoPlayer from "@/components/videoPlayer/VideoPlayer";
import { API_URL } from "@/utils/constants";
import "@cyntler/react-doc-viewer/dist/index.css";
import { useVideoContextCount } from "@/utils/videoContext";
import { usePageContextCountStore } from "@/utils/pageContext";
import { useGetTheUserProgress } from "@/hook/content/useGetTheUserProgress";
import { useGetAllContentInSlide } from "@/hook/content/useGetAllContentInSlide";
import { useGetContent } from "@/hook/content/useGetContent";
// import "react-pdf/dist/esm/Page/AnnotationLayer.css";
// import "react-pdf/dist/esm/Page/TextLayer.css";
import YoutubeEmbed from "./youtubeEmbed";
import WebEmbed from "./WebEmbed";
import { useModuleContentCounterStore } from '@/utils/moduleContent'
import ReactGoogleSlides from "react-google-slides";
import PdfViewer from "./PdfViewer";


const ContentScreen = ({ height, width, contentPanelData, setScreenSize }) => {
  const [hasWindow, setHasWindow] = useState(false);
  const [sheetData, setSheetData] = useState([]);
  const [numPages, setNumPages] = useState(undefined);
  const [pageNumber, setPageNumber] = useState(1);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [base64Content, setBase64Content] = useState(null);
  const [videoID, setvideoID] = useState()

  const { content_id, module_id } = useModuleContentCounterStore();
  const { video_progress, setVideo_progress, video_link } = useVideoContextCount();
  const { page_number, setPage_number } = usePageContextCountStore();
  const divRef = useRef(null);
  // Fetch content details
  const { data: contentDetails } = useGetAllContentInSlide(content_id);
  // const { data: contentDetails } = useGetAllContentInSlide(content_id);

  const linkToPPTFile = "https://www.unm.edu/~unmvclib/powerpoint/pptexamples.ppt"


  const { data: progressData } = useGetTheUserProgress({
    group_id: 2,
    user_id: 3,
    module_id,
  });
  const { data: contentData } = useGetContent(contentDetails?.content_id);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages);
  }

  useEffect(() => {
    setHasWindow(typeof window !== "undefined");
  }, []);


  useEffect(() => {
    if (contentDetails?.file_type?.includes("sheet")) {
      handleXlsxFile();
    }
  }, [contentData, contentDetails]);


  const handleXlsxFile = async () => {
    try {
      const response = await fetch(contentData);
      const blob = await response.blob();
      const fileReader = new FileReader();
      fileReader.onload = (e) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX?.read(data, { type: "array" });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        setSheetData(XLSX.utils.sheet_to_json(sheet));
      };
      fileReader.readAsArrayBuffer(blob);
    } catch (error) {
      console.error("Error reading XLSX file:", error);
    }
  };

  

  // Render content based on file type
  const renderContent = () => {
    const fileType = contentDetails?.file_type;
    console.log("contentDetails.file_path",contentDetails?.file_path)
    switch (true) {
      case fileType?.includes("pdf"):
        return renderPdfViewer();
      case fileType?.includes("sheet"):
        return renderXlsxViewer();
      case fileType?.includes("presentation") || fileType?.includes("vnd.ms-powerpoint") || fileType?.includes("pptx"):
        // Construct the URL for the PowerPoint viewer
        const srcUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(contentData)}`;
        const pptFilePath = contentDetails.file_path.replace(/uploads[\/\\]/, '');
        return renderPptViewer(pptFilePath);
      case fileType?.includes("mp4"):
        const modifiedFilePath = contentDetails.file_path.replace(/(uploads|videos)[\/\\]/, '');
        // const videoIDDetail = contentDetails.file_path.match(/uploads\/videos\/([a-f0-9\-]+)/)[1];
        console.log("modifiedFilePath",modifiedFilePath)
        return renderVideoPlayer(modifiedFilePath);
      case fileType?.includes("None") && contentDetails?.link_path === null:
        return (
          <div>
            <span className="text-red-600 text-2xl"><b>Error: </b>Link path is null</span>
          </div>
        );
      case fileType?.includes("None") && contentDetails?.link_path.includes("youtube"):
        return renderYoutubeEmbed();
      case fileType?.includes("None") && !contentDetails?.link_path.includes("youtube"):
        return renderWebEmbed();

      default:
        return <div>Unsupported File Format</div>;
    }
  };

  const [pptContentData, setPptContentData] = useState<string | null>(null); // URL for the PPT file
  // const [numPages, setNumPages] = useState<number>(1);
  // const [pageNumber, setPageNumber] = useState<number>(1);


  const handleDownloadAndDisplay = async () => {
    try {
      // Replace with your API endpoint
      const apiUrl = "http://localhost:8080/contents/file_content/2";

      // Fetch the file from the API
      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "Accept": "application/json",
          "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmV...",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch file: ${response.statusText}`);
      }

      // Convert the response to a Blob
      const blob = await response.blob();

      // Create a URL for the Blob and set it as contentData
      const blobUrl = window.URL.createObjectURL(blob);
      setContentData(blobUrl);
    } catch (error) {
      console.error("Error downloading or displaying file:", error);
    }
  };

  // PDF Viewer
  const renderPdfViewer = () => (
    <div className="pdf-viewer flex flex-col overflow-auto gap-4"
      style={{  // Directly apply the height
        width: width / 1.01,    // Apply the width as well
        height: height / 1.1
      }}>
      <div className="h-full w-full overflow-auto">
        <PdfViewer />
      </div>
    </div>
  );

  // XLSX Viewer
  const renderXlsxViewer = () => (
    <div className="w-fit h-fit flex flex-col gap-2">
      <div className="w-full h-full flex justify-center items-start bg-transparent"> {/* Updated to h-screen and items-start */}
        <div
          className={`xlsx-viewer p-6 bg-gray-100 rounded-lg overflow-x-auto max-h-[600px]`}
          style={{  // Directly apply the height
            width: width / 1.01,    // Apply the width as well
            height: height / 1.1
          }}
        >
          {sheetData.length > 0 ? (
            <table className="min-w-full bg-white rounded-lg">
              <thead className="bg-gray-200">
                <tr>
                  {Object.keys(sheetData[0]).map((key) => (
                    <th key={key} className="px-4 py-2 text-left text-gray-600 font-medium border-b">
                      {key}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {sheetData.map((row, i) => (
                  <tr key={i} className="hover:bg-gray-50">
                    {Object.values(row).map((value, j) => (
                      <td key={j} className="px-4 py-2 text-gray-800 border-b text-sm">
                        {value}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p className="text-center text-gray-600">No data available to display.</p>
          )}
        </div>
      </div>

      <div className="w-full flex justify-center items-center gap-10">
        <button
          className="bg-secondary p-2 text-white rounded-lg"
          onClick={() => setPageNumber((prev) => Math.max(prev - 1, 1))}
        >
          Previous
        </button>
        <span>
          Page {pageNumber} of {numPages}
        </span>
        <button
          className="bg-secondary p-2 text-white rounded-lg"
          onClick={() => setPageNumber((prev) => Math.min(prev + 1, numPages))}
        >
          Next
        </button>
        <button
          className="bg-secondary p-2 text-white rounded-lg"
          onClick={() => (setScreenSize())}
        >
          Change Screen Size
        </button>
      </div>
    </div>

  );


  // Powerpoint Viewer
  const renderPptViewer = (pptFilePath) => (
    <div className="h-fit w-fit flex flex-col gap-2">
      {base64Content ? (
        <>
          {/* Use the base64 content */}
          <iframe
            src={`data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,${base64Content}`}
            width={width / 1.06}
            height={height / 1.20}
            frameBorder="0"
          />
          <div className="w-full flex justify-center items-center gap-10">
            <button
              className="bg-secondary p-2 text-white rounded-lg"
              onClick={() => setPageNumber((prev) => Math.max(prev - 1, 1))}
            >
              Previous
            </button>
            <span>
              Page {pageNumber} of {numPages}
            </span>
            <button
              className="bg-secondary p-2 text-white rounded-lg"
              onClick={() => setPageNumber((prev) => Math.min(prev + 1, numPages))}
            >
              Next
            </button>
            <button className="bg-secondary p-2 text-white rounded-lg"
              onClick={() => (setScreenSize())}
            >
              Change Screen Size
            </button>
          </div>
        </>
      ) : (
        <p>Loading...</p>
      )}
    </div>
  );

  // Powerpoint Viewer
  // const renderPptViewer = () => {
  //   return (
  //     <ReactGoogleSlides
  //       width={640}
  //       height={480}
  //       slidesLink="https://www.unm.edu/~unmvclib/powerpoint/pptexamples.ppt"
  //       slideDuration={5}
  //       position={1}
  //       showControls
  //       loop
  //     />
  //   );
  // }

  // const renderPptViewer = () =>{
  //   <DocViewer
  //     documents={docs}
  //     initialActiveDocument={docs[1]}
  //     pluginRenderers={DocViewerRenderers}
  //   />
  // }

  // Video Player
  // const renderVideoPlayer = (videoIDDetail:string) => (
  //   // <VideoPlayer
  //   //   inputUrl={contentData}
  //   //   options={{ controls: true, autoplay: false }}
  //   //   inputWidth={width / 1.14}
  //   //   inputHeight={height / 1.2}
  //   // /

  //   <div>
  //   <VideoPlayer
  //     inputUrl={`${API_URL}video_file_content/?video_file_name=${videoIDDetail}`}
  //     inputWidth={width / 1.14}
  //     inputHeight={height / 1.2}
  //     options={{ controls: true, autoplay: false }}
  //   //   onDuration={handleDuration}
  //   //   onProgress={handleProgress}
  //   //   onReady={handleReady}
  //   // onVideoEnd={handleVideoEnd} // Pass the callback for video end
  //   />
  //   {videoIDDetail}</div>

  // );



  const extractVideoId = (url) => {
    const regex = /(?:https?:\/\/(?:www\.)?youtube\.com\/(?:v|watch)\?v=|(?:https?:\/\/(?:www\.)?youtu\.be\/))([a-zA-Z0-9_-]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };


  const renderVideoPlayer = (videoUrl, width, height) => {
    // Extract the video ID from the URL
    // const videoIDDetail = extractVideoId(videoUrl);

    // // Log the video ID and final URL to ensure it's correct

    // if (!videoIDDetail) {
    //   console.error("No video ID extracted!");
    //   return <div>Video ID not found.</div>; // Return early if no ID is found
    // }

    const videoUrlAPI = `${API_URL}video_file_content/?index_segment=index&file_dir=${videoUrl}`;

    return (
      <div>
        <VideoPlayer
          inputUrl={"test_video/index.m3u8"}
          inputWidth={600}
          inputHeight={300}
          options={{ controls: true, autoplay: false }}
        />
        
      </div>
    );
  };


  // Youtube Embed
  const renderYoutubeEmbed = () => {
    const videoId = extractVideoId(contentDetails?.link_path);
    return (
      <div className="w-full h-full mx-[10px] p-1 border-2 border-black">
        <YoutubeEmbed embedId={videoId} height={height} width={width} />
      </div>
    );
  };

  // Web Embed
  const renderWebEmbed = () => (
    <div className="w-full h-full mx-[10px] p-1 border-2 border-black">
      <WebEmbed url={contentDetails?.link_path} height={height} width={width} />
    </div>
  );

  // Extract Video ID from YouTube URL
  const extractYoutubeVideoId = (url) => {
    const regex = /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  return <div className="content-screen">{hasWindow && renderContent()}</div>;
};

export default ContentScreen;
