// "use client";
// import { Fragment, useEffect, useRef, useState } from "react";
// import { useRouter } from "next/navigation";
// import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
// import Image from "next/image";
// import { removeUser, setFirstLogin } from "@/api/user.localStorage";
// import { Disclosure, Menu, Transition } from "@headlessui/react";
// import ProfileModal from "@/components/ui/header/ProfileModel";
// import {
//   Bars3Icon,
//   ChevronDownIcon,
//   UserCircleIcon,
//   XMarkIcon,
// } from "@heroicons/react/24/outline";
// import {
//   Cog6ToothIcon,
//   ArrowRightOnRectangleIcon,
// } from "@heroicons/react/20/solid";
// import { useLogoutAll } from "@/hook/useLogoutAll";


// const navigation = [
//   { name: "Home", href: "/dashboard/roles" }, //May change this later
//   { name: "Assesments", href: "/dashboard/roles/assessment" },
//   { name: "Learning Plan", href: "/dashboard/roles/learning_plan" }
// ];

// const userNavigation = [
//   { name: "Your Profile" },
//   { name: "Sign out" },
// ];

// function classNames(...classes: any) {
//   return classes.filter(Boolean).join(" ");
// }

// export default function Header() {
//   // State to track the active navigation item

//   const [isOpen, setIsOpen] = useState(false);
//   const [isOpenModule, setIsOpenModule] = useState(false);
//   const [profileModalOpen, setProfileModalOpen] = useState(false);

//   const router = useRouter();
//   const [enabled, setEnabled] = useState(false);

//   const getActiveItem = () => {
//     const path = router.pathname ?? '';
//     const activeNavItem = navigation.find((item) => path.startsWith(item.href));
//     return activeNavItem ? activeNavItem.name : '';
//   };

//   const [activeItem, setActiveItem] = useState(getActiveItem());

//   console.log("activeItem:- ", activeItem)

//   const toggleDropdown = () => { setIsOpen(!isOpen); setActiveItem("Assessment"); }
//   const toggleDropdownModule = () => { setIsOpenModule(!isOpenModule); setActiveItem("Module"); }



//   useEffect(() => {
//     // Use optional chaining to safely access pathname and provide a default empty string if it's undefined
//     const path = router.pathname ?? '';

//     const activeNavItem = navigation.find((item) => path.startsWith(item.href));
//     if (activeNavItem) {
//       setActiveItem(getActiveItem());
//     }
//   }, [router.pathname, getActiveItem]);


//   const { data: logout } = useLogoutAll(enabled);
//   const assessmentDropdownRef = useRef(null);
//   const moduleDropdownRef = useRef(null);

//   const closeDropdowns = () => {
//     setIsOpen(false);
//     setIsOpenModule(false);
//   };

//   const handleLogout = () => {
//     setFirstLogin(true)
//     removeUser(); // Call removeUser to clear user data
//     window.history.replaceState(null, null, "/");
//     router.replace("/login"); // Redirect to login page
//     setEnabled(true);
//   };

//   const handleUserNavigation = () => {
//     setProfileModalOpen(true);
//   };

//   const handleCloseProfileModal = () => {
//     setProfileModalOpen(false);
//   };


//   const handleItemClick = (itemName, href, e) => {
//     e.preventDefault();
//     setActiveItem(itemName);

//     router.push(href);
//   };

//   useEffect(() => {
//     function handleClickOutside(event) {
//       if (isOpen && assessmentDropdownRef.current && !assessmentDropdownRef.current.contains(event.target)) {
//         setIsOpen(false);
//       }
//     }
//     if (isOpen) {
//       document.addEventListener('mousedown', handleClickOutside);
//     }
//     return () => {
//       document.removeEventListener('mousedown', handleClickOutside);
//     };
//   }, [isOpen]);

//   // Effect for handling clicks outside the "Module" dropdown
//   useEffect(() => {
//     function handleClickOutside(event) {
//       if (isOpenModule && moduleDropdownRef.current && !moduleDropdownRef.current.contains(event.target)) {
//         setIsOpenModule(false);
//       }
//     }
//     if (isOpenModule) {
//       document.addEventListener('mousedown', handleClickOutside);
//     }
//     return () => {
//       document.removeEventListener('mousedown', handleClickOutside);
//     };
//   }, [isOpenModule]);

//   const queryClient = new QueryClient();

//   return (
//     <QueryClientProvider client={queryClient}>
//       <Disclosure as="nav" className="bg-gradient-to-t from-white to-slate-50">
//         {({ open }) => (
//           <>
//             <div className="mx-auto max-w-7xl px-2 lg:px-8">
//               <div className="relative flex h-16 items-center justify-between">
//                 <div className="absolute inset-y-0 left-0 flex items-center lg:hidden">
//                   {/* Mobile menu button*/}
//                   <Disclosure.Button className="relative inline-flex items-center justify-center rounded-md p-2 text-gray-500 hover:bg-gray-200 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
//                     <span className="absolute -inset-0.5" />
//                     <span className="sr-only">Open main menu</span>
//                     {open ? (
//                       <XMarkIcon className="block h-6 w-6 text-gray-800" aria-hidden="true" />
//                     ) : (
//                       <Bars3Icon className="block h-6 w-6 bg-white text-gray-500" aria-hidden="true" />
//                     )}
//                   </Disclosure.Button>
//                 </div>
//                 <div className="flex flex-1 items-center justify-center lg:items-stretch lg:justify-between xl:gap-20">
//                   <div className="flex flex-shrink-0 items-center">
//                     <Image
//                       className="h-8 w-auto"
//                       src="/logo_colour.png"
//                       alt="Company Logo"
//                       width={40}
//                       height={40}
//                     />
//                   </div>
//                   <div className="hidden lg:ml-6 lg:block">
//                     <div className="flex space-x-4 ">
//                       {navigation.slice(0, 6).map((item) => (
//                         <a
//                           key={item.name}
//                           href={item.href}
//                           onClick={(e) => handleItemClick(item.name, item.href, e)}
//                           className={classNames(
//                             item.name === activeItem
//                               ? 'bg-gray-900 text-white'
//                               : 'text-slate-500 hover:bg-gray-200 hover:text-slate-800',
//                             'px-3 py-2 rounded-md text-sm font-medium'
//                           )}
//                           aria-current={item.name === activeItem ? 'page' : undefined}
//                         >
//                           <div className="">{item.name}</div>
//                         </a>
//                       ))}
//                     </div>
//                   </div>
//                   <div className="absolute inset-y-0 right-0 flex items-center pr-2 lg:static lg:inset-auto lg:ml-6 lg:pr-0">
//                     {/* Profile dropdown */}
//                     <Menu as="div" className="relative ml-3">
//                       <div>
//                         <Menu.Button className="relative flex rounded-full text-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
//                           <span className="absolute -inset-1.5" />
//                           <span className="sr-only">Open user menu</span>
//                           <UserCircleIcon className="h-8 w-8" />
//                         </Menu.Button>
//                       </div>
//                       <Transition
//                         as={Fragment}
//                         enter="transition ease-out duration-100"
//                         enterFrom="transform opacity-0 scale-95"
//                         enterTo="transform opacity-100 scale-100"
//                         leave="transition ease-in duration-75"
//                         leaveFrom="transform opacity-100 scale-100"
//                         leaveTo="transform opacity-0 scale-95"
//                       >
//                         <Menu.Items className="absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
//                           {userNavigation.map((item) => (
//                             <Menu.Item key={item.name}>
//                               {({ active }) => (
//                                 <button
//                                   onClick={() => {
//                                     if (item.name === "Your Profile") {
//                                       window.location.href = "/userProfile";
//                                     } 
//                                     else if (item.name === "Change Password") {
//                                       handleUserNavigation();}
//                                        else if (item.name === "Sign out") {
//                                       handleLogout();
//                                     }
//                                   }}
//                                   className={classNames(
//                                     active ? "bg-gray-100 w-full" : "",
//                                     "block px-4 py-2 text-sm text-gray-700 w-full text-left"
//                                   )}
//                                 >
//                                   {item.name}
//                                 </button>
//                               )}
//                             </Menu.Item>
//                           ))}
//                         </Menu.Items>
//                       </Transition>
//                     </Menu>
//                   </div>
//                 </div>
//               </div>
//               {profileModalOpen && (
//                 <ProfileModal isOpen={true} onClose={handleCloseProfileModal} />
//               )}
//             </div>

//             <Disclosure.Panel className="lg:hidden">
//               <div className="space-y-1 px-2 pb-3 pt-2">
//                 {navigation.slice(0, 6).map((item) => (
//                   <Disclosure.Button
//                     key={item.name}
//                     as="a"
//                     href={item.href}
//                     className={classNames(
//                       item.current
//                         ? "  text-white"
//                         : "text-gray-500  hover:bg-gray-200 hover:text-white",
//                       "block rounded-md px-3 py-2 text-base font-medium"
//                     )}
//                     aria-current={item.current ? "page" : undefined}
//                   >
//                     {item.name}
//                   </Disclosure.Button>
//                 ))}
//               </div>
//             </Disclosure.Panel>
//           </>
//         )}
//       </Disclosure>
//     </QueryClientProvider>

//   );
// }
"use client";
import { Fragment, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { removeUser, setFirstLogin } from "@/api/user.localStorage";
import { Menu, Transition } from "@headlessui/react";
import {
  UserCircleIcon,
} from "@heroicons/react/24/outline";
import {
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from "@heroicons/react/20/solid";

const navigation = [
  { name: "Home", href: "/dashboard/roles" },
  { name: "Assessments", href: "/dashboard/roles/assessment" },
  { name: "Learning Plan", href: "/dashboard/roles/learning_plan" }
];

function classNames(...classes: any) {
  return classes.filter(Boolean).join(" ");
}

export default function Header() {
  const [activeItem, setActiveItem] = useState("Learning Plan");
  const router = useRouter();

  const handleLogout = () => {
    setFirstLogin(true);
    removeUser();
    window.history.replaceState(null, null, "/");
    router.replace("/login");
  };

  const handleItemClick = (itemName, href, e) => {
    e.preventDefault();
    setActiveItem(itemName);
    router.push(href);
  };

  return (
    <header className="bg-white border-b border-gray-200">
      <div className="mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-14 items-center justify-between">
          {/* Logo and Nav Links */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Image
                className="h-8 w-auto"
                src="/logo_colour.png"
                alt="Company Logo"
                width={40}
                height={40}
              />
            </div>
            <nav className="ml-6 hidden md:flex space-x-2">
              {navigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  onClick={(e) => handleItemClick(item.name, item.href, e)}
                  className={classNames(
                    item.name === activeItem
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                    'px-3 py-2 rounded-md text-sm font-medium transition-colors'
                  )}
                >
                  {item.name}
                </a>
              ))}
            </nav>
          </div>

          {/* User Menu */}
          <div className="flex items-center">
            <Menu as="div" className="relative ml-3">
              <div>
                <Menu.Button className="flex rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <UserCircleIcon className="h-8 w-8 text-gray-500" />
                </Menu.Button>
              </div>
              <Transition
                as={Fragment}
                enter="transition ease-out duration-200"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <Menu.Item>
                    {({ active }) => (
                      <a
                        href="/userProfile"
                        className={classNames(
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-4 py-2 text-sm text-gray-700'
                        )}
                      >
                        <UserCircleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                        Your Profile
                      </a>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <a
                        href="/settings"
                        className={classNames(
                          active ? 'bg-gray-100' : '',
                          'flex items-center px-4 py-2 text-sm text-gray-700'
                        )}
                      >
                        <Cog6ToothIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                        Settings
                      </a>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={handleLogout}
                        className={classNames(
                          active ? 'bg-gray-100' : '',
                          'flex w-full items-center px-4 py-2 text-sm text-gray-700'
                        )}
                      >
                        <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-400" aria-hidden="true" />
                        Sign out
                      </button>
                    )}
                  </Menu.Item>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>
    </header>
  );
}