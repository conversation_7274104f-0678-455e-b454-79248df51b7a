"use client"

import { useState, useEffect, Fragment } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Disclosure, Transition } from '@headlessui/react';
import { ChevronUpIcon } from '@heroicons/react/20/solid';
import { useMediaQuery } from "react-responsive";
import Image from "next/image";
import {
  HomeIcon,
  BriefcaseIcon,
  BoltIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ChartBarIcon,
  UserIcon,
  Cog6ToothIcon,
  ArrowLeftOnRectangleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlayIcon,
  RectangleGroupIcon,
  PresentationChartLineIcon,
  DocumentIcon,
  UserGroupIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { getContentLocalStorageData, getUser, removeUser, setFirstLogin } from '@/api/user.localStorage';

interface SidebarProps {
  onItemClick?: () => void;
  isExpanded: boolean;
  toggleSidebar: () => void;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ElementType;
}

const Sidebar = ({ onItemClick, isExpanded, toggleSidebar }: SidebarProps) => {
  const pathname = usePathname();
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const isDesktop = useMediaQuery({ minWidth: 768 });
  const linkData = getContentLocalStorageData();

  useEffect(() => {
    // Get user data when component mounts
    const userData = getUser();
    setUser(userData);
  }, []);

  // Navigation items
  const navigation: NavItem[] = [
    { name: "Program", href: "/dashboard/group", icon: RectangleGroupIcon },
    { name: "Skills", href: "/dashboard/skills", icon: ChartBarIcon },
    { name: "Roles", href: "/dashboard/roles", icon: UserGroupIcon },
    { name: "Assessments", href: "/dashboard/assesments", icon: UserIcon },
    { name: "Results", href: "/dashboard/result", icon: PresentationChartLineIcon },
    { name: "LeaderBoard", href: "/dashboard/leaderboard", icon: AcademicCapIcon },
    { name: "Module", href: "/dashboard/module", icon: DocumentIcon },
    { name: "User Attempts", href: "/dashboard/userattempts", icon: DocumentTextIcon },
  ];

  const isPathActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard';
    }
    
    // For other routes, check if the pathname starts with the href
    return pathname?.startsWith(href);
  };

  const handleNavClick = () => {
    // Simply call the onItemClick callback if provided
    if (onItemClick) {
      onItemClick();
    }
  };

  const handleLogout = () => {
    setFirstLogin(true);
    removeUser(); // Clear user data
    router.replace("/login"); // Redirect to login page
  };

  const classNames = (...classes: string[]) => classes.filter(Boolean).join(" ");

  // Always show full menu in mobile view
  const shouldShowFullMenu = !isDesktop || isExpanded;

  return (
    <div className={`h-screen border-r border-gray-200 bg-gray-100 ${shouldShowFullMenu ? 'w-60' : 'w-20'} flex flex-col transition-all duration-300 overflow-hidden relative z-30`}>
      {/* Logo section */}
      <div className="p-4 border-b border-gray-200">
        <div className={`flex ${shouldShowFullMenu ? 'justify-between' : 'justify-center'} items-center`}>
          {shouldShowFullMenu ? (
            <Image
              src="/logo_colour.png"
              alt="Company Logo"
              width={100}
              height={40}
              className="h-8 w-auto"
            />
          ) : (
            <div className="flex-shrink-0 text-blue-600 font-bold text-lg">S.ai</div>
          )}
          <button 
            onClick={toggleSidebar}
            className="p-1 rounded-full hover:bg-gray-200 text-gray-500"
          >
            {isDesktop ? (
              isExpanded ? (
                <ChevronLeftIcon className="h-5 w-5" />
              ) : (
                <ChevronRightIcon className="h-5 w-5" />
              )
            ) : (
              <XMarkIcon className="h-5 w-5" />
            )}
          </button>
        </div>
      </div>

      {/* Navigation section */}
      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-1 px-2">
          {navigation.map((item) => {
            const active = isPathActive(item.href);
            return (
              <Link
                key={item.name}
                href={item.href}
                onClick={handleNavClick}
                className={`
                  group flex items-center ${shouldShowFullMenu ? 'px-4' : 'justify-center'} py-3 text-sm font-medium rounded-md
                  ${active
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-200'
                  }
                `}
                title={!shouldShowFullMenu ? item.name : ''}
              >
                <item.icon
                  className={`
                    ${shouldShowFullMenu ? 'mr-3' : ''} h-5 w-5 flex-shrink-0
                    ${active 
                      ? 'text-blue-600' 
                      : 'text-gray-500 group-hover:text-gray-600'
                    }
                  `}
                  aria-hidden="true"
                />
                {shouldShowFullMenu && <span>{item.name}</span>}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* User Profile and Logout section */}
      <div className="border-t border-gray-200">
        {shouldShowFullMenu ? (
          <div className="px-2 py-4">
            {/* Collapsible profile menu in expanded view */}
            <Disclosure defaultOpen>
              {({ open }) => (
                <>
                  <Disclosure.Button className="flex w-full justify-between items-center rounded-md px-4 py-3 text-left text-sm font-medium text-gray-700 hover:bg-gray-200">
                    <div className="flex items-center">
                      <Image
                        src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                        alt="User Avatar"
                        width={32}
                        height={32}
                        className="rounded-full h-5 w-5 mr-3 flex-shrink-0"
                      />
                      <span className="text-gray-700 truncate">{user?.user_full_name || "User"}</span>
                    </div>
                    <ChevronUpIcon
                      className={`${open ? 'rotate-180 transform' : ''} h-5 w-5 text-blue-600`}
                    />
                  </Disclosure.Button>
                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-150"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                  >
                    <Disclosure.Panel className="pt-2 pb-1">
                      <div className="space-y-1">
                        {/* Profile Button */}
                        <Link
                          href="/userProfile"
                          className="group flex items-center px-4 py-3 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-200"
                        >
                          <Cog6ToothIcon className="h-5 w-5 mr-3 text-gray-500 flex-shrink-0" />
                          Profile
                        </Link>

                        {/* Resume button (if available) */}
                        {(linkData.content_id || linkData.module_id || linkData.group_id) && (
                          <Link
                            href={`/contentpanel?content_id=${linkData.content_id}&group_id=${linkData.group_id}&module_id=${linkData.module_id}`}
                            className="group flex items-center px-4 py-3 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-200"
                          >
                            <PlayIcon className="h-5 w-5 mr-3 text-gray-500 flex-shrink-0" />
                            Resume
                          </Link>
                        )}

                        {/* Logout Button */}
                        <Link
                          href="/login"
                          replace
                          onClick={handleLogout}
                          className="group flex items-center px-4 py-3 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-200"
                        >
                          <ArrowLeftOnRectangleIcon className="h-5 w-5 mr-3 text-gray-500 flex-shrink-0" />
                          Logout
                        </Link>
                      </div>
                    </Disclosure.Panel>
                  </Transition>
                </>
              )}
            </Disclosure>
          </div>
        ) : (
          // For collapsed desktop view - clicking profile image expands the sidebar
          <div className="flex justify-center pt-4 pb-8">
            <button
              onClick={toggleSidebar}
              className="flex items-center justify-center p-2 rounded-full hover:bg-gray-200 transition-colors"
              title="Open Profile Menu"
            >
              <span className="relative">
                {/* Resume notification indicator */}
                {(linkData.content_id || linkData.module_id || linkData.group_id) && (
                  <span className="absolute top-0 right-0 inline-flex h-2 w-2 rounded-full bg-blue-600 animate-pulse"></span>
                )}
                <Image
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                  alt="User Avatar"
                  width={36}
                  height={36}
                  className="rounded-full"
                />
              </span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;