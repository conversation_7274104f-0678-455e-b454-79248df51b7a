"use client";
import { Fragment, useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query"; 
import Image from "next/image";
import { removeUser, setFirstLogin } from "@/api/user.localStorage";
import { Disclosure, Menu, Transition } from "@headlessui/react";
import {
  Bars3Icon,
  ChevronDownIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import {
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from "@heroicons/react/20/solid";
import { useLogoutAll } from "@/hook/useLogoutAll";


const navigation = [
  // { name: "Home", href: "/admin/" },
  // { name: "Evaluate", href: "/admin/evaluatelist" },
  // { name: "Questions", href: "/admin/searchquestions" },
  // { name: "User Groups", href: "/admin/usergroup" },
  // { name: "User", href: "/admin/usergroup/users" },
  // { name: "Content", href: "/admin/content" },
  { name: "Leader Board", href: "/admin/leaderboard" },
  { name: "Roles", href: "/admin/roles"},
  // { name: "Module", href: "/admin/module" },
  // { name: "Assessment", href: "/admin/assessments" },
];


function classNames(...classes: any) {
  return classes.filter(Boolean).join(" ");
}

export default function Header() {
  // State to track the active navigation item
  
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenModule, setIsOpenModule] = useState(false);

  const router = useRouter();
  const [enabled, setEnabled] = useState(false);

  const getActiveItem = () => {
    const path = router.pathname ?? '';
    const activeNavItem = navigation.find((item) => path.startsWith(item.href));
    return activeNavItem ? activeNavItem.name : '';
  };

  const [activeItem, setActiveItem] = useState(getActiveItem());

  console.log("activeItem:- ",activeItem)

  const toggleDropdown = () => {setIsOpen(!isOpen); setActiveItem("Assessment");}
  const toggleDropdownModule = () => {setIsOpenModule(!isOpenModule); setActiveItem("Module");}


 
  useEffect(() => {
    // Use optional chaining to safely access pathname and provide a default empty string if it's undefined
    const path = router.pathname ?? '';
  
    const activeNavItem = navigation.find((item) => path.startsWith(item.href));
    if (activeNavItem) {
      setActiveItem(getActiveItem());
    } 
  }, [router.pathname, getActiveItem]);

  
  // Update navigation items' current property based on active item
  // const navigation = initialNavigation.map((item) => ({
  //   ...item,
  //   current: item.name === activeItem,
  // }));

  const { data: logout } = useLogoutAll(enabled);
  const assessmentDropdownRef = useRef(null);
  const moduleDropdownRef = useRef(null);

  const closeDropdowns = () => {
    setIsOpen(false);
    setIsOpenModule(false);
  };

  const handleLogout = () => {
    setFirstLogin(true)
    removeUser(); // Call removeUser to clear user data
    window.history.replaceState(null, null, "/");
    router.replace("/login"); // Redirect to login page
    setEnabled(true);
  };
 

  const handleItemClick = (itemName, href, e) => {
    e.preventDefault();
    setActiveItem(itemName);
    
    router.push(href);
  };
 
  useEffect(() => {
    function handleClickOutside(event) {
      if (isOpen && assessmentDropdownRef.current && !assessmentDropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

    // Effect for handling clicks outside the "Module" dropdown
    useEffect(() => {
      function handleClickOutside(event) {
        if (isOpenModule && moduleDropdownRef.current && !moduleDropdownRef.current.contains(event.target)) {
          setIsOpenModule(false);
        }
      }
      if (isOpenModule) {
        document.addEventListener('mousedown', handleClickOutside);
      }
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [isOpenModule]);

    const queryClient = new QueryClient();

  return (
<QueryClientProvider client={queryClient}>
    <Disclosure as="nav" className="bg-gray-800">
      {({ open }) => (
        <>
          <div className="mx-auto max-w-7xl px-2  lg:px-8">
            <div className="relative flex h-16 items-center justify-between">
              <div className="absolute inset-y-0 left-0 flex items-center lg:hidden">
                {/* Mobile menu button*/}
                <Disclosure.Button className="relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                  <span className="absolute -inset-0.5" />
                  <span className="sr-only">Open main menu</span>
                  {open ? (
                    <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Disclosure.Button>
              </div>
              <div className="flex flex-1 items-center justify-center lg:items-stretch lg:justify-start xl:gap-20">
                <div className="flex flex-shrink-0 items-center">
                  <Image
                    className="h-8 w-auto"
                    src="/logo_white.png"
                    alt="Company Logo"
                    width={100}
                    height={100}
                  />
                </div>
                <div className="hidden lg:ml-6 lg:block">
                  <div className="flex space-x-4">
                    {navigation.slice(0,8).map((item) => (
                      <a
                        key={item.name}
                        href={item.href}
                        onClick={(e) => handleItemClick(item.name, item.href, e)}
                        className={classNames(
                          item.name === activeItem
                            ? 'bg-gray-900 text-white'
                            : 'text-gray-200 hover:bg-gray-700 hover:text-white',
                          'px-3 py-2 rounded-md text-sm font-medium'
                        )}
                        aria-current={item.name === activeItem ? 'page' : undefined}
                      >
                        <div className="">{item.name}</div>
                      </a>
                    ))}

                    {/* <div className="relative" data-te-dropdown-ref ref={assessmentDropdownRef}>
                      <button
                        className={classNames(
                          "Assessment" === activeItem
                            ? 'bg-gray-900 text-white'
                            : 'text-gray-200 hover:bg-gray-700 hover:text-white',
                          'px-3 py-2 rounded-md text-sm font-medium flex flex-row gap-1'
                        )}
                        aria-current={"Assessment" === activeItem ? 'page' : undefined}
                        onClick={toggleDropdown}
                        id="dropdownMenuButton2"
                        aria-expanded={isOpen}
                        ref={assessmentDropdownRef}
                      >
                        Assessments
                        <ChevronDownIcon className="w-4" />
                      </button>
                      {isOpen && (
                        <ul
                          className="absolute z-50  w-40 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                          aria-labelledby="dropdownMenuButton2"
                        >
                          <li>
                            <a
                              className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              href="/admin/assessments"
                            >
                              Assign Assessment
                            </a>
                          </li>
                          <li>
                            <a
                              className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              href="/admin/assessments/assessmenttable"
                            >
                              Assessment List
                            </a>
                          </li>

                          <li>
                            <a
                              className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              href="/admin/assessments/createassessments"
                            >
                              Create Assessment
                            </a>
                          </li>
                          <li>
                            <a
                              className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              href="/admin/assessments/allassessmentsattempts"
                            >
                              All Assessment Attempts
                            </a>
                          </li>
                          <li>
                            <a
                              className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              href={`/admin/assessments/attemptsUser`}
                            >
                              User Attempt
                            </a>
                          </li>
                        </ul>
                      )}
                    </div>

                    <div className="relative" data-te-dropdown-ref ref={moduleDropdownRef}>
                      <button
                        className={classNames(
                          "Module" === activeItem
                            ? 'bg-gray-900 text-white'
                            : 'text-gray-200 hover:bg-gray-700 hover:text-white',
                          'px-3 py-2 rounded-md text-sm font-medium flex flex-row gap-1'
                        )}
                        aria-current={"Module" === activeItem ? 'page' : undefined}
                        onClick={toggleDropdownModule}
                        ref={moduleDropdownRef}
                        id="dropdownMenuButton2"
                        aria-expanded={isOpenModule}
                      >
                        Module
                        <ChevronDownIcon className="w-4" />
                      </button>
                      {isOpenModule && (
                        <ul
                          className="absolute z-50 w-40 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                          aria-labelledby="dropdownMenuButton2"
                        >
                          <li>
                            <a
                              className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                              href="/admin/module/modulelist"
                            >
                              Module List
                            </a>
                          </li>
                          <li>
                            <a
                              className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              href="/admin/module"
                            >
                              Create Module
                            </a>
                          </li>
                        </ul>
                      )}
                    </div> */}

                  </div>
                </div>
              </div>

              <div className="absolute inset-y-0 right-0 flex items-center pr-2 lg:static lg:inset-auto lg:ml-6 lg:pr-0">
                {/* Profile dropdown */}
                <Menu as="div" className="relative ml-3">
                  <div>
                    <Menu.Button className="relative flex rounded-full bg-gray-800 text-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
                      <span className="absolute -inset-1.5" />
                      <span className="sr-only">Open user menu</span>
                      <Image
                        className="h-8 w-8 rounded-full"
                        src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                        alt=""
                        width={40}
                        height={40}
                      />
                    </Menu.Button>
                  </div>
                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-100"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-75"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                  >
                    <Menu.Items className="absolute right-0 z-50 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      <Menu.Item>
                        {({ active }) => (
                          <a
                            href="#"
                            className={classNames(
                              active ? "bg-gray-100" : "",
                              "flex px-4 py-2 text-lg text-gray-700"
                            )}
                          >
                            <Cog6ToothIcon
                              className=" h-5 w-5 mr-1"
                              aria-hidden="true"
                            />
                            Profile
                          </a>
                        )}
                      </Menu.Item>
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            onClick={handleLogout}
                            className={classNames(
                              active ? "bg-gray-100" : "",
                              "flex w-full px-4 py-2 text-lg text-left text-gray-700"
                            )}
                          >
                            <ArrowRightOnRectangleIcon
                              className=" h-5 w-5 mr-1"
                              aria-hidden="true"
                            />
                            Logout
                          </button>
                        )}
                      </Menu.Item>
                    </Menu.Items>
                  </Transition>
                </Menu>
              </div>
            </div>
          </div>

          <Disclosure.Panel className="lg:hidden">
            <div className="space-y-1 px-2 pb-3 pt-2">
              {navigation.slice(0,7).map((item) => (
                <Disclosure.Button
                  key={item.name}
                  as="a"
                  href={item.href}
                  className={classNames(
                    item.current
                      ? "  text-white"
                      : "text-gray-300  hover:bg-gray-900 hover:text-white",
                    "block rounded-md px-3 py-2 text-base font-medium"
                  )}
                  aria-current={item.current ? "page" : undefined}
                >
                  {item.name}
                </Disclosure.Button>
              ))}
              <div className="relative" data-te-dropdown-ref ref={assessmentDropdownRef}>
                <button
                  className="text-gray-300 flex items-center gap-1 rounded-md px-3 py-2 text-base font-medium"
                  onClick={toggleDropdown}
                  id="dropdownMenuButton2"
                  aria-expanded={isOpen}
                  ref={assessmentDropdownRef}
                >
                  Assessments
                  <ChevronDownIcon className="w-4" />
                </button>
                {isOpen && (
                  <ul
                    className="absolute z-50 w-3/6 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                    aria-labelledby="dropdownMenuButton2"
                  >
                    <li>
                      <a
                        className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                        href="/admin/assessments"
                      >
                        Assign Assessment
                      </a>
                    </li>
                    <li>
                      <a
                        className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        href="/admin/assessments/assessmenttable"
                      >
                        Assessment List
                      </a>
                    </li>
                    <li>
                      <a
                        className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        href="/admin/assessments/attemptassessment"
                      >
                        Attempt Assessments
                      </a>
                    </li>
                    <li>
                      <a
                        className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        href="/admin/assessments/createassessments"

                      >
                        Create Assessment
                      </a>
                      <a
                        className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        href="/admin/assessments/allassessmentsattempts"

                      >
                        Assessment Attempts
                      </a>
                    </li>
                    <li>
                      <a
                        className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        href="/admin/assessments/allassessmentsattempts"
                      >
                        All Assessment Attempts
                      </a>
                    </li>
                  </ul>
                )}
              </div>

              <div className="relative" data-te-dropdown-ref ref={moduleDropdownRef}>
                <button
                  className="text-gray-300 flex items-center gap-1 rounded-md px-3 py-2 text-base font-medium"
                  onClick={toggleDropdownModule}
                  id="dropdownMenuButton2"
                  aria-expanded={isOpenModule}
                  ref={moduleDropdownRef}
                >
                  Module
                  <ChevronDownIcon className="w-4" />
                </button>
                {isOpenModule && (
                  <ul
                    className="absolute z-50  w-3/6 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                    aria-labelledby="dropdownMenuButton2"
                  >
                    <li>
                      <a
                        className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                        href="/admin/module/modulelist"
                      >
                        Module List
                      </a>
                    </li>
                    <li>
                      <a
                        className="block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        href="/admin/module"
                      >
                        Create Module
                      </a>
                    </li>
                  </ul>
                )}
              </div>
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
    </QueryClientProvider>
  );
}
