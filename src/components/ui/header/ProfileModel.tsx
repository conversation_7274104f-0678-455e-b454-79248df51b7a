import React, { useState } from "react";
//import { FaEye } from "react-icons/fa";
//import { FaEyeSlash } from "react-icons/fa";
import { getUser } from "@/api/user.localStorage";
//create api function for change password inside the api directory

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProfileModal: React.FC<ProfileModalProps> = ({ isOpen, onClose }) => {
  const [input1, setInput1] = useState("");
  const [input2, setInput2] = useState("");
  const [input3, setInput3] = useState("");
  const [error, setError] = useState("");
  const [type, setType] = useState("password");

  const handleSubmit = async () => {
    const user = getUser();
    if (input1 === "" || input2 === "" || input3 === "") {
      setError("Please fill all fields");
    } else if (input1 !== input2) {
      setError("Passwords do not match");
    } else {
      try {
        const response = await fetch(
          `http://127.0.0.1:8080/user/change_password_without_otp?old_password=${input3}&new_password=${input1}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              // If you have authentication, include the token in the headers
              Authorization: `Bearer ${user?.token}`,
              // 'Authorization': `Bearer ${token}`
            },
          }
        );

        if (response) {
          console.log("Password changed successfully");
          alert("Password changed successfully");
        }
      } catch (error) {
        console.error("Failed to change password:", error);
        alert("Failed to change password");
      }
      onClose();
    }
  };

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center gap-5 z-20 ${
        isOpen ? "block" : "hidden"
      }`}
    >
      <div
        className="fixed inset-0 bg-black opacity-50"
        onClick={onClose}
        role="div"
      ></div>

      <div className=" flex flex-col gap-2 bg-white w-full sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/3 p-4 m-4 lg:m-0 rounded-lg shadow-lg z-50">
        <h2 className="text-2xl text-slate-800 font-bold mb-4">
          Change Password
        </h2>
        {error && (
          <p className="text-red-600 text-sm font-medium leading-6 mb-2">
            {error}
          </p>
        )}
        <div className="flex flex-col gap-2">
          <label
            htmlFor="password"
            className="block text-sm font-medium leading-6 text-textColor"
          >
            Old Password
          </label>
          <input
            type={type}
            placeholder="Old password"
            value={input3}
            onChange={(e) => setInput3(e.target.value)}
            className="w-full text-slate-800 p-2 mb-2 border rounded"
            id="password"
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="password"
            className="block text-sm font-medium leading-6 text-textColor"
          >
            New Password
          </label>
          <input
            type={type}
            placeholder="New password"
            value={input1}
            onChange={(e) => setInput1(e.target.value)}
            className="w-full text-slate-800 p-2 mb-2 border rounded"
            id="password"
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="password"
            className="block text-sm font-medium leading-6 text-textColor"
          >
            Confirm Password
          </label>
          <input
            type={type}
            placeholder="Confirm password"
            value={input2}
            onChange={(e) => setInput2(e.target.value)}
            className="w-full text-slate-800 p-2 mb-2 border rounded"
            id="password"
          />
        </div>
        <button
          onClick={handleSubmit}
          className="flex w-full justify-center rounded-md bg-secondary px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75"
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default ProfileModal;

