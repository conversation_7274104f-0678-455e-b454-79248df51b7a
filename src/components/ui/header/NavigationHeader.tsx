import { removeUser, setFirstLogin } from "@/api/user.localStorage";
import { useLogoutAll } from "@/hook/useLogoutAll";
import { QuizLists } from "@/types/LMSTypes";
import {
  Dialog,
  Disclosure,
  Menu,
  Transition
} from "@headlessui/react";
import { Bars3Icon, UserCircleIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Fragment, useState } from "react";
import ProfileModal from "./ProfileModel";
// import 'flowbite-datepicker';
// import 'flowbite/dist/datepicker.turbo.js';

interface NavigationHeaderProps {
  presentTests?: Array<QuizLists>;
}
const user = {
  name: "<PERSON>",
  email: "<EMAIL>",
  imageUrl:
    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
};
const userNavigation = [
  { name: "Your Profile" },
  { name: "Change Password" },
  { name: "Sign out" },
];
function classNames(...classes: any) {
  return classes.filter(Boolean).join(" ");
}

export default function NavigationHeader({
  presentTests,
}: NavigationHeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [profileModalOpen, setProfileModalOpen] = useState(false);

  const router = useRouter();

  const handleLogout = () => {
    setFirstLogin(true)
    console.log("button logout called")
    removeUser(); // Call removeUser to clear user data
    router.replace("/login"); // Redirect to login page
    setEnabled(true)
  };

  const handleUserNavigation = () => {
    setProfileModalOpen(true);
  };

  const [enabled, setEnabled] = useState(false);
  const { data: logout } = useLogoutAll(enabled)

  const handleCloseProfileModal = () => {
    setProfileModalOpen(false);
  };

  return (
    <header className="bg-white max-h-16">
      <nav
        className="mx-auto flex max-w-7xl items-center justify-between p-2 lg:px-8"
        aria-label="Global"
      >
        <div className="flex lg:flex-1">
          <a href="#" className="p-0">
            <span className="sr-only">Your Company</span>
            <Image
              className="h-8 w-auto"
              src="/logo_colour.png"
              alt="navbar"
              height={40}
              width={40}
            />
          </a>
        </div>
        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"
            onClick={() => setMobileMenuOpen(true)}
          >
            <span className="sr-only">Open main menu</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
        </div>
        
        <div className="hidden lg:flex lg:flex-1 lg:justify-end">
          {/* Profile dropdown */}
          <Menu as="div" className="relative ml-3">
            <div>
              <Menu.Button className="relative flex max-w-xs items-center rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
                <span className="absolute -inset-1.5" />
                <span className="sr-only">Open user menu</span>
                <UserCircleIcon className="h-8 w-8"/>
              </Menu.Button>
            </div>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                {userNavigation.map((item) => (
                  <Menu.Item key={item.name}>
                    {({ active }) => (
                      <button
                        onClick={() => {
                          if (item.name === "Your Profile") {
                            window.location.href = "/userProfile";
                          } else if (item.name === "Change Password") {
                            handleUserNavigation();
                          } else if (item.name === "Sign out") {
                            handleLogout();
                          }
                        }}
                        className={classNames(
                          active ? "bg-gray-100 w-full" : "",
                          "block px-4 py-2 text-sm text-gray-700 w-full text-left"
                        )}
                      >
                        {item.name}
                      </button>
                    )}
                  </Menu.Item>
                ))}
              </Menu.Items>
            </Transition>
          </Menu>
          {profileModalOpen && (
            <ProfileModal isOpen={true} onClose={handleCloseProfileModal} />
          )}
        </div>
      </nav>
      <Dialog
        as="div"
        className="lg:hidden"
        open={mobileMenuOpen}
        onClose={setMobileMenuOpen}
      >
        <div className="fixed inset-0 z-10" />
        <Dialog.Panel className="fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
          <div className="flex items-center justify-between">
            <a href="#" className="-m-1.5 p-1.5">
              <span className="sr-only">Your Company</span>
              <Image
                className="h-8 w-auto"
                src="/logo_colour.png"
                alt="logo"
                height={40}
                width={40}
              />
            </a>
            <button
              type="button"
              className="-m-2.5 rounded-md p-2.5 text-gray-700"
              onClick={() => setMobileMenuOpen(false)}
            >
              <span className="sr-only">Close menu</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          <div className="mt-6 flow-root">
            <div className="-my-6 divide-y divide-gray-500/10">
              <div className="py-6">
                {userNavigation.map((item) => (
                  <Disclosure key={item.name} as="div">
                    <Disclosure.Button
                      as="button"
                      onClick={() => {
                        if (item.name === "Your Profile") {
                          window.location.href = "/userProfile";
                        } else if (item.name === "Change Password") {
                          handleUserNavigation();
                        } else if (item.name === "Sign out") {
                          handleLogout();
                        }
                      }}
                      className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 w-full text-left"
                    >
                      {item.name}
                    </Disclosure.Button>
                  </Disclosure>
                ))}
              </div>
            </div>
          </div>
        </Dialog.Panel>
      </Dialog>
      {profileModalOpen && (
        <ProfileModal isOpen={true} onClose={handleCloseProfileModal} />
      )}
    </header>
  );
}

