// "use client"
// import React, { useState, Fragment, useEffect, useRef } from "react";
// import Link from "next/link";
// import { useRouter } from "next/navigation";
// import { Disclosure, Transition } from '@headlessui/react'; // Import Transition
// import { ChevronUpIcon } from '@heroicons/react/20/solid';
// import { useMediaQuery } from "react-responsive";
// import {
//   Bars3Icon,
//   UserGroupIcon,
//   ArrowLeftStartOnRectangleIcon,
//   ArrowRightEndOnRectangleIcon,
//   RectangleGroupIcon,
//   ChartBarIcon,
//   UserIcon,
//   PresentationChartLineIcon,
//   AcademicCapIcon,
//   Cog6ToothIcon,
//   ArrowRightOnRectangleIcon,
//   DocumentIcon,
//   ViewfinderCircleIcon,
//   ComputerDesktopIcon,
//   BuildingLibraryIcon,
//   InboxArrowDownIcon,
//   PlayIcon,
// } from "@heroicons/react/24/outline";
// import { Menu } from "@headlessui/react";
// import Image from "next/image";
// import { getContentLocalStorageData, getUser, removeUser, setFirstLogin } from "@/api/user.localStorage";
// import { browser } from "process";

// const AdminSidebar = () => {
//   const user = getUser();
//   const timeoutId = useRef();
//   const router = useRouter();
//   const [isCollapsed, setIsCollapsed] = useState(false);
//   const [isMobileCollapsed, setIsMobileCollapsed] = useState(false);
//   const [isActive, setIsActive] = useState(1);

//   const isDesktop = useMediaQuery({ minWidth: 768 });

//   console.log("isDesktop", isDesktop);
//   const toggleSidebar = () => {
//     setIsCollapsed(!isCollapsed);
//   };

//   useEffect(() => {
//     clearTimeout(timeoutId.current); // Clear any previous timeout before setting a new one

//     if (!isCollapsed) {
//       timeoutId.current = setTimeout(() => {
//         setIsCollapsed(true);
//         if (!isMobileCollapsed && !isDesktop) {
//           setTimeout(() => {
//             setIsMobileCollapsed(true);
//           }, 4000);
//         }
//       }, 4000);
//     } else if (isCollapsed && !isMobileCollapsed && !isDesktop) {
//       setTimeout(() => {
//         setIsMobileCollapsed(true);
//       }, 3000);
//     }

//     return () => clearTimeout(timeoutId.current); // Cleanup on component unmount
//   }, [isCollapsed, isMobileCollapsed]); // Effect dependencies

//   const mobileCollapsed = () => {
//     setIsMobileCollapsed((prev) => !prev);
//   };

//   const onSelect = (index: number) => {
//     setIsActive(index); // Toggle the state
//   };

//   const handleLogout = () => {
//     setFirstLogin(true);
//     removeUser();

//     // Replace the current history state so the user can't go back
//     router.replace("/login");

//     // Clear history state
//     if (typeof window !== "undefined") {
//       window.history.pushState(null, "", "/login");
//       window.history.replaceState(null, "", "/login");
//     }
//   };


//   const menus = [
//     // Define your menu items here
//     // Example:
//     { name: "Leaderboard", link: "/admin/leaderboard", icon: ChartBarIcon },
//     { name: "Assessments", link: "/admin/assessments", icon: InboxArrowDownIcon },
//     { name: "Content", link: "/admin/content", icon: RectangleGroupIcon },
//     { name: "Evaluate", link: "/admin/evaluatelist", icon: DocumentIcon },
//     { name: "Module", link: "/admin/module", icon: BuildingLibraryIcon },
//     { name: "Roles", link: "/admin/roles", icon: PresentationChartLineIcon },
//     { name: "Questions", link: "/admin/searchquestions", icon: AcademicCapIcon },
//     { name: "User Group", link: "/admin/usergroup", icon: UserGroupIcon },
//     { name: "User", link: "/admin/usergroup/users", icon: UserIcon },


//     // Add more menu items as needed
//   ];

//   const classNames = (...classes: string[]) => classes.filter(Boolean).join(" ");

//   const linkData = getContentLocalStorageData();

//   console.log("linkData", linkData)
//   console.log("user", user)

//   return (
//     <aside
//       className={`absolute sm:relative z-30 pt-[1px] md:pt-[1px] flex flex-col gap-4 justify-between  ${(isMobileCollapsed && !isDesktop) ? "h-10 md:h-full" : "h-screen sm:h-[90vh] backdrop-blur-sm md:backdrop-blur-0 bg-gray-200/30  md:bg-[#E3E3E3]"} transition-all duration-300 ease-in-out ${isCollapsed ? "w-[60px] px-1" : "md:w-52 w-full pr-2 "
//         }`}
//     >
//       {/* Top Section: Logo and Collapse Button */}
//       <div>
//         <div className={`flex items-center justify-between h-[50px] py-2 px-3`}>
//           {!isCollapsed && (
//             <Image
//               src="/logo_colour.png"
//               alt="Company Logo"
//               width={100}
//               height={40}
//               className="h-8 w-auto"
//             />
//           )}
//           <button
//             onClick={(isMobileCollapsed && !isDesktop) ? mobileCollapsed : toggleSidebar}
//             className="p-1 rounded-md transition-colors duration-300"
//           >
//             {!isDesktop ?
//               (isCollapsed ? (
//                 isMobileCollapsed ? (
//                   <Bars3Icon className="w-6 h-6 text-[#314B67]" />
//                 ) : (
//                   <ArrowRightEndOnRectangleIcon className="w-6 h-6 text-[#314B67]" />
//                 )
//               ) : (
//                 <ArrowLeftStartOnRectangleIcon className="w-6 h-6 text-[#314B67]" />
//               ))
//               :
//               (isCollapsed ?
//                 <ArrowRightEndOnRectangleIcon className="w-6 h-6 text-[#314B67]" />
//                 :
//                 <ArrowLeftStartOnRectangleIcon className="w-6 h-6 text-[#314B67]" />
//               )
//             }
//           </button>
//         </div>
//         {(isMobileCollapsed && !isDesktop) ?
//           <></>
//           :
//           <>          {/* Menu Items */}
//             <div className="flex flex-col space-y-2 mt-4 pl-1 md:pl-0 md:px-2">
//               {menus.map((menu, index) => {
//                 const IconComponent = menu.icon;
//                 return (
//                   <Link
//                     href={menu.link}
//                     key={index}
//                     className={`flex items-center text-sm font-medium text-[#314B67] ${isActive == index ? "bg-gray-50 shadow-md" : ""} md:hover:bg-gray-100 hover:bg-white hover:shadow-sm rounded-md transition-colors duration-200 ${isCollapsed ? "px-3 py-2" : "px-4 py-2"
//                       }`}
//                     onClick={() => onSelect(index)}
//                   >
//                     <IconComponent className="h-5 w-5" />
//                     {!isCollapsed && (
//                       <span className="ml-3 text-md">{menu.name}</span>
//                     )}
//                   </Link>
//                 );
//               })}
//             </div>
//           </>
//         }
//       </div>

//       {(isMobileCollapsed && !isDesktop) ?
//         <></>
//         :
//         <>
//           {/* Bottom Section: User Menu */}
//           <div className={`mb-20 flex items-start justify-center text-sm font-medium text-[#314B67] rounded-md transition-colors duration-200 ${isCollapsed ? "mx-[4px] px-[4px] w-fit" : "px-3 mx-4 max-w-[170px]"} h-[200px]`}>
//             {
//               !isCollapsed
//                 ?
//                 <div className="flex flex-col ">
//                   <Disclosure defaultOpen>
//                     {({ open }) => (
//                       <>
//                         <Disclosure.Button className="flex w-full justify-between items-center rounded-lg px-4 py-2 text-left text-sm font-medium focus:outline-none focus-visible:ring hover:bg-gray-100 duration-300">
//                           <div className="w-full flex justify-between items-center">
//                             <Menu as="div" className="relative">
//                               <Menu.Button className="flex items-center justify-center w-full p-2 rounded-md transition-colors duration-200">
//                                 <Image
//                                   src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
//                                   alt="User Avatar"
//                                   width={32}
//                                   height={32}
//                                   className="rounded-full w-10 h-7"
//                                 />
//                               </Menu.Button>
//                               <Transition
//                                 as={Fragment}
//                                 enter="transition ease-out duration-100"
//                                 enterFrom="transform opacity-0 scale-95"
//                                 enterTo="transform opacity-100 scale-100"
//                                 leave="transition ease-in duration-75"
//                                 leaveFrom="transform opacity-100 scale-100"
//                                 leaveTo="transform opacity-0 scale-95"
//                               >
//                                 <Menu.Items className="absolute bottom-12 left-0 z-50 w-48 origin-bottom-left rounded-md bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
//                                   <Menu.Item>
//                                     {({ active }) => (
//                                       <Link
//                                         href="/userProfile"
//                                         className={classNames(
//                                           active ? "bg-gray-100" : "",
//                                           "flex items-center px-4 py-2 text-sm text-gray-700"
//                                         )}
//                                       >
//                                         <Cog6ToothIcon className="h-5 w-5 mr-2" />
//                                         Profile
//                                       </Link>
//                                     )}
//                                   </Menu.Item>
//                                   <Menu.Item>
//                                     {({ active }) => (
//                                       <Link
//                                         href="/login" replace
//                                         onClick={() => handleLogout()}
//                                         className={classNames(
//                                           active ? "bg-gray-100" : "",
//                                           "flex items-center w-full px-4 py-2 text-sm text-gray-700"
//                                         )}
//                                       >
//                                         <ArrowRightOnRectangleIcon className="h-5 w-5 mr-2" />
//                                         Logout
//                                       </Link>
//                                     )}
//                                   </Menu.Item>
//                                 </Menu.Items>
//                               </Transition>
//                             </Menu>
//                             <h1 className="text-[14px] truncate">
//                               {user?.user_full_name}
//                             </h1>
//                           </div>
//                           <ChevronUpIcon
//                             className={`${open ? 'rotate-180 transform' : ''
//                               } h-5 w-5 text-purple-500`}
//                           />
//                         </Disclosure.Button>
//                         <Transition
//                           as={Fragment}
//                           enter="transition ease-out duration-200"
//                           enterFrom="transform opacity-0 scale-95"
//                           enterTo="transform opacity-100 scale-100"
//                           leave="transition ease-in duration-150"
//                           leaveFrom="transform opacity-100 scale-100"
//                           leaveTo="transform opacity-0 scale-95"
//                         >
//                           <Disclosure.Panel className="px-4 pb-2 pt-4 text-sm text-gray-500 flex flex-col space-y-2">
//                             <Link className="text-[16px] text-center bg-gray-100 rounded-lg py-2 hover:bg-gray-50" href="/userProfile">Profile</Link>
//                             <Link className="text-[16px] text-center bg-gray-100 rounded-lg py-2 hover:bg-gray-50" onClick={() => handleLogout()} href="/login" replace>Logout</Link>
//                           </Disclosure.Panel>
//                         </Transition>
//                       </>
//                     )}
//                   </Disclosure>
//                 </div>
//                 :
//                 <>
//                   <Menu as="div" className="relative ">
//                     <Menu.Button className="flex items-center justify-center w-full p-2 rounded-md transition-colors duration-200">
//                       <span className="relative flex size-3">
//                         <Image
//                           src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
//                           alt="User Avatar"
//                           width={32}
//                           height={32}
//                           className="rounded-full"
//                         />
//                       </span>
//                     </Menu.Button>
//                     <Transition
//                       as={Fragment}
//                       enter="transition ease-out duration-100"
//                       enterFrom="transform opacity-0 scale-95"
//                       enterTo="transform opacity-100 scale-100"
//                       leave="transition ease-in duration-75"
//                       leaveFrom="transform opacity-100 scale-100"
//                       leaveTo="transform opacity-0 scale-95"
//                     >
//                       <Menu.Items className="absolute bottom-12 left-0 z-50 w-48 origin-bottom-left rounded-md bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
//                         <Menu.Item>
//                           {({ active }) => (
//                             <Link
//                               href="/userProfile"
//                               className={classNames(
//                                 active ? "bg-gray-100" : "",
//                                 "flex items-center px-4 py-2 text-sm text-gray-700"
//                               )}
//                             >
//                               <Cog6ToothIcon className="h-5 w-5 mr-2" />
//                               Profile
//                             </Link>
//                           )}
//                         </Menu.Item>
//                         <Menu.Item>
//                           {({ active }) => (
//                             <Link
//                               href="/login" replace
//                               onClick={() => handleLogout()}
//                               className={classNames(
//                                 active ? "bg-gray-100" : "",
//                                 "flex items-center w-full px-4 py-2 text-sm text-gray-700"
//                               )}
//                             >
//                               <ArrowRightOnRectangleIcon className="h-5 w-5 mr-2" />
//                               Logout
//                             </Link>
//                           )}
//                         </Menu.Item>
//                       </Menu.Items>
//                     </Transition>
//                   </Menu>
//                 </>
//             }
//           </div>
//         </>}
//     </aside>
//   );
// };

// export default AdminSidebar;

"use client"
import React, { useState, Fragment, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { Disclosure, Transition } from '@headlessui/react';
import { ChevronUpIcon } from '@heroicons/react/20/solid';
import { useMediaQuery } from "react-responsive";
import {
  UserGroupIcon,
  RectangleGroupIcon,
  ChartBarIcon,
  UserIcon,
  PresentationChartLineIcon,
  AcademicCapIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  DocumentIcon,
  BuildingLibraryIcon,
  InboxArrowDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
  PlayIcon,
} from "@heroicons/react/24/outline";
import { Menu } from "@headlessui/react";
import Image from "next/image";
import { getContentLocalStorageData, getUser, removeUser, setFirstLogin } from "@/api/user.localStorage";

interface AdminSidebarProps {
  isExpanded?: boolean;
  toggleSidebar?: () => void;
  onItemClick?: () => void;
}

const AdminSidebar = ({
  isExpanded = true,
  toggleSidebar = () => {},
  onItemClick = () => {},
}: AdminSidebarProps) => {
  const pathname = usePathname();
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const isDesktop = useMediaQuery({ minWidth: 768 });
  const linkData = getContentLocalStorageData();

  useEffect(() => {
    // Get user data when component mounts
    const userData = getUser();
    setUser(userData);
  }, []);

  const handleLogout = () => {
    setFirstLogin(true);
    removeUser();

    // Replace the current history state so the user can't go back
    router.replace("/login");

    // Clear history state
    if (typeof window !== "undefined") {
      window.history.pushState(null, "", "/login");
      window.history.replaceState(null, "", "/login");
    }
  };

  const menus = [
    { name: "Dashboard", link: "/admin", icon: ChartBarIcon },
    { name: "Leaderboard", link: "/admin/leaderboard", icon: ChartBarIcon },
    { name: "Assessments", link: "/admin/assessments", icon: InboxArrowDownIcon },
    { name: "Content", link: "/admin/content", icon: RectangleGroupIcon },
    { name: "Evaluate", link: "/admin/evaluatelist", icon: DocumentIcon },
    { name: "Module", link: "/admin/module", icon: BuildingLibraryIcon },
    { name: "Roles", link: "/admin/roles", icon: PresentationChartLineIcon },
    { name: "Questions", link: "/admin/searchquestions", icon: AcademicCapIcon },
    { name: "User Group", link: "/admin/usergroup", icon: UserGroupIcon },
    { name: "User", link: "/admin/usergroup/users", icon: UserIcon },
  ];

  const isPathActive = (path: string) => {
    if (path === '/admin') {
      return pathname === '/admin';
    }
    
    // For other routes, check if the pathname starts with the path
    return pathname?.startsWith(path);
  };

  const classNames = (...classes: string[]) => classes.filter(Boolean).join(" ");

  // Always show full menu in mobile view
  const shouldShowFullMenu = !isDesktop || isExpanded;

  return (
    <div className={`h-screen border-r border-gray-200 bg-gray-100 ${shouldShowFullMenu ? 'w-64' : 'w-20'} flex flex-col transition-all duration-300 overflow-hidden relative z-30`}>
      {/* Logo section */}
      <div className="p-4 border-b border-gray-200">
        <div className={`flex ${shouldShowFullMenu ? 'justify-between' : 'justify-center'} items-center`}>
          {shouldShowFullMenu ? (
            <Image
              src="/logo_colour.png"
              alt="Company Logo"
              width={100}
              height={40}
              className="h-8 w-auto"
            />
          ) : (
            <div className="flex-shrink-0 text-blue-600 font-bold text-lg">S.AI</div>
          )}
          <button 
            onClick={toggleSidebar}
            className="p-1 rounded-full hover:bg-gray-200 text-gray-500"
          >
            {isDesktop ? (
              isExpanded ? (
                <ChevronLeftIcon className="h-5 w-5" />
              ) : (
                <ChevronRightIcon className="h-5 w-5" />
              )
            ) : (
              <XMarkIcon className="h-5 w-5" />
            )}
          </button>
        </div>
      </div>

      {/* Navigation section */}
      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-1 px-2">
          {menus.map((menu, index) => {
            const IconComponent = menu.icon;
            const active = isPathActive(menu.link);
            return (
              <Link
                href={menu.link}
                key={index}
                className={`
                  group flex items-center ${shouldShowFullMenu ? 'px-4' : 'justify-center'} py-3 text-sm font-medium rounded-md
                  ${active
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-200'
                  }
                `}
                title={!shouldShowFullMenu ? menu.name : ''}
                onClick={() => onItemClick()}
              >
                <IconComponent
                  className={`
                    ${shouldShowFullMenu ? 'mr-3' : ''} h-5 w-5 flex-shrink-0
                    ${active 
                      ? 'text-blue-600' 
                      : 'text-gray-500 group-hover:text-gray-600'
                    }
                  `}
                  aria-hidden="true"
                />
                {shouldShowFullMenu && <span>{menu.name}</span>}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* User Profile and Logout section */}
      <div className="border-t border-gray-200">
        {shouldShowFullMenu ? (
          <div className="px-2 py-4">
            {/* Collapsible profile menu in expanded view */}
            <Disclosure defaultOpen>
              {({ open }) => (
                <>
                  <Disclosure.Button className="flex w-full justify-between items-center rounded-md px-4 py-3 text-left text-sm font-medium text-gray-700 hover:bg-gray-200">
                    <div className="flex items-center">
                      <Image
                        src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                        alt="User Avatar"
                        width={32}
                        height={32}
                        className="rounded-full h-5 w-5 mr-3 flex-shrink-0"
                      />
                      <span className="text-gray-700 truncate">{user?.user_full_name || "Admin User"}</span>
                    </div>
                    <ChevronUpIcon
                      className={`${open ? 'rotate-180 transform' : ''} h-5 w-5 text-blue-600`}
                    />
                  </Disclosure.Button>
                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-150"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                  >
                    <Disclosure.Panel className="pt-2 pb-1">
                      <div className="space-y-1">
                        {/* Profile Button */}
                        <Link
                          href="/userProfile"
                          className="group flex items-center px-4 py-3 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-200"
                        >
                          <Cog6ToothIcon className="h-5 w-5 mr-3 text-gray-500 flex-shrink-0" />
                          Profile
                        </Link>

                        {/* Logout Button */}
                        <button
                          onClick={handleLogout}
                          className="group flex w-full items-center px-4 py-3 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-200"
                        >
                          <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3 text-gray-500 flex-shrink-0" />
                          Logout
                        </button>
                      </div>
                    </Disclosure.Panel>
                  </Transition>
                </>
              )}
            </Disclosure>
          </div>
        ) : (
          // For collapsed desktop view - clicking profile image expands the sidebar
          <div className="flex justify-center pt-4 pb-8">
            <button
              onClick={toggleSidebar}
              className="flex items-center justify-center p-2 rounded-full hover:bg-gray-200 transition-colors"
              title="Open Profile Menu"
            >
              <Image
                src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                alt="User Avatar"
                width={36}
                height={36}
                className="rounded-full"
              />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminSidebar;