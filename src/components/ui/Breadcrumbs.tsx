import { ChevronLeftIcon, ChevronRightIcon, FolderOpenIcon, DocumentIcon, ArrowLeftIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation"; // Use next/router instead of next/navigation
import Link from "next/link";
import Button001 from "./Button001";

interface Crumb {
  label: string;
  path: string; // Include path for future use
}

interface BreadcrumbsProps {
  crumbs: Crumb[];
}

const Breadcrumbs = ({ crumbs }: BreadcrumbsProps) => {
  const router = useRouter(); // Initialize the router

  const handleBackClick = () => {
    router.back(); // Navigate back to the previous page
  };

  return (
    <nav className="flex flex-row bg-transparent w-full justify-between pl-20 md:px-8 py-[12px] pr-2" aria-label="Breadcrumb">
      <ol className="flex flex-wrap items-center overflow-x-auto whitespace-nowrap w-full">
        {crumbs.map((crumb, index) => (
          <li key={index} className="flex items-center font-semibold text-gray-500">
            {index > 0 && (
              <span className="mx-2 text-[18px] md:text-[22px] font-light">
                <ChevronRightIcon className="w-3 h-3 md:w-5 md:h-5" />
              </span>
            )}
            {index < crumbs.length - 1 ? (
              <Link href={crumb.path} className="hover:text-textSecondary transition delay-100 flex gap-1 md:gap-2 text-[8px] md:text-[12px]">
                <FolderOpenIcon className="w-3 h-3 md:w-5 md:h-5" />
                {crumb.label}
              </Link>
            ) : (
              <span className="flex gap-1 md:gap-2 text-[8px] md:text-[12px]">
                <DocumentIcon className="w-3 h-3 md:w-5 md:h-5" />
                {crumb.label}
              </span>
            )}
          </li>
        ))}
      </ol>
      {/* <Button001
        onClick={() => handleBackClick()}
        buttonName="Back"
      /> */}
    </nav>
  );
};

export default Breadcrumbs;