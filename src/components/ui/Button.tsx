import React from "react";

// Updated interface for the component props
interface ButtonProps {
  btnName?: string;
  BtnIcon?: React.ElementType<
    React.SVGProps<SVGSVGElement> & { ref?: React.Ref<SVGSVGElement> }
  >;
  onClickFunction?: (
    event?: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ) => void;
  // Updated to accept mouse event parameters
}

// Updated component implementation
const Button: React.FC<ButtonProps> = ({
  btnName,
  BtnIcon,
  onClickFunction,
}) => {
  return (
    <button
      className="flex gap-2 py-2 px-4 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
      onClick={onClickFunction} // Updated prop name for consistency
    >
      {BtnIcon && (
        <BtnIcon className="h-[15px] w-[15px]  md:h-[20px] md:w-[20px] text-white" />
      )}
      {btnName}
    </button>
  );
};


export default Button;
