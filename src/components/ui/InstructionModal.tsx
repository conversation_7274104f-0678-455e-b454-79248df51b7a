import React, { useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import <PERSON><PERSON><PERSON><PERSON>, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import "@cyntler/react-doc-viewer/dist/index.css";

interface InstructionModalProps {
    title: string;
    type?: string; // pdf, text
    paragraph?: React.ReactNode;
    documentUrl?: string; // URL for the document to be displayed
    closeModal: () => void;
}

const InstructionModal: React.FC<InstructionModalProps> = ({
    title,
    type = "text",
    paragraph = "",
    documentUrl = "/samplejd.pdf",
    closeModal
}) => {
    // Add escape key event listener
    useEffect(() => {
        const handleEscKey = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                closeModal();
            }
        };

        document.addEventListener('keydown', handleEscKey);
        return () => document.removeEventListener('keydown', handleEscKey);
    }, [closeModal]);

    const docs = [
        {
            uri: documentUrl,
            fileType: "pdf",
            fileName: "samplejd.pdf"
        }
    ];

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
            <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{
                    duration: 0.2,
                    ease: "easeOut"
                }}
                className="relative w-full max-w-2xl max-h-[90vh] bg-white rounded-lg shadow-xl overflow-hidden"
            >
                {/* Header */}
                <div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-200">
                    <h2 className="text-xl md:text-2xl font-semibold text-gray-900">{title}</h2>
                    <button
                        onClick={closeModal}
                        className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1"
                        aria-label="Close"
                    >
                        <XMarkIcon className="w-6 h-6" />
                    </button>
                </div>
                
                {/* Content */}
                <div className="p-4 md:p-6 overflow-y-auto max-h-[calc(90vh-130px)]">
                    {type === "text" ? (
                        <div className="text-gray-800 text-base">
                            {typeof paragraph === 'string' ? (
                                <p>{paragraph}</p>
                            ) : (
                                paragraph
                            )}
                        </div>
                    ) : (
                        <DocViewer
                            pluginRenderers={DocViewerRenderers}
                            documents={docs}
                            style={{
                                width: '100%',
                                height: '400px',
                                boxShadow: 'none',
                                border: '1px solid #e5e7eb',
                                borderRadius: '0.375rem'
                            }}
                            config={{
                                header: {
                                    disableHeader: false,
                                    disableFileName: false,
                                    retainURLParams: false
                                }
                            }}
                        />
                    )}
                </div>

                {/* Optional footer with close button */}
                {type === "pdf" && (
                    <div className="p-4 md:p-6 border-t border-gray-200 flex justify-end">
                        <button
                            onClick={closeModal}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            Close
                        </button>
                    </div>
                )}
            </motion.div>
        </div>
    );
};

export default InstructionModal;