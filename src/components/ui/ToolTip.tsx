import { FC, ReactNode, useRef } from "react";

interface Props {
  children: ReactNode;
  tooltip?: string;
}

const ToolTip: FC<Props> = ({ children, tooltip }): JSX.Element => {
  const tooltipRef = useRef<HTMLSpanElement>(null);
  const container = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={container}
      onMouseEnter={() => {
        if (!tooltipRef.current || !container.current) return;
        const { width, left } = container.current.getBoundingClientRect();
        const tooltipWidth = tooltipRef.current.offsetWidth;

        tooltipRef.current.style.left = `${Math.min(
          width - tooltipWidth,
          container.current.clientWidth / 2 - tooltipWidth / 2
        )}px`;
        tooltipRef.current.style.opacity = "1";
      }}
      onMouseLeave={() => {
        if (tooltipRef.current) {
          tooltipRef.current.style.opacity = "0";
        }
      }}
      className="relative inline-block"
    >
      {children}
      {tooltip ? (
        <span
          ref={tooltipRef}
          className="absolute top-full left-1/2 transform -translate-x-1/2 bg-white text-black py-1 px-2 rounded opacity-0 transition-opacity duration-300 whitespace-nowrap z-50"
          style={{ transition: 'opacity 0.3s' }}
        >
          {tooltip}
        </span>
      ) : null}
    </div>
  );
};

export default ToolTip;
