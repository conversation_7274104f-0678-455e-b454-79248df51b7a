
import React from 'react';
import Button from '@/components/ui/Button';
interface NothingProps {
    title:string;
    para:string;
    btnName? :string;
    btnIcon?: React.ElementType<
    React.SVGProps<SVGSVGElement> & { ref?: React.Ref<SVGSVGElement> }
  >;
  onClickFunctionForBtn?: (
    event?: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ) => void;
  // Updated to accept mouse event parameters
}

export default function Nothing({title,para,btnName,btnIcon,onClickFunctionForBtn} :NothingProps) {
  return (
    <div className="flex flex-col justify-center items-center gap-3 w-full h-full ">
    <h1 className="text-xl font-semibold">{title}</h1>
    <p className="text-md text-gray-600 ">{para}
    </p>
    {btnName && (
        <Button btnName={btnName} BtnIcon={btnIcon} onClickFunction={onClickFunctionForBtn} />
      )}

  </div>
  )
}
