import React from 'react';
import Link from 'next/link';

interface Button001Props {
    href?: string;
    buttonName?: string;
    onClick?: () => void;
}

const Button001: React.FC<Button001Props> = ({ href, buttonName = 'Select', onClick }) => {
    if (href) {
        return (
            <Link href={href} className="w-fit sm:w-[150px]">
                <button 
                    className="self-center w-fit border-2 border-textSecondary text-textSecondary font-semibold p-2 rounded text-[0.6rem] md:text-sm hover:bg-textSecondary hover:text-white transition-all duration-300 whitespace-nowrap"
                    onClick={onClick}
                >
                    {buttonName}
                </button>
            </Link>
        );
    }
    
    return (
        <button 
            className="self-center w-fit border-2 border-textSecondary text-textSecondary font-semibold p-2 rounded text-sm hover:bg-textSecondary text-[0.6rem] md:text-sm hover:text-white transition-all duration-300 whitespace-nowrap"
            onClick={onClick}
        >
            {buttonName}
        </button>
    );
};

export default Button001;
