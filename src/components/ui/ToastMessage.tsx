import React, { useState, useEffect } from 'react';

const ToastMessage = ({ message, type, duration = 3000, onClose }) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (visible) {
    //   const timer = setTimeout(() => {
    //     setVisible(false);
    //     if (onClose) {
    //       onClose();
    //     }
    //   }, duration);

    //   return () => clearTimeout(timer);
    }
  }, [visible, duration, onClose]);

  if (!visible) return null;

  // Determine the background color based on the type
  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'info':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div
      className={`fixed bottom-4 right-4 p-4 rounded-lg text-white ${getBackgroundColor()} shadow-lg transition-opacity duration-300`}
    >
      {message}
    </div>
  );
};

export default ToastMessage;