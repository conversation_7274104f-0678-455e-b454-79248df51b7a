import React from 'react';

const DividedCircleWithArc = ({ percentage }) => {
    // Ensure the percentage is between 0 and 100
    const validPercentage = Math.min(Math.max(percentage, 0), 100);

    // Calculate the stroke-dasharray and stroke-dashoffset
    const radius = 12; // Circle radius
    const circumference = 2 * Math.PI * radius; // Circumference of the circle
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (circumference * validPercentage) / 100; // Calculate the offset for the percentage

    // Set a fixed color for the stroke
    const strokeColor = '#007bff'; // Set a single color (blue in this case)

    return (
        <div style={{ display: 'inline-block', position: 'relative', textAlign: 'center' }}>
            <svg
                height="40"
                width="40"
                xmlns="http://www.w3.org/2000/svg">
                {/* Circle background (gray) */}
                <circle
                    r={radius}
                    cx="16"
                    cy="16"
                    fill="none"
                    stroke="#e6e6e6" // Light gray background
                    strokeWidth="10"
                />
                {/* Circle stroke (colorized arc) */}
                <circle
                    r={radius}
                    cx="16"
                    cy="16"
                    fill="none"
                    stroke={strokeColor} // Use fixed color
                    strokeWidth="6"
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round" // Optional: makes the ends of the arc rounded
                />
            </svg>

            {/* Checkbox positioned in the center of the circle */}
            <input
                type="checkbox"
                style={{
                    position: 'absolute',
                    top: '40%',
                    left: '40%',
                    transform: 'translate(-50%, -50%)',
                    width: '12px',  // Adjust the size of the checkbox
                    height: '12px',
                    zIndex: 1,  // Ensure it sits above the circle
                    appearance: 'none',  // Remove default checkbox appearance
                    border: 'none',  // Remove the border
                    outline: 'none',  // Remove the outline
                    backgroundColor: '#fff',  // Optional: Set background to white
                    boxShadow: 'none',  // Remove any shadow effect
                }}
            />
        </div>
    );
};

export default DividedCircleWithArc;
