import React, { useState, useEffect } from 'react';
import { 
  XMarkIcon, 
  DocumentIcon
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import DocViewer, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import "@cyntler/react-doc-viewer/dist/index.css";

interface SampleDocumentModalProps {
  title: string;
  documentUrl?: string;
  onClose: () => void;
}

const SampleDocumentModal: React.FC<SampleDocumentModalProps> = ({
  title,
  documentUrl = "/samplejd.pdf",
  onClose
}) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    
    // Simulate initial loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);
    
    return () => {
      document.removeEventListener('keydown', handleEscKey);
      clearTimeout(timer);
    };
  }, [onClose]);

  // Document configuration for DocViewer
  const docs = [
    {
      uri: documentUrl,
      fileType: "pdf",
      fileName: documentUrl.split('/').pop() || "document.pdf"
    }
  ];

  // Configuration to hide all controls
  const config = {
    header: {
      disableHeader: true,
      disableFileName: true,
      retainURLParams: false
    },
    pdfZoom: {
      noDefaultZoomControls: true
    },
    loadingRenderer: {
      showLoadingTimeout: false
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-hidden bg-black/70 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 flex items-center justify-center sm:p-6"
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          transition={{ type: "spring", duration: 0.4 }}
          className="bg-white rounded-lg shadow-2xl overflow-hidden flex flex-col w-full max-w-5xl h-[85vh]"
        >
          {/* Header */}
          <div className="flex items-center justify-between px-4 py-3 bg-blue-600 text-white">
            <div className="flex items-center space-x-3">
              <DocumentIcon className="h-5 w-5" />
              <h2 className="text-lg font-medium truncate">{title}</h2>
            </div>
            <button
              onClick={onClose}
              className="p-1.5 rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-1 focus:ring-offset-blue-600"
              aria-label="Close"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
          
          {/* Document content area */}
          <div className="flex-1 overflow-auto bg-gray-100 relative">
            {isLoading ? (
              <div className="flex items-center justify-center h-full py-12">
                <div className="flex flex-col items-center">
                  <div className="loader h-12 w-12 mb-4 rounded-full border-4 border-blue-200 border-t-blue-600 animate-spin"></div>
                  <p className="text-gray-600">Loading document...</p>
                </div>
              </div>
            ) : (
              <div className="pdf-container h-full">
                <DocViewer
                  pluginRenderers={DocViewerRenderers}
                  documents={docs}
                  style={{
                    width: '100%',
                    height: '100%'
                  }}
                  config={config}
                />
              </div>
            )}
          </div>
          
          {/* Footer */}
          <div className="border-t border-gray-200 p-4 bg-gray-50">
            <div className="flex justify-end space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                Close
              </button>
              <button
                onClick={onClose}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
              >
                Got it
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default SampleDocumentModal;