import React from 'react';

const CircleWithArc = ({ percentage }) => {
    // Ensure the percentage is between 0 and 100
    const validPercentage = Math.min(Math.max(percentage, 0), 100);

    // Calculate the stroke-dasharray and stroke-dashoffset
    const radius = 20; // Circle radius
    const circumference = 2 * Math.PI * radius; // Circumference of the circle
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (circumference * validPercentage) / 100; // Calculate the offset for the percentage

    // Determine the color based on the percentage
    let strokeColor = '#001a45'; // Default color, if none of the ranges match
    if (validPercentage >= 0 && validPercentage <= 30) {
        strokeColor = 'red';
    } else if (validPercentage >= 31 && validPercentage <= 50) {
        strokeColor = 'orange';
    } else if (validPercentage >= 51 && validPercentage <= 70) {
        strokeColor = 'yellow';
    } else if (validPercentage >= 71 && validPercentage <= 90) {
        strokeColor = 'green';
    } else if (validPercentage >= 91 && validPercentage <= 100) {
        strokeColor = 'blue';
    }

    return (
        <div style={{ display: 'inline-block', position: 'relative', textAlign: 'center' }}>
            <svg
                height="58"
                width="58"
                xmlns="http://www.w3.org/2000/svg">
                {/* Circle background (gray) */}
                <circle
                    r={radius}
                    cx="30"
                    cy="30"
                    fill="none"
                    stroke="#e6e6e6" // Light gray background
                    strokeWidth="10"
                />
                {/* Circle stroke (colorized arc) */}
                <circle
                    r={radius}
                    cx="30"
                    cy="30"
                    fill="none"
                    stroke={strokeColor} // Use dynamic color
                    strokeWidth="5"
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round" // Optional: makes the ends of the arc rounded
                />
            </svg>
            {/* Percentage text in the center */}
            <div
                style={{
                    position: 'absolute',
                    top: '40%', left: '28%',
                    fontSize: '10px',
                    fontWeight: 'normal'
                }}>
                {validPercentage}%
            </div>
        </div>
    );
};

export default CircleWithArc;
