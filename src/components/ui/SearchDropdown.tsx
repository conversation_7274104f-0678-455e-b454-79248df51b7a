import React, { useEffect, useRef, useState } from "react";

interface DropdownProps {
  options: { label: string; content: string[] }[];
}

const SearchDropdown: React.FC<DropdownProps> = ({ options, setQuestionType, setDifficultyLevel, setTopic, setQuestionSource }) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [optionOpenStates, setOptionOpenStates] = useState(
    options.map(() => false)
  );
  

  const handleOptionDropdownToggle = (index: number) => {
    const newOptionOpenStates = optionOpenStates.map((state, i) =>
      i === index ? !state : false
    );
    setOptionOpenStates(newOptionOpenStates);
  };

  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };
  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className=" relative inline-block text-left z-50" ref={dropdownRef}>
      <div>
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="inline-flex justify-center w-49 px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring focus:border-blue-300"
          id="options-menu"
          aria-haspopup="true"
          aria-expanded="true"
        >
          Filter
          <svg
            className="w-5 h-5 ml-2 -mr-1"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 11.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>

      {isOpen && (
        <div className="origin-top-right absolute right-0 mt-1 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
          <div
            className="py-1"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="options-menu"
          >
            {options.map((option, index) => (
              <div key={option.label}>
                <button
                  type="button"
                  onClick={() => handleOptionDropdownToggle(index)}
                  className="inline-flex justify-between w-full px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring focus:border-blue-300"
                  id={`option-menu-${index}`}
                  aria-haspopup="true"
                  aria-expanded={optionOpenStates[index]}
                >
                  <p>{option.label}</p>
                  <div>
                    <svg
                      className="w-5 h-5 ml-2 -mr-1"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 11.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </button>
                <div>
                  {optionOpenStates[index] && (
                    <div key={option.label}>
                      {option.content.map((radioOption, i) => (
                        <label
                          key={radioOption}
                          className="inline-flex justify-between w-full px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring focus:border-blue-300"
                        >
                          <p>{radioOption}</p>
                          <div>
                            <input
                              type="radio"
                              name={`radioGroup-${index}`}
                              className="form-radio h-5 w-5 text-blue-500"
                            />
                          </div>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchDropdown;

