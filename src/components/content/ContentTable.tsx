"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import { TrashIcon } from "@heroicons/react/24/outline";
import { useRouter, useSearchParams } from "next/navigation";
import DeleteModal from "../admin/userGroup/DeleteModal";
import DeletedUserModal from "../admin/userGroup/DeletedUserModal";
import Nothing from "@/components/ui/Nothing";
import { useCount } from "@/context/searchStore";

export default function ContentTable({ contentData, onDelete }) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const examId = searchParams.get("exam_id");
  const [isRemoveModal, setIsRemoveModal] = useState(false);
  const [showDeletedUserModal, setShowDeletedUserModal] = useState(false);
  const [selectedContentId, setSelectedContentId] = useState(null);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const totalPages = Math.ceil(contentData.length / itemsPerPage);
  const { searchValue, searchNumber } = useCount();


  let temp2:boolean = false 

useEffect(() => {
  temp2 = useCount.getState().searchValue
  console.log("search numberss:- ", temp2)
  console.log("search numberss page:- ", currentPage)
  if(temp2==true && currentPage != 1){
    setCurrentPage(1)
  }
}, [searchNumber])
  
  const handleDeleteClick = (contentId) => {
    setSelectedContentId(contentId); // Store the ID for deletion
    setIsRemoveModal(true); // Open the modal for confirmation
  };

  // Then, adjust the modal confirmation handler to actually perform the deletion
  const handleConfirmDelete = () => {
    if (selectedContentId !== null) {
      onDelete(selectedContentId); // Now, actually call onDelete
    }
    setIsRemoveModal(false); // Close the modal
    setSelectedContentId(null); // Reset the selected ID
  };

  const handleDeleteUser = () => {
    setIsRemoveModal(false); // Close the DeleteModal
    setShowDeletedUserModal(true); // Open the DeletedUserModal
    if (selectedContentId != null) {
      onDelete(selectedContentId); // Use the onDelete prop
    }
  };

  const handleCloseDeletedUserModal = () => {
    setShowDeletedUserModal(false); // Close the DeletedUserModal
  };

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = contentData.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="flex flex-col gap-2 justify-start  w-full rounded-lg h-full border shadow-md overflow-hidden bg-white">
      {/* Scrollable table container for medium to large screens */}
      <div className="overflow-auto w-full h-full  rounded-md bg-white">
        <table className="min-w-full divide-y divide-gray-200 h-full ">
          <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
            <tr className="w-full">
              <th className="p-2 text-left">Content Id</th>
              <th className="p-2 text-left">Content Name</th>
              <th className="p-2 text-left">Description</th>
              <th className="p-2 text-left">Topics</th>
              <th className="p-2 text-left">File Path</th>
              <th className="p-2 text-center">Delete</th>
              <th className="p-2 text-center">Open Content</th>
            </tr>
          </thead>
          <tbody className=" h-full">
            {/* Display rows or a message if there are no items */}
            {Array.isArray(currentItems) && currentItems.length > 0 ? (
              currentItems.map((item) => (
                <tr key={item.content_id} className="border-b bg-white h-10">
                  <td className="p-2 px-3 text-left">{item.content_id}</td>
                  <td className="p-2 text-left">{item.content_name}</td>
                  <td className="p-2 text-left">{item.content_description}</td>
                  <td className="p-2 text-left">{item.topics}</td>
                  <td className="p-2 text-left">{item.file_path}</td>
                  <td className="p-2 text-center">
                    {/* Replace TrashIcon with actual icon component */}
                    <button onClick={() => handleDeleteClick(item.content_id)}>
                      <TrashIcon className="md:w-[25px] md:h-[25px] w-[17px] h-[17px] text-red-500" />
                    </button>
                  </td>
                  <td className="p-2 text-center">
                    {/* Replace Link with actual Link component or <a> tag */}
                    <a
                      href={`content/${item.content_id}`}
                      className="text-blue-500 underline"
                    >
                      Link
                    </a>
                  </td>
                </tr>
                 
                

              ))

            
            ) : (
              <tr>
              <td colSpan="7" className="text-center p-4">
                <Nothing
                  title="No Content Available"
                  para="There are currently no content to display.
                          Please check back later,or add content."
                />
              </td>
            </tr>
            )}
            {/* Empty rows to maintain the table's appearance for fewer items */}
        {currentItems.length < itemsPerPage && 
          [...Array(itemsPerPage - currentItems.length)].map((_, index) => (
            <tr key={`empty-${index}`} className=" align-text-top h-10">
              <td colSpan="7"></td>
            </tr>
          ))
        }
          </tbody>
        </table>
      </div>

      {/* Card layout for small screens, hidden on larger screens */}
      <div className="hidden">
        {Array.isArray(currentItems) && currentItems.length > 0 ? (
          currentItems.map((item) => (
            <div
              key={item.content_id}
              className="p-4 m-2 bg-white shadow-md rounded-lg"
            >
              <div>
                <strong>Content Id:</strong> {item.content_id}
              </div>
              <div>
                <strong>Content Name:</strong> {item.content_name}
              </div>
              <div>
                <strong>Description:</strong> {item.content_description}
              </div>
              <div>
                <strong>Topics:</strong> {item.topics}
              </div>
              <div>
                <strong>File Path:</strong> {item.file_path}
              </div>
              {/* Actions */}
              <div className="flex justify-end space-x-2">
                <button
                  className="text-red-500"
                  onClick={() => handleDeleteClick(item.content_id)}
                >
                  {" "}
                  <TrashIcon className="md:w-[25px] md:h-[25px] w-[17px] h-[17px] text-red-500" />
                </button>
                {/* Replace Link with actual Link component or <a> tag */}
                <a
                  href={`content/${item.content_id}`}
                  className="text-blue-500 underline"
                >
                  Link
                </a>
              </div>
            </div>
          ))
        ) : (
          <div className="hidden"><Nothing
          title="No Content Available"
          para="There are currently no content to display.
                  Please check back later,or add content."
        /></div>
        )}
      </div>

      <div className="flex justify-between items-end w-full  ">
        <button
          onClick={handlePreviousPage}
          disabled={currentPage <= 1}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] "
        >
          Previous
        </button>
        <button
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Next
        </button>
      </div>

      {isRemoveModal && (
        <DeleteModal
          deleteName="content"
          onClose={() => setIsRemoveModal(false)}
          onOpen={handleConfirmDelete}
        />
      )}
      {showDeletedUserModal && (
        <DeletedUserModal
          deleteName="content"
          onClose={handleCloseDeletedUserModal}
        />
      )}
    </div>
  );
}
