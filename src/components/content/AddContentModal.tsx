"use client";

import { motion } from "framer-motion";
import React, { useState, useEffect, ChangeEvent } from "react";
import {
  ArrowUpTrayIcon,
  XMarkIcon,
  QuestionMarkCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import Image from "next/image";
import { useCreateContentModule } from "@/hook/content/useCreateContentModule";
import { useQueryClient } from "@tanstack/react-query";

interface FormData {
  topics: string;
  contentName: string;
  contentDescription: string;
  linkPath: string;
  file?: File;
}

interface FormErrors {
  [key: string]: string;
}

interface AddContentModalProps {
  onClose: () => void;
  onContentAdded: () => void;
}

const fileIcons: { [key: string]: string } = {
  "application/pdf": "/pdf.png",
  "application/vnd.ms-powerpoint": "/ppt.png",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation": "/ppt.png",
  "application/vnd.ms-excel": "/excel.png",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "/excel.png",
  "video/mp4": "/video.png",
};

export default function AddContentModal({
  onClose,
  onContentAdded,
}: AddContentModalProps) {
  const [formData, setFormData] = useState<FormData>({
    topics: "",
    contentName: "",
    contentDescription: "",
    linkPath: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [compressValue, setCompressValue] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingText, setLoadingText] = useState<string>("Loading");

  const { mutate: createContent } = useCreateContentModule();
  const queryClient = useQueryClient();

  // Loading Animation Effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isLoading) {
      interval = setInterval(() => {
        setLoadingText((prev) => {
          if (prev === "Loading") return "Loading.";
          if (prev === "Loading.") return "Loading..";
          if (prev === "Loading..") return "Loading...";
          return "Loading";
        });
      }, 500); // Update every 500ms
    } else {
      setLoadingText("Loading"); // Reset loading text when not loading
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isLoading]);

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type, checked } = e.target;
    const fieldValue = type === "checkbox" ? checked : value;

    setFormData((prev) => ({ ...prev, [name]: fieldValue }));
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || undefined;
    setFormData((prev) => ({ ...prev, file }));
    if (file) {
      setFormData((prev) => ({ ...prev, linkPath: "" })); // Clear linkPath when a file is selected
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors["linkPath"];
        return newErrors;
      });
    }
  };

  const validateForm = (): FormErrors => {
    const newErrors: FormErrors = {};

    if (!formData.file && !formData.linkPath) {
      newErrors.file = "Either a file or link is required";
      newErrors.linkPath = "Either a link or file is required";
    }

    ["contentName", "topics", "contentDescription"].forEach((field) => {
      if (!formData[field as keyof FormData]) {
        newErrors[field] = "This field is required";
      }
    });

    return newErrors;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validationErrors = validateForm();

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    const contentData = {
      options: {
        content_name: formData.contentName,
        content_description: formData.contentDescription,
        linkPath: formData.linkPath,
        topics: formData.topics,
        compression: compressValue,
      },
      file: formData.file,
    };

    setIsLoading(true); // Start loading

    createContent(contentData, {
      onSuccess: () => {
        queryClient.invalidateQueries(["fetchAllContent"]);
        setIsLoading(false); // Stop loading
        onContentAdded();
      },
      onError: (error) => {
        console.error("Error creating content:", error);
        setIsLoading(false); // Stop loading even on error
      },
    });
  };

  const getFileIcon = (fileType?: string) => {
    if (!fileType) return <QuestionMarkCircleIcon className="h-10 w-10" />;
    const iconSrc = fileIcons[fileType] || "/link.png";
    return (
      <Image
        className="mx-auto h-10 w-auto"
        src={iconSrc}
        alt="File Icon"
        width={60}
        height={60}
      />
    );
  };

  return (
    <div className="flex justify-center items-center fixed inset-0 bg-black bg-opacity-40 overflow-auto z-10">
      <div className="flex flex-col gap-4 bg-gradient-to-tr from-slate-50 via-slate-300 to-slate-200 p-6 border border-gray-300 rounded-md bg-opacity-25 max-w-lg w-full mx-4">
        <button
          className="self-end"
          onClick={onClose}
          aria-label="Close Modal"
        >
          <XMarkIcon className="h-6 w-6 text-secondary" />
        </button>

        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          {/* Content Name and Topics */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 flex flex-col w-[100px]">
              <label
                htmlFor="contentName"
                className="text-sm font-medium text-gray-700"
              >
                Content Name
              </label>
              <input
                type="text"
                id="contentName"
                name="contentName"
                value={formData.contentName}
                onChange={handleChange}
                className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50"
              />
              {errors.contentName && (
                <p className="text-red-500 text-xs">{errors.contentName}</p>
              )}
            </div>

            <div className="flex-1 flex flex-col w-[100px]">
              <label
                htmlFor="topics"
                className="text-sm font-medium text-gray-700"
              >
                Topics
              </label>
              <input
                type="text"
                id="topics"
                name="topics"
                value={formData.topics}
                onChange={handleChange}
                className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50"
              />
              {errors.topics && (
                <p className="text-red-500 text-xs">{errors.topics}</p>
              )}
            </div>
          </div>

          {/* Content Description */}
          <div className="flex flex-col">
            <label
              htmlFor="contentDescription"
              className="text-sm font-medium text-gray-700"
            >
              Content Description
            </label>
            {formData.file?.type.startsWith("video/") && (
              <div className="flex gap-4 mt-1">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="compression"
                    checked={!compressValue}
                    onChange={() => setCompressValue(false)}
                    className="accent-sky-950"
                  />
                  <span className="ml-2 text-sm">Original</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="compression"
                    checked={compressValue}
                    onChange={() => setCompressValue(true)}
                    className="accent-sky-950"
                  />
                  <span className="ml-2 text-sm">Optimize</span>
                </label>
              </div>
            )}
            <textarea
              id="contentDescription"
              name="contentDescription"
              value={formData.contentDescription}
              onChange={handleChange}
              rows={3}
              className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50"
            />
            {errors.contentDescription && (
              <p className="text-red-500 text-xs">
                {errors.contentDescription}
              </p>
            )}
          </div>

          {/* Link Input */}
          {
            !formData.file
            &&
            <div className="flex flex-col">
              <label
                htmlFor="linkPath"
                className="text-sm font-medium text-gray-700"
              >
                Link from the web
              </label>
              <input
                type="url"
                id="linkPath"
                name="linkPath"
                value={formData.linkPath}
                onChange={handleChange}
                className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50"
                placeholder="https://example.com"
                disabled={!!formData.file} // Disable when file is present
              />
              {errors.linkPath && (
                <p className="text-red-500 text-xs">{errors.linkPath}</p>
              )}
            </div>}

          {!formData.linkPath && (
            /* File Upload */
            !formData.file && (
              <label
                htmlFor="file_upload"
                className={`flex justify-center items-center w-full h-32 p-4 border-2 border-dashed rounded-md cursor-pointer ${formData.linkPath ? "bg-slate-100 border-slate-300" : "bg-white border-gray-300"}`}
              >
                <div className="flex flex-col items-center space-y-2">
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <ArrowUpTrayIcon className="h-10 w-10 text-slate-400" />
                  </motion.div>
                  <div className="font-medium">
                    <span className="text-sm font-sans text-slate-400">Drop files to attach or {" "} </span>
                    <span className={`text-sm ${formData.linkPath ? "text-slate-400" : "text-blue-600"} underline`}>
                      browse
                    </span>
                  </div>
                </div>
                <input
                  type="file"
                  id="file_upload"
                  name="file_upload"
                  onChange={handleFileChange}
                  className="hidden"
                  disabled={!!formData.linkPath}
                />
              </label>
            )
          )}

          {errors.file && (
            <p className="text-red-500 text-xs">{errors.file}</p>
          )}

          {/* Uploaded File Preview */}
          {formData.file && (
            <div className="flex items-center p-2 border border-gray-300 rounded-md bg-gray-50">
              <div className="mr-4">{getFileIcon(formData.file.type)}</div>
              <div className="flex-1 truncate">{formData.file.name}</div>
              <div className="mr-4 text-sm text-gray-600">
                {(formData.file.size / 1024).toFixed(2)} KB
              </div>
              <button
                type="button"
                onClick={() => setFormData((prev) => ({ ...prev, file: undefined }))}
                className="text-red-500 hover:text-red-700"
                aria-label="Remove File"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>
          )}

          {/* Icon Previews (Optional) */}
          {!formData.file && (
            <div className="flex justify-center gap-6">
              {["/pdf.png", "/ppt.png", "/excel.png", "/video.png", "/link.png"].map((src, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <Image
                    src={src}
                    alt="Icon Preview"
                    width={40}
                    height={40}
                    className="rounded-md"
                  />
                </motion.div>
              ))}
            </div>
          )}


          {/* Submit Button with Loading Indicator */}
          <motion.button
            type="submit"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="w-full px-4 py-2 bg-gradient-to-r from-slate-600 via-slate-700 to-slate-800 text-white rounded-md text-sm md:text-base flex justify-center items-center"
            disabled={isLoading} // Disable during loading
          >
            {isLoading ? (
              <span className="text-white">{loadingText}</span>
            ) : (
              "Upload"
            )}
          </motion.button>
        </form>
      </div>
    </div>
  );
}
