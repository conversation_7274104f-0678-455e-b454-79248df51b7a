"use client"
import { useParams } from "next/navigation";
import {
  XMarkIcon
} from "@heroicons/react/24/outline";

import React, { useState, useEffect } from 'react';
import Link from "next/link";
import { ContentInformationProps } from "@/types/LMSTypes";
import { API_URL } from "@/utils/constants";
import { motion } from "framer-motion";

interface ContentInformationProp {
  contentDetails?: ContentInformationProps;
  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}

const ContentInformation = ({ contentDetails, onClose }: ContentInformationProp) => {
  console.log("ContentInformationProps ", contentDetails);

  useEffect(() => {

  }, [onClose]);

  const params = useParams();
  const content_id: number = parseInt(params.contentId, 10);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [showContentInformation, setshowContentInformation] = useState(false)
  const [numPages, setNumPages] = useState<number>();
  const [autoNext, setautoNext] = useState<boolean>(false);
  const [containerWidth, setContainerWidth] = useState<number>();

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (autoNext && pageNumber == numPages) {
      interval = setInterval(() => {
        setPageNumber((prevPageNumber) => prevPageNumber + 1);
      }, 10000); //update to next page after 10 seconds
    } else if (!autoNext && interval) {
      clearInterval(interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoNext]);

  function onDocumentLoadSuccess({ numPages: nextNumPages }: PDFDocumentProxy): void {
    setNumPages(nextNumPages);
  }

  console.log("numPages: ", numPages)
  document.addEventListener('contextmenu', event => {
    event.preventDefault();
  });
  console.log("contentDetails: ", contentDetails)
  console.log("showContentInformation: ", showContentInformation)

  const handleClickNotWorking = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50" onClick={() => onClose()}>

      <motion.div
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.2 }}
      >
        <div className="h-4/6 w-11/12 lg:w-[600px] lg:h-fit flex flex-col bg-white rounded-lg p-2" onClick={(e) => handleClickNotWorking(e)}>

          <div className="w-full h-10 flex justify-end px-2">
            <motion.button
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
            >
              <XMarkIcon className="w-[20px] h-[20px] bg-secondary text-white rounded-full" />
            </motion.button>
          </div>
          <div className="flex flex-col lg:flex-row">
            <div className='flex lg:flex-1 flex-col  rounded-md h-[300px] p-4 gap-4'>
              <div className='w-full h-full flex justify-center items-center'>
                {contentDetails && contentDetails.file_path && (() => {
                  const modifiedFilePath = contentDetails.file_path.replace(/uploads[\/\\]/, '');
                  return (
                    <div className="flex flex-col text-white justify-between basis-1/2 gap-1 rounded-lg text-center p-2 h-fit">
                      <div className="w-full h-fit flex justify-center items-center border-double border-4">
                        {/* <Document
                          className="flex  flex-col justify-center items-center h-fit"
                          file={`${API_URL}file_content/${modifiedFilePath}`}
                          onLoadSuccess={onDocumentLoadSuccess}
                        >

                          <Page
                            pageNumber={1}
                            height={40}
                            width={160}
                          />
                        </Document> */}
                      </div>
                    </div>
                  );
                })()}
              </div>
              {/* Add content or component here */}
            </div>
            <div className='flex-auto lg:flex-1 rounded-md h-fit lg:h-auto text-sm p-4 overflow-y-auto flex flex-col justify-between '>
              <div className="antialiased tracking-wide leading-relaxed h-fit pb-8">
                <strong>Content Name:</strong> {contentDetails?.content_name}<br />
                <strong>Content Description:</strong> {contentDetails?.content_description}
              </div>
              <div className="flex justify-center">
                <motion.button
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <button className="inline-flex w-full justify-center rounded-md bg-secondary px-10 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75">
                    Close
                  </button>
                </motion.button>
              </div>
            </div>
          </div>

        </div>
      </motion.div>
    </div >
  );
};

export default ContentInformation;
