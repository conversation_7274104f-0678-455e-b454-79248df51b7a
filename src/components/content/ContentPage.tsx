"use client"
import React, { useEffect, useState } from 'react';

import Heading from '@/components/ui/Heading';
import AddContentModal from './AddContentModal';
import Button from '../ui/Button';
import ContentTable from './ContentTable';
import { useGetAllContentInTable } from '@/hook/content/useGetAllContentInTable';
import { useContentDelete } from '@/hook/content/useContentDelete';
import SubmitModal from '../ui/SubmitModal';
import SearchBar from "@/components/admin/SearchBar"
import { useSearchContent } from '@/hook/content/useSearchContent';

export default function ContentPage() {
  // Trigger for refreshing the content list
  const [refreshTrigger, setRefreshTrigger] = useState(false);
  const { data, isLoading, error } = useGetAllContentInTable(refreshTrigger); // Assuming the hook can accept a dependency
  const [contentData, setContentData] = useState([]); 
  const [showAddContentModal, setShowAddContentModal] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [searchContent, setSearchContent] = useState("");


  const { data: searchContentData } = useSearchContent(searchContent); 
  
  useEffect(() => {
    if (!isLoading && !error && data) {
      setContentData(data); // Update contentData state when data is fetched successfully
    }
  }, [data, isLoading, error]);

  const fetchAllContent = () => {
    setRefreshTrigger(!refreshTrigger); 
    setShowAddContentModal(false); // Close AddContentModal
    setShowSubmitModal(true);// Toggle the trigger to refresh content
  };

const { mutate: deleteContent } = useContentDelete();
 

// Adjusted handleDelete function
const handleDelete = (contentId :number) => {
  deleteContent(contentId, {
    onSuccess: () => {
      // Filter out the deleted item from contentData
      const updatedContentData = contentData.filter(item => item.content_id !== contentId);
      setContentData(updatedContentData);
    },
    onError: () => {
      console.error('Deletion error:', deleteError);
    }
  });
};

const handleAddContent = () => {
  setShowAddContentModal(true);
};

const onCloseAddContentModal = () => {
  setShowAddContentModal(false);
};

const onCloseSubmitModal = () => {
  setShowSubmitModal(false);
};

const handleSearch = (contentSearch:string) => {
  //Call the filter content API and use the filtered content here
  console.log("Check for content",contentSearch );
  setSearchContent(contentSearch);
};

  return (
<div className="w-full flex h-[88vh] bg-white flex-col gap-3 p-3 overflow-auto">

        <div className='flex flex-col justify-start h-[5vh]'>
      <Heading pgHeading="Content" />
      </div>

      <div className='flex justify-betweenflex flex-col md:flex-row gap-2 w-full h-[10vh] justify-between items-end'>
        <SearchBar onSearch={handleSearch}/>
        <div><Button btnName='Add Content' onClickFunction={handleAddContent} /></div>
      </div>

      {showAddContentModal && (
        <AddContentModal
          onClose={onCloseAddContentModal}
          onContentAdded={fetchAllContent}
        />
      )}
      {showSubmitModal && (
        <SubmitModal
          modalName="Content Upload Success"
          onClose={onCloseSubmitModal}
        />
      )}
      <div className='h-[75vh] '>
      {searchContent.length > 2 && searchContentData?
        <ContentTable contentData={searchContentData} onDelete={handleDelete}/>
        :
        <ContentTable contentData={contentData} onDelete={handleDelete}/>
      }</div>
    </div>
  );
}
