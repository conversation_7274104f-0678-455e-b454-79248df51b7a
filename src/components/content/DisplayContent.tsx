import React, { useEffect, useState } from 'react'
import { ChevronDoubleLeftIcon, ChevronDoubleRightIcon } from '@heroicons/react/24/outline';
import Nothing from '@/components/ui/Nothing';
import { useGetAllContentInSlide } from '@/hook/content/useGetAllContentInSlide';
import Heading from '../Heading';import { useRouter } from 'next/router';


export default function DisplayContent() {  
 
  return (
    <main className="w-full flex min-h-screen bg-primary flex-col items-center justify-between p-2 ">
      <div className="w-full ">
        <div className="p-3">
          <Heading pgHeading="Content" />
        </div>
        {content ? (

          <div className="flex flex-col justify-between overflow-y-auto bg-white  m-1 w-full h-full rounded-2xl p-5">
            <div className='h-full flex flex-col gap-7 '>

              <div className="flex  justify-between  w-full gap-2">
                <h1 className='  text-md font-medium leading-tight text-textPrimary'>
                  Content Name : {content.content_name}
                </h1>
                <h1 className=' text-md font-medium leading-tight text-textPrimary '>
                  Topics : {content.topics}
                </h1>
              </div>
              <div className="w-full px-[4rem]  border border-slate-300" />


              <div className='flex justify-between gap-2 '>
                <div className='flex  flex-col justify-between basis-1/4 rounded-lg gap-1 text-center border bg-slate-100 '>
                  <p>File Size</p>
                  <p>{content.file_size}</p>
                </div>
                <div className='flex flex-col justify-between basis-1/2 gap-1 rounded-lg text-center border bg-slate-100'>
                  <p>File Path</p>
                  <p>{content.file_path}</p>
                </div>
                <div className='flex flex-col justify-between basis-1/4 gap-1 rounded-lg text-center border bg-slate-100'>
                  <p>File Type</p>
                  <p>{content.file_type}</p>
                </div>
              </div>

              <div className="flex flex-col justify-between text-base font-medium leading-tight text-textPrimary gap-2">
                <h1 className='flex text-center justify-center'>Content Description</h1> <div className="w-full px-[4rem]  border border-slate-300" />
                <p className='flex text-center justify-center' >  {content.content_description}
                </p>
              </div>



            </div>
            <div className='flex justify-between items-end'>
              <button
                disabled={currentContentIndex === 0}
                onClick={handlePrevious}
                className={`flex gap-2 py-2 px-4 ${currentContentIndex === 0 ? 'bg-gray-200 text-gray-500' : 'bg-textColor text-white'} rounded-md text-[10px] md:text-[12px] lg:text-[15px]`}
              >
                <ChevronDoubleLeftIcon className="h-[15px] w-[15px] md:h-[20px] md:w-[20px]" />
                Previous
              </button>
              <button
                disabled={currentContentIndex === data.length - 1}
                onClick={handleNext}
                className={`flex gap-2 py-2 px-4 ${currentContentIndex === data.length - 1 ? 'bg-gray-200 text-gray-500' : 'bg-textColor text-white'} rounded-md text-[10px] md:text-[12px] lg:text-[15px]`}
              >
                Next
                <ChevronDoubleRightIcon className="h-[15px] w-[15px] md:h-[20px] md:w-[20px]" />
              </button>
            </div>

          </div>
        ) : (
          <div className="flex flex-col justify-between overflow-y-auto bg-white  m-1 w-full h-full rounded-2xl p-5">
          <Nothing
            title="No Content Available"
            para="There are currently no content to display.Please check back later, or contact admin."
          />
          </div>
        )}
      </div>
    </main>
  )
}
