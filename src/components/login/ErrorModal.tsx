import React, { useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface ErrorModalProp {
  isOpen: boolean;
  onClose: () => void;
}

const ErrorModal: React.FC<ErrorModalProp> = ({ isOpen, onClose }) => {
  return (
    <div
      className={`fixed inset-0 flex items-center justify-center gap-5 z-20 ${
        isOpen ? "block" : "hidden"
      }`}
    >
      <div
        className="fixed inset-0 bg-black opacity-50"
        onClick={onClose}
      ></div>

      <div className=" flex flex-col gap-2 bg-white w-full sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/3 p-4 rounded-lg shadow-lg z-50">
        <button className="flex justify-end" onClick={onClose}>
          <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
        </button>
        <h2 className="text-l text-slate-800 font-bold ">
          Invalid UserId/ Invalid Password
        </h2>
      </div>
    </div>
  );
};

export default ErrorModal;
