import React, { useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
//import { FaEye } from "react-icons/fa";
//import { FaEyeSlash } from "react-icons/fa";
import { getUser } from "../../api/user.localStorage";
//create api function for change password inside the api directory

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ForgetPassword: React.FC<ProfileModalProps> = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = async () => {
    const user = getUser();
    if (email === "") {
      setError("Please fill all fields");
    } else {
      try {
        const response = await fetch(
          `http://127.0.0.1:8080/user/change_password_without_otp?old_password=&new_password=`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              // If you have authentication, include the token in the headers
              Authorization: `Bearer ${user?.token}`,
              // 'Authorization': `Bearer ${token}`
            },
          }
        );

        if (response) {
          console.log("Email send successfully");
          alert("Email send successfully");
        }
      } catch (error) {
        console.error("Failed to email:", error);
        alert("Failed to email");
      }
      onClose();
    }
  };

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center gap-5 z-20 ${
        isOpen ? "block" : "hidden"
      }`}
    >
      <div
        className="fixed inset-0 bg-black opacity-50"
        onClick={onClose}
      ></div>

      <div className=" flex flex-col gap-2 bg-white w-full sm:w-1/2 md:w-1/2 lg:w-1/2 xl:w-1/3 p-4 rounded-lg shadow-lg z-50">
        <button className="flex justify-end" onClick={onClose}>
          <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
        </button>
        <h2 className="text-2xl text-slate-800 font-bold mb-4">
          Forget Password(Coming soon)
        </h2>
        {error && (
          <p className="text-red-600 text-sm font-medium leading-6 mb-2">
            {error}
          </p>
        )}

        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium leading-6 text-textColor"
          >
            Email address
          </label>
          <div className="mt-2">
            <input
              id="email"
              name="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              autoComplete="email"
              required
              className="block w-full rounded-md border-0 py-1.5 px-1 text-textColor shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            />
          </div>
        </div>
        <button
          onClick={handleSubmit}
          className="flex w-full justify-center rounded-md bg-secondary px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75"
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default ForgetPassword;

