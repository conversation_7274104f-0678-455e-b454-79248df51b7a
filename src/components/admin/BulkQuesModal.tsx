import React, { useEffect } from "react";
import {
  XMarkIcon,
  CheckBadgeIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import Button from "../ui/Button";
import Link from "next/link";

interface SubmitModalProps {
  modalName?: string;
  modalText?: string;
  okclickPath?: any;
  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}

export default function BulkQuesModal({
  modalName,
  modalText,
  onClose,
  okclickPath,
}: SubmitModalProps) {
  useEffect(() => {}, [onClose]);

  return (
    <div className="modal flex justify-center items-center fixed inset-0 bg-black bg-opacity-40 overflow-auto z-10">
      <div className="flex flex-col gap-5 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md w-[400px] h-[300px]">
        <button className="flex justify-end" onClick={onClose}>
          <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
        </button>

        {modalName === "Error" ? (
          <div className="flex flex-col gap-5 justify-center items-center text-center">
            <XCircleIcon className="h-[50px] w-[50px] text-red-500" />
            <h1 className="text-lg">{modalName}</h1>
            {modalText ? (
              <p>{modalText}</p>
            ) : (
              <p>{`The ${modalName} is submit.`}</p>
            )}
            
          </div>
        ) : (
          <div className="flex flex-col gap-5 justify-center items-center text-center">
            <CheckBadgeIcon className="h-[50px] w-[50px] text-green-500" />
            <h1 className="text-lg">{modalName}</h1>
            {modalText ? (
              <p>{modalText}</p>
            ) : (
              <p>{`The ${modalName} is submit.`}</p>
            )}
            {okclickPath ? (
              <Link href={okclickPath}>
                <Button btnName="OK" />
              </Link>
            ) : null}
          </div>
        )}
      </div>
    </div>
  );
}


