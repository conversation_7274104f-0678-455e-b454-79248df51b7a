"use client";
import { useModuleContentByModuleId } from '@/hook/admin/module/usegetModuleContentByModuleId';
import React, { useEffect, useState } from 'react';
import ContentListForModule from './ContentListForModule';
import { useGetAllContentInModule } from '@/hook/admin/module/useGetAllContentInModule';

export default function AddRemoveContentFromModule({ moduleIDProp }: any) {
  const [moduleId, setModuleId] = useState(parseInt(moduleIDProp || ''));

  const { data: userValues, isLoading: userValuesLoading, isError: userValuesError } = useModuleContentByModuleId(moduleId);
  const { data: contents, isLoading: contentsLoading, isError: contentsError } = useGetAllContentInModule(moduleId);

  useEffect(() => {
    if (!userValuesLoading && !userValuesError) {
      console.log('data:', userValues);
    }
  }, [userValues, userValuesLoading, userValuesError]);

  useEffect(() => {
    if (!contentsLoading && !contentsError) {
      console.log("contents data:", contents);
    }
  }, [contents, contentsLoading, contentsError]);

  if (contentsLoading || userValuesLoading) {
    return <div>Loading...</div>;
  }

  if (contentsError || userValuesError) {
    return <div>Error fetching contents</div>;
  }

  return (
    <div className='h-[80vh] flex flex-col gap-4'>
      <div className="h-[40vh]">
        <ContentListForModule type={"all"} userValues={contents} moduleId={moduleId} />
      </div>
      <div className="h-[40vh]">
        <ContentListForModule type={"remove"} userValues={userValues} moduleId={moduleId} />
      </div>
    </div>
  );
}
