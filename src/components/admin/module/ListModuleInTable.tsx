"use client";
import Nothing from "@/components/ui/Nothing";
import { PencilSquareIcon, TrashIcon } from "@heroicons/react/24/outline";
import React, { useState, useEffect } from "react";

import Link from "next/link";
import DeleteModal from "../userGroup/DeleteModal";
import DeletedUserModal from "../userGroup/DeletedUserModal";
import { useCount } from "@/context/searchStore";

export default function ListModuleInTable({ ModuleData, onDelete }) {

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const totalPages = Math.ceil(ModuleData?.length / itemsPerPage);
  const [isRemoveModal, setIsRemoveModal] = useState(false);
  const [showDeletedUserModal, setShowDeletedUserModal] = useState(false);
  const [selectedModuleId, setSelectedModuleId] = useState(null);
  const { searchValue, searchNumber } = useCount();


  let temp2:boolean = false 

useEffect(() => {
  temp2 = useCount.getState().searchValue
  console.log("search numberss:- ", temp2)
  console.log("search numberss page:- ", currentPage)
  if(temp2==true && currentPage != 1){
    setCurrentPage(1)
  }
}, [searchNumber])


  const handleDeleteClick = (moduleId: any) => {
    setSelectedModuleId(moduleId); // Store the ID for deletion
    setIsRemoveModal(true); // Open the modal for confirmation
  };

  const handleConfirmDelete = () => {
    if (selectedModuleId !== null) {
      onDelete(selectedModuleId); // Now, actually call onDelete
    }
    setIsRemoveModal(false); // Close the modal
    setSelectedModuleId(null); // Reset the selected ID
  };

  const handleCloseDeletedUserModal = () => {
    setShowDeletedUserModal(false); // Close the DeletedUserModal
  };


  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));


  };
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = ModuleData?.slice(indexOfFirstItem, indexOfLastItem) || [];

  

  return (
    <div className="flex flex-col gap-2 justify-start bg-white items-start rounded-md w-full h-full border shadow-md">
      {/* Scrollable table container for medium to large screens */}
      <div className="overflow-auto h-full  w-full  rounded-md">
        <table className="min-w-full divide-y divide-gray-200 h-full">
          <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
            <tr className="w-full ">
              <th className="p-2  text-left">Module No</th>
              <th className="p-2  text-left">Module Name</th>
              <th className="p-2  text-left">Module Headline</th>
              <th className="p-2  text-left">Module Description</th>
            
              <th className="p-2  text-center">Delete</th>
              <th className="p-2  text-center">Edit</th>
              <th className="p-2 text-center">Open Module</th>
            </tr>
          </thead>

          <tbody className="h-full" >
            {Array.isArray(currentItems) && currentItems.length > 0 ? (
              currentItems.map((item) => (
                <tr key={item.module_id} className="border-b h-10 ">
                  <td className="p-2 px-3 text-left">{item.module_id}</td>
                  <td className="p-2 text-left">{item.module_name}</td>
                  <td className="p-2 text-left">{item.module_headline}</td>
                  <td className="p-2 text-left">{item.module_description}</td>
                

                  <td className="p-2 text-center">

                    <button
                      onClick={() =>
                        handleDeleteClick(item.module_id)
                      }
                    >
                      <TrashIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-red-500" />
                    </button>

                  </td>

                  <td className=" p-2 " >
                  
                      <a
                      href={`/admin/module/${item.module_id}?action=edit`}
                      className="text-blue-500 underline flex justify-center "
                    >
                      <PencilSquareIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-secondary" />

                     </a>
                  </td>

                  <td className="p-2 text-center">
                    <a
                      href={`/admin/module/${item.module_id}?action=view`}
                      className="text-blue-500 underline"
                    >
                      Link
                      </a>
            
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="text-center p-4">
                  <Nothing
                    title="No Module Available"
                    para="There are currently no Module to display.
                        Please check back later."
                  />
                </td>
              </tr>
            )}
          {/* Empty rows to maintain the table's appearance for fewer items */}
          {currentItems.length < itemsPerPage && 
          [...Array(itemsPerPage - currentItems.length)].map((_, index) => (
            <tr key={`empty-${index}`} className=" h-10">
              <td colSpan={7}></td>
            </tr>
          ))
        }
          </tbody>
        </table>
      </div>

      <div className="flex justify-between  items-end w-full ">
        <button
          onClick={handlePreviousPage}
          disabled={currentPage <= 1}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] "
        >
          Previous
        </button>
        <button
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Next
        </button>
      </div>
      {isRemoveModal && (
        <DeleteModal
          deleteName="module"
          onClose={() => setIsRemoveModal(false)}
          onOpen={handleConfirmDelete}
        />
      )}
      {showDeletedUserModal && (
        <DeletedUserModal
          deleteName="module"
          onClose={handleCloseDeletedUserModal}
        />
      )}
    </div>
  );
}
