import React, { useState } from 'react';


const SearchComponent = ({ data, types, setFilterData }) => {
    const [searchTerm, setSearchTerm] = useState('');

    const handleSearch = (e) => {
        if(types==="content"){
            setSearchTerm(e.target.value);
            const filteredData = data.filter(item =>
                item.content_name.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilterData(filteredData) //coming as a prop
            console.log("searchTerm")
        }else if(types==="assessment"){
            setSearchTerm(e.target.value);
            const filteredData = data.filter(item =>
                item.assessment_name.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilterData(filteredData) //coming as a prop
            console.log("searchTerm")
        }else{
            console.log("logic not working SEARCHBAR")
        }
        
    };

    return (
        <div className=''>
            {types === "content" ? 
                <input
                type="text"
                placeholder="Search by content "
                value={searchTerm}
                onChange={handleSearch}
            /> : <input
                type="text"
                placeholder="Search by assessment"
                value={searchTerm}
                onChange={handleSearch}
            />}
        </div>
    );
};

export default SearchComponent;
