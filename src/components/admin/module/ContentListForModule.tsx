"use client";
import Heading from "@/components/ui/Heading";
import Nothing from "@/components/ui/Nothing";
import SubmitModal from "@/components/ui/SubmitModal";
import { useDeleteModuleContent } from "@/hook/admin/module/useDeleteModuleContent";
import { usePostAddModuleContent } from "@/hook/admin/module/usePostAddModuleContent";
import { usePutUpdateModuleSeqContent } from "@/hook/admin/module/usePutUpdateModuleSeqContent";
import { AddModuleContentOptions, UpdateModuleSeqContentOptions } from "@/types/LMSTypes";
import { ArrowDownIcon, ArrowUpIcon, MinusIcon, PlusIcon } from "@heroicons/react/24/outline";

import React, { useEffect, useState } from "react";

export default function ContentListForModule({ type, userValues, moduleId }: any) {
  const [isModuleId, setIsModuleId] = useState(moduleId);
  const [isContentId, setIsContentId] = useState<number | null>(null);
  const [contentData, setContentData] = useState(userValues);
  const [showSubmitModal, setShowSubmitModal] = useState(false);



  useEffect(() => {
    if (Array.isArray(userValues)) {
      setContentData(userValues.map((item, index) => ({ ...item, sequence: index + 1 })));
    }
  }, [userValues]);
  

  const sequence = contentData ? contentData.length + 1 : 1;
  const newDetail: AddModuleContentOptions = {
    module_id: isModuleId,
    content_id: isContentId,
    sequence: sequence,
  };

  const addContentToModule = usePostAddModuleContent(newDetail);
  const updateModuleSeqContent = usePutUpdateModuleSeqContent();

  const { mutate: deleteContent, isLoading, isError } = useDeleteModuleContent();

  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const newContentData = [...contentData];
    const temp = newContentData[index - 1];
    newContentData[index - 1] = newContentData[index];
    newContentData[index] = temp;
    setContentData(newContentData.map((item, idx) => ({ ...item, sequence: idx + 1 })));
  };

  const handleMoveDown = (index: number) => {
    if (index === contentData.length - 1) return;
    const newContentData = [...contentData];
    const temp = newContentData[index + 1];
    newContentData[index + 1] = newContentData[index];
    newContentData[index] = temp;
    setContentData(newContentData.map((item, idx) => ({ ...item, sequence: idx + 1 })));
  };

  const handleClickUser = async (type: string) => {
    if (type === "add" && isContentId !== null) {
      try {
        await addContentToModule.mutate();
        setTimeout(() => {}, 1000);
        console.log("Content added successfully.");
      } catch (error) {
        console.error("Error adding content to module:", error);
      }
    }
  };

  const handleDelete = (content_id: number) => {
    if (!moduleId) {
      console.error("moduleId is undefined");
      return;
    }
    
    deleteContent({ module_id: moduleId, content_id }, {
      onSuccess: () => {
        setContentData((prevData) => {
          const newData = prevData.filter(item => item.content_id !== content_id);
          return newData.length ? newData : []; // Ensure state updates correctly even if empty
        });
      },
      onError: (error) => {
        console.error("Error deleting assessment:", error);
      },
    });
  };
  

  // const handleDelete = (content_id: number) => {
  //   if (!moduleId) {
  //     console.error("moduleId is undefined");
  //     return;
  //   }
  //   deleteContent({ module_id: moduleId, content_id });
  // };

  const handleSave = () => {
    contentData.forEach((item) => {
      const updateDetail: UpdateModuleSeqContentOptions = {
        module_id: isModuleId,
        content_id: item.content_id,
        sequence: item.sequence,
      };
     
      console.log("Sending updateDetail to backend:", updateDetail);
      setShowSubmitModal(true)
      updateModuleSeqContent.mutate(updateDetail, {
        onError: (error) => {
          console.error("Error updating sequence:", error);
        },
        onSuccess: (data) => {
          console.log("Update success:", data);
      
        },
      });
    });
    
  };

  if (type === "remove") {
    return (
      <div className="flex flex-col gap-2  shadow-lg border-y-8 border-white bg-white mx-auto w-full rounded-2xl h-full">
        <div className="flex justify-between px-2"> 
        <Heading pgHeading="Content In Module" />
        <button type="submit" className="w-30 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]" 
         onClick={handleSave}>
          Save the Sequence
        </button>
        </div>
       
        <div className="overflow-y-auto w-full h-[30vh] bg-white rounded-lg">
          <table className="rounded-xl items-start w-full h-fit">
            <thead className="bg-secondary text-white text-sm z-10 sticky top-0 uppercase w-full">
              <tr className="w-full h-fit">
                <th className="p-2 text-left">Sequence</th>
                <th className="p-2 text-left">Content Id</th>
                <th className="p-2 text-left">Content Name</th>
                <th className="p-2 text-left">Description</th>
                <th className="p-2 text-left">Topics</th>
                <th className="p-2 text-left">File Path</th>
                <th className="p-2 text-center">Action</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto">
              {Array.isArray(contentData) && contentData.length > 0 ? (
                contentData.map((item: any, index: number) => (
                  <tr key={item.content_id} className="w-full border-b align-top h-fit">
                    <td className="p-2 flex text-center gap-2">
                      <div className="flex gap-2">
                        <button onClick={() => handleMoveUp(index)}>
                          <ArrowUpIcon className="h-6 w-6 text-white p-[2px] rounded-full bg-textColor" />
                        </button>
                        {item.sequence}
                        <button onClick={() => handleMoveDown(index)}>
                          <ArrowDownIcon className="h-6 w-6 text-white p-[2px] rounded-full bg-textColor" />
                        </button>
                      </div>
                    </td>
                    <td className="p-2 px-3 text-left">{item.content_id}</td>
                    <td className="p-2 text-left">{item.content_name}</td>
                    <td className="p-2 text-left">{item.content_description}</td>
                    <td className="p-2 text-left">{item.topics}</td>
                    <td className="p-2 text-left">{(item.file_size / 1000).toFixed(2)} KB</td>
                    <td className="p-2 text-center">
                      <button onClick={() => handleDelete(item.content_id)}>
                        <MinusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="text-center p-4">
                    <Nothing
                      title="No Content Available"
                      para="There are currently no content to display. Please check back later, or add content."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {showSubmitModal && (
    <SubmitModal modalName={" Save "} modalText={"The Sequence is Save "} type={1} onClose={()=>{setShowSubmitModal(false)}} />
  )}
      </div>
    );
  } else if (type === "all") {
    return (
      <div className="shadow-lg border-y-8 border-white mx-auto w-full h-full rounded-2xl bg-white">
        <div className="px-2"><Heading  pgHeading="All Content" /></div>
        <div className="overflow-y-auto w-full h-[30vh] bg-white rounded-lg">
          <table className="rounded-xl items-start w-full h-fit">
            <thead className="bg-secondary text-white text-sm z-10 sticky top-0 uppercase w-full">
              <tr className="w-full h-fit">
                <th className="p-2 text-left">Content Id</th>
                <th className="p-2 text-left">Content Name</th>
                <th className="p-2 text-left">Description</th>
                <th className="p-2 text-left">Topics</th>
                <th className="p-2 text-left">File Path</th>
                <th className="p-2 text-center">Action</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto">
              {Array.isArray(userValues) && userValues.length > 0 ? (
                userValues.map((item: any) => (
                  <tr key={item.content_id} className="w-full border-b align-top h-fit">
                    <td className="p-2 px-3 text-left">{item.content_id}</td>
                    <td className="p-2 text-left">{item.content_name}</td>
                    <td className="p-2 text-left">{item.content_description}</td>
                    <td className="p-2 text-left">{item.topics}</td>
                    <td className="p-2 text-left">{item.file_path}</td>
                    <td className="p-2 text-center">
                      <button
                        onClick={() => {
                          handleClickUser("add");
                          setIsContentId(item.content_id);
                        }}
                      >
                        <PlusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="text-center p-4">
                    <Nothing
                      title="No Content Available"
                      para="There are currently no content to display. Please check back later, or add content."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
  
      </div>
    );
  } else {
    return <Nothing title="Invalid Type" para="Please provide a valid type to display content." />;
  }
 
}
