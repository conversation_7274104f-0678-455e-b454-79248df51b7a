"use client"

import { useModuleContentByModuleId } from '@/hook/admin/module/usegetModuleContentByModuleId';
import { useGetAllContentInTable } from '@/hook/content/useGetAllContentInTable';
import React, { useEffect, useState } from 'react'
import ContentListForModule from './ContentListForModule';
import Nothing from '@/components/ui/Nothing';
import Heading from '@/components/ui/Heading';

export default function ContentInModule({ moduleIDProp }: any) {

  const [moduleId, setmoduleId] = useState(parseInt(moduleIDProp || ''));

  console.log("module id for Table" + moduleIDProp)

  const { data: userValues, isLoading: userValuesLoading, isError: userValuesError } = useModuleContentByModuleId(moduleId);


  useEffect(() => {
    if (!userValuesLoading && !userValuesError) {
      console.log('data:', userValues);
    }
  }, [userValues, userValuesLoading, userValuesError]);



  if (userValuesLoading) {
    return <div>Loading...</div>;
  }

  if (userValuesError) {
    return <div>Error fetching contents</div>;
  }

  return (
    <>
      <Heading pgHeading="Content in Module" />
      <div className="flex flex-col gap-2 justify-between  bg-white items-start rounded-md w-full h-full border shadow-md">
        {/* Scrollable table container for medium to large screens */}
        <div className="overflow-auto  w-full h-[50vh]   rounded-md">
          <table className="min-w-full divide-y divide-gray-200 h-fit">
            <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
              <tr className="w-full h-fit">
              <th className="p-2 text-left">Sequence No.</th>
                <th className="p-2 text-left">Content Id</th>
                <th className="p-2 text-left">Content Name</th>
                <th className="p-2 text-left">Description</th>
                <th className="p-2 text-left">Topics</th>
                <th className="p-2 text-left">File Path</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto">
              {Array.isArray(userValues) && userValues.length > 0 ? (
                [
                  ...new Map(
                    userValues.map((item) => [item.content_id, item])
                  ).values(),
                ].map((item) => (
                  <tr
                    key={item.content_id}
                    className="  w-full   border-b h-fit"
                  >
                    <td className="p-2 px-3 text-left">{item.sequence}</td>
                    <td className="  p-2  px-3 text-left  ">
                      {item.content_id}
                    </td>
                    <td className="  p-2  text-left  ">{item.content_name}</td>
                    <td className="  p-2  text-left ">
                      {item.content_description}
                    </td>
                    <td className="  p-2  text-left ">{item.topics}</td>
                    <td className=" p-2  text-left ">{item.file_path}</td>


                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="7" className="text-center p-4">
                    <Nothing
                      title="No Content Available"
                      para="There are currently no content to display.
                      Please check back later,or add content."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
}
