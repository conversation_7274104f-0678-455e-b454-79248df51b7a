"use client";
import React, { useState, useEffect } from "react";
import { PencilSquareIcon, TrashIcon } from "@heroicons/react/24/outline";
import SearchBar from "@/components/admin/module/SearchBar";
import CreateContentAssessmentModal from "@/components/admin/module/CreateContentAssessmentModal";
import DeleteModal from "../userGroup/DeleteModal";
import DeletedUserModal from "../userGroup/DeletedUserModal";
import { useGetAllContents } from "@/hook/admin/content/useGetAllContents"
import { useGetAllAssessments } from "@/hook/assessments/useGetAllAssessments"
import { useGetAllContentInTable } from "@/hook/content/useGetAllContentInTable";



const mockContentData = [
  {
    "content_id": 1,
    "content_name": "Python",
    "content_description": "python description",
    "topics": "Python topic",
    "created_by": "<EMAIL>",
    "file_type": "application/x-sql",
    "file_size": "60855",
    "file_path": "uploads\\lms_data_backup.sql"
  },
  {
    "content_id": 2,
    "content_name": "asd",
    "content_description": "asd",
    "topics": "asd",
    "created_by": "<EMAIL>",
    "file_type": "application/x-sql",
    "file_size": "60855",
    "file_path": "uploads\\lms_data_backup.sql"
  },
  {
    "content_id": 3,
    "content_name": "Coasdasd",
    "content_description": "asdasdas",
    "topics": "asdasdas",
    "created_by": "<EMAIL>",
    "file_type": "application/x-sql",
    "file_size": "60855",
    "file_path": "uploads\\lms_data_backup.sql"
  },
  {
    "content_id": 4,
    "content_name": "python explaining video",
    "content_description": "python explaining video desc",
    "topics": "python explaining video topics",
    "created_by": "<EMAIL>",
    "file_type": "application/x-sql",
    "file_size": "60855",
    "file_path": "uploads\\lms_data_backup.sql"
  },
  {
    "content_id": 5,
    "content_name": "Java explaining video",
    "content_description": "Java explaining video desc",
    "topics": "Java explaining video topics",
    "created_by": "<EMAIL>",
    "file_type": "application/x-sql",
    "file_size": "60855",
    "file_path": "uploads\\lms_data_backup.sql"
  }
]

const mockAssessment = [
  {
    "assessment_id": 1,
    "assessment_name": "Python test 1",
    "instructions": "instructions",
    "total_time_allowed": 20,
    "total_marks": 50,
    "source": "Dono Consulting",
    "assessment_evaluation_strategy": 0
  },
  {
    "assessment_id": 2,
    "assessment_name": "string",
    "instructions": "string",
    "total_time_allowed": 0,
    "total_marks": 0,
    "source": "string",
    "assessment_evaluation_strategy": 0
  }
]

const AddContentAndAssessment: React.FC<UploadQuestionProps> = ({
  setShowModal,
  setIndexNumber,
}) => {
  const [selectedContent, setSelectedContent] = useState<number[]>([]);
  const [selectedAssessment, setSelectedAssessment] = useState<number[]>([]);
  const [showSelectedOnly, setShowSelectedOnly] = useState<boolean>(false);
  const [questionSearch, setQuestionSearch] = useState("");
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [isRemoveModal, setIsRemoveModal] = useState<boolean>(false);
  const [showDeletedUserModal, setShowDeletedUserModal] = useState(false);
  const [filteredContentData, setFilteredContentData] = useState([]);
  const [filteredAssessmentData, setFilteredAssessmentData] = useState([]);

  const { data: contentData, isLoading: groupLoading, isError: groupError } = useGetAllContentInTable();

  useEffect(() => {
    if (!groupLoading && !groupError) {
      // your logic
    }
  }, [contentData, groupLoading, groupError]);

  const { data: AssessmentData, isLoading: AssessmentLoading, isError: AssessmentError } = useGetAllAssessments(0,1000);

  useEffect(() => {
    if (!groupLoading && !groupError) {
      // your logic
    }
  }, [contentData, groupLoading, groupError]);

  // console.log("contentData", contentData)
  console.log("FilteredcontentData", filteredContentData)
  // console.log("AssessmentData", AssessmentData)
  console.log("filteredAssessmentData", filteredAssessmentData)
  console.log("selectedAssessment", selectedAssessment)
  console.log("selectedContent", selectedContent)


  const handleCheckboxChange = (type: string, id: number) => {
    if (type === "assessment") {
      if (selectedAssessment.includes(id)) {
        setSelectedAssessment(prevState => prevState.filter(item => item !== id));
      } else {
        setSelectedAssessment(prevState => [...prevState, id]);
      }
    } else if (type === "content") {
      if (selectedContent.includes(id)) {
        setSelectedContent(prevState => prevState.filter(item => item !== id));
      } else {
        setSelectedContent(prevState => [...prevState, id]);
      }
    }
  };



  const toggleView = () => setShowSelectedOnly(!showSelectedOnly);


  // const selectedData = data.filter((item) => item.selected);

  const handleCloseModal = () => {
    setIsRemoveModal(false);
  };

  return (
    <div className="flex flex-col justify-start h-full w-full gap-4">
      <div className="flex justify-between">
        {showSelectedOnly ? <SearchBar data={contentData} type="content" setFilterData={setFilteredContentData} /> : <SearchBar data={AssessmentData}  setFilterData={setFilteredAssessmentData} />}

        <label className="autoSaverSwitch relative inline-flex cursor-pointer select-none items-center">
          <input
            type="checkbox"
            name="autoSaver"
            className="sr-only"
            checked={showSelectedOnly}
            onChange={toggleView}
          />
          <span
            className={`slider mr-3 flex h-[26px] w-[50px] items-center rounded-full p-1 duration-200 ${showSelectedOnly ? "bg-secondary" : "bg-secondary"
              }`}
          >
            <span
              className={`dot h-[18px] w-[18px] rounded-full bg-white duration-200 ${showSelectedOnly ? "translate-x-6" : ""
                }`}
            ></span>
          </span>
          <span className="label flex items-center text-md font-medium text-black">
            {showSelectedOnly
              ? "Show Content"
              : "Show Assessment"}{" "}
          </span>
        </label>
      </div>
      <div className="overflow-y-auto border rounded-lg ">
        {showSelectedOnly ? <table className="w-full h-fit ">
          <thead className="bg-secondary text-white">
            <tr className="h-fit">
              <th className="p-2 text-left">Assessment Id</th>
              <th className="p-2 text-left">Assessment Name</th>
              <th className="p-2 text-left">Instructions</th>
              <th className="p-2 text-left">Total Time Allowed</th>
              <th className="p-2 text-left">Total Marks</th>
              <th className="p-2 text-left">Source</th>
              <th className="p-2 text-left">Assessment Evaluation Strategy</th>
              <th className="p-2 text-left">Select</th>
            </tr>
          </thead>
          <tbody>
            {(mockAssessment).map((item, index) => (
              <tr key={item.assessment_id} className="border-b h-fit">
                <td className="p-2 px-3 text-left">{item.assessment_id}</td>
                <td className="p-2 text-left">{item.assessment_name}</td>
                <td className="p-2 text-left">{item.instructions}</td>
                <td className="p-2 text-left">{(item.total_time_allowed/60).toFixed(2)}</td>
                <td className="p-2 text-left">{item.total_marks}</td>
                <td className="p-2 text-left">{item.source}</td>
                <td className="p-2 text-left">{item.assessment_evaluation_strategy}</td>

                <td className="p-2">
                  <input
                    type="checkbox"
                    onChange={() => handleCheckboxChange("assessment", item.assessment_id)}
                  />

                </td>
              </tr>
            ))}
          </tbody>
        </table> : <table className="w-full h-fit ">
          <thead className="bg-secondary text-white">
            <tr className="h-fit">
              <th className="p-2 text-left">Content Id</th>
              <th className="p-2 text-left">Content Name</th>
              <th className="p-2 text-left">Content Description</th>
              <th className="p-2 text-left">Topics</th>
              <th className="p-2 text-left">Created By</th>
              <th className="p-2 text-left">File Type</th>
              <th className="p-2 text-left">File Size</th>
              <th className="p-2 text-left">Select</th>
            </tr>
          </thead>
          <tbody>
            {(mockContentData).map((item) => (
              <tr key={item.content_id} className="border-b h-fit">
                <td className="p-2 px-3 text-left">{item.content_id}</td>
                <td className="p-2 text-left">{item.content_name}</td>
                <td className="p-2 text-left">{item.content_description}</td>
                <td className="p-2 text-left">{item.topics}</td>
                <td className="p-2 text-left">{item.created_by}</td>
                <td className="p-2 text-left">{item.file_type}</td>
                <td className="p-2 text-left">{item.file_size}</td>

                <td className="p-2">
                  <input
                    type="checkbox"
                    onChange={() => handleCheckboxChange("content", item.content_id)}
                  />

                </td>
              </tr>
            ))}
          </tbody>
        </table>}
      </div>{" "}
      {showSelectedOnly ? (
        <div className="flex justify-end items-end gap-2">
          <button
            type="button"
            className={`flex gap-2 py-2 px-6 text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] ${selectedAssessment.length > 0
              ? "bg-textColor" // Button enabled
              : "bg-gray-400" // Button disabled
              }`}
            disabled={selectedAssessment.length <= 0} // Button is disabled 
            onClick={() => {
              setShowCreateModal(true);
            }}
          >
            Add Assessment
          </button>
        </div>
      ) : <div className="flex justify-end items-end gap-2">
        <button
          type="button"
          className={`flex gap-2 py-2 px-6 text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] ${selectedContent.length > 0
            ? "bg-textColor" // Button enabled
            : "bg-gray-400" // Button disabled
            }`}
          disabled={selectedContent.length <= 0} // Button is disabled if selectedContent array is empty
          onClick={() => {
            setShowCreateModal(true);
          }}
        >
          Add Content
        </button>
      </div>}
      {showCreateModal && (
        <CreateContentAssessmentModal
        type="content"
          onClose={() => {
            setShowCreateModal(false);
          }}
        />
      )}
    </div>
  );
};

export default AddContentAndAssessment;
