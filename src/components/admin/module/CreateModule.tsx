import CreateContentAssessmentModal from "./CreateContentAssessmentModal";
import React, { useState } from "react";
import { useAddModule } from "@/hook/admin/module/useAddModule";

import { AddModule } from "@/types/LMSTypes";
interface FormErrors {
  [key: string]: string;
}

export default function CreateModule({ setModuleNumber }) {
  const [moduleName, setModuleName] = useState("");
  const [heading, setHeading] = useState("");
  const [moduleDescription, setModuleDescription] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const myModule: AddModule = {
    module_config: {
      from_attributes: true
    },
    module_name: moduleName,
    module_headline: heading,
    module_description: moduleDescription
  };

  const handleModuleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setModuleName(e.target.value);
  };

  const handleHeadingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHeading(e.target.value);
  };

  const handleModuleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setModuleDescription(e.target.value);
  };

  const addingModule = useAddModule(myModule);
  const temp = addingModule.data
  console.log("temp value",temp) 
  setModuleNumber(temp)
  const handleSubmit = async () => {
    const newErrors: FormErrors = {};
    if (moduleName.length > 0 && heading.length > 0 && moduleDescription.length >0 && addingModule) {
      try {
        await addingModule.mutate();
        setShowCreateModal(true);
        setTimeout(() => {
          setShowCreateModal(false);
        }, 1000);
      } catch (error) {
        console.error("Error while submitting:", error);
      }
    } else {
      console.log("Please fill in all fields before submitting.");
     
      if (moduleName.length === 0){
        newErrors.moduleName = "Please enter a module name";
        setErrors(newErrors);
      } 
      if (heading.length === 0){
        newErrors.heading = "Please enter a module heading";
        setErrors(newErrors);
      } 
      if (moduleDescription.length === 0){
        newErrors.moduleDescription = "Please enter a module description";
        setErrors(newErrors);
      } 
    }
  };

  return (
    <div className="w-full p-4">
      <form>
        <div className="flex justify-between w-full  h-5/6 gap-3">
          <div className="flex flex-col  w-full gap-2">
            <div className="flex flex-col gap-1">
              <label
                htmlFor="moduleName"
                className="block text-sm font-medium text-gray-700 "
              >
                Module Name
              </label>
              <input
                type="text"
                required
                id="moduleName"
                name="moduleName"
                value={moduleName}
                onChange={handleModuleNameChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black"
              />
              {errors.moduleName && (
              <p className="text-red-500 text-xs italic">
                {errors.moduleName}
              </p>
              )}
            </div>
          </div>
          <div className="flex flex-col h-full w-full gap-2">
            <div className="flex flex-col gap-1">
              <label
                htmlFor="heading"
                className="block text-sm font-medium text-gray-700"
              >
                Module Heading
              </label>
              <input
                type="text"
                id="heading"
                name="heading"
                required
                value={heading}
                onChange={handleHeadingChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black"
              />
              {errors.heading && (
              <p className="text-red-500 text-xs italic">
                {errors.heading}
              </p>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <label
            htmlFor="moduleDescription"
            className="block text-sm font-medium text-gray-700"
          >
            Module Description
          </label>
          <textarea
            id="moduleDescription"
            name="moduleDescription"
            value={moduleDescription}
            required
            onChange={handleModuleDescriptionChange}
            onBlur={handleSubmit} // Submit on blur
            rows={5}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-black"
          ></textarea>
          {errors.moduleDescription && (
            <p className="text-red-500 text-xs italic">
              {errors.moduleDescription}
            </p>
           )}
        </div>
        {showCreateModal && (
          <CreateContentAssessmentModal
            type=""
            onClose={() => {
              setShowCreateModal(false);
            }}
          />
        )}
      </form>
    </div>
  );
}
