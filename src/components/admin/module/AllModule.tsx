"use client"
import { getAllModules } from '@/api/admin/module/getAllModules';
import React, { useEffect, useState } from 'react'
import ModuleTable from './ModuleTable';
import ModuleInGroup from './ModuleInGroup';

export default function AllModule({type}:string) {
    //all module table
    const [isModuleData, setIsModuleData] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

      //all module table
    useEffect(() => {
        getAllModules(0,10)
          .then(data => {
            setIsModuleData(data);
            setIsLoading(false);
          })
          .catch(error => {
            setError(String(error))
            setIsLoading(false);
          });
      }, []);
    
      if (isLoading) {
        return <div>Loading...</div>;
      }
    
      if (error) {
        return <div>Error loading content: {error}</div>;
      }

  return (
    <>
    
    <ModuleTable moduleData={isModuleData} setShowModal={setIsModuleData} />
  
    <ModuleInGroup moduleData={isModuleData} setShowModal={setIsModuleData} />
  </>
  )
}
