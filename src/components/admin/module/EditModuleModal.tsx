"use client";

import React, { useState, ChangeEvent } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import {  UpdateModule } from "@/types/LMSTypes";


import { useUpdateModule } from "@/hook/admin/module/useUpdateModule";



interface FormErrors {
  [key: string]: string;
}

interface EditModuleModalProps {
  onClose: () => void;
  onsubmit: () => void;
  moduleData: any;
  
}

export default function EditModuleModal({ onClose, onsubmit,moduleData }: EditModuleModalProps) {

  const [isModuleName, setIdModuleName] = useState(moduleData?.module_name || '');
  const [isModuleHeading, setIdModuleHeading] = useState(moduleData?.module_headline || '');
  const [isModuleDescription, setIdModuleDescription] = useState(moduleData?.module_description || '');

  const muduleId = moduleData.module_id;

  const updateModuleData: UpdateModule = {
  module_name: isModuleName,
  module_heading: isModuleHeading,
  module_description: isModuleDescription,
  module_Id:muduleId,
  };
  const [errors, setErrors] = useState<FormErrors>({});
  
  const { mutateAsync: updateModule } = useUpdateModule();


  const handleSubmit = async (e) => {
    e.preventDefault();
  
    // Reset errors
    setErrors({});
  
    // Validation logic
    let newErrors = {};
    if (!isModuleName.trim()) newErrors.ModuleName = "Module Name is required.";
    if (!isModuleHeading.trim()) newErrors.ModuleHeading = "Module Heading is required.";
    if (!isModuleDescription.trim()) newErrors.ModuleDescription = "Module Description is required.";
  
    if  (Object.keys(errors).length === 0) {
      const updateData = {
          module_id: moduleData.module_id, // Ensure you include the module_id
          module_name: isModuleName,
          module_heading: isModuleHeading,
          module_description: isModuleDescription,
      };

      try {
          await updateModule(updateData); // Use mutateAsync if using React Query
          onsubmit();
      } catch (error) {
          console.error("Failed to update module:", error);
          setErrors({ update: "Failed to update the module. Please try again." });
      }
  }
};
  
  return (
    <div className="modal flex justify-center items-center  fixed inset-0 bg-black bg-opacity-40  overflow-auto z-10">
      <div className="flex flex-col gap-3 modal-content bg-white m-auto p-4 border border-gray-300 rounded-md ">
        <button className="flex justify-end" onClick={onClose}>
          <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
        </button>

        <form onSubmit={handleSubmit} className="flex flex-col gap-3 ">
          <div className="flex justify-between  w-full gap-5 ">
            <div className="flex  h-full w-full gap-2">
              <div className="flex flex-col gap-1">
                <label
                  htmlFor="ModuleName"
                  className="block text-sm font-medium text-gray-700 "
                >
                  Module Name
                </label>
                <input
                  type="text"
                  id="ModuleName"
                  name="ModuleName"
                  value={isModuleName}
                  onChange={(e) => setIdModuleName(e.target.value)}
                  className="mt-1 block w-full border text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
                ></input>
                {errors.ModuleName && (
                  <p className="text-red-500 text-xs italic">
                    {errors.ModuleName}
                  </p>
                )}
              </div>
              <div className="flex flex-col gap-1">
                <label
                  htmlFor="ModuleHeading"
                  className="block text-sm font-medium text-gray-700"
                >
                  Module Heading
                </label>
                <input
                  id="ModuleHeading"
                  name="ModuleHeading"
                  value={isModuleHeading}
                  onChange={(e) => setIdModuleHeading(e.target.value)}
                  className="mt-1 block w-full border  text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
                ></input>
                {errors.ModuleHeading && (
                  <p className="text-red-500 text-xs italic">{errors.ModuleHeading}</p>
                )}
              </div>
            </div>
            
          </div>
          <div className="flex flex-col gap-1">
            <label
              htmlFor="ModuleDescription"
              className="block text-sm font-medium text-gray-700"
            >
              Module Description
            </label>
            <textarea
              id="ModuleDescription"
              name="ModuleDescription"
              value={isModuleDescription}
              onChange={(e) => setIdModuleDescription(e.target.value)}
              rows={3}
              className="mt-1 block w-full border  text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
            ></textarea>{" "}
            {errors.ModuleDescription && (
              <p className="text-red-500 text-xs italic">
                {errors.ModuleDescription}
              </p>
            )}
          </div>

          <div className="flex justify-end items-end h-full">
            <button 
              type="submit"
              className="px-6 py-2 flex gap-2  bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
            >
             Update
            </button>
          </div>
         
        </form>
      </div>
    </div>
  );
}