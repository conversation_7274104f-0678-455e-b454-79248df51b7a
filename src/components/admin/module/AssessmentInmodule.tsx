"use client";
import React, { useEffect, useState } from "react";
import { useGetModuleAssessmentByModuleId } from "@/hook/admin/module/useGetModuleAssessmentByModuleID";
import Nothing from "@/components/ui/Nothing";
import Heading from "@/components/ui/Heading";

export default function AssessmentInModule({ moduleIDProp }: any) {
  const [moduleId, setmoduleId] = useState(parseInt(moduleIDProp || ""));
  

  console.log("module id for Table" + moduleIDProp);

  const {
    data: userValues,
    isLoading: userValuesLoading,
    isError: userValuesError,
  } = useGetModuleAssessmentByModuleId(moduleId);

  useEffect(() => {
    if (!userValuesLoading && !userValuesError) {
      console.log("data:", userValues);
    }
  }, [userValues, userValuesLoading, userValuesError]);

  if (userValuesLoading) {
    return <div>Loading...</div>;
  }

  if (userValuesError) {
    return <div>Error fetching assessments</div>;
  }

  const itemsPerPage = 6;
  const totalPages = Math.ceil(userValues?.length / itemsPerPage);



  return (<>
    <Heading pgHeading="Assesement in Module" />
    <div className="flex flex-col gap-2 justify-between  bg-white items-start rounded-md w-full h-full border shadow-md">
    {/* Scrollable table container for medium to large screens */}

    <div className="overflow-auto  w-full h-[50vh]   rounded-md">
      <table className="min-w-full divide-y divide-gray-200 h-fit">
        <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
            <tr className="w-full h-fit">
            <th className="p-2 text-left">Sequence No.</th>
              <th className="p-2 text-left">Assessment Id</th>
              <th className="p-2 text-left">Assessment Name</th>
              <th className="p-2 text-left">Instructions</th>
              <th className="p-2 text-center">Total Time</th>
              <th className="p-2 text-center">Total Marks</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto">
            {Array.isArray(userValues) && userValues.length > 0 ? ([...new Map(userValues.map((item) => [item.assessment_id, item])).values(),].map((item) => (
                <tr
                  key={item.assessment_id}
                  className="w-full border-b h-fit"
                >
                  <td className="p-2 px-3 text-left">{item.sequence}</td>
                  <td className="p-2 px-3 text-left">{item.assessment_id}</td>
                  <td className="p-2 text-left">{item.assessment_name}</td>
                  <td className="p-2 text-left">{item.instructions}</td>
                  <td className="p-2 text-center">{(item.total_time_allowed/60).toFixed(2)}</td>
                  <td className="p-2 text-center">{item.total_marks}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="5" className="text-center p-4">
                  <Nothing
                    title="No assessment Available"
                    para="There are currently no assessment to display.
                          Please check back later,or add assessment."
                  />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
   
    </div>
    </>
  );
}
