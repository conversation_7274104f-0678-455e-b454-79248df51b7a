"use client";
import React, { useState } from "react";
import { XMarkIcon, CheckBadgeIcon } from "@heroicons/react/24/outline";
import Link from "next/link";


interface CreateModalProps {

  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}

export default function CreateContentAssessmentModal({  onClose }: CreateModalProps) {

  


  return (
    <div className="modal flex justify-center items-center  fixed inset-0 bg-black bg-opacity-40  overflow-auto z-10">
    
          <div className="flex flex-col gap-5 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md w-[400px] h-[250px]">
            <button className="flex justify-end" onClick={onClose}>
              <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
            </button>
            <div className="flex flex-col justify-center items-center gap-5">
              {/* Update your icon and message accordingly */}
              <CheckBadgeIcon className="h-[60px] w-[60px] text-green-500" />
              <p>Module Added Successfully</p>
            </div>
            
            <div className="flex justify-center">
            <a  href={"/admin/module/modulelist"}>
              <button
                className="r w-[100px] gap-2 p-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
              >
                
                Ok
              </button>
              </a>
       
            </div>
          </div>


    </div>
  );
}
