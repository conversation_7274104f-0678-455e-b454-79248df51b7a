"use client";
import React, { useState, useEffect } from "react";
import SearchBar from "../SearchBar";
import { PlusIcon, MinusIcon, ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";
import { AddRemoveModuleGroup } from "@/types/LMSTypes";
import { useRemoveModuleFromGroup } from "@/hook/admin/group/useRemoveModuleFromGroup";
import { useAddModuleToGroup } from "@/hook/admin/group/useAddModuleToGroup";
import Nothing from "@/components/ui/Nothing";
import { useGetSearchFromGroupModuleInTable } from "@/hook/admin/usergroup/module/useGetSearchFromGroupModuleInTable";
import { useGetSearchFromAllModuleInTable } from "@/hook/admin/usergroup/module/useGetSearchFromAllModuleInTable";

import { usePutUpdateModule } from "@/hook/admin/group/usePutUpdateModuleSeq";
import SubmitModal from "@/components/ui/SubmitModal";

const ModuleTable = ({ modules, type, groupId }) => {
  const [search, setSearch] = useState("");
  const [groupIdValue, setGroupIdValue] = useState(groupId);
  const [moduleIdValue, setModuleIdValue] = useState(Number);
  const [search2, setSearch2] = useState("");
  const [updatedModules, setUpdatedModules] = useState([]);
  const [showSubmitModal, setShowSubmitModal] = useState(false);

  const { data: SearchFromGroupModule } = useGetSearchFromGroupModuleInTable(groupIdValue, search);
  const { data: SearchFromAllModule } = useGetSearchFromAllModuleInTable(groupIdValue, search2);

  

  function convertIsoToCustomFormat(isoString) {
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const customFormat = `${year}/${month}/${day} ${hours}:${minutes}`;
    return customFormat;
  }

  
  useEffect(() => {
    if (SearchFromGroupModule) {
      setUpdatedModules(SearchFromGroupModule.map((item, index) => ({ ...item, sequence: index + 1 })));
    }
  }, [SearchFromGroupModule]);

  const sequence = (updatedModules ? updatedModules.length : 0) + 1;
  const newData: AddRemoveModuleGroup = {
    module_id: moduleIdValue,
    group_id: groupIdValue,
    sequence: sequence,
  };

  const addingModuleToGroup = useAddModuleToGroup(newData);
  const updateModuleSeq = usePutUpdateModule();
  const removeModuleToGroup = useRemoveModuleFromGroup(moduleIdValue, groupIdValue);



  const handleMoveUp = (index) => {
    if (index === 0) return;
    const newUpdatedModules = [...updatedModules];
    [newUpdatedModules[index - 1], newUpdatedModules[index]] = [newUpdatedModules[index], newUpdatedModules[index - 1]];
    setUpdatedModules(newUpdatedModules.map((module, idx) => ({ ...module, sequence: idx + 1 })));
  };

  const handleMoveDown = (index) => {
    if (index === updatedModules.length - 1) return;
    const newUpdatedModules = [...updatedModules];
    [newUpdatedModules[index], newUpdatedModules[index + 1]] = [newUpdatedModules[index + 1], newUpdatedModules[index]];
    setUpdatedModules(newUpdatedModules.map((module, idx) => ({ ...module, sequence: idx + 1 })));
  };

  const handleClickUser = async (type) => {
    if (type === "add") {
      try {
        await addingModuleToGroup.mutateAsync();
        console.log("printing search", search);
      } catch (error) {
        console.error("Error adding user in group:", error);
      }
    } else if (type === "remove") {
      try {
        await removeModuleToGroup.mutateAsync();
      } catch (error) {
        console.error("Error mutating user in group:", error);
      }
    }
  };



  const handleSave = () => {
    updatedModules.forEach((module) => {
      const updateDetail = {
        module_id: module.module_id,
        group_id: groupIdValue,
        sequence: module.sequence,
      };
      updateModuleSeq.mutate(updateDetail, {
        onError: (error) => {
          console.error("Error updating sequence:", error);
        },
        onSuccess: (data) => {
          console.log("Sequence update success:", data);
          setShowSubmitModal(true);
        },
      });
    });
  };

  if (type === "remove") {
    return (
      <div className="flex flex-col w-auto h-full p-1 gap-1">
        <div className="flex justify-between mx-1 md:mx-4">
          <Heading pgHeading="Group Modules" />
          <div className="flex justify-end gap-1 ">
          <SearchBar onSearch={setSearch} />
          <button type="submit" className="w-30 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]" 
         onClick={handleSave}>
          Save the Sequence
        </button>
        </div>
        </div>
        <div className="overflow-y-auto w-full h-full bg-white rounded-lg border">
          <table className="rounded-xl items-start w-full h-fit">
            <thead className="bg-secondary text-white text-sm z-10 sticky top-0 uppercase w-full">
              <tr className="w-full h-fit">
                <th className="p-2 text-left">Sequence No.</th>
                <th className="p-2 text-left">module_id</th>
                <th className="p-2 text-left">module_name</th>
                <th className="p-2 text-left">module_headline</th>
                <th className="p-2 text-left">module_description</th>
                <th className="p-2 text-left">created by</th>
                <th className="p-2 text-center">Actions</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto">
              {Array.isArray(updatedModules) && updatedModules.length > 0 ? (
                updatedModules.map((module, index) => (
                  <tr key={index} className="w-full border-b align-top h-fit">
                    <td className="p-2 flex text-center gap-2">
                      <div className="flex gap-2">
                        <button onClick={() => handleMoveUp(index, addingModuleToGroup)}>
                          <ArrowUpIcon className="h-6 w-6 text-white p-[2px] rounded-full bg-textColor" />
                        </button>
                        {module.sequence}
                        <button onClick={() => handleMoveDown(index, addingModuleToGroup)}>
                          <ArrowDownIcon className="h-6 w-6 text-white p-[2px] rounded-full bg-textColor" />
                        </button>
                      </div>
                    </td>
                    <td className="p-2 px-3 text-left">{module.module_id}</td>
                    <td className="p-2 text-left">{module.module_name}</td>
                    <td className="p-2 text-left">{module.module_headline}</td>
                    <td className="p-2 text-left">{module.module_description}</td>
                    <td className="p-2 text-left">{module.created_by}</td>
                    <td className="p-2 text-center">
                      <button
                        onClick={() => {
                          handleClickUser("remove");
                          setModuleIdValue(module.module_id);
                        }}
                        className="p-1 bg-secondary text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring focus:border-blue-300"
                      >
                        <MinusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} className="text-center p-4">
                    <Nothing
                      title="No Module Available"
                      para="There are currently no module to display. Please check back later or add module."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {showSubmitModal && (
    <SubmitModal modalName={" Save "} modalText={"The Sequence is Save "} type={1} onClose={()=>{setShowSubmitModal(false)}} />
  )}
      </div>
    );
  } else if (type == "add") {
    return (
      <div className="flex flex-col w-auto h-full p-2 gap-1">
        <div className="flex justify-between mx-1 md:mx-4">
          <Heading pgHeading="All Existing Modules" />
          <SearchBar onSearch={setSearch2} />
        </div>
        <div className="overflow-y-auto w-full h-full bg-white rounded-lg border">
          <table className="rounded-xl items-start w-full h-fit">
            <thead className="bg-secondary text-white text-sm  sticky top-0 z-10 uppercase w-full">
              <tr className="w-full h-fit ">
                <th className="p-2 text-left">module_id</th>
                <th className="p-2 text-left">module_name</th>
                <th className="p-2 text-left">module_headline</th>
                <th className="p-2 text-left">module_description</th>
                <th className="p-2 text-left">creation date</th>
                <th className="p-2 text-left">created by</th>
                <th className="p-2 text-center">Actions</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto">
              {(Array.isArray(SearchFromAllModule) && SearchFromAllModule.length > 0) || search2.trim() === '' ? (
                (search2.trim() === '' ? modules : SearchFromAllModule).map((module, index) => (
                  <tr key={index} className="w-full border-b align-top h-fit">
                    <td className="p-2 px-3 text-left">{module.module_id}</td>
                    <td className="p-2 text-left">{module.module_name}</td>
                    <td className="p-2 text-left">{module.module_headline}</td>
                    <td className="p-2 text-left">{module.module_description}</td>
                    <td className="p-2 text-left">{convertIsoToCustomFormat(module.creation_date)}</td>
                    <td className="p-2 text-left">{module.created_by}</td>
                    <td className="p-2 text-center">
                      <button
                        onClick={() => {
                          handleClickUser("add");
                          setModuleIdValue(module.module_id);
                        }}
                        className="p-1 bg-secondary text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring focus:border-blue-300"
                      >
                        <PlusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="text-center p-4">
                    <Nothing
                      title="No Module Available"
                      para="There are currently no module to display. Please check back later or add module."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  } else {
    return (
      <>
        <h1>Wrong type</h1>
      </>
    );
  }
}
export default ModuleTable;
