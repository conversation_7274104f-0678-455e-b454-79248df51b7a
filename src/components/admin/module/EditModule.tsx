"use client";

import React, { useState, ChangeEvent, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { UpdateModule } from "@/types/LMSTypes";

import { useUpdateModule } from "@/hook/admin/module/useUpdateModule";
import { useParams } from "next/navigation";
import { useGetModuleById } from "@/hook/admin/module/useGetModuleById";
import Link from "next/link";

interface FormErrors {
  [key: string]: string;
}

export default function EditModule() {
  const params = useParams;
  const module_id: number = parseInt(params.moduleId, 10);
  console.log("moduleId" + module_id);
  const { data: moduleData, isError, isLoading } = useGetModuleById(module_id);
  
  console.log("moduleIdddd" + module_id);
  const [isModuleName, setIdModuleName] = useState("");
  const [isModuleHeading, setIdModuleHeading] = useState("");
  const [isModuleDescription, setIdModuleDescription] = useState("");
  const [errors, setErrors] = useState<FormErrors>({});



  console.log({ moduleData, isError, isLoading });

  useEffect(() => {
    if (moduleData) {
      setIdModuleName(moduleData?.module_name || "");
      setIdModuleHeading(moduleData?.module_heading || "");
      setIdModuleDescription(moduleData?.module_description || "");
    }
  }, [moduleData]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError || !moduleData) {
    // Log an error or detailed message if possible
    console.error("Error fetching content details", {
      isError,
      moduleData,
    });
    return <div>Error loading content details.</div>;
  }

  return (
    <div className="w-full p-4">
      <form  className="flex flex-col gap-3 ">
        <div className="flex justify-between  w-full gap-5 ">
          <div className="flex  h-full w-full gap-2">
            <div className="flex flex-col gap-1">
              <label
                htmlFor="ModuleName"
                className="block text-sm font-medium text-gray-700 "
              >
                Module Name
              </label>
              <input
                type="text"
                id="ModuleName"
                name="ModuleName"
                value={isModuleName} 
                onChange={(e) => setIdModuleName(e.target.value)}
                className="mt-1 block w-full border text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
              ></input>
              {errors.ModuleName && (
                <p className="text-red-500 text-xs italic">
                  {errors.ModuleName}
                </p>
              )}
            </div>
            <div className="flex flex-col gap-1">
              <label
                htmlFor="ModuleHeading"
                className="block text-sm font-medium text-gray-700"
              >
                Module Heading
              </label>
              <input
                id="ModuleHeading"
                name="ModuleHeading"
                value={isModuleHeading}
                onChange={(e) => setIdModuleHeading(e.target.value)}
                className="mt-1 block w-full border  text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
              ></input>
              {errors.ModuleHeading && (
                <p className="text-red-500 text-xs italic">
                  {errors.ModuleHeading}
                </p>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <label
            htmlFor="ModuleDescription"
            className="block text-sm font-medium text-gray-700"
          >
            Module Description
          </label>
          <textarea
            id="ModuleDescription"
            name="ModuleDescription"
            value={isModuleDescription}
            onChange={(e) => setIdModuleDescription(e.target.value)}
            rows={3}
            className="mt-1 block w-full border  text-gray-600 border-gray-300 rounded-md shadow-sm p-2"
          ></textarea>{" "}
          {errors.ModuleDescription && (
            <p className="text-red-500 text-xs italic">
              {errors.ModuleDescription}
            </p>
          )}
        </div>

        <div className="flex justify-end items-end h-full">
          <Link href={}>
          <button
            type="submit"
            className="px-6 py-2 flex gap-2  bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
          >
            Next
          </button>
          </Link>
        </div>
      </form>
    </div>
  );
}
