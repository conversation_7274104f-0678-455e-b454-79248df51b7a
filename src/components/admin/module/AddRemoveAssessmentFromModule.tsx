"use client"
import React, { useEffect, useState } from 'react'
import AssessmentListForModule from './AssessmentListForModule';
import { useGetModuleAssessmentByModuleId } from '@/hook/admin/module/useGetModuleAssessmentByModuleID';
import { useGetAllAssessmentInModule } from '@/hook/admin/module/useGetAllAssessmentInModule';

export default function AddRemoveAssessmentFromModule({moduleIDProp}:any) {

  const [moduleId, setmoduleId] = useState(parseInt(moduleIDProp || ''));
 
  console.log("module id for Table" + moduleIDProp)

  const { data: userValues, isLoading:userValuesLoading, isError:userValuesError } = useGetModuleAssessmentByModuleId(moduleId);
  const { data: assessments, isLoading: assessmentsLoading, isError: assessmentsError} = useGetAllAssessmentInModule(moduleId);

  useEffect(() => {
    if (!userValuesLoading && !userValuesError) {
      console.log('data:', userValues);
    }
  }, [userValues, userValuesLoading, userValuesError]);
  
  useEffect(() => {
    if (!assessmentsLoading && !assessmentsError) {
      console.log("Assessments data:", assessments);
    }
  }, [assessments, assessmentsLoading, assessmentsError]);

  if (assessmentsLoading || userValuesLoading) {
    return <div>Loading...</div>;
  }

  if (assessmentsError || userValuesError) {
    return <div>Error fetching assessments</div>;
  }

  return (
 <div className='h-[90vh]'>
    <div className="h-[40vh]">
   <AssessmentListForModule type={"all"}  userValues={assessments} moduleId={moduleId}/>
   </div>
   <div className="h-[40vh]">
   <AssessmentListForModule type={"remove"}  userValues={userValues} moduleId={moduleId}/>
   </div>
   
   </div>  
  )
}
