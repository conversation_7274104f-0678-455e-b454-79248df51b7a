"use client";
import React, { useState, useEffect } from "react";
import Heading from "@/components/ui/Heading";
import { MinusIcon, PlusIcon, ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline";
import Nothing from "@/components/ui/Nothing";
import { usePostAddModuleAssessemen } from "@/hook/admin/module/usePostAddModuleAssessment";
import { useDeleteModuleAssessment } from "@/hook/admin/module/useDeleteModuleAssessment";
import { usePutUpdateModuleSeqAssessment } from "@/hook/admin/module/usePutUpdateModuleSeqAssessment";
import { AddModuleAssessmentOptions } from "@/types/LMSTypes";
import SubmitModal from "@/components/ui/SubmitModal";

export default function AssessmentListForModule({
  type,
  userValues,
  moduleId,
}: any) {
  const [isModuleId, setIsModuleId] = useState(moduleId);
  const [isAssessmentId, setIsAssessmentId] = useState<number | null>(null);
  const [assessmentData, setAssessmentData] = useState(userValues);
  const [showSubmitModal, setShowSubmitModal] = useState(false);

  useEffect(() => {
    if (Array.isArray(userValues)) {
      setAssessmentData(userValues.map((item, index) => ({ ...item, sequence: index + 1 })));
      console.log("userValues :" ,userValues)
    }
  }, [userValues]);

  const sequence = assessmentData ? assessmentData.length + 1 : 1;
  const newDetail: AddModuleAssessmentOptions = {
    module_id: isModuleId,
    assessment_id: isAssessmentId!,
    sequence: sequence,
  };

  const addAssessmentToModule = usePostAddModuleAssessemen(newDetail);
  const updateModuleSeqAssessment = usePutUpdateModuleSeqAssessment();

  const { mutate: deleteAssessment, isLoading, isError } = useDeleteModuleAssessment();

  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const newAssessmentData = [...assessmentData];
    const temp = newAssessmentData[index - 1];
    newAssessmentData[index - 1] = newAssessmentData[index];
    newAssessmentData[index] = temp;
    setAssessmentData(newAssessmentData.map((item, idx) => ({ ...item, sequence: idx + 1 })));
  };

  const handleMoveDown = (index: number) => {
    if (index === assessmentData.length - 1) return;
    const newAssessmentData = [...assessmentData];
    const temp = newAssessmentData[index + 1];
    newAssessmentData[index + 1] = newAssessmentData[index];
    newAssessmentData[index] = temp;
    setAssessmentData(newAssessmentData.map((item, idx) => ({ ...item, sequence: idx + 1 })));
  };

  const handleClickUser = async (type: string) => {
    if (type === "add" && isAssessmentId !== null) {
      try {
        await addAssessmentToModule.mutate();
        console.log("Assessment added successfully.");
      } catch (error) {
        console.error("Error adding assessment to module:", error);
      }
    }
  };

  // const handleDelete = (assessment_id: number) => {
  //   if (!moduleId) {
  //     console.error("moduleId is undefined");
  //     return;
  //   }
  //   deleteAssessment({ module_id: moduleId, assessment_id });
  // };

  const handleDelete = (assessment_id: number) => {
    if (!moduleId) {
      console.error("moduleId is undefined");
      return;
    }
    
    deleteAssessment({ module_id: moduleId, assessment_id }, {
      onSuccess: () => {
        setAssessmentData((prevData) => {
          const newData = prevData.filter(item => item.assessment_id !== assessment_id);
          return newData.length ? newData : []; // Ensure state updates correctly even if empty
        });
      },
      onError: (error) => {
        console.error("Error deleting assessment:", error);
      },
    });
  };
  

  const handleSave = () => {
    assessmentData.forEach((item) => {
      const updateDetail = {
        module_id: isModuleId,
        assessment_id: item.assessment_id,
        sequence: item.sequence,
      };
      setShowSubmitModal(true)
      console.log("Sending updateDetail to backend:", updateDetail);
      updateModuleSeqAssessment.mutate(updateDetail, {
        onError: (error) => {
          console.error("Error updating sequence:", error);
        },
        onSuccess: (data) => {
          console.log("Update success:", data);
        },
      });
    });
  };

  if (type === "remove") {
    return (
      <div className="flex flex-col gap-2 shadow-lg border-y-8 border-white bg-white mx-auto w-full rounded-2xl h-full">
        <div className="flex justify-between px-2">
          <Heading pgHeading="Assessment In Module" />
          <button
            type="submit"
            className="w-30 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
            onClick={handleSave}
          >
            Save the Sequence
          </button>
        </div>

        <div className="overflow-y-auto w-full h-[30vh] bg-white rounded-lg">
          <table className="rounded-xl items-start w-full h-fit">
            <thead className="bg-secondary text-white text-sm z-10 sticky top-0 uppercase w-full">
              <tr className="w-full h-fit">
                <th className="p-2 text-left">Sequence</th>
                <th className="p-2 text-left">Assessment Id</th>
                <th className="p-2 text-left">Assessment Name</th>
                <th className="p-2 text-left">Instructions</th>
                <th className="p-2 text-center">Total Time</th>
                <th className="p-2 text-center">Total Marks</th>
                <th className="p-2 text-center">Action</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto">
              {Array.isArray(assessmentData) && assessmentData.length > 0 ? (
                assessmentData.map((item, index) => (
                  <tr key={item.assessment_id} className="w-full border-b align-top h-fit">
                    <td className="p-2 flex text-center gap-2">
                      <div className="flex gap-2">
                        <button onClick={() => handleMoveUp(index)}>
                          <ArrowUpIcon className="h-6 w-6 text-white p-[2px] rounded-full bg-textColor" />
                        </button>
                        {item.sequence}
                        <button onClick={() => handleMoveDown(index)}>
                          <ArrowDownIcon className="h-6 w-6 text-white p-[2px] rounded-full bg-textColor" />
                        </button>
                      </div>
                    </td>
                    <td className="p-2 px-3 text-left">{item.assessment_id}</td>
                    <td className="p-2 text-left">{item.assessment_name}</td>
                    <td className="p-2 text-left">{item.instructions}</td>
                    <td className="p-2 text-center">{(item.total_time_allowed / 60).toFixed(2)}</td>
                    <td className="p-2 text-center">{item.total_marks}</td>
                    <td className="p-2 text-center">
                      <button onClick={() => handleDelete(item.assessment_id)}>
                        <MinusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="text-center p-4">
                    <Nothing
                      title="No Assessment Available"
                      para="There are currently no assessments to display. Please check back later or add assessments."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {showSubmitModal && (
    <SubmitModal modalName={" Save "} modalText={"The Sequence is Save "} type={1} onClose={()=>{setShowSubmitModal(false)}} />
  )}
      </div>
      
    );
  } else if (type === "all") {
    return (
      <div className="shadow-lg border-y-8 border-white mx-auto w-full rounded-2xl bg-white">
        <Heading pgHeading="All Assessments" />
        <div className="overflow-y-auto w-full h-[30vh] bg-white rounded-lg border">
          <table className="rounded-xl items-start w-full">
            <thead className="bg-secondary text-white text-sm sticky top-0 z-10 uppercase w-full">
              <tr className="w-full h-fit">
                <th className="p-2 text-left">Assessment Id</th>
                <th className="p-2 text-left">Assessment Name</th>
                <th className="p-2 text-left">Instructions</th>
                <th className="p-2 text-center">Total Time</th>
                <th className="p-2 text-center">Total Marks</th>
                <th className="p-2 text-center">Action</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto">
              {Array.isArray(userValues) && userValues.length > 0 ? (
                userValues.map((item) => (
                  <tr key={item.assessment_id} className="w-full border-b align-top h-fit">
                    <td className="p-2 px-3 text-left">{item.assessment_id}</td>
                    <td className="p-2 text-left">{item.assessment_name}</td>
                    <td className="p-2 text-left">{item.instructions}</td>
                    <td className="p-2 text-center">{(item.total_time_allowed / 60).toFixed(2)}</td>
                    <td className="p-2 text-center">{item.total_marks}</td>
                    <td className="p-2 text-center">
                      <button onClick={() => {
                        handleClickUser("add", item.assessment_id);
                        setIsAssessmentId(item.assessment_id);
                      }}>
                        <PlusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="text-center p-4">
                    <Nothing
                      title="No Assessments Available"
                      para="There are currently no assessments to display. Please check back later or add assessments."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  } else {
    return (
      <>
        <h1>Wrong type</h1>
      </>
    );
  }
}
