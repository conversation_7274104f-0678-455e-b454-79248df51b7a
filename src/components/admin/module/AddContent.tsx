"use client";
import React, { useState, useEffect } from "react";
import { ArrowDownIcon, ArrowUpIcon, PencilSquareIcon, TrashIcon } from "@heroicons/react/24/outline";
import SearchBar from "@/components/admin/module/SearchBar";
import CreateContentAssessmentModal from "@/components/admin/module/CreateContentAssessmentModal";
import Nothing from "@/components/ui/Nothing";

import { useAddModuleContent } from "@/hook/admin/module/useAddModuleContent";
import { AddModuleContent } from "@/types/LMSTypes";
import { useGetAllContentInTable } from "@/hook/content/useGetAllContentInTable";

const AddContent: React.FC<any> = ({
  moduleIdUpcoming,
  onPreviousClick,
  onNextClick,
}) => {
  const [selectedContent, setSelectedContent] = useState<number[]>([]);
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [filteredContentData, setFilteredContentData] = useState([]);
  const [contentData, setContentData] = useState([]);
  const { data: contentDataFetched, isLoading: contentLoading, isError: contentError } = useGetAllContentInTable();
  
  useEffect(() => {
    if (!contentLoading && !contentError && contentDataFetched) {
      setContentData(contentDataFetched.map((item, index) => ({ ...item, sequence: index + 1 })));
    }
  }, [contentDataFetched, contentLoading, contentError]);

  console.log("contentData", contentData);
  console.log("FilteredcontentData", filteredContentData);
  console.log("selectedContent", selectedContent);
  console.log("upcoming module id for content", moduleIdUpcoming);

  const handleContentCheckboxChange = (id: number) => {
    if (selectedContent.includes(id)) {
      setSelectedContent(prevState => prevState.filter(item => item !== id));
    } else {
      setSelectedContent(prevState => [...prevState, id]);
    }
  };

  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const newContentData = [...contentData];
    const temp = newContentData[index - 1];
    newContentData[index - 1] = newContentData[index];
    newContentData[index] = temp;
    setContentData(newContentData.map((item, idx) => ({ ...item, sequence: idx + 1 })));
  };

  const handleMoveDown = (index: number) => {
    if (index === contentData.length - 1) return;
    const newContentData = [...contentData];
    const temp = newContentData[index + 1];
    newContentData[index + 1] = newContentData[index];
    newContentData[index] = temp;
    setContentData(newContentData.map((item, idx) => ({ ...item, sequence: idx + 1 })));
  };

  const ContentModuleData: AddModuleContent[] = selectedContent.map(contentId => {
    const contentvar = contentData.find(a => a.content_id === contentId);
    return {
      module_id: parseInt(moduleIdUpcoming),
      content_id: contentId,
      sequence: contentvar.sequence,
    };
  }); 

  const addingContentModuleData = useAddModuleContent(ContentModuleData);

  const handleSubmit = async () => {
    if (ContentModuleData.length > 0) {
      setShowCreateModal(true); 
      try {
        await addingContentModuleData.mutate(ContentModuleData);
    
      } catch (error) {
        console.error("Error while submitting:", error);
      }
    } else {
      console.log("Please fill in all fields before submitting.");
    }
  };

  return (
    <div className="flex flex-col justify-start h-[70vh] w-full gap-4">
      {/* <div className="flex justify-between">
        <SearchBar data={contentData} types="content" setFilterData={filteredContentData} />
      </div> */}
      <div className="overflow-auto w-full h-full border rounded-lg">
        {contentData?.length > 0 ? (
          <table className="w-full text-sm text-left rtl:text-right text-gray-800 dark:text-gray-800 h-fit">
            <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
              <tr>
                <th className="p-2 text-left">Sequence</th>
                <th className="p-2 text-left">Content Id</th>
                <th className="p-2 text-left">Content Name</th>
                <th className="p-2 text-left">Content Description</th>
                <th className="p-2 text-left">Topics</th>
                <th className="p-2 text-left">Created By</th>
                <th className="p-2 text-left">File Type</th>
                <th className="p-2 text-left">File Size</th>
                <th className="p-2 text-left">Select</th>
              </tr>
            </thead>
            <tbody>
              {contentData.map((item, index) => (
                <tr key={item.content_id} className="border-b">
                  <td className="p-2 flex text-center gap-2">
                    <div className="flex gap-2">
                      <button onClick={() => handleMoveUp(index)}>
                        <ArrowUpIcon className="h-6 w-6 text-white p-[2px] rounded-full bg-textColor" />
                      </button>
                      {item.sequence}
                      <button onClick={() => handleMoveDown(index)}>
                        <ArrowDownIcon className="h-6 w-6 text-white p-[2px] rounded-full bg-textColor" />
                      </button>
                    </div>
                  </td>
                  <td className="p-2 px-3 text-left">{item.content_id}</td>
                  <td className="p-2 text-left">{item.content_name}</td>
                  <td className="p-2 text-left">{item.content_description}</td>
                  <td className="p-2 text-left">{item.topics}</td>
                  <td className="p-2 text-left">{item.created_by}</td>
                  <td className="p-2 text-left">{item.file_type}</td>
                  <td className="p-2 text-left">{(item.file_size / 1000).toFixed(2)} KB</td>
                  <td className="p-2">
                    <input
                      type="checkbox"
                      onChange={() => {
                        handleContentCheckboxChange(item.content_id);
                      }}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="flex flex-col justify-between overflow-y-auto bg-white m-1 w-full h-full rounded-2xl p-5">
            <Nothing
              title="No Content Available"
              para="There are currently no content to display. Please check back later, or contact admin."
            />
          </div>
        )}
      </div>
      <div className="flex justify-center items-end gap-2">
        {/* <button
          className="w-24 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
          onClick={onPreviousClick}
        >
          Previous
        </button> */}
        <button
          type="button"
          className={`flex gap-2 py-2 px-6 text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] ${selectedContent.length > 0
            ? "bg-textColor" // Button enabled
            : "bg-gray-400" // Button disabled
            }`}
          disabled={selectedContent.length <= 0} // Button is disabled if selectedContent array is empty
          onClick={() => {
            handleSubmit();
            setShowCreateModal(true);
          }}
        >
          Add Content & Save
        </button>
      </div>
      {showCreateModal && (
        <CreateContentAssessmentModal
          onClose={() => {
            setShowCreateModal(false);
          }}
        />
      )}
    </div>
  );
};

export default AddContent;
