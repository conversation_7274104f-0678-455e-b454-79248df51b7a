"use client";
import React, { useState, useEffect } from "react";
import { PencilSquareIcon, TrashIcon, ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/outline";
import SearchBar from "@/components/admin/module/SearchBar";
import CreateContentAssessmentModal from "@/components/admin/module/CreateContentAssessmentModal";
import { useGetAllAssessments } from "@/hook/assessments/useGetAllAssessments";
import { AddModuleAssessment } from "@/types/LMSTypes";
import { useAddModuleAssessment } from "@/hook/admin/module/useAddModuleAssessment";

const AddAssessment: React.FC<any> = ({
  moduleIdUpcoming,
  onPreviousClick,
  onNextClick,
}) => {
  const [selectedAssessment, setSelectedAssessment] = useState<number[]>([]);
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [filteredAssessmentData, setFilteredAssessmentData] = useState([]);
  const [assessmentData, setAssessmentData] = useState([]);

  const { data: AssessmentData, isLoading: AssessmentLoading, isError: AssessmentError } = useGetAllAssessments(0, 1000);

  useEffect(() => {
    if (!AssessmentLoading && !AssessmentError && AssessmentData) {
      setAssessmentData(AssessmentData.map((item, index) => ({ ...item, sequence: index + 1 })));
    }
  }, [AssessmentData, AssessmentLoading, AssessmentError]);

  console.log("AssessmentData", AssessmentData);
  console.log("filteredAssessmentData", filteredAssessmentData);
  console.log("selectedAssessment", selectedAssessment);
  console.log("moduleIdUpcoming", moduleIdUpcoming);

  const handleAssessmentCheckboxChange = (id: number) => {
    if (selectedAssessment.includes(id)) {
      setSelectedAssessment(prevState => prevState.filter(item => item !== id));
    } else {
      setSelectedAssessment(prevState => [...prevState, id]);
    }
  };

  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const newAssessmentData = [...assessmentData];
    const temp = newAssessmentData[index - 1];
    newAssessmentData[index - 1] = newAssessmentData[index];
    newAssessmentData[index] = temp;
    setAssessmentData(newAssessmentData.map((item, idx) => ({ ...item, sequence: idx + 1 })));
  };

  const handleMoveDown = (index: number) => {
    if (index === assessmentData.length - 1) return;
    const newAssessmentData = [...assessmentData];
    const temp = newAssessmentData[index + 1];
    newAssessmentData[index + 1] = newAssessmentData[index];
    newAssessmentData[index] = temp;
    setAssessmentData(newAssessmentData.map((item, idx) => ({ ...item, sequence: idx + 1 })));
  };

  const AssessmentModuleData: AddModuleAssessment[] = selectedAssessment.map(assessmentId => {
    const assessment = assessmentData.find(a => a.assessment_id === assessmentId);
    return {
      module_id: moduleIdUpcoming,
      assessment_id: assessmentId,
      sequence: assessment.sequence,
    };
  });

  const addingAssessmentModuleData = useAddModuleAssessment(AssessmentModuleData);

  const handleSubmit = async () => {
    if (AssessmentModuleData.length > 0) {
      try {
        await addingAssessmentModuleData.mutate(AssessmentModuleData);
        onNextClick();
      } catch (error) {
        console.error("Error while submitting:", error);
      }
    } else {
      console.log("Please fill in all fields before submitting.");
    }
  };

  return (
    <div className="flex flex-col justify-start h-[70vh] w-full gap-4">
      {/* <div className="flex justify-between">
        <SearchBar data={AssessmentData} types="assessment" setFilterData={setFilteredAssessmentData} />
      </div> */}
      <div className="overflow-auto w-full h-full border rounded-lg">
        <table className="w-full text-sm text-left rtl:text-right text-gray-800 dark:text-gray-800 h-fit">
          <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
            <tr className="h-fit">
           
           
            <th className="p-2 text-left">Sequence</th>
              <th className="p-2 text-left">Assessment Id</th>
              <th className="p-2 text-left">Assessment Name</th>
              <th className="p-2 text-left">Instructions</th>
              <th className="p-2 text-left">Total Time Allowed</th>
              <th className="p-2 text-left">Total Marks</th>
              <th className="p-2 text-left">Source</th>
              <th className="p-2 text-left">Assessment Evaluation Strategy</th>
              
              <th className="p-2 text-left">Select</th>
            </tr>
          </thead>
          <tbody>
            {assessmentData.map((item, index) => (
              <tr key={item.assessment_id} className="border-b h-fit">
                <td className="p-2 flex text-center gap-2 ">
                <div className="flex  gap-2  ">
                  <button onClick={() => handleMoveUp(index)}>
                    <ArrowUpIcon className="h-6 w-6 text-white p-[2px]  rounded-full bg-textColor" />
                  </button>
                  {item.sequence}
                  <button onClick={() => handleMoveDown(index)}>
                    <ArrowDownIcon className="h-6 w-6 text-white p-[2px] rounded-full bg-textColor" />
                  </button>
                </div>
                </td>
                <td className="p-2 px-3 text-left">{item.assessment_id}</td>
                <td className="p-2 text-left">{item.assessment_name}</td>
                <td className="p-2 text-left">{item.instructions}</td>
                <td className="p-2 text-left">{(item.total_time_allowed / 60).toFixed(2)}</td>
                <td className="p-2 text-left">{item.total_marks}</td>
                <td className="p-2 text-left">{item.source}</td>
                <td className="p-2 text-left">{item.assessment_evaluation_strategy}</td>
             
              
                <td className="p-2 text-center">
                  <input
                    type="checkbox"
                    onChange={() => handleAssessmentCheckboxChange(item.assessment_id)}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-center items-end gap-2">
        {/* <button
          className="w-24 px-2 py-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
          onClick={onPreviousClick}
        >
          Previous
        </button> */}
        <button
          type="button"
          className={`flex gap-2 py-2 px-6 text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] ${selectedAssessment.length > 0
            ? "bg-textColor"
            : "bg-gray-400"
            }`}
          disabled={selectedAssessment.length <= 0}
          onClick={handleSubmit}
        >
          Add Assessment & Next
        </button>
      </div>
      {showCreateModal && (
        <CreateContentAssessmentModal
          onClose={() => {
            setShowCreateModal(false);
          }}
        />
      )}
    </div>
  );
};

export default AddAssessment;
