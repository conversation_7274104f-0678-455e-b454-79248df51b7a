"use client";
import React from "react";
import { MinusIcon, PencilSquareIcon, PlusIcon } from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";

export default function ModuleInGroup({
  moduleData,
  setShowModal,
  setIndexNumber,
}) {
  // Check if moduleData is undefined
  if (!moduleData) {
    // You can return some default content or a loading indicator here
    return <div>Loading...</div>;
  }


  const data = moduleData.map((item) => ({
    module_id: item.module_id,
    module_name: item.module_name,
    module_headline: item.module_headline,
    module_description: item.module_description,
    created_by: item.created_by,
    creation_date: item.creation_date,
  }));

  return (
  <div className="flex flex-col gap-2 h-full w-full" >
    <Heading pgHeading="Module In Group  " />
    <div className="w-full rounded-lg overflow-y-auto border shadow-md bg-white h-[200px]">
      <table className="w-full h-[250px]">
        <thead className="bg-secondary text-white text-sm sticky top-0 uppercase  ">
          <tr>
            <th className="p-2  text-left">Module No</th>
            <th className="p-2  text-left">Module Name</th>
            <th className="p-2  text-left">Module Headline</th>
            <th className="p-2  text-left">Module Description</th>
            <th className="p-2  text-left">Created By</th>
            <th className="p-2  text-left">Creation Date</th>
           
            <th className="p-2  ">Action</th>
          </tr>
        </thead>

        <tbody>
          {data.map((item, index) => (
            <tr key={item.module_id} className="border-b-2 text-sm">
              <td className="p-2 px-3 text-left">{item.module_id}</td>
              <td className="p-2 text-left">{item.module_name}</td>
              <td className="p-2 text-left">{item.module_headline}</td>
              <td className="p-2 text-left">{item.module_description}</td>
              <td className="p-2 text-left">{item.created_by}</td>
              <td className="p-2 text-left">{item.creation_date}</td>
            
              <td className=" p-1 md:px-2 md:py-1 lg:px-6 lg:py-1">
                    <button
                    //   onClick={() => {
                    //     handleClickUser("remove");
                    //     setuserIdValue(userValue.user_id);
                     // }}
                      className="p-1 bg-secondary text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring focus:border-blue-300">
                      <MinusIcon className="text-white md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                    </button>
                  </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div></div>
    );
}
