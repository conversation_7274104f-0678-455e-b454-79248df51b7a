import React, { useState, useCallback } from 'react';
import { ArrowUpTrayIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

interface FileDropzoneProps {
  onFilesAdded: (files: Array<{
    name: string;
    size: number;
    type: string;
    lastModified: number;
  }>) => void;
  acceptedFileTypes?: string;
  maxFileSizeMB?: number;
}

const FileDropzone: React.FC<FileDropzoneProps> = ({
  onFilesAdded,
  acceptedFileTypes = '.pdf,.doc,.docx',
  maxFileSizeMB = 10
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const validateFiles = useCallback((fileList: FileList | File[]) => {
    const validFiles: File[] = [];
    const maxSizeBytes = maxFileSizeMB * 1024 * 1024;
    const acceptedTypes = acceptedFileTypes.split(',');

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      
      if (!acceptedTypes.includes(fileExtension) && !acceptedTypes.includes(file.type)) {
        setErrorMessage(`File type not accepted: ${fileExtension}. Please upload ${acceptedFileTypes} files.`);
        continue;
      }
      
      if (file.size > maxSizeBytes) {
        setErrorMessage(`File too large: ${file.name}. Maximum size is ${maxFileSizeMB}MB.`);
        continue;
      }
      
      validFiles.push(file);
    }
    
    return validFiles;
  }, [acceptedFileTypes, maxFileSizeMB]);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    setErrorMessage(null);
    
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length === 0) return;

    const validFiles = validateFiles(droppedFiles);
    if (validFiles.length > 0) {
      const fileObjects = Array.from(validFiles).map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      }));
      
      onFilesAdded(fileObjects);
    }
  }, [validateFiles, onFilesAdded]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setErrorMessage(null);
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const validFiles = validateFiles(selectedFiles);
    if (validFiles.length > 0) {
      const fileObjects = Array.from(validFiles).map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      }));
      
      onFilesAdded(fileObjects);
    }
    
    // Reset the file input
    e.target.value = "";
  }, [validateFiles, onFilesAdded]);

  return (
    <div className="w-full">
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center ${
          isDragging 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
        } transition-colors cursor-pointer`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          onChange={handleFileInputChange}
          accept={acceptedFileTypes}
          multiple
        />
        
        <div className="flex flex-col items-center justify-center py-4">
          <div className="rounded-full bg-blue-50 p-3 mb-4">
            <ArrowUpTrayIcon className="h-6 w-6 text-blue-600" />
          </div>
          
          <p className="text-sm font-medium text-gray-900">
            Drag & drop files here, or click to select files
          </p>
          <p className="mt-1 text-xs text-gray-500">
            Supports {acceptedFileTypes.split(',').join(', ')} (Max size: {maxFileSizeMB}MB)
          </p>
        </div>
      </div>
      
      {errorMessage && (
        <div className="mt-2 text-sm text-red-600">
          {errorMessage}
        </div>
      )}
    </div>
  );
};

export default FileDropzone;