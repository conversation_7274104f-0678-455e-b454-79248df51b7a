import React from 'react';
import { 
  DocumentTextIcon, 
  ArrowRightIcon,
  ChartBarIcon,
  ArrowLeftIcon,
  ClipboardDocumentCheckIcon
} from '@heroicons/react/24/outline';

interface Job {
  job_id: number;
  job_title: string;
  job_description?: string;
  sectors?: string[];
  domains?: string[]; // Added domains
  created_at?: string;
  status?: 'active' | 'draft' | 'archived';
  source_documents?: string[]; // Changed to array for multiple documents
}

interface RoleCardProps {
  job: Job;
  index: number;
  colors: string[];
  selected: boolean;
  onClick: () => void;
  onViewSkills: () => void;
  onViewDocument?: () => void; // Optional function for viewing documents
  adminType: string; // Added admin type to control UI elements
  onViewSpecificDocument?: (fileName: string) => void; // Function to view a specific document
}

const RoleCard: React.FC<RoleCardProps> = ({ 
  job, 
  index, 
  colors, 
  selected, 
  onClick, 
  onViewSkills,
  onViewDocument,
  adminType,
  onViewSpecificDocument
}) => {
  // Pick a color based on the index
  const colorClass = colors[index % colors.length];
  
  // Status badge color
  const statusColor = {
    'active': 'bg-green-100 text-green-800',
    'draft': 'bg-amber-100 text-amber-800',
    'archived': 'bg-gray-100 text-gray-800'
  };

  // Format date to be more readable
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Check if we have only one button (for center alignment)
  const hasSingleButton = onViewDocument === undefined;

  return (
    <div 
      className={`relative overflow-hidden rounded-xl border ${
        selected ? 'border-blue-400 shadow-sm ring-2 ring-blue-200' : 'border-gray-200'
      } transition-all duration-200 hover:shadow-md h-full flex flex-col`}
    >
      {/* Top Color Bar */}
      <div className={`h-2 w-full ${colorClass}`}></div>
      
      <div className="p-5 flex-1 flex flex-col">
        <div className="flex justify-between items-start mb-3">
          <h3 className="text-lg font-medium text-gray-900 truncate max-w-[80%]">
            {job.job_title}
          </h3>
          
          {job.status && (
            <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
              statusColor[job.status] || 'bg-gray-100 text-gray-800'
            }`}>
              {job.status}
            </span>
          )}
        </div>
        
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {job.job_description || 'No description available'}
        </p>
        
        {job.sectors && job.sectors.length > 0 && (
          <div className="mb-3">
            <span className="text-xs font-medium text-gray-500 mb-1 block">Sectors:</span>
            <div className="flex flex-wrap gap-1.5">
              {job.sectors.map((sector, i) => (
                <span key={i} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                  {sector}
                </span>
              ))}
            </div>
          </div>
        )}
        
        {job.domains && job.domains.length > 0 && (
          <div className="mb-4">
            <span className="text-xs font-medium text-gray-500 mb-1 block">Domains:</span>
            <div className="flex flex-wrap gap-1.5">
              {job.domains.map((domain, i) => (
                <span key={i} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700">
                  {domain}
                </span>
              ))}
            </div>
          </div>
        )}
        
        {job.source_documents && job.source_documents.length > 0 && adminType !== 'adminb2b' && (
          <div className="text-xs text-gray-500 mt-2 mb-3">
            <div className="flex items-center mb-1">
              <DocumentTextIcon className="h-3.5 w-3.5 mr-1 text-gray-400" />
              <span>Source{job.source_documents.length > 1 ? 's' : ''}:</span>
            </div>
            
            <div className="ml-5 space-y-1">
              {job.source_documents.map((doc, i) => (
                <div key={i} className="flex items-center">
                  <button 
                    onClick={() => onViewSpecificDocument && onViewSpecificDocument(doc)}
                    className="text-blue-600 hover:text-blue-800 hover:underline truncate max-w-[200px] text-left"
                    aria-label={`View document ${doc}`}
                  >
                    {doc}
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Spacer to push buttons to the bottom */}
        <div className="flex-grow"></div>
        
        <div className={`mt-4 pt-3 border-t border-gray-100 flex ${
          hasSingleButton ? 'justify-center' : 'flex-wrap justify-between'
        } gap-2`}>
          <button
            onClick={onViewSkills}
            className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800"
          >
            <ClipboardDocumentCheckIcon className="h-4 w-4 mr-1" />
            View Skills
          </button>
          
          {onViewDocument && (
            <button
              onClick={onViewDocument}
              className="inline-flex items-center text-sm font-medium text-gray-600 hover:text-gray-800"
            >
              <DocumentTextIcon className="h-4 w-4 mr-1" />
              View Documents
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default RoleCard;