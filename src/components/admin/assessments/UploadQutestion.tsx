"use client";
import React, { useEffect, useState } from "react";
// import { useRouter } from "next/navigation";
import { PencilSquareIcon, TrashIcon } from "@heroicons/react/24/outline";

import { useGetAllQuestionUplaodTable } from "@/hook/questions/useGetAllQuestionUploadTable";
import { UploadQuestionTable } from "@/types/LMSTypes";
import SearchBar from "../SearchBar";
import CreateModal from "./CreateModal";
import DeleteModal from "../userGroup/DeleteModal";
import DeletedUserModal from "../userGroup/DeletedUserModal";
import EditQuestionModal from "../searchQuestion/EditQuestionModal";

type UploadQuestionProps = {};

const UploadQuestion: React.FC<UploadQuestionProps> = ({}) => {
  const [selectedQuestions, setSelectedQuestions] = useState<number[]>([]);
  const [showSelectedOnly, setShowSelectedOnly] = useState<boolean>(false);
  const [questionSearch, setQuestionSearch] = useState("");
  const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
  const [isRemoveModal, setIsRemoveModal] = useState<boolean>(false);

  const [selectAll, setSelectAll] = useState<boolean>(false);
  const [showModal, setShowModal] = useState(false);
  const [indexNumber, setIndexNumber] = useState(Number);

  const { data: questionData, isLoading } = useGetAllQuestionUplaodTable();

  const data =
    questionData?.map((item) => ({
      questionNo: item.question_id,
      question: item.question,
      questionDifficulty: item.question_difficulty,
      questionSource: item.question_source,
      questionTopics: item.topics_string,
      selected: selectedQuestions.includes(item.question_id),
    })) || [];

    useEffect(() => {
      // If "Select All" is checked, select all questions
      if (selectAll) {
        setSelectedQuestions(questionData?.map((item) => item.question_id) || []);
      } else {
        setSelectedQuestions([]); // Otherwise, deselect all questions
      }
    }, [selectAll, questionData]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const toggleView = () => setShowSelectedOnly(!showSelectedOnly);

  const handleCloseModal = () => {
    setIsRemoveModal(false);
  };

  // const router = useRouter();
  // const handleSucessModal = () => {
  //   setShowCreateModal(false);
  //   router.push("/admin/assessments");
  // };

  const selectedData = data.filter((item) => item.selected);

  const handleCheckboxChange = (question_id: number) => {
    setSelectedQuestions((prevSelectedQuestions) =>
      prevSelectedQuestions.includes(question_id)
        ? prevSelectedQuestions.filter((id) => id !== question_id)
        : [...prevSelectedQuestions, question_id]
    );
  };

  const handleSelectAllChange = () => {
    setSelectAll(!selectAll); // Toggle the "Select All" checkbox
  };

  const handleEditClick = (questionNo) => {
    const questionDetails = data.find((item) => item.questionNo === questionNo);
    if (questionDetails) {
      setShowModal(true);
      setIndexNumber(data.indexOf(questionDetails));
    }
  };

  const handleDeselectQuestion = (questionNo) => {
    setSelectedQuestions((currentSelected) =>
      currentSelected.filter((id) => id !== questionNo)
    );
    setIsRemoveModal(true);
  };

  return (
    <>
      {screenCount === 1 && (
        <div className="flex flex-col justify-start h-full w-full gap-4  ">
          <div className="flex justify-between">
            {/* <SearchBar onSearch={setQuestionSearch}  /> */}

            <label className="autoSaverSwitch relative inline-flex cursor-pointer select-none items-center">
              <input
                type="checkbox"
                name="autoSaver"
                className="sr-only"
                checked={showSelectedOnly}
                onChange={toggleView}
              />
              <span
                className={`slider mr-3 flex h-[26px] w-[50px] items-center rounded-full p-1 duration-200 ${
                  showSelectedOnly ? "bg-secondary" : "bg-secondary"
                }`}
              >
                <span
                  className={`dot h-[18px] w-[18px] rounded-full bg-white duration-200 ${
                    showSelectedOnly ? "translate-x-6" : ""
                  }`}
                ></span>
              </span>
              <span className="label flex items-center text-md font-medium text-black">
                {showSelectedOnly
                  ? "Show All Questions"
                  : "Show Selected Questions"}{" "}
              </span>
            </label>
          </div>
          <div
            className={`w-full rounded-lg  ${
              showSelectedOnly ? "h-[350px] " : " h-[400px] "
            } overflow-y-auto border shadow-md`}
          >
            <table className="w-full h-fit">
              <thead className="bg-secondary text-white text-sm sticky top-0 uppercase">
                <tr className="h-fit">
                  <th className="p-2 text-left ">Question No</th>
                  <th className="p-2 text-left">Question</th>
                  <th className="p-2 text-left">Difficulty Level</th>
                  <th className="p-2 text-left">Question Source</th>
                  <th className="p-2 text-left">Topics</th>
                  <th className="p-2 text-left">
                    {showSelectedOnly ? "Marks" : null}
                  </th>
                  <th className="p-2">
                    {showSelectedOnly ? "Remove" : "Edit"}
                  </th>
                  <th className="flex gap-1 p-2 text-left">
                    Select
                    <span>
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAllChange}
                      />
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {(showSelectedOnly ? selectedData : data).map(
                  (item, index: number) => (
                    <tr key={item.questionNo} className="border-b text-sm h-fit">
                      <td className="p-2 px-3 text-left">{item.questionNo}</td>
                      <td className="p-2 text-left">{item.question}</td>
                      <td className="p-2 text-left">
                        {item.questionDifficulty}
                      </td>
                      <td className="p-2 text-left">{item.questionSource}</td>
                      <td className="p-2 text-left">{item.questionTopics}</td>
                      <td className="p-2 text-left">
                        {showSelectedOnly ? (
                          <input
                            type="number"
                            id="totalMarks"
                            name="totalMarks"
                            placeholder="00"
                            className="block border border-gray-300 rounded-md w-[70px]"
                            min=""
                            step="1"
                            required
                          />
                        ) : null}
                      </td>
                      <td className="p-2 flex justify-center">
                        {showSelectedOnly ? (
                          <button
                            onClick={() =>
                              handleDeselectQuestion(item.questionNo)
                            }
                          >
                            <TrashIcon className="md:w-[25px] md:h-[25px] w-[13px] h-[13px] text-red-500" />
                          </button>
                        ) : (
                          <button
                            onClick={() => handleEditClick(item.questionNo)}
                          >
                            <PencilSquareIcon className="md:w-[25px] md:h-[25px] w-[13px] h-[13px] text-secondary " />
                          </button>
                        )}
                      </td>
                      <td className="p-2 text-center ">
                        {showSelectedOnly ? (
                          <input
                            disabled
                            type="checkbox"
                            checked={item.selected}
                            onChange={() =>
                              handleCheckboxChange(item.questionNo)
                            }
                          />
                        ) : (
                          <input
                            type="checkbox"
                            checked={selectedQuestions.includes(
                              item.questionNo
                            )} // Directly depend on selectedQuestions state
                            onChange={() =>
                              handleCheckboxChange(item.questionNo)
                            }
                          />
                        )}
                      </td>
                    </tr>
                  )
                )}
              </tbody>
            </table>
          </div>

          {showSelectedOnly ? (
            <div className="flex justify-end items-end gap-2">
              <button
                type="button"
                className={`flex gap-2 py-2 px-6 text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] ${
                  selectedQuestions.length > 0
                    ? "bg-textColor" // Button enabled
                    : "bg-gray-400" // Button disabled
                }`}
                disabled={selectedQuestions.length === 0} // Button is disabled if selectedQuestions array is empty
                onClick={() => {
                  setShowCreateModal(true);
                }}
              >
                Create Assessment
              </button>
            </div>
          ) : null}

          {showCreateModal && <CreateModal onClose={handleSucessModal} />}
          {isRemoveModal && (
            <DeletedUserModal
              deleteName="question"
              onClose={handleCloseModal}
            />
          )}

          {showModal && (
            <EditQuestionModal
              questionDetails={data[indexNumber]} // Ensure this is the correct way to access the details, based on how you manage state
              setShowModal={setShowModal}
            />
          )}
        </div>
      )}
    </>
  );
};

export default UploadQuestion;
