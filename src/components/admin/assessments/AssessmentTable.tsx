"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import DeletedUserModal from "../userGroup/DeletedUserModal";
import DeleteModal from "../userGroup/DeleteModal";
import Nothing from "@/components/ui/Nothing";
import { TrashIcon } from "@heroicons/react/24/outline";
import { useCount } from "@/context/searchStore";

export default function AssessmentTable({ AssessmentData , onDelete}) {


  const [isRemoveModal, setIsRemoveModal] = useState(false);
  const [showDeletedUserModal, setShowDeletedUserModal] = useState(false);
  const [selectedAssessmentId, setSelectedAssessmentId] = useState(null);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const totalPages = Math.ceil(AssessmentData?.length / itemsPerPage);
  const { searchValue, searchNumber } = useCount();


  let temp2:boolean = false 

useEffect(() => {
  temp2 = useCount.getState().searchValue
  console.log("search numberss:- ", temp2)
  console.log("search numberss page:- ", currentPage)
  if(temp2==true && currentPage != 1){
    setCurrentPage(1)
  }
}, [searchNumber])


  const handleDeleteClick = (assessmentId : any) => {
    setSelectedAssessmentId(assessmentId); // Store the ID for deletion
    setIsRemoveModal(true); // Open the modal for confirmation
  };

  const handleConfirmDelete = () => {
    if (selectedAssessmentId !== null) {
      onDelete(selectedAssessmentId); // Now, actually call onDelete
    }
    setIsRemoveModal(false); // Close the modal
    setSelectedAssessmentId(null); // Reset the selected ID
  };

  const handleCloseDeletedUserModal = () => {
    setShowDeletedUserModal(false); // Close the DeletedUserModal
  };

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = AssessmentData?.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="flex flex-col bg-white gap-2 justify-start items-start w-full rounded-md h-full  border shadow-md">
    {/* Scrollable table containe r for medium to large screens */}
    <div className="overflow-auto   w-full h-full rounded-md">
        <table className="min-w-full h-full">
          <thead className="bg-secondary text-white text-sm uppercase sticky top-0 z-10">
          <tr className="w-full">
            <th className="p-2 text-left">Assessment Id</th>
            <th className="p-2 text-left">Assessment Name</th>
            <th className="p-2 text-left">Instructions</th>
            <th className="p-2 text-center">Total Time</th>
            <th className="p-2 text-center">Total Marks</th>
            <th className="p-2  text-center">Delete</th>
          </tr>
        </thead>
        <tbody className="">
          {Array.isArray(currentItems) && currentItems.length > 0 ? (
            currentItems.map((item) => (
              <tr
                key={item.assessment_id}
                className="   border-b h-10 "
              >
                <td className="  p-2  px-3 text-left  ">{item.assessment_id}</td>
                <td className="  p-2  text-left  ">{item.assessment_name}</td>
                <td className="  p-2  text-left ">
                  {item.instructions}
                </td>
                <td className="  p-2  text-center ">{(item.total_time_allowed/60).toFixed(2)}</td>
                <td className=" p-2  text-center ">{item.total_marks}</td>
                <td className="p-2 text-center">
                     
                     <button
                       onClick={() =>
                         handleDeleteClick(item.assessment_id)
                       }
                     >
                       <TrashIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-red-500" />
                     </button>
                   
                 </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={6} className="text-center p-4">
                <Nothing
                  title="No Assessmet Available"
                  para="There are currently no assessmet to display.
                        Please check back later."
                />
              </td>
            </tr>
          )}
        </tbody>
         {/* Empty rows to maintain the table's appearance for fewer items */}
         {currentItems.length < itemsPerPage && 
          [...Array(itemsPerPage - currentItems.length)].map((_, index) => (
            <tr key={`empty-${index}`} className=" align-text-top h-10">
              <td colSpan={7}></td>
            </tr>
          ))
        }
      </table>
      </div>
   {/* Pagination controls */}
   <div className="flex justify-between items-end w-full">
        <button
          onClick={handlePreviousPage}
          disabled={currentPage <= 1}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Previous
        </button>
        <button
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Next
        </button>
      </div>
     

      {isRemoveModal && (
        <DeleteModal
          deleteName="content"
          onClose={() => setIsRemoveModal(false)}
          onOpen={handleConfirmDelete}
        />
      )}
      {showDeletedUserModal && (
        <DeletedUserModal
          deleteName="content"
          onClose={handleCloseDeletedUserModal}
        />
      )}
   
    </div>
  );
}
