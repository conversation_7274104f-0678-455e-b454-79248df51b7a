import React from "react";

export default function AssessmentAttemptsTable({ attempts }) {
  console.log("attempts", attempts);

  const options = {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
    timeZoneName: "short"
  };

  return (
    <div className="p-4 overflow-x-auto">
      <table className="w-full  text-sm text-left rtl:text-right text-gray-800 dark:text-gray-800 h-fit">
        <thead className="bg-secondary text-white text-sm uppercase">
          <tr className="h-fit">
            <th className="px-6 py-3">User Assessment Attempt Id</th>
            <th className="px-6 py-3">User Id</th>
            <th className="px-6 py-3">User Full Name</th>
            <th className="px-6 py-3">Email</th>
            <th className="px-6 py-3">User Assessment Id</th>
            <th className="px-6 py-3">Attempt Status</th>
            <th className="px-6 py-3">Attempt Number</th>
            <th className="px-6 py-3">Attempt Start Date</th>
            <th className="px-6 py-3">Attempt End Date</th>
            <th className="px-6 py-3">Attempt Total Time</th>
            <th className="px-6 py-3">Attempt Total</th>
            <th className="px-6 py-3">Attempt Evaluation</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {attempts.map((attempt) => (
            <tr key={attempt.user_assessment_attempt_id} className="h-fit">
              <td className="px-6 py-4">{attempt.user_assessment_attempt_id}</td>
              <td className="px-6 py-4">{attempt.user_id}</td>
              <td className="px-6 py-4">{attempt.user_full_name}</td>
              <td className="px-6 py-4">{attempt.email}</td>
              <td className="px-6 py-4">{attempt.user_assessment_id}</td>
              <td className="px-6 py-4">{attempt.attempt_status}</td>
              <td className="px-6 py-4">{attempt.attempt_number}</td>
              <td className="px-6 py-4">{new Date(attempt.attempt_start_date).toLocaleDateString("en-US", options)}</td>
              <td className="px-6 py-4">{new Date(attempt.attempt_end_date).toLocaleDateString("en-US", options)}</td>
              <td className="px-6 py-4">{attempt.attempt_total_time}</td>
              <td className="px-6 py-4">{attempt.attempt_total}</td>
              <td className="px-6 py-4">{attempt.attempt_evaluation}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
