"use client";
import { InformationCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import React, {useState} from 'react'
import { useGetUserPrompt } from '@/hook/assessments/useGetUserPrompt';

interface ModalProps {
    onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
    onTextSubmit: (text: string) => void ;
    isLoading :boolean;
  }
  
 
export default function Propmt({ onClose,onTextSubmit ,isLoading}: ModalProps) {

  const [text, setText] = useState("")

  const handleSubmit = () => {
    onTextSubmit(text); 
    onClose();  
  };

  console.log("text working",text)
  return (
    <div className="modal flex justify-center items-center  fixed inset-0 bg-black bg-opacity-40  overflow-auto z-10">
    <div className="flex flex-col gap-3 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md w-[400px] h-[300px] ">
      <button className="flex justify-end" onClick={onClose} >
        <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
      </button>{" "}
      <div className="flex flex-col gap-2 justify-center items-center w-full h-full ">
      
        <label
          className="flex  text-gray-700 text-sm font-bold mb-2 gap-2"
          htmlFor="textarea"
          title="Submit Button will post the all question at once"
        >
             
             <InformationCircleIcon className="h-[20px] w-[20px] " /> Enter text:
        </label>
        <textarea
          id="text"
          name="text"
          onChange={(e)=>setText(e.target.value)}
          className="resize-none border shadow rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          rows={4}
          placeholder="Type text..."
        />
      </div>
      <div className="w-full  flex justify-center items-end p-2">
              <button
              onClick={handleSubmit}
                type="submit"
                className=" flex gap-1 bg-secondary hover:bg-hoverColor text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                Submit
               
              </button>
            </div>
    </div>
  </div>
  )
}
