"use client";
import React, { useState, useRef } from "react";
import SearchBar from "../SearchBar";
import { PlusIcon, MinusIcon } from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";

import { AddRemoveAssessmentToGroup } from "@/types/LMSTypes";
import { useRemoveUserFromGroup } from "@/hook/admin/group/useRemoveUserFromGroup";
import { useRemoveAssessmentFromGroup } from "@/hook/admin/assessments/useRemoveAssessmentFromGroup";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import Nothing from "@/components/ui/Nothing";

import AddAssessmentModal from "@/components/admin/userGroup/AddAssessmentModal";
import SubmitModal from "@/components/ui/SubmitModal";
import { useGetGroupAssessment } from "@/hook/admin/usergroup/assessments/useGetGroupAssessment";
import { useGetNonGroupAssessment } from "@/hook/admin/usergroup/assessments/useGetNonGroupAssessment";
import { useQueryClient } from "@tanstack/react-query";

export default function AllAssessments({ type, groupId }: any) {
  const [search, setsearch] = useState("");
  const [groupIdValue, setGroupIdValue] = useState(groupId);
  const [groupAssessmentId, setGroupAssessmentId] = useState(Number);
  const [open, setOpen] = useState<boolean>(false);
  const [userIdValue, setuserIdValue] = useState(Number);
  const [search2, setsearch2] = useState("");
  const [first, setfirst] = useState(false)
  const [assessmentId, setAssessmentId] = useState<number>()
  const [assessmentName, setAssessmentName] = useState("")
  const [type1, setType1] = useState("")


  const assessment_name = useRef("")

  const [showSubmitModal, setShowSubmitModal] = useState(false);

  const onAddAssessment = () => {
    setOpen(false);
    setShowSubmitModal(true);
  };
  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
  };

  const newDetail: AddRemoveAssessmentToGroup = {
    group_assessment_id: groupAssessmentId,
  };

  console.log("search 1:-", search)
  console.log("search 2:-", search2)


  const { data: gettingAssessmentInGroup } = useGetGroupAssessment(groupId, search2)

  const { data: gettingAssessmentNotInGroup } = useGetNonGroupAssessment(groupId, search)

  const queryClient = useQueryClient();

  // if (type1 == "add") {
  //   queryClient.invalidateQueries({ queryKey: ['getNonGroupAssessment',groupId, search2] })
  // } else {
  //   queryClient.invalidateQueries({ queryKey: ['getAssessmentsInGroup',groupId, search] })
  // }


  function convertIsoToCustomFormat(isoString) {
    // Parse the ISO 8601 string
    const date = new Date(isoString);

    // Get the components of the date
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based, so we add 1
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    // Concatenate the components in the desired format
    const customFormat = `${year}/${month}/${day} ${hours}:${minutes}`;

    return customFormat;
  }


  console.log("data gettingAssessmentInGroup:-", gettingAssessmentInGroup)
  console.log("data gettingAssessmentNotInGroup:-", gettingAssessmentNotInGroup)

  const removeAssessmentToGroup = useRemoveAssessmentFromGroup(newDetail);

  const handleClickUser = async (type: string) => {
    if (type === "remove") {
      try {
        await removeAssessmentToGroup.mutate();
        queryClient.invalidateQueries({ queryKey: ['getNonGroupAssessment', groupId, search2] })
      } catch (error) {
        console.error("Error mutating assessment in group:", error);
      }
    } else {
      console.log("not working");
    }
  };

  if (type == "remove") {
    return (
      <div className=" flex flex-col w-auto h-full  p-2 gap-1">
        <div className=" flex justify-between mx-1 md:mx-4">
          <Heading pgHeading="Group Assessment" />
          <SearchBar onSearch={setsearch} />
        </div>
        <div className="overflow-y-auto  w-full  h-full bg-white  rounded-lg  border">
          <table className="w-full text-sm text-left rtl:text-right text-gray-800 dark:text-gray-800 h-fit">
            <thead className=" bg-secondary  text-white text-sm sticky top-0 uppercase w-full  z-10">
              <tr className="w-full h-fit">
                <th className="p-2  text-left">assessment id</th>
                <th className="p-2 text-left">assessment name</th>
                <th className="p-2 text-left">start date</th>
                <th className="p-2 text-left">end date</th>
                <th className="p-2 text-left">max attempts</th>
                <th className="p-2 text-left">assigned by</th>
                <th className="p-2 text-center">Action</th>
              </tr>
            </thead>
            <tbody className="relative  overflow-x-auto">
            {gettingAssessmentInGroup && gettingAssessmentInGroup.length > 0 ? (
              gettingAssessmentInGroup?.map((assessment, index) => (
                <tr key={index} className="  w-full   border-b align-top h-fit">
                  <td className="p-2  px-3 text-left">{assessment.assessment_id}</td>
                  <td className="p-2 text-center">{assessment.assessment_name}</td>

                  <td className="  p-2  text-left  ">
                    {convertIsoToCustomFormat(assessment.start_date)} 
                  </td>
                  <td className="  p-2  text-left  ">{convertIsoToCustomFormat(assessment.end_date)}</td>
                  <td className="  p-2  text-left  ">
                    {assessment.max_attempts}
                  </td>
                  <td className="  p-2  text-left  ">
                    {assessment.assigned_by}
                  </td>
                  <td className="  p-2  text-center  ">
                    <button
                      onClick={() => {
                        handleClickUser("remove");
                        setGroupAssessmentId(assessment.group_assessment_id);
                        setType1("remove")
                      }}
                      className="p-1 bg-secondary text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring focus:border-blue-300"
                    >
                      <MinusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
           
              <tr>
              <td colSpan={7} className="text-center p-4">
              <Nothing
            title="No Assessments Available"
            para="There are currently no assessments available. 
            Please check back later, or consider creating new assessments."
          />
              </td>
            </tr>)}
            </tbody>
          </table> 
        </div>
      </div> 
    );
  } else if (type == "add") {
    return (
      <div className=" flex flex-col w-auto h-full  p-2 gap-1">
        <div className=" flex justify-between mx-1 md:mx-4">
          <Heading pgHeading="All Assessment" />
          <SearchBar onSearch={setsearch2} />
        </div>
        <div className="overflow-y-auto  w-full  h-full bg-white  rounded-lg  border">
          <table className="w-full text-sm text-left rtl:text-right text-gray-800 dark:text-gray-800 h-fit">
            <thead className=" bg-secondary  text-white text-sm sticky top-0 uppercase w-full z-10 ">
              <tr className="w-full h-fit">
                <th className="p-2 text-left">assessment id</th>
                <th className="p-2 text-left">assessmentname</th>

                <th className="p-2 text-left">instructions</th>
                <th className="p-2 text-left">total time allowed</th>
                <th className="p-2 text-left">total marks</th>
                <th className="p-2 text-left">source</th>
                <th className="p-2 text-center">Action</th>
              </tr>
            </thead>
            <tbody className="relative  overflow-x-auto">
            {gettingAssessmentNotInGroup && gettingAssessmentNotInGroup.length > 0 ? (
              gettingAssessmentNotInGroup?.map((assessment, index) => (
                <tr key={index} className="  w-full   border-b align-top h-fit">
                  <td className="p-2  px-3 text-left">{assessment.assessment_id}</td>
                  <td className="  p-2 text-left">{assessment.assessment_name}</td>

                  <td className="  p-2  text-left  ">
                    {assessment.instructions}
                  </td>
                  <td className="  p-2  text-left  ">
                    {(assessment.total_time_allowed/60).toFixed(2)}
                  </td>
                  <td className="  p-2  text-left  ">
                    {assessment.total_marks}
                  </td>
                  <td className="  p-2  text-left  ">{assessment.source}</td>
                  <td className="  p-2  text-center  ">
                    <button
                      onClick={() => {
                        handleClickUser("add");
                        setuserIdValue(assessment.user_id);
                        setAssessmentId(assessment.assessment_id)
                        setAssessmentName(assessment.assessment_name)
                        setOpen(true)
                        setfirst(true)
                        setType1("add")
                      }}
                      className="p-1 bg-secondary text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring focus:border-blue-300"
                    >
                      <PlusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                    </button>

                    {showSubmitModal && (
                      <SubmitModal
                        modalName="Assessment Added Success"
                        onClose={onCloseSubmitModal}
                      />
                    )}
                  </td>
                </tr>
              ))
            ) : (
           
              <tr>
              <td colSpan={7} className="text-center p-4">
              <Nothing
            title="No Assessments Available"
            para="There are currently no assessments available. 
            Please check back later, or consider creating new assessments."
          />
              </td>
            </tr>)}
            </tbody>
          </table> 

        </div>
        {first && <AddAssessmentModal open={open} onClose={() => setOpen(false)} group_Id={groupId} assessment_Id={assessmentId ? assessmentId : 0} assessmentName={assessmentName} />}
      </div>
    );
  } else {
    return (
      <>
        <h1>Wrong type</h1>
      </>
    );
  }
}
