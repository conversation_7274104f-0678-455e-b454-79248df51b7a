"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import DeletedUserModal from "../userGroup/DeletedUserModal";
import DeleteModal from "../userGroup/DeleteModal";
import Nothing from "@/components/ui/Nothing";

export default function AttemptAssessmentTable({ AssessmentData }) {


  const [isRemoveModal, setIsRemoveModal] = useState(false);
  const [showDeletedUserModal, setShowDeletedUserModal] = useState(false);
  const [selectedContentId, setSelectedContentId] = useState(null);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const totalPages = Math.ceil(AssessmentData?.length / itemsPerPage);


  const handleDeleteClick = (contentId) => {
    setSelectedContentId(contentId); // Store the ID for deletion
    setIsRemoveModal(true); // Open the modal for confirmation
  };



  const handleCloseDeletedUserModal = () => {
    setShowDeletedUserModal(false); // Close the DeletedUserModal
  };

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = AssessmentData?.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="flex flex-col gap-2 justify-start bg-white items-start rounded-md w-full h-[400px] border shadow-md">
    {/* Scrollable table container for medium to large screens */}
    <div className="overflow-auto max-h-[400px] w-full h-full  rounded-md">
      <table className="w-full text-sm text-left rtl:text-right text-gray-800 dark:text-gray-800 h-fit">
        <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
          <tr className="w-full h-fit">
            <th className="p-2 text-left">Assessment Id</th>
            <th className="p-2 text-left">Assessment Name</th>
            <th className="p-2 text-center">Total Time</th>
            <th className="p-2 text-center">Total Marks</th>
          </tr>
        </thead>
        <tbody className=" w-[100%] align-top  ">
          {Array.isArray(currentItems) && currentItems.length > 0 ? (
            currentItems.map((item) => (
              <tr
                key={item.assessment_id}
                className="w-full border-b align-top h-fit"
              >
                <td className="  p-2  px-3 text-left  ">{item.assessment_id}</td>
                <td className="  p-2  text-left  ">{item.assessment_name}</td>
                <td className="  p-2  text-center ">{(item.total_time_allowed/60).toFixed(2)}</td>
                <td className=" p-2  text-center ">{item.total_marks}</td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={5} className="text-center p-4">
                <Nothing
                  title="No Assessmet Available"
                  para="There are currently no assessmet to display.
                        Please check back later."
                />
              </td>
            </tr>
          )}
        </tbody>
      </table>
    
</div>
<div className="flex justify-between  items-end w-full ">
        <button
          onClick={handlePreviousPage}
          disabled={currentPage <= 1}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] "
        >
          Previous
        </button>
        <button
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Next
        </button>
      </div>
      {isRemoveModal && (
        <DeleteModal
          deleteName="content"
          onClose={() => setIsRemoveModal(false)}
          onOpen={handleConfirmDelete}
        />
      )}
      {showDeletedUserModal && (
        <DeletedUserModal
          deleteName="content"
          onClose={handleCloseDeletedUserModal}
        />
      )}
    </div>
  );
}
