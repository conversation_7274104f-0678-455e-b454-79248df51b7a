import React, { useState, useEffect, ChangeEvent } from 'react';
import { SubmitUserState } from "@/types/LMSTypes"
import DateTimePicker from 'react-datetime-picker';
import { PostUserAssessmentsOptions, EditUserAssessmentsOptions } from "@/types/LMSTypes"
import { useQueryClient } from '@tanstack/react-query'
import { useGetAllUserIds } from "@/hook/admin/useGetAllUserIds";
import { usePostUsersForAssessment } from "@/hook/assessments/usePostUsersForAssessment";
import { useEditUsersForAssessment } from "@/hook/assessments/useEditUsersForAssessment";
import 'react-datetime-picker/dist/DateTimePicker.css';
import 'react-datetime-picker/dist/DateTimePicker.css';
import 'react-calendar/dist/Calendar.css';
import 'react-clock/dist/Clock.css';
import { useRef } from 'react';

type ValuePiece = Date | null;
type Value = ValuePiece | [ValuePiece, ValuePiece];
const numberOfCheckboxes = 3;



type propType = {
  assessmentIdValue: any;
  userIdValue: any;
  formNoValue: any;
  formValue: any;
  allUserExtData: any;
  //later change the types to correct one
}

const FormComponent: React.FC<propType> = ({ assessmentIdValue, userIdValue, formNoValue, formValue, allUserExtData }) => {

  const userIdRef = useRef<number>(0);

  const queryClient = useQueryClient();
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const [selectedUserIdValue, setSelectedUserIdValue] = useState<number[]>([]);
  const [startDateValue, setStartDateValue] = useState<Value>(new Date());
  const [submissionStatus, setSubmissionStatus] = useState<'idle' | 'submitting' | 'submitted'>('idle');
  const [endDateValue, setEndDateValue] = useState<Value>(new Date());
  const [totalTimeAllowed, setTotalTimeAllowed] = useState<string>('');
  const [submissionComplete, setSubmissionComplete] = useState<boolean>(false);
  const [formNumber, setFormNumber] = useState<number>(1);
  const [maxAttemptsAllowed, setMaxAttemptsAllowed] = useState<string>('');
  const formArray = [1, 2, 3];
  const [inputValidation, setinputValidation] = useState<boolean>(false)
  const [formNo, setFormNo] = useState<number>(formArray[0]);
  const [state, setState] = useState<SubmitUserState>({
    start_date: new Date(),
    end_date: new Date(),
    max_attempts: 0,
    total_time_allowed: 0,
    checkboxList: Array.from({ length: numberOfCheckboxes }, () => false), // Initialize checkbox array
  });

  console.log("formNoValue: " + formNoValue)
  // setFormNo(formNoValue)

  const userAssessmentIdsValue: number[] = selectedUserIdValue.map((userId) => {
    const matchingEntry = userIdValue.find((entry) => entry.userId === userId);
    return matchingEntry ? matchingEntry.userAssessmentId : -1; // Use -1 if no match is found
  });

  const { data: gettingAllUserIds, isLoading: otherLoading, isError: otherError } = useGetAllUserIds();

  useEffect(() => {
    if (!otherLoading && !otherError) {
      queryClient.invalidateQueries({ queryKey: ['fetchAllUserExt'] })
    }
  }, [queryClient, gettingAllUserIds, otherLoading, otherError]);

  const checkBoxUserIdValueHandleForm1 = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (e.target.checked) {
      userIdRef.current = 1
    }
    else {
      userIdRef.current = 0
    }
    if (e.target.type === 'checkbox') {
      const userId = parseInt(e.target.value, 10);
      if ((e.target as HTMLInputElement).checked) {
        setSelectedUserIds((prevSelectedUserIds) => [...prevSelectedUserIds, userId]);

        userIdRef.current = userId;

      } else {
        setSelectedUserIds((prevSelectedUserIds) => prevSelectedUserIds.filter((id) => id !== userId));
      }
    } else {
      userIdRef.current = 0
      setState({
        ...state,
        [e.target.name]: e.target.value,
      });
    }
  };

  const checkBoxUserIdValueHandleForm2 = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {

    if (e.target.checked) {
      userIdRef.current = 1
    }
    else {
      userIdRef.current = 0
    }
    if (e.target.type === 'checkbox') {
      const userId = parseInt(e.target.value, 10);
      if ((e.target as HTMLInputElement).checked) {
        setSelectedUserIdValue((prevSelectedUserIds) => [...prevSelectedUserIds, userId]);
        userIdRef.current = userId;


      } else {
        setSelectedUserIdValue((prevSelectedUserIds) => prevSelectedUserIds.filter((id) => id !== userId));
      }
    } else {
      console.log("valuess: ", selectedUserIds.includes(user.user_id))
      userIdRef.current = 0
      setState({
        ...state,
        [e.target.name]: e.target.value,
      });
    }
  };




  const inputHandle = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setState({
      ...state,
      [e.target.name]: e.target.value,
    });
  };


  const next = () => {
    if (formNo === 1 && userIdRef.current !== 0) {

      console.log("selected values:- ", selectedUserIds)
      setFormNo(formNo + 1);
    } else if (formNo === 2 && state.start_date && state.end_date) {
      setFormNo(formNo + 1);
    } else {

    }



    // if (formNo === 1 && state.checkboxList) {
    //   setFormNo(formNo + 1);
    // } else if (formNo === 2 && state.start_date && state.end_date) {
    //   setFormNo(formNo + 1);
    // } else {

    // }
  };

  const pre = () => {
    setFormNo(formNo - 1);
  };

  const options: PostUserAssessmentsOptions = {
    userIds: selectedUserIds, // Replace with actual user IDs
    startDate: startDateValue, // Replace with actual start date
    endDate: endDateValue, // Replace with actual end date
    maxAttempts: maxAttemptsAllowed, // Replace with actual max attempts
    totalTimeAllowed: totalTimeAllowed * 60, // Replace with actual total time allowed in minutes

  };



  const userValueChange: EditUserAssessmentsOptions = {
    userAssessmentId: selectedUserIdValue,
    startDate: startDateValue,
    endDate: endDateValue,
    maxAttempts: maxAttemptsAllowed,
    totalTimeAllowed: totalTimeAllowed * 60,

  };
  const resetFunction = () => {
    setSelectedUserIds([])
    setSelectedUserIdValue([])
    setStartDateValue(new Date())
    setEndDateValue(new Date())
    setMaxAttemptsAllowed('')
    setinputValidation(true)
  }



  const availableUserIds = gettingAllUserIds.filter(value => {
    const userIds = userIdValue.map(obj => obj.userId);
    return !userIds.includes(value);
  });

  const postUsersForAssessmentMutation = usePostUsersForAssessment(assessmentIdValue, options);
  const editUsersForAssessmentMutation = useEditUsersForAssessment(userAssessmentIdsValue, userValueChange);

  const finalSubmit = async () => {
    try {
      setSubmissionStatus('submitting');

      if (
        selectedUserIds.length > 0 &&
        startDateValue &&
        endDateValue &&
        totalTimeAllowed &&
        maxAttemptsAllowed
      ) {
        setinputValidation(false)
        await postUsersForAssessmentMutation.mutate();
        queryClient.invalidateQueries();

        setSubmissionComplete(true);

        setTimeout(() => {
          setSubmissionComplete(false);
          setSubmissionStatus('idle');
          setFormNo(1); // Reset form number to 1 after successful submission
          setTimeout(() => {
            resetFunction(); //reset all the values in the form
          }, 1000);
        }, 1000);
      } else {
        setinputValidation(true)
      }
    } catch (error) {
      console.error('Error during submission:', error);
      console.error('Error details:', error.response?.data);
      setSubmissionStatus('idle'); // Reset status in case of an error
    }
  };

  const finalSubmitChange = async () => {
    try {
      setSubmissionStatus('submitting');

      if (
        selectedUserIdValue.length > 0 &&
        startDateValue &&
        endDateValue &&
        totalTimeAllowed &&
        maxAttemptsAllowed
      ) {
        setinputValidation(false)
        await editUsersForAssessmentMutation.mutate();
        queryClient.invalidateQueries();

        setSubmissionComplete(true);

        setTimeout(() => {
          setSubmissionComplete(false);
          setSubmissionStatus('idle');
          setTimeout(() => {
            resetFunction(); //reset all the values in the form
          }, 1000);
          setFormNo(1); // Reset form number to 1 after successful submission
        }, 1000);
      } else {
        setinputValidation(true)
      }
    } catch (error) {
      console.error('Error during submission:', error);
      console.error('Error details:', error.response?.data);
      setSubmissionStatus('idle'); // Reset status in case of an error
    }
  };

  //converting array of numbers into array of objects
  const availableUsers = availableUserIds.map(userId => ({ user_id: userId }));


  const mergedArray = availableUsers.filter(item => item.user_id != 1).map(item1 => {
    const matchingItem = allUserExtData?.find(item2 => item2.user_id === item1.user_id);

    if (matchingItem) {
      // Merge the properties from both arrays
      const mergedObject = { ...item1, ...matchingItem };
      console.log("mergedObject:-", mergedObject)


      return mergedObject;
    }
    return item1;
  });




  //converting userId to user_id
  const userIdValueNew = userIdValue.map((obj) => {
    const newOjb = { user_id: obj.userId, userAssessmentId: obj.userAssessmentId }
    return newOjb
  });

  const mergedArray2 = userIdValueNew.map(item1 => {
    const matchingItem = allUserExtData?.find(item2 => item2.user_id === item1.user_id);

    if (matchingItem) {
      // Merge the properties from both arrays
      const mergedObject = { ...item1, ...matchingItem };
      return mergedObject;
    }
    return item1;
  });


  if (formValue == 1 && !submissionComplete) {
    //adding the user value
    console.log(formValue)
    queryClient.invalidateQueries();

    return (


      <div className="card w-full h-full rounded-md bg-white justify-center p-4">



        <div className='flex justify-center items-center'>
          {
            formArray.map((v, i) => <><div className={`w-[35px] my-3 text-white rounded-full ${formNo - 1 === i || formNo - 1 === i + 1 || formNo === formArray.length ? 'bg-secondary' : 'bg-slate-400'} h-[35px] flex justify-center items-center`}>
              {v}
            </div>
              {
                i !== formArray.length - 1 && <div className={`w-[85px] h-[2px] ${formNo === i + 2 || formNo === formArray.length ? 'bg-secondary' : 'bg-slate-400'}`}></div>
              }
            </>)
          }
        </div>
        {
          formNo === 1 && <div className="w-full" style={{ height: '28rem' }}><div className="overflow-y-auto h-80 w-full" style={{ height: '28rem' }}>
            <table className="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 justify-center h-fit">
              <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr className='h-fit'>
                  <th scope="col" className="px-6 py-3">
                    User ID
                  </th>
                  <th scope="col" className="px-6 py-3">
                    Name
                  </th>
                  <th scope="col" className="flex gap-2 px-6 py-3">
                    Check
                    <input
                          id={`checkbox`}
                          type="checkbox"
                          

                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        />
                         
                  </th>

                </tr>
              </thead>
              <tbody>
                {mergedArray?.map((user, index) => (
                  <tr key={index} className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 h-fit">
                    <th scope="row" className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      {user.user_id}
                    </th>
                    <th scope="row" className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      {user.user_full_name}
                    </th>
                    <th scope="row" className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      <div className="flex items-center">
                        <input
                          id={`checkbox-${user.user_id}`}
                          type="checkbox"
                          value={user.user_id}
                          checked={selectedUserIds.includes(user.user_id)}
                          onChange={checkBoxUserIdValueHandleForm1}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        />
                        <label htmlFor="default-checkbox" className="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300" />
                      </div>
                    </th>
                  </tr>
                ))}
              </tbody>
            </table>

          </div>
            <div className='mt-4 flex justify-center items-center'>
              <button
                onClick={next}
                className={`px-3 w-40 py-2 text-lg rounded-md w-full text-white ${userIdRef.current === 0 ? "bg-hoverColor" : "bg-secondary"} hover:bg-hoverColor`}>
                Next
              </button>
            </div>
          </div>

        }

        {
          formNo === 2 && <div className="w-full" style={{ height: '28rem' }}><div className="h-80 w-full" style={{ height: '28rem' }}>
            <div className='flex flex-col mb-2'>
              <label className='text-slate-500' htmlFor="varsity">Start Date</label>
              <DateTimePicker
                className='text-slate-500 mb-10 p-2 mt-1 outline-0 focus:border-blue-500 rounded-md'
                onChange={(date: Date | [Date, Date] | null) => setStartDateValue(date as Date)}
                value={startDateValue}
              />
            </div>
            <div className='flex flex-col mb-2'>
              <label className='text-slate-500' htmlFor="session">End Date</label>
              <DateTimePicker
                className='text-slate-500 mb-10 p-2 mt-1 outline-0 focus:border-blue-500 rounded-md'
                onChange={(date: Date | [Date, Date] | null) => setEndDateValue(date as Date)}
                value={endDateValue}
              />
            </div>
          </div>
            <div className='mt-4 gap-3 flex justify-center items-center'>
              <button onClick={pre} className='px-3 py-2 text-lg rounded-md w-full text-white bg-secondary hover:bg-hoverColor' >Previous</button>
              <button
                onClick={next}
                className='px-3 py-2 text-lg rounded-md w-full text-white bg-secondary hover:bg-hoverColor '>
                Next
              </button>
            </div>
          </div>
        }

        {
          formNo === 3 && <div className="w-full" style={{ height: '28rem' }}><div className="h-80 w-full" style={{ height: '28rem' }}>
            <div className='flex flex-col mb-6'>
              <label className='text-slate-500' htmlFor="district">Total Time Allowed &#40; in minutes &#41; </label>
              <input
                value={totalTimeAllowed}
                onInput={(e) => setTotalTimeAllowed(e.currentTarget.value)}
                className='text-slate-500 p-2 border border-slate-400 mt-1 outline-0 focus:border-blue-500 rounded-md'
                type="number"
                name='total_time_allowed'
                placeholder='Enter the total time allowed'
                id='total_time_allowed'
              />
              {(totalTimeAllowed == '' && inputValidation) ? (<p className='text-sm text-red-500'> Please enter total time allowed</p>) : ''}
            </div>
            <div className='flex flex-col mb-2'>
              <label className='text-slate-500' htmlFor="thana">Max Attempts</label>
              <input
                value={maxAttemptsAllowed}
                onInput={(e) => setMaxAttemptsAllowed(e.currentTarget.value)}
                className='text-slate-500 p-2 border border-slate-400 mt-1 outline-0 focus:border-blue-500 rounded-md'
                type="number"
                name='max_attempts'
                placeholder='Enter maximum attempts'
                id='max_attempts'
              />
            </div>
            {(maxAttemptsAllowed == '' && inputValidation) ? (<p className='text-sm text-red-500'> Please enter attempts</p>) : ''}


          </div>
            <div className='mt-4 gap-3 flex justify-center items-center'>
              <button onClick={pre} className='px-3 py-2 text-lg rounded-md w-full text-white bg-secondary hover:bg-hoverColor '>Previous</button>
              <button
                onClick={finalSubmit}
                className={`px-3 py-2 text-lg rounded-md w-full text-white bg-secondary hover:bg-hoverColor ${submissionStatus === 'submitted' ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
              >
                {submissionStatus === 'submitted' ? 'Submitted' : 'Submit'}
              </button>

            </div>
          </div>

        }

      </div>
    );
  }
  else if (formValue == 2 && !submissionComplete) {
    //changing the user values
    console.log("form Value:" + formValue)
    queryClient.invalidateQueries();


    return (

      <div className="card w-full h-full rounded-md bg-white justify-center p-4">
        <div className='flex justify-center items-center'>
          {
            formArray.map((v, i) => <><div className={`w-[35px] my-3 text-white rounded-full ${formNo - 1 === i || formNo - 1 === i + 1 || formNo === formArray.length ? 'bg-secondary' : 'bg-slate-400'} h-[35px] flex justify-center items-center`}>
              {v}
            </div>
              {
                i !== formArray.length - 1 && <div className={`w-[85px] h-[2px] ${formNo === i + 2 || formNo === formArray.length ? 'bg-secondary' : 'bg-slate-400'}`}></div>
              }
            </>)
          }
        </div>
        {
          formNo === 1 && <div className="w-full" style={{ height: '28rem' }}><div className="overflow-y-auto h-80 w-full" style={{ height: '28rem' }}>
            <table className="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400 justify-center h-fit">
              <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr className='h-fit'>
                  <th scope="col" className="px-6 py-3">
                    User ID
                  </th>
                  <th scope="col" className="px-6 py-3">
                    Name
                  </th>
                  <th scope="col" className="flex gap-2 px-6 py-3">
                    Check
                    <input
                          id={`checkbox`}
                          type="checkbox"
                          

                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        />
                         
                  </th>

                </tr>
              </thead>
              <tbody>
                {mergedArray2?.map((user, index) => (
                  <tr key={index} className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 h-fit">
                    <th scope="row" className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      {user.user_id}
                    </th>
                    <th scope="row" className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      {user.user_full_name}
                    </th>
                    <th scope="row" className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      <div className="flex items-center">
                        <input
                          id={`checkbox-${user.user_id}`}
                          type="checkbox"
                          value={user.user_id}
                          checked={selectedUserIdValue.includes(user.user_id)}
                          onChange={checkBoxUserIdValueHandleForm2}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        />
                        <label htmlFor="default-checkbox" className="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300" />
                      </div>
                    </th>
                  </tr>
                ))}
              </tbody>
            </table>

          </div>
            <div className='mt-4 flex justify-center items-center'>
              <button
                onClick={next}
                className={`px-3 w-40 py-2 text-lg rounded-md w-full text-white ${userIdRef.current === 0 ? "bg-hoverColor" : "bg-secondary"} hover:bg-hoverColor`}>
                Next
              </button>

            </div>
          </div>

        }

        {
          formNo === 2 && <div className="w-full" style={{ height: '28rem' }}><div className="h-80 w-full" style={{ height: '28rem' }}>
            <div className='flex flex-col mb-2'>
              <label className='text-slate-500' htmlFor="varsity">Start Date</label>
              <DateTimePicker
                className='text-slate-500 mb-10 p-2 mt-1 outline-0 focus:border-blue-500 rounded-md'
                onChange={(date: Date | [Date, Date] | null) => setStartDateValue(date as Date)}
                value={startDateValue}
              />
            </div>
            <div className='flex flex-col mb-2'>
              <label className='text-slate-500' htmlFor="session">End Date</label>
              <DateTimePicker
                className='text-slate-500 mb-10 p-2 mt-1 outline-0 focus:border-blue-500 rounded-md'
                onChange={(date: Date | [Date, Date] | null) => setEndDateValue(date as Date)}
                value={endDateValue}
              />
            </div>
          </div>
            <div className='mt-4 gap-3 flex justify-center items-center'>
              <button onClick={pre} className='px-3 py-2 text-lg rounded-md w-full text-white bg-secondary hover:bg-hoverColor' >Previous</button>
              <button
                onClick={next}
                className='px-3 py-2 text-lg rounded-md w-full text-white bg-secondary hover:bg-hoverColor '>
                Next
              </button>
            </div>
          </div>
        }

        {
          formNo === 3 && <div className="w-full" style={{ height: '28rem' }}><div className="h-80 w-full" style={{ height: '28rem' }}>
            <div className='flex flex-col mb-6'>
              <label className='text-slate-500' htmlFor="district">Total Time Allowed &#40; in minutes &#41; </label>
              <input
                value={totalTimeAllowed}
                onInput={(e) => setTotalTimeAllowed(e.currentTarget.value)}
                className='text-slate-500 p-2 border border-slate-400 mt-1 outline-0 focus:border-blue-500 rounded-md'
                type="number"
                name='total_time_allowed'
                placeholder='Enter the total time allowed'
                id='total_time_allowed'
              />
              {(totalTimeAllowed == '' && inputValidation) ? (<p className='text-sm text-red-500'> Please enter total time allowed</p>) : ''}
            </div>
            <div className='flex flex-col mb-2'>
              <label className='text-slate-500' htmlFor="thana">Max Attempts</label>
              <input
                value={maxAttemptsAllowed}
                onInput={(e) => setMaxAttemptsAllowed(e.currentTarget.value)}
                className='text-slate-500 p-2 border border-slate-400 mt-1 outline-0 focus:border-blue-500 rounded-md'
                type="number"
                name='max_attempts'
                placeholder='Enter maximum attempts'
                id='max_attempts'
              />
            </div>
            {(maxAttemptsAllowed == '' && inputValidation) ? (<p className='text-sm text-red-500'> Please enter attempts</p>) : ''}


          </div>
            <div className='mt-4 gap-3 flex justify-center items-center'>
              <button onClick={pre} className='px-3 py-2 text-lg rounded-md w-full text-white bg-secondary hover:bg-hoverColor '>Previous</button>
              <button
                onClick={finalSubmitChange}
                className={`px-3 py-2 text-lg rounded-md w-full text-white bg-secondary hover:bg-hoverColor ${submissionStatus === 'submitted' ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
              >
                {submissionStatus === 'submitted' ? 'Submitted' : 'Submit'}
              </button>

            </div>
          </div>

        }

      </div>
    );
  }
  else {
    return (
      <div className="text-center mt-28">
        <h1 className="text-2xl font-bold">Submitted!</h1>
      </div>
    );
  }


};

export default FormComponent;
