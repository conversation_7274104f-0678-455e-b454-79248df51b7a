"use client";
import Nothing from "@/components/ui/Nothing";
import React, { useEffect, useState } from "react";

export default function ShowAssessmentAttemptsUser({ Data }) {
  const [currentUserId, setCurrentUserId] = useState("");
  const [currentAssessmentName, setCurrentAssessmentName] = useState("");
  const [currentAssessmentId, setCurrentAssessmentId] = useState("");

  useEffect(() => {
    if (Array.isArray(Data) && Data.length > 0) {
      setCurrentUserId(Data[0].user_id);
      setCurrentAssessmentName(Data[0].assessment_name);
      setCurrentAssessmentId(Data[0].assessment_id);
    } else {
      setCurrentUserId("");
      setCurrentAssessmentName("");
      setCurrentAssessmentId("");
    }
  }, [Data]);


  if (!Array.isArray(Data) || Data.length === 0) {
    return <div className="text-center"></div>;
  }

  return (
    <div className="flex flex-col justify-start items-start  w-full border rounded-lg  border-secondary">

      <div className='flex justify-between w-full bg-secondary text-white  rounded-t-lg  px-4 py-2  '>
        <p>User ID: {currentUserId}</p>
        <p>Assessment Name: {currentAssessmentName}</p>
        <p>Assessment Id: {currentAssessmentId}</p>
      </div>

      <div className="flex flex-col justify-between w-full h-full p-1  gap-2  ">
        {Data.map((attempt, index) => (
          <div
            key={index}
            className="flex flex-col  justify-center w-full h-full md:justify-between rounded-lg  bg-white p-3 "
          >

            <div className="flex   justify-center w-full h-full md:justify-between">
              <div className="flex flex-col  justify-between gap-1">
                <p><span className=" text-gray-500  ">
                  Start Date:{" "}</span>
                  {new Date(attempt.start_date).toLocaleDateString()}
                </p>
                <p><span className=" text-gray-500  ">
                  Attempt Total:{" "}</span>
                  {attempt?.attempt_total}
                </p>
                <p><span className=" text-gray-500  ">
                  End Date:{" "}</span>
                  {new Date(attempt.end_date).toLocaleDateString()}
                </p>

              </div>
              <div className="flex flex-col  justify-center gap-1">
                <p><span className=" text-gray-500  "> Attempt Number: </span>{attempt.attempt_number}</p>
                <p><span className=" text-gray-500  ">Evaluation: </span>{attempt.attempt_evaluation}</p>
                <p><span className=" text-gray-500  ">Total Marks : </span>{attempt.total_marks}</p>
              </div>

              <div className="flex flex-col   justify-between gap-1">

                <p><span className=" text-gray-500  ">
                  Start Time:{" "}</span>
                  {new Date(attempt.start_date).toLocaleTimeString()}
                </p>

                <p><span className=" text-gray-500  ">
                  End Time:{" "}</span>
                  {new Date(attempt.end_date).toLocaleTimeString()}
                </p>
              </div>

            </div>

          </div>
        ))}
      </div>
    </div>
  );
}
