import React, { useState } from "react";
import { PencilSquareIcon } from "@heroicons/react/24/outline";

type SelectedQuestionProps = {
  selectedque: Array;
};

const SelectedQuestion: React.FC<UploadQuestionProps> = ({ selectedque }) => {
  const [selectedQuestions, setSelectedQuestions] = useState<number[]>([]);

  const handleCheckboxChange = (questionNo: number) => {
    setSelectedQuestions((prevSelectedQuestions) =>
      prevSelectedQuestions.includes(questionNo)
        ? prevSelectedQuestions.filter((id) => id !== questionNo)
        : [...prevSelectedQuestions, questionNo]
    );
  };

  const data = mockQuestionData.map((item) => ({
    questionNo: item.question_id,
    question: item.question,
    questionDifficulty: item.question_difficulty,
    questionSource: item.question_source,
    questionTopics: item.topics_string,
    selected: selectedQuestions.includes(item.question_id),
  }));

  const selectedData = data.filter((item) => item.selected);
  return (
    <div className="flex flex-col h-full w-full gap-2 ">
      <div className="w-full rounded-lg h-1/2 overflow-y-auto mt-4">
        <table className="w-full h-fit">
          <thead className="bg-secondary text-white sticky top-0">
            <tr className="h-fit">
              <th className="p-2 text-left">Question No</th>
              <th className="p-2 text-left">Question</th>
              <th className="p-2 text-left">Difficult Level</th>
              <th className="p-2 text-left">Question Source</th>
              <th className="p-2 text-left">Topics</th>
              <th className="p-2">Remove</th>
            </tr>
          </thead>
          <tbody>
            {selectedData.map((item, index) => (
              <tr key={item.questionNo} className="border-b-2 h-fit">
                <td className="p-2 px-3 text-left">{item.questionNo}</td>
                <td className="p-2 text-left">{item.question}</td>
                <td className="p-2 text-left">{item.questionDifficulty}</td>
                <td className="p-2 text-left">{item.questionSource}</td>
                <td className="p-2 text-left">{item.questionTopics}</td>
                <td className="p-2 flex justify-center"></td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
export default UploadQuestion;
