import React from 'react'
import ShowAssessmentAttemptsUser from './ShowAssessmentAttemptsUser'
import { useSearchParams } from 'next/navigation';
import { useGetAllAttemptsForUser } from '@/hook/assessment_attempts/useGetAllAttemptsForUser';
import DeletedUserModal from '../userGroup/DeletedUserModal';

export default function PageForAssessmentUserShow({ userId, assessmentId }: any) {

  const { data: AttemptsData } = useGetAllAttemptsForUser(assessmentId, userId)
  return (
    <div className="flex flex-col flex-grow overflow-auto ">
      <ShowAssessmentAttemptsUser Data={AttemptsData} />
    </div>
  )
}
