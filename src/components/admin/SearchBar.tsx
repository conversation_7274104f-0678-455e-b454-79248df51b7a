// src/components/SearchBar.tsx
"use client";
import React, { useState } from "react";
import { User_ext } from "@/types/LMSTypes";
import { useCount } from "@/context/searchStore";

interface SearchBarProps {
  onSearch?: (value: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch }) => {
  const {searchValue, setSearchCurrentValue ,setSearchValue , setSearchNumber} = useCount();

  const handleSubmit = (value: string) => {
    if (value) {
      setSearchValue(true);
      setSearchNumber();
      setSearchCurrentValue(value);
    } else if (!value) {
      setSearchValue(false);
      setSearchNumber();
    }else{

    }
  };

  return (
    <div className="flex items-center">
      <input
        type="text"
        onChange={(e) => {
          const value = e.target.value;
          if (onSearch) onSearch(value);
          handleSubmit(value);
        }}
        placeholder="Search..."
        className="w-[200px] md:w-[250px] lg:w-[300px] px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring focus:border-blue-300"
      />
    </div>
  );
};

export default SearchBar;
