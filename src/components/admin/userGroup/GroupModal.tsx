import React, { useState, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import SearchBar from "../SearchBar";
import { useGetGroupByID } from "@/hook/admin/group/useGetGroupByID";
import { useGetAllUserExt } from "@/hook/admin/useGetAllUserExt";
import { User_ext } from "@/types/LMSTypes";
import { useAddGroup } from "@/hook/admin/group/useAddGroup";
import { useUpdateGroup } from "@/hook/admin/group/useUpdateGroup";
import { AddGroup } from "@/types/LMSTypes";
import { UpdateGroup } from "@/types/LMSTypes";
import { useGetSearchedUsers } from "@/hook/admin/useGetSearchedUsers";


interface FormErrors {
  [key: string]: string;
}

const GroupModal = ({
  onClose,
  onSubmit,
  groupID, // Define the groupID prop
  type
}: {
  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  onSubmit: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  groupID: number; // Define the type of groupID
  type: string;
}) => {
  const [search, setSearch] = useState("");
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const { data: groupData, isLoading: isLoadingGroup, isError: isErrorGroup } = useGetGroupByID(groupID);
  const { data: userData, isLoading: isLoadingUser, isError: isErrorUser } = useGetAllUserExt();
  const [selectedGroupName, setselectedGroupName] = useState()
  const [selectedGroupFAQ, setselectedGroupFAQ] = useState()
  const [selectedGroupID, setSelectedGroupID] = useState(Number)
  const [selectedisActiveState, setSelectedisActiveState] = useState(Boolean)
  const [selectedGroupAdmin, setSelectedGroupAdmin] = useState(String)
  const [selectedGroupAdminId, setselectedGroupAdminId] = useState(groupData?.group_admin_user_id)
  const [isActive, setIsActive] = useState(Number)
  const [submitState, setSubmitState] = useState("Save");
  const [errors, setErrors] = useState<FormErrors>({});

  console.log("editiong values", groupData)

  const details: AddGroup = {
    group_name: selectedGroupName,
    created_by: selectedGroupAdmin,
    group_faq: selectedGroupFAQ,
    group_admin_user_id: selectedGroupAdminId
  }

  const updateGroupData: UpdateGroup = {
    group_id: groupID,
    created_by: selectedGroupAdmin,
    group_name: selectedGroupName,
    group_faq: selectedGroupFAQ,
    group_admin_user_id: selectedGroupAdminId,
    isactive: selectedisActiveState ? 1 : 0
  }

  const addingGroup = useAddGroup(details); // Use the hook here
  const updatingGroup = useUpdateGroup(updateGroupData); // Use the hook here

  const { data: searchedUsers } = useGetSearchedUsers(search);

  // console.log("groupID", groupID)

  useEffect(() => {
    if (groupData) {
      setselectedGroupName(groupData.group_name || "");
      setselectedGroupFAQ(groupData.group_faq || "");
      setselectedGroupAdminId(groupData.group_admin_user_id || null);
      setSelectedisActiveState(groupData.isactive === 1);
      if (userData) {
        const admin = userData.find(user => user.user_id === groupData.group_admin_user_id);
        setSelectedGroupAdmin(admin ? admin.user_full_name : "");
      }
    }
  }, [groupData, userData]);

  useEffect(() => {
    if (!isLoadingGroup && !isErrorGroup) {
      console.log('groupData:', groupData);
    }
    if (!isLoadingUser && !isErrorUser) {
      console.log('userData:', userData);
    }
  }, [groupID, isLoadingGroup, isErrorGroup, userData, isLoadingUser, isErrorUser]);

  if (isLoadingGroup || isLoadingUser) {
    return <div>Loading...</div>;
  }

  if (isErrorGroup || isErrorUser) {
    return <div>Error fetching data</div>;
  }


  const handleChangeActiveState = () => {
    if (selectedisActiveState === true) {
      setSelectedisActiveState(false)
    } else if (selectedisActiveState === false) {
      setSelectedisActiveState(true)
    }
  }

  function findCommonObjects(array1, array2) {
    // Check if array2 is not an array or undefined, return array1
    if (!Array.isArray(array2) || typeof array2 !== 'object') {
      return array1;
    }

    const commonObjects = [];

    // Iterate through the first array
    for (let obj1 of array1) {
      // Check if the object exists in the second array
      const found = array2.find(obj2 => JSON.stringify(obj1) === JSON.stringify(obj2));
      if (found) {
        commonObjects.push(obj1);
      }
    }

    // If no common objects found, return all objects from array1
    if (commonObjects.length === 0) {
      return array1;
    }

    return commonObjects;
  }

  const userFilteredValues = findCommonObjects(userData, searchedUsers)

  // console.log("searched users values:- ", userFilteredValues)

  const handleChangeUserDetails = (user_id: number, user_full_name: string) => {
    if (selectedGroupAdminId === user_id && selectedUserId === user_id) {
      setSelectedGroupAdmin("");
      setselectedGroupAdminId(0);
      setSelectedUserId(0);
    } else {
      setSelectedGroupAdmin(user_full_name);
      setselectedGroupAdminId(user_id);
      setSelectedUserId(user_id);
    }
  };


  const handleSearch = (userSearch: String) => {
    //Call the filter content API and use the filtered content here
    console.log("Check for content", userSearch);
    setSearch(userSearch);
    console.log("search things:- ", search)
  };

  // // Function to handle checkbox change
  // const handleCheckboxChange = (userId: number) => {
  //   // Toggle selection: if the clicked checkbox's user is already selected as Admin, deselect it, otherwise select it
  //   setSelectedUserId((prevSelectedUserId) =>
  //     prevSelectedUserId === userId ? null : userId
  //   )
  // };

  const handleSubmitDetails = async (type: string, event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault(); // Prevent default form submission behavior

    const newErrors: FormErrors = {};

    if (!selectedGroupName || selectedGroupName.length < 3) {
      newErrors.groupName = "Please enter a valid group name";
    }

    if (!selectedGroupAdmin || selectedGroupAdmin.length < 2) {
      newErrors.groupAdmin = "Please select a group Admin from below";
    }

    if (!selectedGroupFAQ || selectedGroupFAQ.length < 2) {
      newErrors.groupFAQ = "Please enter a FAQ for group";
    }

    setErrors(newErrors);

    // Only proceed if there are no errors
    if (Object.keys(newErrors).length === 0) {
      try {
        setSubmitState("Loading");
        if (type === "create") {
          await addingGroup.mutate(details);
        } else if (type === "edit") {
          await updatingGroup.mutate(updateGroupData);
        }
        setSubmitState("Save");
        onSubmit();
      } catch (error) {
        console.error("Error handling group:", error);
      }
    }
  };

  if (type == "createGroup") {
    return (
      <div className="modal flex justify-center items-center  fixed inset-0 bg-black bg-opacity-40  overflow-auto z-10">
        <form onSubmit={(e) => handleSubmitDetails("create", e)} className="flex flex-col r gap-3 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md w-5/6 lg:w-[650px] h-[550px]">

          <div className="flex justify-between"> <h1 className="text-xl flex">Create Group</h1>

            <button className="flex justify-end" onClick={onClose}>
              <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
            </button>
          </div>

          <div className="flex flex-col lg:flex-row gap-2  ">
            <label className="flex flex-col gap-2 w-full">
              Group Name:
              <input
                className="border w-full py-2 px-4 rounded-md"
                type="text"

                onChange={(e) => setselectedGroupName(e.target.value)}
              />
              {errors.groupName && (
                <p className="text-red-500 text-xs italic">
                  {errors.groupName}
                </p>
              )}
            </label>
            <label className="flex flex-col  gap-2 w-full">
              <p className="flex-none">Group Admin:</p>
              <input
                className="border w-full py-2 px-4 rounded-md bg-white"
                type="text"

                value={selectedGroupAdmin}
                disabled
                onChange={(e) => setSelectedGroupAdmin(e.target.value)}
              />
              {errors.groupAdmin && (
                <p className="text-red-500 text-xs italic">
                  {errors.groupAdmin}
                </p>
              )}
            </label>
          </div>

          <div><label className="flex flex-col gap-2 w-full">
            Program Details:
            <textarea
              className="border w-full py-2 px-4 rounded-md"
              rows={2}
              cols={50}

              onChange={(e) => setselectedGroupFAQ(e.target.value)}
            />
            {errors.groupFAQ && (
              <p className="text-red-500 text-xs italic">
                {errors.groupFAQ}
              </p>
            )}
          </label></div>

          <SearchBar onSearch={handleSearch} />

          <div className="w-full rounded-lg h-[400px] overflow-y-auto border shadow-md">
            <table className="w-full h-fit">
              <thead className="bg-secondary text-white text-[12px] sticky top-0 uppercase z-10 ">
                <tr className="h-fit">
                  <th scope="col" className="px-4 py-1">
                    User ID
                  </th>
                  <th scope="col" className="px-4 py-1">
                    Name
                  </th>
                  <th scope="col" className="px-4 py-1">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="relative">
                {userFilteredValues?.map((userValue: User_ext) => (
                  <tr
                    key={userValue.user_id}
                    className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 h-fit"
                  >
                    <th
                      scope="row"
                      className="px-4 py-1 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                    >
                      {userValue.user_id}
                    </th>
                    <td className="px-4 py-1 text-center ">{userValue.user_full_name}</td>
                    <td className="px-4 py-1  text-center ">
                      <input
                        type="radio"
                        id={userValue.user_id.toString()}
                        checked={selectedUserId === userValue.user_id}
                        onClick={() => handleChangeUserDetails(userValue.user_id, userValue.user_full_name)}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="flex justify-center ">
            <button
              className="py-2 px-4 bg-textColor text-white rounded-md"
              type="submit" // Add type="button" to prevent form submission

            >
              {"Create"}
            </button>

          </div>
        </form>
      </div>
    );
  } else if (type == "editGroup") {
    //setselectedGroupName(groupData.group_name);
    //setSelectedGroupAdmin(groupData.)

    const admin = userData.find(user => user.user_id === groupData.group_admin_user_id);


    return (
      <div className="modal flex justify-center items-center  fixed inset-0 bg-black bg-opacity-40  overflow-auto z-10">
        <form onSubmit={(e) => handleSubmitDetails("edit", e)} className="flex flex-col r gap-3 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md  w-[650px] h-[550px]">

          <div className="flex justify-between"> <h1 className="text-xl flex">Edit Group</h1>

            <button className="flex justify-end" onClick={onClose}>
              <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
            </button>
          </div>

          <div className="flex gap-2  ">
            <label className="flex flex-col w-full gap-2">
              Group Name:
              <input
                className="border w-full py-2 px-4 rounded-md"
                type="text"
                required
                value={selectedGroupName ? selectedGroupName : groupData?.group_name}
                onChange={(e) => setselectedGroupName(e.target.value)}
              />
              {errors.groupName && (
                <p className="text-red-500 text-xs italic">
                  {errors.groupName}
                </p>
              )}
            </label>

            <label className="flex flex-col w-full gap-2">
              Group Admin:
              <input
                className="border w-full py-2 px-4 rounded-md"
                type="text"

                required
                value={selectedGroupAdmin ? selectedGroupAdmin : admin.user_full_name}
                disabled
                onChange={(e) => setSelectedGroupAdmin(e.target.value)}
              />
              {errors.groupAdmin && (
                <p className="text-red-500 text-xs italic">
                  {errors.groupAdmin}
                </p>
              )}
            </label>
          </div>
          <div><label className="flex flex-col gap-2 w-full">
            Program Details:
            <textarea
              className="border w-full py-2 px-4 rounded-md"

              rows={2}
              cols={50}
              value={selectedGroupFAQ ? selectedGroupFAQ : groupData?.group_faq}
              onChange={(e) => setselectedGroupFAQ(e.target.value)}


            />
            {errors.groupFAQ && (
              <p className="text-red-500 text-xs italic">
                {errors.groupFAQ}
              </p>
            )}
          </label></div>
          <div className="flex justify-between  gap-2">
            <SearchBar onSearch={handleSearch} />
            <div className="flex justify-end items-end">
              <input
                type="checkbox"
                className="sr-only peer "
                checked={selectedisActiveState}
                onChange={() => handleChangeActiveState()}
              />
              <div className={`relative w-11 h-6 bg-gray-200 ${selectedisActiveState ? 'peer-checked:bg-blue-600' : ''} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600`}
              ></div>
              <span className="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Is active</span>
            </div>
          </div>

          <div className="w-full rounded-lg h-[400px] overflow-y-auto border shadow-md">
            <table className="w-full h-fit">
              <thead className="bg-secondary text-white text-[12px] sticky top-0 uppercase z-10 ">
                <tr className="h-fit">
                  <th scope="col" className="px-4 py-1">
                    UserID
                  </th>
                  <th scope="col" className="px-4 py-1 ">
                    Name
                  </th>
                  <th scope="col" className="px-4 py-1">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="relative">
                {userFilteredValues?.map((userValue: User_ext) => (
                  <tr
                    key={userValue.user_id}
                    className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 h-fit"
                  >
                    <th
                      scope="row"
                      className="px-4 py-1 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                    >
                      {userValue.user_id}
                    </th>
                    <td className="px-4 py-1 text-center">{userValue.user_full_name}</td>
                    <td className="px-4 py-1 text-center">
                      <input
                        type="radio"
                        id={userValue.user_id.toString()}
                        checked={selectedUserId === userValue.user_id}
                        onChange={(e) => {

                          handleChangeUserDetails(userValue.user_id, userValue.user_full_name,)
                        }}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="flex justify-center">
            <button
              className="py-2 px-4 bg-textColor text-white rounded-md"
              type="submit" // Add type="button" to prevent form submission

            >
              {submitState}
            </button>
          </div>
        </form>
      </div>
    );
  }


};

export default GroupModal;