import React, { useState, useEffect, useRef } from "react";
import BulkQuesModal from "../BulkQuesModal";
import Link from "next/link";
import {
  PencilSquareIcon,
  UserPlusIcon,
  DocumentTextIcon,
  AdjustmentsVerticalIcon,
  FolderIcon,
  CloudArrowDownIcon,
} from "@heroicons/react/24/outline";
import DeletedUserModal from "./DeletedUserModal";
import ResetPasswordModal from "./EditUserModal";
import GroupModal from "./GroupModal";
import { useQueryClient } from "@tanstack/react-query";
import { useGetUsersInGroup } from "@/hook/admin/group/useGetUsersInGroup";
import Nothing from "@/components/ui/Nothing";
import { useGetAllUserExt } from "@/hook/admin/useGetAllUserExt";
import { getUsersInGroup, getUsersInGroupDownload } from '@/api/admin/group/getUsersInGroup';
import { useCount } from "@/context/searchStore";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import GroupAssignmentFlow from "@/components/admin/userGroup/GroupAssignmentFlow";

interface UserGroupTableProps {
  userValues: Array<{
    group_id: number;
    group_admin_user_id: number;
    group_name: string;
    isactive: number; 
    created_by: string;
    total_member: string;
  }>;
}

const defaultType = "editGroup";

function getUserFullNameById(data, userId) {
  const user = data?.find((item) => item.user_id === userId);
  return user ? user.user_full_name : null;
}
// Place this above your component definition
const mockGroups = [
  {
    group_id: 1,
    group_admin_user_id: 101,
    group_name: "Frontend Team",
    isactive: 1,
    created_by: "Admin",
    total_member: "4",
  },
  {
    group_id: 2,
    group_admin_user_id: 102,
    group_name: "Backend Team",
    isactive: 1,
    created_by: "Admin",
    total_member: "3",
  },
  {
    group_id: 3,
    group_admin_user_id: 103,
    group_name: "QA Team",
    isactive: 0,
    created_by: "Admin",
    total_member: "2",
  },
];
export default function UserGroupTable() {
//export default function UserGroupTable({ userValues }: UserGroupTableProps) {
  const [isPasswordModal, setIsPasswordModal] = useState(false);
  const [isDeletedUserModalOpen, setIsDeletedUserModalOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [groupId, setgroupId] = useState(Number);
  const { data: allUserExtData } = useGetAllUserExt();
  const [popup1, setPopup1] = useState<boolean>(false)
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
 // const totalPages = Math.ceil(userValues.length / itemsPerPage);
  const totalPages = 1;
  const groupIdRef = useRef<number>(0);
  const userInGroupRefBool = useRef<boolean>(false);
  const { searchValue, searchNumber } = useCount();
  const [showAssignmentFlow, setShowAssignmentFlow] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);

  const handleOpenAssignmentFlow = (groupId: number) => {
  setSelectedGroupId(groupId);
  setShowAssignmentFlow(true);
  };

const handleCloseAssignmentFlow = () => {
    setShowAssignmentFlow(false);
    setSelectedGroupId(null);
 };

  let temp2:boolean = false 

useEffect(() => {
  temp2 = useCount.getState().searchValue
  console.log("search numberss:- ", temp2)
  console.log("search numberss page:- ", currentPage)
  if(temp2==true && currentPage != 1){
    setCurrentPage(1)
  }
}, [searchNumber])


  const { data: groupData } = useGetUsersInGroup(groupIdRef.current);

  console.log("groupData ispppp", groupData)
  console.log("groupIdRef", groupIdRef.current)


  const handleCloseDeleteModal = () => {
    setIsPasswordModal(false);
  };

  useEffect(() => {
    console.log("groupIdRef 2test", groupData)
  }, [groupIdRef.current])



  // const handleOpenDeletedUserModal = () => {
  //   setIsDeletedUserModalOpen(true);
  //   setIsPasswordModal(false);
  // };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    //queryClient.invalidateQueries() //Invalidate every query in cache
  };

  function convertToCSV(jsonData: any) {
    var csv = "";
    var keys = Object.keys(jsonData[0]);

    // Append header
    csv += keys.join(",") + "\n";

    // Append rows
    jsonData.forEach(function (row: any) {
      csv +=
        keys
          .map(function (key) {
            return row[key];
          })
          .join(",") + "\n";
    });
    console.log("Homelander:- ", jsonData)
    console.log("Homelander2:- ", csv)
    return csv;
  }

  // Download CSV file
  function downloadCSV(csvData: any, fileName: string) {
    var blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });

    if (navigator.msSaveBlob) {
      // For IE
      navigator.msSaveBlob(blob, fileName);
    } else {
      var link = document.createElement("a");
      if (link.download !== undefined) {
        var url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", fileName);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  }

  const handleEditClick = (group_id: number) => {


    setgroupId(group_id);
    groupIdRef.current = group_id
    setIsModalOpen(true);
  };

  const handleDownloadUserInGroupData = async (group_id: any) => {
    userInGroupRefBool.current = true
    console.log("Download...")
    console.log("groupIdRef1", groupIdRef)

    setgroupId(group_id);

    const userGroupData = await getUsersInGroupDownload(groupIdRef.current)

    console.log("temptest:- ", userGroupData)
    try {
      if (userGroupData.length > 0) {
        var csvData = convertToCSV(userGroupData);
        console.log("csvData: ", csvData)
        // Download CSV file
        downloadCSV(csvData, 'data.csv')
      } else {
        console.log("starlight")
        setPopup1(true)
        setTimeout(() => {
          setPopup1(false)
        }, 2000);

      }
    } catch (e) {
      console.log("exception occured", e);
    } finally {
      //no code to execute here
    }
  };
 // console.log(userValues);

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  // Instead of:
// const currentItems = userValues.slice(indexOfFirstItem, indexOfLastItem);
const currentItems = mockGroups.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="flex flex-col bg-white gap-2 justify-start items-start w-full rounded-md h-full  border shadow-md">
    {/* Scrollable table containe r for medium to large screens */}
    <div className="overflow-auto w-full h-full  rounded-md">
        <table className="min-w-full h-full">
          <thead className="bg-secondary text-white text-sm uppercase sticky top-0 z-10 ">
          <tr className="w-full">
              <th className="p-2 px-3 text-left">Group name</th>
              <th className="p-2 text-left">Group admin</th>
              <th className="p-2 text-left">Is Active</th>
              <th className="p-2 text-center">Add/Remove Assessment</th>
              <th className="p-2 text-center">ADD /Remove user</th>
              <th className="p-2 text-center">ADD /Remove Module</th>
              {/* <th className="p-2 text-center">ADD /Remove Content</th> */}
              <th className="p-2 text-center">Group User Data Download</th>
              <th className=" p-2 text-center">Edit</th>
            </tr>
          </thead>

          <tbody className="h-full">
            {Array.isArray(currentItems) && currentItems.length > 0 ? (
              currentItems.map((userValue) => (
                <tr key={userValue.group_id} className="border-b h-10">
                  <td className="p-2 px-5 text-left">{userValue.group_name}</td>
                  <td className="p-2  text-left">
                    {getUserFullNameById(
                      allUserExtData,
                      userValue.group_admin_user_id
                    )}
                  </td>
                  <td className="p-2  text-left">
                    {userValue.isactive === 1 ? "True" : "False"}
                  </td>
                  <td className="p-2 text-center">
                    <Link
                      className=""
                      href={{
                        pathname: "/admin/usergroup/assessments",
                        query: {
                          groupId: userValue.group_id,
                        },
                      }}
                    >
                      <button>
                        <DocumentTextIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-secondary" />
                      </button>
                    </Link>
                  </td>
                  <td className=" p-2  text-center">
                    <Link
                      href={{
                        pathname: "usergroup/alluser",
                        query: {
                          groupId: userValue.group_id,
                          groupName: userValue.group_name,
                        },
                      }}
                    >
                      <button>
                        <UserPlusIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-secondary " />
                      </button>
                    </Link>
                  </td>
                  <td className="p-2   text-center">
                    <Link
                      href={{
                        pathname: "/admin/usergroup/addremovemodule",
                        query: { groupId: userValue.group_id },
                      }}
                    >
                      <button>
                        <AdjustmentsVerticalIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-secondary" />
                      </button>
                    </Link>
                  </td>
                  {/* <td className="p-2   text-center">
                    <Link
                      href={{
                        pathname: "/admin/usergroup/addremovecontent",
                        query: {
                          groupId: userValue.group_id,
                        },
                      }}
                    >
                      <button>
                        <FolderIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-secondary" />
                      </button>
                    </Link>
                  </td> */}
                  <td className=" p-2 text-center">
                    <button onClick={() => {
                      groupIdRef.current = userValue.group_id;
                      handleDownloadUserInGroupData(userValue.group_id);

                    }}
                    >
                      <CloudArrowDownIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-secondary " />
                    </button>
                  </td>
                  <td className="p-2  ">
                    {/* <PencilSquareIcon
                      className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-secondary"
                      onClick={() => handleEditClick(userValue.group_id)}
                    /> */}
                    <button onClick={() => handleOpenAssignmentFlow(userValue.group_id)}>
          Manage Roles & Users
        </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={9} className="text-center p-4">
                  <Nothing
                    title="No Group Available"
                    para="There are currently no group to display.
            Please check back later,or create group."
                  />
                </td>
              </tr>
            )}

            {/* Empty rows to maintain the table's appearance for fewer items */}
            {currentItems.length < itemsPerPage &&
              [...Array(itemsPerPage - currentItems.length)].map((_, index) => (
                <tr key={`empty-${index}`} className=" h-10 ">
                  <td colSpan={9}></td>
                </tr>
              ))}
          </tbody>
        </table>

      </div>
      <div className="flex justify-between  items-end w-full ">
        <button
          onClick={handlePreviousPage}
          disabled={currentPage <= 1}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] "
        >
          Previous
        </button>
        <button
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Next
        </button>
      
      </div>
      {isPasswordModal && (
        <ResetPasswordModal onClose={handleCloseDeleteModal} />
      )}
      {isDeletedUserModalOpen && (
        <DeletedUserModal onClose={() => setIsDeletedUserModalOpen(false)} />
      )}
      {isModalOpen && (
        <GroupModal
          onClose={() => {
            handleCloseModal();
          }}
          groupID={groupId}
          type={defaultType}
        />
      )}{
        popup1 && (
          <BulkQuesModal
            modalName={"Error"}
            modalText={"Unable to download User Group Data"}
            onClose={() => setPopup1(false)} />
        )
      }
      <Modal open={showAssignmentFlow} onClose={handleCloseAssignmentFlow}>
      {selectedGroupId && (
        <GroupAssignmentFlow
      groupId={selectedGroupId}
      onClose={handleCloseAssignmentFlow}
    />
  )}
      </Modal>
    </div>
  );
}
