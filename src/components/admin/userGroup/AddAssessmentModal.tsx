import React, { useState, useEffect, useRef } from "react";
import DateTimePicker from 'react-datetime-picker';
import 'react-datetime-picker/dist/DateTimePicker.css';
import 'react-datetime-picker/dist/DateTimePicker.css';
import 'react-calendar/dist/Calendar.css';
import 'react-clock/dist/Clock.css';
import { AddAssessmentToGroupInput } from "@/types/LMSTypes";
import { useAddAssessmentToGroup } from "@/hook/admin/assessments/useAddAssessmentToGroup";
import { useQueryClient } from "@tanstack/react-query";

type propType = {
    open: boolean;
    onClose: () => void;
    group_Id: number;
    assessment_Id: number;
    assessmentName: string;
}

type ValuePiece = Date | null;
type Value = ValuePiece | [ValuePiece, ValuePiece];

const AddAssessmentModal: React.FC<propType> = ({ dataRemover, open, onClose, group_Id, assessment_Id, assessmentName }) => {
    const [startDate, setStartDate] = useState<string | null>(null); // Initialize with null
    const [endDate, setEndDate] = useState<string | null>(null); // Initialize with null
    const [maxAttempts, setMaxAttempts] = useState<number | null>(null)
    const [error, setError] = useState(false)
    const [error2, setError2] = useState(false)
    const queryClient = useQueryClient();
    console.log("group ID:", group_Id)
    console.log("assessment_Id:", assessment_Id)
    console.log("Assessment Name inside component:", assessmentName)

    

    const setValuesToDefault = () => {
        console.log("Executing")
        setStartDate("");
        setEndDate("");
        setMaxAttempts(0);
        setError2(false);
        setError(false)
    }

    useEffect(() => {
        if (true) {
          // Call your function here
          setValuesToDefault();
        }
      }, [onClose]);

    const newDetail: AddAssessmentToGroupInput = {
        group_id: group_Id,
        assessment_id: assessment_Id,
        is_active: 1,
        start_date: startDate,
        end_date: endDate,
        max_attempts: maxAttempts
    }

    const addingAssessmentToGroup = useAddAssessmentToGroup(newDetail)

    const handleSubmit = async () => {

        if (addingAssessmentToGroup) {
            try {
                await addingAssessmentToGroup.mutate();
                queryClient.invalidateQueries({ queryKey: ['getAssessmentsInGroup',groupId, search] })
            } catch (error) {
                console.error("Error while submitting:", error);
            }
        
        } else {
            console.log("Else block");
        }
    }

    return (
        <div className={`fixed inset-0 z-50 flex justify-center items-center transition-color ${open ? "visible bg-black/50" : "invisible"}`}
            onClick={() => {
                onClose();
                setValuesToDefault();
            }}
        >
            <div className={`bg-white flex flex-col justify-center rounded-lg w-10/12 sm:w-6/12 h-[40%] lg:h-auto shadow p-4 transition-all max-w-md ${open ? "scale-100 opacity-100" : "scale-110 opacity-0"}`}
                style={{ top: "50px" }} // Adjust top position here
                onClick={(e) => e.stopPropagation()}
            >
                <button className="absolute top-2 right-2 py-1 px-2 border border-neutral-200 rounded-md text-gray-400 bg-white hover:bg-gray-50 hover:text-gray-600"
                    onClick={() => {
                        onClose();
                        setValuesToDefault();
                    }}>X</button>
                <p className="text-slate-500 text-xs">Assessment Name</p>
                <p className="text-2xl pb-4">
                    {assessmentName}
                </p>

                <div className="flex flex-col gap-5 sm:gap-5">
                    <div className="flex flex-col gap-5">
                        <div className="flex flex-col">
                            <label className='' htmlFor="startDate">Start Date</label>
                            <DateTimePicker
                                id="startDate"
                                onChange={(date: Date | null) => {
                                    let tempDateString = date ?
                                        `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}` :
                                        '';
                                    setStartDate(tempDateString);
                                }}
                                value={startDate}
                            />
                        </div>
                        <div className="flex flex-col">
                            <label className='' htmlFor="endDate">End Date</label>
                            <DateTimePicker
                                id="endDate"
                                onChange={(date: Date | null) => {
                                    let tempDateString = date ?
                                        `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}` :
                                        '';
                                    setEndDate(tempDateString);
                                }}
                                value={endDate}
                            />
                        </div>
                    </div>
                    <div className=" ">
                        <label htmlFor="default-input" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Max Attempts</label>
                        <input
                            type="number"
                            id="default-input"
                            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            onChange={(e) => {
                                const value = parseInt(e.target.value, 10);
                                if (value >= 1) {
                                    setError(false)
                                    setMaxAttempts(parseInt(e.target.value, 10))
                                } else {
                                    setError(true)
                                }
                            }}
                            value={maxAttempts}
                        />
                        {error && <div className="text-xs text-red-500">Attemp cannot be zero</div>}
                    </div>
                    <div className="flex flex-col justify-center">
                        <button
                            type="button"
                            className="flex justify-center gap-2 py-2 px-6 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
                            onClick={() => {
                                if (startDate == null || endDate == null || maxAttempts == null || maxAttempts == 0) {
                                    setError2(true);
                                } else {
                                    setError2(false);
                                    handleSubmit();
                                    onClose();
                                    
                                }

                            }}

                        >
                            Add Assessment
                        </button>
                        {error2 && <div className="flex justify-center text-xs text-red-500 pt-2 pl-4">Please fill all fields correctly</div>}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default AddAssessmentModal;
