"use client";
import React, { useEffect, useState } from "react";
import Heading from "@/components/ui/Heading";
import Nothing from "@/components/ui/Nothing";

import { MinusIcon, PlusIcon, TrashIcon } from "@heroicons/react/24/outline";
import { useAddContentToGroup } from "@/hook/admin/usergroup/content/useAddContentToGroup";
import { AddRemoveContentGroup } from "@/types/LMSTypes";
import { useRemoveContentFromGroup } from "@/hook/admin/usergroup/content/useRemoveContentFromGroup";
import SearchBar from "../SearchBar";
import { useGetSearchFromGroupContentTable } from "@/hook/admin/usergroup/content/useGetSearchFromGroupContentTable";
import { useGetSearchFromAllContentInTable } from "@/hook/admin/usergroup/content/usegetSearchFormContentInGroup";


export default function AddRemoveContentForGroup({ type, dataforAll, groupId }: any) {
  const [search, setsearch] = useState("");
  const [groupIdValue, setGroupIdValue] = useState(groupId)
  const [contentIdValue, setcontentIdValue] = useState(Number)
  const [search2, setsearch2] = useState("");


  const newData: AddRemoveContentGroup = {
    content_id: contentIdValue,
    group_id: groupIdValue
  }

  const { data: SearchFromGroupContent } = useGetSearchFromGroupContentTable(groupIdValue, search);

  const { data: SearchFromAllContent } = useGetSearchFromAllContentInTable(groupIdValue, search2);



  const addingContentToGroup = useAddContentToGroup(newData);
  const removeContentToGroup = useRemoveContentFromGroup(contentIdValue, groupIdValue);

  const handleClickUser = async (type: string) => {

    if (type === "add") {
      try {
        await addingContentToGroup.mutate();

        setTimeout(() => {
        }, 1000);

      } catch (error) {
        console.error("Error adding user in group:", error);
      }
    } else if (type === "remove") {
      try {
        await removeContentToGroup.mutate();
        setTimeout(() => {
        }, 1000);
      } catch (error) {
        console.error("Error mutating user in group:", error);
      }
    }
  }



  // Utility function to ensure the input is an array.
  function ensureArray(input) {
    return Array.isArray(input) ? input : [];
  }

  // Usage in your component.
  const contentToMap = ensureArray(search2.trim() === '' ? dataforAll : SearchFromAllContent)

  if (type == "remove") {
    return (
      <div className=" flex flex-col w-auto h-full  p-2 ">

        <div className=" flex justify-between mx-1 md:mx-4">
          <Heading pgHeading="Group Contents" />
          <SearchBar onSearch={setsearch} />
        </div>
        <div className="overflow-y-auto  bg-white w-full  h-full rounded-lg border" >
          <table className=" rounded-xl items-start w-full h-fit">
            <thead className=" bg-secondary  text-white text-sm sticky top-0 uppercase w-full z-10  ">
              <tr className="w-full h-fit">
                <th className="p-2 text-left">Content Id</th>
                <th className="p-2 text-left">Content Name</th>
                <th className="p-2 text-left">Description</th>
                <th className="p-2 text-left">Topics</th>
                <th className="p-2 text-left">File Path</th>

                <th className="p-2 text-left">Actions</th>
              </tr>
            </thead>
            <tbody className="relative  overflow-x-auto">
              {Array.isArray(SearchFromGroupContent) && SearchFromGroupContent.length > 0 ? (
                SearchFromGroupContent.map((item) => (

                  <tr
                    key={item.content_id}
                    className="    border-b  h-fit"
                  >
                    <td className="  p-2  px-3 text-left  ">
                      {item.content_id}
                    </td>
                    <td className="  p-2  text-left  ">{item.content_name}</td>
                    <td className="  p-2  text-left ">
                      {item.content_description}
                    </td>
                    <td className="  p-2  text-left ">{item.topics}</td>
                    <td className=" p-2  text-left ">{item.file_path}</td>

                    <td className="p-2  text-center ">
                      <button
                        onClick={() => {
                          handleClickUser("remove");
                          setcontentIdValue(item.content_id);
                        }}
                      >
                        <MinusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} className="text-center p-4">
                    <Nothing
                      title="No Content Available"
                      para="There are currently no content to display.
                        Please check back later or add content."
                    />
                  </td>
                </tr>
              )}


            </tbody>
          </table>{" "}
        </div>
      </div>
    );
  } else if (type == "all") {
    return (
      <div className=" flex flex-col w-auto h-full  p-2 ">
        <div className=" flex justify-between mx-1 md:mx-4">
          <Heading pgHeading="All Existing Content" />
          <SearchBar onSearch={setsearch2} />
        </div>

        <div className="overflow-y-auto  w-full  h-full bg-white  rounded-lg  border">
          <table className=" rounded-xl items-start w-full h-fit">
            <thead className=" bg-secondary  text-white text-sm sticky top-0 uppercase w-full z-10 ">
              <tr className="w-full h-fit">
                <th className="p-2 text-left">Content Id</th>
                <th className="p-2 text-left">Content Name</th>
                <th className="p-2 text-left">Description</th>
                <th className="p-2 text-left">Topics</th>
                <th className="p-2 text-left">File Path</th>

                <th className="p-2 text-left">Actions</th>
              </tr>
            </thead>
            <tbody className="relative  overflow-x-auto">

              {contentToMap.length > 0 ? (
                contentToMap.map((item, index) => (
                  <tr
                    key={index}
                    className="  w-full  h-full border-b align-top "
                  >
                    <td className="  p-2  px-3 text-left  ">
                      {item.content_id}
                    </td>
                    <td className="  p-2  text-left  ">{item.content_name}</td>
                    <td className="  p-2  text-left ">
                      {item.content_description}
                    </td>
                    <td className="  p-2  text-left ">{item.topics}</td>
                    <td className=" p-2  text-left ">{item.file_path}</td>


                    <td className="p-2  text-center ">
                      <button
                        onClick={() => {
                          handleClickUser("add");
                          setcontentIdValue(item.content_id);
                        }}
                      >
                        <PlusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="8" className="text-center p-4">
                    <Nothing
                      title="No Content Available"
                      para="There are currently no content to display.
                        Please check back later or add content."
                    />
                  </td>
                </tr>
              )}
            </tbody>
          </table>{" "}
        </div>
      </div>
    );
  } else {
    return (
      <>
        <h1>Wrong type</h1>
      </>
    );
  }
}
