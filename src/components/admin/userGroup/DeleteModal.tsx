"use client";
import React from "react";
import { TrashIcon, XMarkIcon } from "@heroicons/react/24/outline";

interface DeleteModalProps {
  deleteName:string;
  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  onOpen: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}

export default function DeleteModal({ deleteName ,onClose, onOpen }: DeleteModalProps) {
  return (
    <div className="modal flex justify-center items-center  fixed inset-0 bg-black bg-opacity-40  overflow-auto z-10">
      <div className="flex flex-col  gap-5 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md  w-[400px] h-[250px]">
        <button className="flex justify-end" onClick={onClose}>
          <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
        </button>
        <div className="flex flex-col justify-center items-center   gap-5">
          <TrashIcon className="h-[50px] w-[50px]  text-red-500" />{" "}
          <p>{`Do you want to remove the ${deleteName}?`}</p>
        </div>

        <div className="flex justify-between ">
          <button
            className="py-2 px-4  border border-textColor  text-textColor rounded-md"
            type="submit"
            onClick={onClose}
          >
            No
          </button>
          <button
            className="py-2 px-4  border border-textColor  text-textColor rounded-md"
            type="submit"
            onClick={onOpen}
          >
            Yes
          </button>
        </div>
      </div>
    </div>
  );
}
