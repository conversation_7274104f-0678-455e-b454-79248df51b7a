"use client"
import Heading from '@/components/ui/Heading';
import Nothing from '@/components/ui/Nothing';
import ExcelJS from 'exceljs';
import { useGetUserAttempt } from '@/hook/user/useGetUserAttempt';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

import React, { useEffect } from 'react';

export default function ShowUserAttemptsTable({ userId }: any) {

    const { data: userValues, isLoading, isError } = useGetUserAttempt(userId);

    useEffect(() => {
        if (!isLoading && !isError) {
            console.log('userValues:', userValues); // Log the entire userValues object
            console.log('userValues data:', userValues.data); // Log a specific field (if applicable)
        }
    }, [userValues, isLoading, isError]);

    const handleDownload = async () => {
        if (typeof userValues !== 'object' || userValues === null) {
            console.error("userValues is not an object", userValues);
            return;
        }
    
        // Convert the userValues object to an array
        const dataToExport = Object.entries(userValues).flatMap(([key, { assessment_info, attempts }]) =>
            attempts.map(attempt => ({
                'Assessment Name': assessment_info.assessment_name,
                'Total Marks': assessment_info.total_marks,
                'User ID': attempt.user_id,
                'User Assessment ID': attempt.user_assessment_id,
                'User Name': attempt.user_full_name,
                'Evaluation': attempt.attempt_evaluation,
                'Start Date': new Date(attempt.attempt_start_date).toLocaleDateString(),
                'End Date': new Date(attempt.attempt_end_date).toLocaleDateString(),
                'Start Time': new Date(attempt.attempt_start_date).toLocaleTimeString(),
                'End Time': new Date(attempt.attempt_end_date).toLocaleTimeString()
            }))
        );
    
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('User Attempts');
    
          // Add columns with specific widths
    worksheet.columns = Object.keys(dataToExport[0]).map(key => ({
        header: key,
        key,
        width: 22 // Set the width of the columns (adjust as needed)
    }));


    
        // Add rows
        dataToExport.forEach(data => worksheet.addRow(data));
    
        // Apply header styles
        const headerRow = worksheet.getRow(1);
        headerRow.eachCell(cell => {
            cell.font = {
                bold: true,
                size: 14,
               
                color: { argb: 'FF000000' } // Black text
            };
            cell.alignment = {
                horizontal: 'center'
            };
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFFFFF00' } // Yellow background
            };
        });
    
        // Apply data rows styles
        worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
            if (rowNumber === 1) return; // Skip header row
    
            row.eachCell(cell => {
                cell.font = {
                    size: 12,
                    color: { argb: 'FF000000' } // Black text
                };
                cell.alignment = {
                    horizontal: 'center'
                };
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFD3D3D3' } // Light gray background
                };
            });
        });
    
        // Save the file
        const buffer = await workbook.xlsx.writeBuffer();
        saveAs(new Blob([buffer], { type: "application/octet-stream" }), "UserAttempts.xlsx");
    };
    

    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (isError) {
        return <div>Error fetching assessments</div>;
    }

    if (Object.keys(userValues).length === 0) {
        return (
            <div className='flex justify-center items-center h-full'>
                <div className="text-center p-4">
                    <Nothing
                        title="No Data Available"
                        para="There are currently no data to display. Please check back later, or create data."
                    />
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col gap-4 justify-start items-start rounded-md w-full h-[100vh] flex-grow">
            <div className='flex justify-end items-end w-full'>
                <button
                    onClick={handleDownload}
                    className="flex justify-center items-center w-[150px] mx-1 py-2 px-4 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px] gap-2 h-[40px]"
                >
                    <ArrowDownTrayIcon className="w-[22px] h-[22px]" />
                    <h1>Download</h1>
                </button>
            </div>
            {Object.entries(userValues).map(([assessmentKey, { assessment_info, attempts }]) => (
                <div className='flex flex-col flex-grow justify-between w-full' key={assessmentKey}>
                    <div className='flex justify-between w-full bg-secondary text-white px-4 py-2 rounded-t-md'>
                        <h3>{`Assessment Name: ${assessment_info.assessment_name}`}</h3>
                        <h3>{`Total Marks: ${assessment_info.total_marks}`}</h3>
                    </div>
                    <div className="w-full rounded-md bg-white border shadow-md overflow-auto h-full">
                        {attempts.map((attempt, index) => (
                            <div key={index} className="flex flex-col md:flex-row border-[1px] rounded-lg m-2 border-secondary justify-center md:justify-between py-2 px-6">
                                <div className='flex flex-col justify-between'>
                                    <p><span className="text-gray-500">User ID:</span> {attempt.user_id}</p>
                                    <p><span className="text-gray-500">User Assessment ID:</span> {attempt.user_assessment_id}</p>
                                </div>
                                <div className='flex flex-col justify-between gap-1'>
                                    <p><span className="text-gray-500">User Name:</span> {attempt.user_full_name}</p>
                                    <p><span className="text-gray-500">Evaluation:</span> {attempt.attempt_evaluation}</p>
                                </div>
                                <div className='flex flex-col justify-between'>
                                    <p><span className="text-gray-500">Start Date:</span> {new Date(attempt.attempt_start_date).toLocaleDateString()}</p>
                                    <p><span className="text-gray-500">End Date:</span> {new Date(attempt.attempt_end_date).toLocaleDateString()}</p>
                                </div>
                                <div className='flex flex-col justify-between'>
                                    <p><span className="text-gray-500">Start Time:</span> {new Date(attempt.attempt_start_date).toLocaleTimeString()}</p>
                                    <p><span className="text-gray-500">End Time:</span> {new Date(attempt.attempt_end_date).toLocaleTimeString()}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );
}
