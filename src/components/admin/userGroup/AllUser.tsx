"use client";
import React, { useState, useRef } from "react";
import SearchBar from "../SearchBar";
import { PlusIcon, MinusIcon } from "@heroicons/react/24/outline";
import Heading from "@/components/ui/Heading";
import { useAddUserToGroup } from "@/hook/admin/group/useAddUserToGroup";
import { AddRemoveUserToGroup } from "@/types/LMSTypes";
import { useRemoveUserFromGroup } from "@/hook/admin/group/useRemoveUserFromGroup";
import { useGetNonUserSearch } from "@/hook/admin/usergroup/alluser/useGetNonUserSearch";
import { useGetUserSearch } from "@/hook/admin/usergroup/alluser/useGetUserSearch";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import Nothing from "@/components/ui/Nothing";

interface AllUserProps {
  type: string;
  groupId: number;
}

const AllUser = ({ type, groupId }: AllUserProps) => {
  const [search, setsearch] = useState("");
  const [groupIdValue, setGroupIdValue] = useState(groupId);
  const [userIdValue, setuserIdValue] = useState(Number);
  const [search2, setsearch2] = useState("");
  const refreshCount = useRef(0);

  const newDetail: AddRemoveUserToGroup[] = [{
    group_id: groupIdValue,
    user_id: userIdValue
  }]

  const { data: groupUserSearchData } = useGetUserSearch(groupId, search2)

  const { data: nonGroupUserSearchData } = useGetNonUserSearch(groupId, search)

  console.log("nonGroupUserSearchData:-", nonGroupUserSearchData)
  console.log("groupUserSearchData:-", groupUserSearchData)
  // console.log("search:-", search)
  // console.log("search2:-", search2)
  // console.log("Re-render:-")
  // console.log("useRef:-", refreshCount.current)
  const addingUserToGroup = useAddUserToGroup(newDetail);

  const removeUserToGroup = useRemoveUserFromGroup(newDetail);

  const handleClickUser = async (type: string) => {
    if (type === "add") {
      try {
        await addingUserToGroup.mutate();
        setTimeout(() => { }, 1000);
        console.log("printing search", search);
      } catch (error) {
        console.error("Error adding user in group:", error);
      }
    } else if (type === "remove") {
      try {
        await removeUserToGroup.mutate();
        setTimeout(() => { }, 1000);
      } catch (error) {
        console.error("Error mutating user in group:", error);
      }
    }
  };

  if (type == "remove") {
    //the table where all the users are listed and we wanted to remove them
    return (
      <div className=" flex flex-col w-auto h-full  p-2 gap-1">
        <div className=" flex justify-between mx-1 md:mx-4">
          <Heading pgHeading="All Existing User" />
          <SearchBar onSearch={setsearch} />
        </div>
        <div className="overflow-y-auto  w-full  h-full bg-white  rounded-lg  border">
           <table className=" rounded-xl items-start w-full ">
            <thead className=" bg-secondary  text-white text-sm sticky top-0 uppercase w-full z-10 h-fit">
              <tr className="w-full h-fit">
                <th className="p-2 text-left">User ID</th>
                <th className="p-2 text-left">Name</th>
                <th className="p-2 text-left">Phone No</th>
                <th className="p-2 text-left">Email</th>
                <th className="p-2 text-center">Actions</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto">
  {nonGroupUserSearchData && nonGroupUserSearchData.length > 0 ? (
    nonGroupUserSearchData.map((userValue, index) => (
      <tr key={index} className="w-full border-b align-top h-fit">
        <td className="p-2 px-3 text-left">{userValue.user_id}</td>
        <td className="p-2 text-left">{userValue.user_name}</td>
        <td className="p-2 text-left">{userValue.phone}</td>
        <td className="p-2 text-left">{userValue.email}</td>
        <td className="p-2 text-center">
          <button
            onClick={() => {
              refreshCount.current += 1;
              handleClickUser("add");
              setuserIdValue(userValue.user_id);
            }}
            className="p-1 bg-secondary text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring focus:border-blue-300"
          >
            <PlusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
          </button>
        </td>
      </tr>
    ))
  ) : (
 
    <tr>
    <td colSpan={5} className="text-center p-4">
      <Nothing 
        title="No User Available"
        para="There are currently no users to display. Please check back later or consider creating new users."
      />
    </td>
  </tr>
  
    
  )}
</tbody>
          </table> 

        </div>
      </div>
    );
  } else if (type == "all") {
    return (
      <div className=" flex flex-col w-auto h-full  p-2 gap-1">
        <div className=" flex justify-between mx-1 md:mx-4">
          <Heading pgHeading="Group User" />
          <SearchBar onSearch={setsearch2} />
        </div>
        <div className="overflow-y-auto  w-full  h-full bg-white  rounded-lg  border">
        <table className=" rounded-xl items-start w-full ">
            <thead className=" bg-secondary  text-white text-sm sticky top-0 z-10 uppercase w-full  ">
              <tr className="w-full">
                <td className="p-2 text-left">User ID</td>
                <th className="p-2 text-left">Name</th>
                <th className="p-2 text-left">Phone No</th>
                <th className="p-2 text-left">Email</th>
                <th className="p-2 text-center">Actions</th>
              </tr>
            </thead>
            <tbody className="relative  overflow-x-auto">
            {groupUserSearchData && groupUserSearchData.length > 0 ? (
              groupUserSearchData?.map((userValue, index) => (
                <tr key={index} className="  w-full   border-b align-top ">
                  <td className="p-2 px-3 text-left">{userValue.user_id}</td>
                  <td className="p-2 text-left">{userValue.user_name}</td>
                  <td className="p-2 text-left">{userValue.phone}</td>
                  <td className="p-2 text-left">{userValue.email}</td>
                  <td className="p-2 text-center">
                    <button
                      onClick={() => {
                        refreshCount.current = refreshCount.current - 1;
                        handleClickUser("remove");
                        setuserIdValue(userValue.user_id);
                      }}
                      className="p-1 bg-secondary text-white rounded-full hover:bg-blue-600 focus:outline-none focus:ring focus:border-blue-300"
                    >
                      <MinusIcon className="text-white bg-secondary rounded-full md:w-[20px] md:h-[20px] w-[13px] h-[13px]" />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
           
              <tr>
              <td colSpan={5} className="text-center p-4">
                <Nothing 
                  title="No User Available"
                  para="There are currently no users to display. Please check back later or consider creating new users."
                />
              </td>
            </tr>)}
            </tbody>
          </table> 

        </div>
      </div>
    );
  } else {
    return (
      <>
        <h1>Wrong type</h1>
      </>
    );
  }
};

export default AllUser;
