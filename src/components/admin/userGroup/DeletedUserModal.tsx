"use client";
import React from "react";
import { XMarkIcon, TrashIcon } from "@heroicons/react/24/outline";

interface DeletedUserModalProps {
  deleteName:string;
  onClose: (event?: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
}

export default function DeletedUserModal({ deleteName, onClose }: DeletedUserModalProps) {
  return (
    <div className="modal flex justify-center items-center  fixed inset-0 bg-black bg-opacity-40  overflow-auto z-10">
      <div className="flex flex-col gap-5 modal-content bg-white m-auto p-5 border border-gray-300 rounded-md w-[400px] h-[250px] ">
        <button className="flex justify-end" onClick={onClose}>
          <XMarkIcon className="h-[22px] w-[22px] text-secondary" />
        </button>{" "}
        <div className="flex flex-col gap-5 justify-center items-center ">
          <TrashIcon className="h-[50px] w-[50px]  text-red-500" />
          <p> {`The ${deleteName} is deleted. `}</p>
        </div>
      </div>
    </div>
  );
}
