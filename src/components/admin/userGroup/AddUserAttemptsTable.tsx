"use client";
import React, { useState, useEffect } from "react";
import Nothing from "@/components/ui/Nothing";
import { DocumentTextIcon, ArrowDownTrayIcon } from "@heroicons/react/24/outline";
import ExcelJS from 'exceljs';
import { useGetUserAttempt } from '@/hook/user/useGetUserAttempt';
import { saveAs } from 'file-saver';
import { useCount } from "@/context/searchStore";

interface AddRemoveUserToGroupProps {
  userValuesTable: Array<{
    user_ext_id: number;
    creation_date: string;
    comment: string;
    user_id: number;
    user_full_name: string;
    created_by: string;
    user: {
      phone: string;
      email: string;
    };
    status?: string;
  }>;
}

interface UserStatuses {
  [userId: string]: boolean;
}

export default function AddUserAttemptsTable({ userValuesTable }: AddRemoveUserToGroupProps) {
  const [userStatuses, setUserStatuses] = useState<UserStatuses>({});
  const [screenCount, setScreenCount] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const totalPages = Math.ceil(userValuesTable?.length / itemsPerPage);
  const { searchValue, searchNumber } = useCount();

 
  let temp2:boolean = false 

useEffect(() => {
  temp2 = useCount.getState().searchValue
  console.log("search numberss:- ", temp2)
  console.log("search numberss page:- ", currentPage)
  if(temp2==true && currentPage != 1){
    setCurrentPage(1)
  }
}, [searchNumber])

  const [userAttemptsData, setUserAttemptsData] = useState([]);
  
  useEffect(() => {
    const statuses: UserStatuses = userValuesTable?.reduce((acc, user) => {
      acc[user.user_id] = user.status === "active";
      return acc;
    }, {});
    setUserStatuses(statuses);
  }, [userValuesTable]);


  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = userValuesTable?.slice(indexOfFirstItem, indexOfLastItem);

  const handleScreenChange = (userId) => {
    setScreenCount(2);
    loadUserAttempts(userId);
    console.log("userId",userId)
  };

  const [userId, setUserId] = useState<number | null>(null);
  const { data: userValues, isLoading, isError } = useGetUserAttempt(userId);

  useEffect(() => {
    if (!isLoading && !isError && userValues) {
      console.log('userValues:', userValues); // Log the entire userValues object
      setUserAttemptsData(userValues); // Store user attempts data when it's available
    }
  }, [userValues, isLoading, isError]);

  const loadUserAttempts = (userId) => {
    setUserId(userId);
  };

 
    const handleDownload = async () => {

      const dataToExport = [];
      // Check if we have a valid userId and userValues
  if (userId && userValues && typeof userValues === 'object' && userValues !== null) {
    // Find the selected user based on userId
    const selectedUser = userValuesTable.find(user => user.user_id === userId);
console.log("selectedUser",selectedUser)
    if (selectedUser) {
      // Flag to check if the user has any attempts
      let hasAttempts = false;
    

      Object.entries(userValues).forEach(([assessmentKey, { assessment_info, attempts }]) => {
        attempts.forEach(attempt => {
          if (attempt.user_id === selectedUser.user_id) { // Check if this attempt belongs to the selected user
            hasAttempts = true; // Set flag to true if there are attempts

            console.log("attempt.user_id", attempt.user_id)
            console.log("selectedUser.user_id", selectedUser.user_id)

            dataToExport.push({
              'User ID': selectedUser.user_ext_id,
              'Name': selectedUser.user_full_name,
              'Phone No': selectedUser.user.phone,
              'Email': selectedUser.user.email,
              'Assessment Name': assessment_info.assessment_name,
              'Total Marks': assessment_info.total_marks,
              'User Marks': assessment_info.total_marks,
              'User Assessment ID': attempt.user_assessment_id,
              'Attempt Total Time': attempt.attempt_total_time,
              
              'User Name': attempt.user_full_name,
              'Evaluation': attempt.attempt_evaluation,
              'Start Date': new Date(attempt.attempt_start_date).toLocaleDateString(),
              'End Date': new Date(attempt.attempt_end_date).toLocaleDateString(),
              'Start Time': new Date(attempt.attempt_start_date).toLocaleTimeString(),
              'End Time': new Date(attempt.attempt_end_date).toLocaleTimeString(),
            });
          }
        });
      });

      // Check if there are no attempts for the user
      if (!hasAttempts) {
        
        dataToExport.push({
          'User ID': selectedUser.user_ext_id,
          'Name': selectedUser.user_full_name,
          'Phone No': selectedUser.user.phone,
          'Email': selectedUser.user.email,
          'Assessment Name': 'No Assessments',
          'Total Marks': 'N/A',
          'User Marks': 'N/A',
          'User Assessment ID': 'N/A',
          'Attempt Total Time': 'N/A',
          'User Name': 'N/A',
          'Evaluation': 'N/A',
          'Start Date': 'N/A',
          'End Date': 'N/A',
          'Start Time': 'N/A',
          'End Time': 'N/A',
        });
      }
    }
  }
      // Create workbook and worksheet, and add columns
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('User Attempts');
    
      // Define columns
      worksheet.columns = [
        { header: 'User ID', key: 'User ID', width: 15 },
        { header: 'Name', key: 'Name', width: 25 },
        { header: 'Phone No', key: 'Phone No', width: 15 },
        { header: 'Email', key: 'Email', width: 25 },
        { header: 'Assessment Name', key: 'Assessment Name', width: 25 },
        { header: 'Total Marks', key: 'Total Marks', width: 10 },
        { header: 'User Marks', key: 'User Marks', width: 10 },
        { header: 'User Assessment ID', key: 'User Assessment ID', width: 16 },
        { header: 'Attempt Total Time', key: 'Attempt Total Time', width: 10 },
        { header: 'User Name', key: 'User Name', width: 25 },
        { header: 'Evaluation', key: 'Evaluation', width: 15 },
        { header: 'Start Date', key: 'Start Date', width: 15 },
        { header: 'End Date', key: 'End Date', width: 15 },
        { header: 'Start Time', key: 'Start Time', width: 15 },
        { header: 'End Time', key: 'End Time', width: 15 },
      ];


   // Add rows to the worksheet
  dataToExport.forEach(data => worksheet.addRow(data));
    console.log("Data to export:", dataToExport);

    // Apply header styles
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell(cell => {
      cell.font = {
        bold: true,
        size: 14,
        color: { argb: 'FF000000' } // Black text
      };
      cell.alignment = {
        horizontal: 'center'
      };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'ADD8E6' } // blue background
      };
    });
  
    // Apply data rows styles
    worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber === 1) return; // Skip header row
  
      row.eachCell(cell => {
        cell.font = {
          size: 12,
          // color: { argb: 'FF000000' } // Black text
        };
        cell.alignment = {
          horizontal: 'center'
        };
        // cell.fill = {
        //   // type: 'pattern',
        //   // pattern: 'solid',
        //   // fgColor: { argb: 'FFFFFF' } // white bg 
        // };
      });
    });
  
    // Save the file
    const buffer = await workbook.xlsx.writeBuffer();
    saveAs(new Blob([buffer], { type: "application/octet-stream" }), "UserAttempts.xlsx");
  };
  

  if (screenCount === 1) {
    return (
      <div className="flex flex-col gap-2 justify-start bg-white items-start rounded-md w-full h-full border shadow-md">
        <div className="overflow-auto w-full h-full rounded-md">
          <table className="min-w-full divide-y divide-gray-200 h-full">
            <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
              <tr className="w-full">
                <th className="p-2 text-left">User ID</th>
                <th className="p-2 text-left">Name</th>
                <th className="p-2 text-left">Phone No</th>
                <th className="p-2 text-left">Email</th>
                <th className="p-2 text-center">Assessment Attempts</th>
              </tr>
            </thead>
            <tbody className="h-full ">
              {Array.isArray(currentItems) && currentItems.length > 0 ? (
                currentItems.map((userValueTable) => {
                  const isChecked = userStatuses[userValueTable.user_id] || false;
                  return (
                    <tr
                      key={userValueTable.user_id}
                      className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-sm h-10"
                    >
                      <th
                        scope="row"
                        className="p-1 md:px-2 md:py-1 lg:px-6 lg:py-1 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                      >
                        {userValueTable.user_id}
                      </th>
                      <td className="p-2 text-left">{userValueTable.user_full_name}</td>
                      <td className="p-2 text-left">{userValueTable.user.phone}</td>
                      <td className="p-2 text-left">{userValueTable.user.email}</td>
                      <td className="p-2 text-center">
                        <button onClick={() => handleScreenChange(userValueTable.user_id)}>
                          <DocumentTextIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-secondary" />
                        </button>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan="7" className="text-center p-4">
                    <Nothing
                      title="No User"
                      para="There are currently no user to display. Please check back later, or add user."
                    />
                  </td>
                </tr>
              )}
              {/* Empty rows to maintain the table's appearance for fewer items */}
          {currentItems.length < itemsPerPage &&
            [...Array(itemsPerPage - currentItems.length)].map((_, index) => (
              <tr key={`empty-${index}`} className=" h-10">
                <td colSpan={5} className="b-0"></td>
              </tr>
            ))}
            </tbody>
          </table>
        </div>
        <div className="flex justify-between items-end w-full">
          <button
            onClick={handlePreviousPage}
            disabled={currentPage <= 1}
            className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
          >
            Previous
          </button>
          <button
            onClick={handleNextPage}
            disabled={currentPage >= totalPages}
            className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
          >
            Next
          </button>
        </div>
      </div>
    );
  }

  if (screenCount === 2 && userValues && Object.keys(userValues).length > 0) {
    return (
      <div className="flex flex-col gap-2 justify-start  w-full rounded-lg h-full border shadow-md overflow-hidden bg-white">
      {/* Scrollable table container for medium to large screens */}
      <div className="overflow-auto w-full h-full  rounded-md bg-white">
        <table className="min-w-full divide-y divide-gray-200 h-fit ">
          <thead className="bg-secondary text-white text-sm uppercase sticky top-0">
            <tr className="w-full"> 
              <th className="p-2 text-left">User ID</th>
              <th className="p-2 text-left">User Name</th>
              <th className="p-2 text-left">User Email</th>
              <th className="p-2 text-left">User Assessment ID</th>
                <th className="p-2 text-left">Assessment Name</th>
                <th className="p-2 text-left">Total Marks</th>
                <th className="p-2 text-left">Attempt Total Time</th>
                <th className="p-2 text-left">Evaluation</th>
                <th className="p-2 text-left">Start Date</th>
                <th className="p-2 text-left">End Date</th>
                <th className="p-2 text-left">Start Time</th>
                <th className="p-2 text-left">End Time</th>
              </tr>
            </thead>
            <tbody className="h-full ">
              {Object.entries(userValues).map(([assessmentKey, { assessment_info, attempts }]) => (
                attempts.map(attempt => (
                  <tr
                    key={attempt.user_assessment_id}
                    className="bg-white border-b  h-10"
                  >
                    <td className="p-2 text-left">{attempt.user_id}</td>
                    <td className="p-2 text-left">{attempt.user_full_name}</td>
                    <td className="p-2 text-left">{attempt.email}</td>
                    <td className="p-2 text-left">{attempt.user_assessment_id}</td>
                    <td className="p-2 text-left">{assessment_info.assessment_name}</td>
                    <td className="p-2 text-left">{assessment_info.total_marks}</td>
                    
                   <td className="p-2 text-left">{attempt.attempt_total_time}</td>
                   
                    <td className="p-2 text-left">{attempt.attempt_evaluation}</td>
                    <td className="p-2 text-left">{new Date(attempt.attempt_start_date).toLocaleDateString()}</td>
                    <td className="p-2 text-left">{new Date(attempt.attempt_end_date).toLocaleDateString()}</td>
                    <td className="p-2 text-left">{new Date(attempt.attempt_start_date).toLocaleTimeString()}</td>
                    <td className="p-2 text-left">{new Date(attempt.attempt_end_date).toLocaleTimeString()}</td>
                  </tr>
                ))
              ))}
            </tbody>
   
          </table>
        </div>
        <div className="flex justify-end w-full p-2">
        <button
          onClick={() => setScreenCount(1)}
          className="self-start py-2 px-4 m-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Back
        </button>
          <button
            onClick={handleDownload}
            className="flex gap-2 py-2 px-4 m-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
          >
            Download <ArrowDownTrayIcon className="w-[20px] h-[20px]" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className='flex justify-center items-center h-full'>
      <div className="text-center p-4">
        <Nothing
          title="No Data Available"
          para="There are currently no data to display. Please check back later, or create data."
        />
        <button
          onClick={() => setScreenCount(1)}
          className="self-start py-2 px-4 m-2 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Back
        </button>
      </div>
    </div>
  );
}
