import React, { useState } from "react";
import JobCard from "@/components/admin/roles/JobCard";
import Button from "@/components/ui/Button";
import Nothing from "@/components/ui/Nothing";

// --- Enhanced Mock Data ---
const mockJobRoles = [
  { 
    job_id: 1, 
    job_title: "Frontend Developer", 
    job_description: "UI/UX, React, CSS",
    sectors: ["Technology", "Development"],
    domains: ["Web Development", "User Interface"]
  },

  { 
    job_id: 3, 
    job_title: "DevOps Engineer", 
    job_description: "CI/CD, Cloud, Docker",
    sectors: ["Infrastructure", "Operations"],
    domains: ["Cloud Operations", "Deployment"]
  },
  { 
    job_id: 4, 
    job_title: "QA Tester", 
    job_description: "Automation, Manual Testing",
    sectors: ["Quality Assurance"],
    domains: ["Software Testing", "Test Automation"]
  },
  { 
    job_id: 5, 
    job_title: "DevOps Engineer", 
    job_description: "CI/CD, Cloud, Docker",
    sectors: ["Infrastructure", "Operations"],
    domains: ["Cloud Operations", "Deployment"]
  },
  { 
    job_id: 6, 
    job_title: "QA Tester", 
    job_description: "Automation, Manual Testing",
    sectors: ["Quality Assurance"],
    domains: ["Software Testing", "Test Automation"]
  },
  { 
    job_id: 2, 
    job_title: "Senior Backend Developer with Specialization in Database Architecture", 
    job_description: "Node.js, APIs, DB, SQL, NoSQL, Database Architecture and Optimization",
    sectors: ["Technology", "Infrastructure"],
    domains: ["Server Architecture", "Database Design"]
  },
];

const mockUsers = [
  { user_id: 1, user_full_name: "Alice Johnson", email: "<EMAIL>" },
  { user_id: 2, user_full_name: "Bob Smith", email: "<EMAIL>" },
  { user_id: 3, user_full_name: "Charlie Brown", email: "<EMAIL>" },
  { user_id: 4, user_full_name: "Diana Prince", email: "<EMAIL>" },
];

// --- Mock API Calls ---
const mockAssignRolesToGroup = async (groupId, roleIds) => {
  return new Promise((resolve) => setTimeout(() => resolve({ success: true }), 600));
};
const mockAddUsersToGroup = async (groupId, userIds) => {
  return new Promise((resolve) => setTimeout(() => resolve({ success: true }), 600));
};

const steps = ["Assign Job Roles", "Add Users"];

export default function GroupAssignmentFlow({ groupId, onClose }) {
  const [activeStep, setActiveStep] = useState(0);

  // Step 1: Job Roles
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [assigningRoles, setAssigningRoles] = useState(false);

  // Step 2: Users
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [addingUsers, setAddingUsers] = useState(false);

  // UI Feedback
  const [success, setSuccess] = useState(false);

  // Handlers
  const handleRoleSelect = (job_id) => {
    setSelectedRoles((prev) =>
      prev.includes(job_id) ? prev.filter((id) => id !== job_id) : [...prev, job_id]
    );
  };

  const handleAssignRoles = async () => {
    setAssigningRoles(true);
    await mockAssignRolesToGroup(groupId, selectedRoles);
    setAssigningRoles(false);
    setActiveStep(1);
  };

  const handleUserSelect = (user_id) => {
    setSelectedUsers((prev) =>
      prev.includes(user_id) ? prev.filter((id) => id !== user_id) : [...prev, user_id]
    );
  };

  const handleAddUsers = async () => {
    setAddingUsers(true);
    await mockAddUsersToGroup(groupId, selectedUsers);
    setAddingUsers(false);
    setSuccess(true);
    setTimeout(() => {
      setSuccess(false);
      onClose && onClose();
    }, 1200);
  };

  // Custom RoleCard component
const RoleCard = ({ role, selected, onClick }) => {
  const colorMap = {
    "Technology": "bg-blue-100",
    "Infrastructure": "bg-emerald-100",
    "Quality Assurance": "bg-amber-100",
    "Development": "bg-sky-100",
    "Operations": "bg-amber-100",
    "default": "bg-gray-100"
  };

  const firstSector = role.sectors && role.sectors.length > 0 ? role.sectors[0] : '';
  const headerColor = colorMap[firstSector] || colorMap.default;
  const jobSectors = role.sectors || [role.sector];
  const jobDomains = role.domains || [role.domain];

  return (
    <div
      className={`
        bg-white rounded-lg shadow-md transition-all duration-200 cursor-pointer flex flex-col
        ${selected ? 'ring-2 ring-blue-500' : 'hover:shadow-lg border border-gray-200'}
      `}
      onClick={onClick}
    >
      {/* Header with color and icon */}
      <div className={`${headerColor} w-full relative flex items-start px-3 pt-3 pb-2 rounded-t-lg`}>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700 mr-2 flex-shrink-0 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
        <div className="flex-1 flex flex-col min-w-0">
          <h3 className="text-base font-bold text-gray-800 break-words leading-snug" title={role.job_title}>
            {role.job_title}
          </h3>
        </div>
        {selected && (
          <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs font-medium px-2 py-0.5 rounded-full">
            ✓
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col px-3 pb-3 pt-1">
        <p className="text-gray-600 text-xs mb-2 whitespace-pre-line">
          {role.job_description}
        </p>
        <div className="space-y-2">
          {/* Sectors */}
          <div>
            <div className="text-xs text-gray-500 mb-1">Sectors:</div>
            <div className="flex flex-wrap gap-1">
              {jobSectors.map((sector, idx) => (
                <div key={idx} className="inline-flex items-center rounded-full bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700 border border-blue-100">
                  {sector}
                </div>
              ))}
            </div>
          </div>
          {/* Domains */}
          <div>
            <div className="text-xs text-gray-500 mb-1">Domains:</div>
            <div className="flex flex-wrap gap-1">
              {jobDomains.map((domain, idx) => (
                <div key={idx} className="inline-flex items-center rounded-full bg-indigo-50 px-2 py-0.5 text-xs font-medium text-indigo-700 border border-indigo-100">
                  {domain}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

  // --- Custom Stepper ---
  const Stepper = () => (
    <div className="mb-4">
      <div className="flex items-center justify-between">
        {steps.map((label, idx) => (
          <React.Fragment key={label}>
            {/* Step circle and label */}
            <div className="flex flex-col items-center relative">
              <div
                className={`
                  w-8 h-8 rounded-full flex items-center justify-center font-medium text-white
                  ${activeStep >= idx ? "bg-blue-600" : "bg-gray-300"}
                  transition-all duration-200
                `}
              >
                {idx + 1}
              </div>
              <span className={`mt-1 text-xs font-medium ${activeStep >= idx ? "text-blue-600" : "text-gray-500"}`}>
                {label}
              </span>
              
              {/* Small dot indicator below active step */}
              {activeStep === idx && (
                <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
                  <div className="w-1 h-1 rounded-full bg-blue-600"></div>
                </div>
              )}
            </div>
            
            {/* Connector line between steps */}
            {idx < steps.length - 1 && (
              <div className="w-full h-px flex-1 bg-gray-300 mx-3">
                <div 
                  className="h-full bg-blue-600 transition-all duration-300" 
                  style={{ width: activeStep > idx ? '100%' : '0%' }}
                ></div>
              </div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );

  return (
    <div className="w-full mx-auto bg-white rounded-lg">
      <div className="p-4">
        <h2 className="text-xl font-bold text-gray-800 mb-3" id="modal-title">
          {activeStep === 0 ? "Configure Group Job Roles" : "Add Users to Group"}
        </h2>
        
        <Stepper />

        {activeStep === 0 && (
          <>
            <div className="mb-3">
              <h3 className="text-base font-medium text-gray-700 mb-1">Select Job Roles for this Group</h3>
              <p className="text-gray-500 text-xs">Choose the roles that will be available to members of this group.</p>
            </div>
            
            <div className="flex flex-col">
  {/* Roles Grid */}
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 p-1 items-start">
    {mockJobRoles.map((role) => (
      <RoleCard
        key={role.job_id}
        role={role}
        selected={selectedRoles.includes(role.job_id)}
        onClick={() => handleRoleSelect(role.job_id)}
      />
    ))}
  </div>

  {/* Sticky Footer Buttons */}
  <div className="sticky bottom-0 bg-white pt-2 pb-2 border-t z-10 flex justify-end gap-2">
    <button
      type="button"
      onClick={onClose}
      className="py-1.5 px-3 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 text-xs"
    >
      Cancel
    </button>
    <button
      type="button"
      onClick={handleAssignRoles}
      disabled={selectedRoles.length === 0 || assigningRoles}
      className={`
        py-1.5 px-4 rounded-md text-white font-medium text-xs
        ${selectedRoles.length === 0 || assigningRoles 
          ? "bg-blue-300" 
          : "bg-blue-600 hover:bg-blue-700"
        }
        transition-colors duration-200
      `}
    >
      {assigningRoles ? "Assigning..." : "Continue"}
    </button>
  </div>
</div>
          </>
        )}

        {activeStep === 1 && (
          <>
            <div className="mb-3">
              <h3 className="text-base font-medium text-gray-700 mb-1">Add Users to this Group</h3>
              <p className="text-gray-500 text-xs">Select which users should be members of this group.</p>
            </div>
            
            {mockUsers.length === 0 ? (
              <div className="py-8">
                <Nothing text="No users available" />
              </div>
            ) : (
              <div className="border rounded-lg">
                <div className="max-h-[60vh] overflow-y-auto">
                  {mockUsers.map((user) => (
                    <label
                      key={user.user_id}
                      className={`
                        flex items-center py-2 px-3 border-b last:border-0 cursor-pointer
                        ${selectedUsers.includes(user.user_id) ? "bg-blue-50" : "hover:bg-gray-50"}
                        transition-colors duration-150
                      `}
                    >
                      <div className="flex-shrink-0 mr-3">
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(user.user_id)}
                          onChange={() => handleUserSelect(user.user_id)}
                          className="w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-800 text-sm">{user.user_full_name}</p>
                        <p className="text-xs text-gray-500">{user.email}</p>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            )}
            
            <div className="sticky bottom-0 bg-white pt-2 pb-2 border-t z-10">
              <div className="flex justify-between items-end">
                <button
                  type="button"
                  onClick={() => setActiveStep(0)}
                  className="py-1.5 px-3 text-gray-600 hover:text-gray-800 text-xs"
                >
                  ← Back to Roles
                </button>
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={onClose}
                    className="py-1.5 px-3 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 text-xs"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddUsers}
                    disabled={selectedUsers.length === 0 || addingUsers}
                    className={`
                      py-1.5 px-4 rounded-md text-white font-medium text-xs
            ${selectedUsers.length === 0 || addingUsers
                        ? "bg-blue-300"
                        : "bg-blue-600 hover:bg-blue-700"
                      }
            transition-colors duration-200
                   `}
                  >
                    {addingUsers ? "Adding..." : "Add Users"}
                  </button>
                </div>
              </div>
            </div>
            {success && (
              <div className="mt-3 p-2 bg-green-50 border border-green-200 text-green-700 rounded-md flex items-center text-xs">
                <svg className="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Users successfully added!
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}