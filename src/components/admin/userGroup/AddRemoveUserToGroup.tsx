"use client";
import React, { useState, useEffect } from "react";
import { toast } from 'react-toastify';

import DeletedUserModal from "./DeletedUserModal";
import AddUserModal from "@/components/admin/userGroup/AddUserModal";
import EditUserModal from "./EditUserModal";
import Nothing from "@/components/ui/Nothing";
import { 
  PencilSquareIcon, 
  TrashIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon
} from "@heroicons/react/24/outline";
import SubmitModal from "@/components/ui/SubmitModal";
import { useCount } from "@/context/searchStore";

interface AddRemoveUserToGroupProps {
  userValues: Array<{
    user_ext_id: number;
    creation_date: string;
    comment: string;
    user_id: number;
    user_full_name: string;
    created_by: string;
    status?: string | boolean; // Added optional status field
    is_active?: boolean; // Alternative status field
    active?: boolean; // Another alternative
    // Photo fields - only shown if present
    profile_photo?: string;
    avatar?: string;
    image?: string;
    user: {
      phone: string;
      email: string;
      // Photo fields in user object
      profile_photo?: string;
      avatar?: string;
      image?: string;
      photo?: string;
      [key: string]: any; // Allow additional properties
    };
  }>;
}

interface UserStatuses {
  [userId: string]: boolean;
}

export default function AddRemoveUserToGroup({
  userValues,
}: AddRemoveUserToGroupProps) {

  const [userValueTemp, setUserValueTemp] = useState(Object);
  const [userStatuses, setUserStatuses] = useState<UserStatuses>({});
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8; // Increased for better use of space
  const totalPages = Math.ceil(userValues?.length / itemsPerPage);
  const { searchValue, searchNumber } = useCount();

  let temp2: boolean = false

  // Helper function to get profile photo URL for a user
  const getUserPhotoUrl = (user: any) => {
    return user.profile_photo || 
           user.avatar || 
           user.image || 
           user.user?.profile_photo || 
           user.user?.avatar || 
           user.user?.image || 
           user.user?.photo || 
           null;
  };

  // Helper function to check if any user has a profile photo
  const hasAnyProfilePhotos = () => {
    return userValues?.some(user => getUserPhotoUrl(user) !== null) || false;
  };

  useEffect(() => {
    temp2 = useCount.getState().searchValue
    console.log("search numberss:- ", temp2)
    console.log("search numberss page:- ", currentPage)
    if (temp2 == true && currentPage != 1) {
      setCurrentPage(1)
    }
  }, [searchNumber])

  useEffect(() => {
    // Debug: Log the actual user data structure
    console.log("User data structure:", userValues?.[0]);
    console.log("Has profile photos:", hasAnyProfilePhotos());
    
    const statuses: UserStatuses = userValues?.reduce((acc, user) => {
      // Check various possible status representations
      let isActive = true; // Default to active
      
      if (user.hasOwnProperty('status')) {
        if (typeof user.status === 'boolean') {
          isActive = user.status;
        } else if (typeof user.status === 'string') {
          isActive = user.status.toLowerCase() === "active";
        }
      } else if (user.hasOwnProperty('is_active')) {
        isActive = user.is_active;
      } else if (user.hasOwnProperty('active')) {
        isActive = user.active;
      }
      
      acc[user.user_id] = isActive;
      return acc;
    }, {});
    
    setUserStatuses(statuses);
  }, [userValues]);

  const handleCheckboxChange = async (userId: string, userName: string) => {
    const newStatus = !userStatuses[userId];
    
    // Optimistically update the UI
    setUserStatuses((prevStatuses) => ({
      ...prevStatuses,
      [userId]: newStatus,
    }));
    
    try {
      // TODO: Replace with your actual API call
      // Example: await updateUserStatus(userId, newStatus);
      console.log(`Updating user ${userId} status to:`, newStatus);
      
      // Show success toast
      toast.success(`${userName} ${newStatus ? 'activated' : 'deactivated'} successfully!`, {
        position: "bottom-right",
        autoClose: 2000,
      });
      
    } catch (error) {
      // Revert the status on error
      setUserStatuses((prevStatuses) => ({
        ...prevStatuses,
        [userId]: !newStatus,
      }));
      
      toast.error(`Failed to update ${userName}'s status. Please try again.`, {
        position: "bottom-right",
        autoClose: 3000,
      });
      
      console.error("Error updating user status:", error);
    }
  };

  const handleAddUser = () => {
    setShowAddUserModal(false);
    setShowSubmitModal(true);
    toast.success('User updated successfully!', {
      position: "bottom-right",
      autoClose: 3000,
    });
  };

  const onCloseAddUserModal = () => {
    setShowAddUserModal(false);
  };

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
  };

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = userValues?.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Table Header Stats */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="flex items-center text-sm text-gray-600">
              <UserIcon className="h-5 w-5 mr-2 text-gray-400" />
              <span className="font-medium">{userValues?.length || 0}</span>
              <span className="ml-1">total users</span>
            </div>
            <div className="text-sm text-gray-500">
              Page {currentPage} of {totalPages}
            </div>
          </div>
          <div className="text-sm text-gray-500">
            Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, userValues?.length || 0)} of {userValues?.length || 0}
          </div>
        </div>
      </div>

      {/* Table Container */}
      <div className="overflow-x-auto">
        <table className="w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User Details
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact Information
              </th>
              <th scope="col" className="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>

          <tbody className="bg-white divide-y divide-gray-200">
            {Array.isArray(currentItems) && currentItems.length > 0 ? (
              currentItems.map((userValue, index) => {
                const isChecked = userStatuses[userValue.user_id] || false;
                return (
                  <tr
                    key={userValue.user_id}
                    className="hover:bg-gray-50 transition-colors duration-150"
                  >
                    {/* User Details Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {hasAnyProfilePhotos() && (
                          <div className="flex-shrink-0 h-10 w-10 mr-4">
                            {getUserPhotoUrl(userValue) ? (
                              <img
                                className="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                                src={getUserPhotoUrl(userValue)}
                                alt={userValue.user_full_name}
                                onError={(e) => {
                                  // Show placeholder if image fails to load
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const placeholder = target.nextElementSibling as HTMLElement;
                                  if (placeholder) placeholder.style.display = 'flex';
                                }}
                              />
                            ) : null}
                            <div 
                              className={`h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center ${
                                getUserPhotoUrl(userValue) ? 'hidden' : 'flex'
                              }`}
                            >
                              <span className="text-sm font-medium text-white">
                                {userValue.user_full_name?.charAt(0)?.toUpperCase() || 'U'}
                              </span>
                            </div>
                          </div>
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {userValue.user_full_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {userValue.user_ext_id}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Contact Information Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{userValue.user.email}</div>
                      <div className="text-sm text-gray-500">{userValue.user.phone}</div>
                    </td>

                    {/* Status Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={isChecked}
                          onChange={() => handleCheckboxChange(userValue.user_id, userValue.user_full_name)}
                        />
                        <div className={`relative w-11 h-6 transition-colors duration-200 ease-in-out rounded-full ${
                          isChecked ? 'bg-green-500' : 'bg-red-500'
                        }`}>
                          <div className={`absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-sm transition-transform duration-200 ease-in-out ${
                            isChecked ? 'translate-x-5' : 'translate-x-0'
                          }`} />
                        </div>
                        <span className="ml-3 text-sm font-medium text-gray-700">
                          {isChecked ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <CheckCircleIcon className="h-3 w-3 mr-1" />
                              Active
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <XCircleIcon className="h-3 w-3 mr-1" />
                              Inactive
                            </span>
                          )}
                        </span>
                      </label>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => {
                          setUserValueTemp(userValue);
                          setShowAddUserModal(true);
                        }}
                        className="inline-flex items-center p-2 text-sm font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200"
                        title="Edit User"
                      >
                        <PencilSquareIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={4} className="px-6 py-12 text-center">
                  <Nothing
                    title="No Users Found"
                    para="There are currently no users to display. Please check back later or add users."
                  />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {userValues?.length > 0 && (
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
              <span className="font-medium">{Math.min(indexOfLastItem, userValues.length)}</span> of{' '}
              <span className="font-medium">{userValues.length}</span> results
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={handlePreviousPage}
                disabled={currentPage <= 1}
                className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  currentPage <= 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gray-900 shadow-sm'
                }`}
              >
                <ChevronLeftIcon className="h-4 w-4 mr-1" />
                Previous
              </button>
              
              <span className="text-sm text-gray-700 px-3 py-2">
                Page {currentPage} of {totalPages}
              </span>
              
              <button
                onClick={handleNextPage}
                disabled={currentPage >= totalPages}
                className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  currentPage >= totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gray-900 shadow-sm'
                }`}
              >
                Next
                <ChevronRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      {showAddUserModal && (
        <EditUserModal
          onSubmit={handleAddUser}
          onClose={onCloseAddUserModal}
          userValueTemp={userValueTemp}
        />
      )}
      
      {showSubmitModal && (
        <SubmitModal
          modalName="User Update Success"
          onClose={onCloseSubmitModal}
        />
      )}
    </div>
  );
}