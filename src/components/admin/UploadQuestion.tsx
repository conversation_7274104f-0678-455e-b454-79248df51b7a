"use client";

import { useState } from "react";
import { getUser } from "@/api/user.localStorage";
import { API_URL } from "@/utils/constants";
import BulkQuesModal from "./BulkQuesModal";

export default function UploadForm() {
  const [file, setFile] = useState<File>();
  const[isModal,setIsModal] = useState(false);
  const [modalText, setModalText] = useState("");
  const [modalName, setModalName] = useState("");
  const user = getUser();

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!file) return;

    try { 
      const data = new FormData();
      data.set("question_file", file);
      const url = API_URL+"questions/bulk_upload/"
      const res = await fetch(url,
        {
          method: "POST",
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Authorization": `Bearer ${user?.token}`
          },
          body: data,
        }
      );
      console.log("Upload result", res);
      // handle the error
      if (!res.ok) throw new Error(await res.text());
      if (res.ok) {
        setModalText("The questions file was uploaded successfully");
        setModalName("Bulk Question Added ")
        setIsModal(true);
      }
    } catch (error: any) {
      // Handle errors here
      const errorMsg = JSON.parse(error.message)
      setModalText(`The questions file was not uploaded. ${errorMsg.detail}`);
      setModalName("Error")
      setIsModal(true);
      console.error(e);
    }
  };

  return (
    <form onSubmit={onSubmit} className="h-full">
      <div className="flex flex-col justify-center items-center gap-5 w-full h-full ">
        <label
          htmlFor="totalTime"
          className="block text-lg font-medium text-gray-700"
        >
          Upload Questions File
        </label>

        <input
          type="file"
          name="file"
          title="Choose file"
          className=" flex justify-center  w-full max-w-sm  border border-gray-300 rounded-md shadow-sm px-3 py-1.5"
          onChange={(e) => setFile(e.target.files?.[0])}
        />

        <input
          className="flex w-full max-w-sm justify-center rounded-md bg-secondary px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-hoverColor focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 active:scale-95 scale-100 duration-75"
          type="submit"
          value="Upload"
          title="submit bulk question"
        />
      </div>
      {isModal &&
      <BulkQuesModal modalName= {modalName} modalText={modalText} onClose={()=>setIsModal(false)} okclickPath="/admin/searchquestions"/>}

    </form>

    
  );
}