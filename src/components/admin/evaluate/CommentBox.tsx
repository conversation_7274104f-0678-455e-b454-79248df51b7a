"use client";
import React, { useState } from "react";

const CommentBox = () => {
  const [textAreaValue, setTextAreaValue] = useState("");

  const [enteredNumber, setEnteredNumber] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEnteredNumber(e.target.value);
  };

  const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextAreaValue(e.target.value);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    console.log("Form submitted comment:", textAreaValue);
    console.log("marks:", enteredNumber);
  };

  return (
    <div>
      <form onSubmit={handleSubmit} className="flex flex-col h-full w-full">
        <label
          className="block text-gray-700 text-sm font-bold mb-2"
          htmlFor="textarea"
        >
          Enter Comment:
        </label>
        <textarea
          id="textarea"
          name="textarea"
          value={textAreaValue}
          onChange={handleTextAreaChange}
          className="resize-none border shadow rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          rows={4}
          placeholder="Type Comment..."
        />
        <div className="py-2">
          <label
            className="block text-gray-700 text-sm font-bold mb-2"
            htmlFor="number"
          >
            Enter a Marks out of 10:
          </label>

          <input
            id="number"
            name="number"
            value={enteredNumber}
            onChange={handleInputChange}
            placeholder="Enter Marks..."
            className="resize-none border shadow rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          />
        </div>
        <div className="w-full flex justify-end items-end " >
        <button
          type="submit"
          className="  bg-secondary hover:bg-hoverColor text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        >
          Save
        </button>
        </div>
      </form>
    </div>
  );
};

export default CommentBox;
