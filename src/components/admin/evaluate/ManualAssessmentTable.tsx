"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import Nothing from "@/components/ui/Nothing";
import { InformationCircleIcon } from "@heroicons/react/24/outline";
import { SetManualAttemptStatusCount } from "@/hook/admin/evaluatelist/useGetManualAttemptStatusCount";
import ButtonComponent from "./ButtonComponent";
import { useCount } from "@/context/searchStore";
import assessmentFilter from "@/utils/provider"

export default function ManualAssessmentTable({ AssessmentData }) {
  // const [isRemoveModal, setIsRemoveModal] = useState(false);
  // const [showDeletedUserModal, setShowDeletedUserModal] = useState(false);
  // const [selectedAssessmentId, setSelectedAssessmentId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const totalPages = Math.ceil(AssessmentData?.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = AssessmentData?.slice(indexOfFirstItem, indexOfLastItem);
  const { searchValue, searchNumber } = useCount();
  const [filterValue, setfilterValue] = useState<[]>()

  function filterAssessments(searchString, assessments) {
    console.log("Current assessments incoming:", assessments);
    console.log("Search string:", searchString);

    const filteredAssessments = assessments.filter(assessment =>
      assessment.assessment_name.toLowerCase().includes(searchString.toLowerCase())
    );

    console.log("Filtered assessments:", filteredAssessments);
    return filteredAssessments;
  }

  let temp2: boolean = false;
  let temp3: string;
  useEffect(() => {

    temp2 = useCount.getState().searchValue
    temp3 = useCount.getState().searchCurrentValue
    console.log("search numberss:- ", temp2)
    console.log("search numberss page:- ", temp3)
    if (temp2 == true) {
      if (currentPage != 1) {
        setCurrentPage(1)
      }
      console.log("current currentItems incoming", currentItems)
      setfilterValue(filterAssessments(temp3, AssessmentData))
    }
  }, [searchNumber]) 

  console.log("currentItems", currentItems)
  console.log("currentItems values", filterValue)

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  let finalData = (filterValue && useCount.getState().searchValue) ? filterValue : currentItems

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  return (
    <div className="flex flex-col bg-white gap-2 justify-start items-start w-full rounded-md h-full  border shadow-md">
    {/* Scrollable table containe r for medium to large screens */}
    <div className="overflow-auto w-full h-full  rounded-md">
        <table className="min-w-full h-fit">
          <thead className="bg-secondary text-white text-sm uppercase sticky top-0 z-10 ">
          <tr className="w-full">
              <th className="p-2 text-left">Assessment Id</th>
              <th className="p-2 text-left">Assessment Name</th>
              <th className="p-2 text-left">Instructions</th>
              <th className="p-2 text-center">Total Time</th>
              <th className="p-2 text-center">Total Marks</th>
              <th className=" flex justify-center gap-1 p-2  text-center w-full">
                Status
                <span>
                  <InformationCircleIcon className="w-5 h-5" title=" " />
                </span>
              </th>
            </tr>
          </thead>
          <tbody className="h-full">
            {Array.isArray(finalData) && finalData.length > 0 ? (
              finalData.map((item, index) => {
                // setActive(index)


                return (
                  <tr key={item.assessment_id} className="   border-b h-10 ">
                    <td className="  p-2  px-3 text-left  ">
                      {item.assessment_id}
                    </td>
                    <td className="p-2  text-left  ">{item.assessment_name}</td>
                    <td className="p-2  text-left ">{item.instructions}</td>
                    {/* <td className="  p-2  text-center ">{(item.total_time_allowed/60).toFixed(2)}</td> */}
                    <td className="p-2  text-center ">
                      {(item.total_time_allowed / 60).toFixed(2)}
                    </td>
                    <td className="p-2  text-center ">{item.total_marks}</td>
                    <td className="p-2 text-center">
                      <div className="flex justify-center w-full">
                        <Link
                          href={{
                            pathname: "/admin/evaluatelist/questionuserattemptlist",
                            query: {
                              assessmentId: item.assessment_id,
                            },
                          }} >
                          <ButtonComponent id={item.assessment_id} type={1} />
                        </Link>
                      </div>
                    </td>
                  </tr>
                )
              })
            )
              :
              (
                <tr>
                  <td colSpan={6} className="text-center p-4">
                    <Nothing
                      title="No Assessmet Available"
                      para="There are currently no assessmet to display.
                        Please check back later."
                    />
                  </td>
                </tr>
              )}
          </tbody>
          {/* Empty rows to maintain the table's appearance for fewer items */}
          {currentItems.length < itemsPerPage &&
            [...Array(itemsPerPage - currentItems.length)].map((_, index) => (
              <tr key={`empty-${index}`} className=" h-10">
                <td colSpan={7} className="b-0"></td>
              </tr>
            ))}
        </table>
      </div>
      {/* Pagination controls */}
      <div className="flex justify-between items-end w-full">
        <button
          onClick={handlePreviousPage}
          disabled={currentPage <= 1}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Previous
        </button>
        <button
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Next
        </button>
      </div>
    </div>
  );
}