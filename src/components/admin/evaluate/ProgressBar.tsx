import useGetNumberOfEvaluted from '@/hook/admin/evaluatelist/useGetNumberOfEvaluted';
import React from 'react';

interface ProgressBarProps {
  assessmentId:number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ assessmentId }) => {
    const { data: evaluatedata, isLoading, isError } = useGetNumberOfEvaluted(assessmentId);

    console.log("assessmentId in ProgressBar:", assessmentId);
    console.log("Data loaded:", evaluatedata);
    console.log("Loading state:", isLoading);
    console.log("Error state:", isError);

    if (isLoading) return <p>Loading...</p>;
    if (isError) return <p>Error loading data</p>;
    if (!evaluatedata) return <p>No data available</p>;  // Adjusted to check for nullish data

    const evaluatedWidth = `${(evaluatedata.evaluated / (evaluatedata.evaluated + evaluatedata.non_evaluated) * 100).toFixed(2)}%`;
const notEvaluatedWidth = 100 - evaluatedWidth;
    return (
    //     <div className='flex w-full justify-between gap-2' >
    //     <div className="w-full bg-red-500 h-5 rounded flex ">
    //     Not Evaluated
    //         <div style={{ width: evaluatedWidth }} className="bg-green-500 h-5 ">Evaluated</div>
    //     </div>
    //     <div className='text-black text-sm'>                
    //     {evaluatedata.evaluated}/{evaluatedata.non_evaluated}
    // </div>
    // </div>
     <div className='flex w-full justify-between gap-2'>
     <div className="w-full bg-red-500 h-5 rounded flex relative">
       <div style={{ width: `${notEvaluatedWidth}%` }} className="flex justify-center items-center overflow-hidden">
         <span className="text-white text-xs font-semibold">Not </span>
       </div>
       <div style={{ width: `${evaluatedWidth}%` }} className="bg-green-500 h-5 flex justify-center items-center">
         <span className="text-white text-xs font-semibold">Evaluated</span>
       </div>
     </div>
     <div className='text-black text-sm'>
     {evaluatedata.evaluated}/{evaluatedata.non_evaluated}
     </div>
   </div>
    );
};
export default ProgressBar;


// import React from 'react';



// const ProgressBar: React.FC<ProgressBarProps> = () => {
//   const evaluatedWidth = (4 / 8) * 100;

//   return (
//     <div className="w-full bg-red-500 h-6 rounded">
//       <div style={{ width: `${evaluatedWidth}%` }} className="bg-green-500 h-6"></div>
//     </div>
//   );
// };

// export default ProgressBar;