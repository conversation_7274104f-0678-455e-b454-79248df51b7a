import React, { useState, useEffect } from 'react'
import { SetManualAttemptStatusCount } from "@/hook/admin/evaluatelist/useGetManualAttemptStatusCount";

type CountData = {
  evaluated: number;
  non_evaluated: number;
}

interface ButtonComponentProps {
  id?: number;
  evaluation_status?: number;
  type?: number
}


const ButtonComponent = ({ id, evaluation_status, type }: ButtonComponentProps) => {

  const [percentage, setPercentage] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [loading1, setLoading1] = useState(false)
  const [isHovered, setIsHovered] = useState(false);

  const { data: countData } = SetManualAttemptStatusCount(id)

  useEffect(() => {
    let tempData = (countData?.evaluated / (countData?.non_evaluated + countData?.evaluated)) * 100
    setPercentage(tempData)
  }, [countData])
  console.log("datais", countData)
  console.log("datais1 percentage", percentage)




  if (loading) {
    return (
      <button className={`p-[2px] ${percentage <= 25 ? "border-red-500" : percentage > 25 && percentage <= 50 ? "border-yellow-400" : percentage > 50 && percentage <= 100 ? "border-green-500" : ""} border-2 rounded-[40px] w-[160px] h-[30px] `}>
        Loading...
      </button>
    )
  } else {
    if (countData && type==1) {
      return (
        <button className={`p-[2px] ${percentage <= 25 ? "border-red-500" : percentage > 25 && percentage <= 50 ? "border-yellow-400" : percentage > 50 && percentage <= 100 ? "border-green-500" : ""} border-2 rounded-[40px] w-[160px] h-[30px] `} onClick={() => setLoading(true)}>
          <div className='relative w-full h-full rounded-[38px] '>
            <div className={`${percentage <= 25 ? `w-[25%] bg-gradient-to-r from-red-200 to-red-500` : percentage > 25 && percentage <= 50 ? `w-[50%] bg-gradient-to-r from-yellow-200 to-yellow-500` : percentage > 50 && percentage < 100 ? `w-[75%] bg-gradient-to-r from-green-200 to-green-500` : percentage == 100 ? `w-[100%] bg-gradient-to-r from-green-200 to-green-500` : ""} h-full rounded-[38px]
             z-0 absolute top-0 left-0 -p-[1px] `}>
            </div>
            <div className='z-10 absolute top-0 left-0 w-full h-full flex items-center justify-center text-sm text-gray-700 font-bold'>
              <span>{countData?.evaluated}/{countData?.non_evaluated + countData?.evaluated} </span>
            </div>
          </div>
        </button>
      )
    } else {
      return (
        <button className="p-[2px] border-2 rounded-[40px] w-[160px] h-[30px] " onClick={() => setLoading1(true)}>
          <div className='relative w-full h-full bg-white rounded-[38px]'>
            <div className={`${evaluation_status == 4 ? "bg-gradient-to-r from-green-200 to-green-600" : "bg-gradient-to-r from-red-300 to-red-500"} z-10 absolute top-0 left-0 w-full h-full flex items-center justify-center text-sm font-bold rounded-[38px]`}>
              {loading1 ? <span>Loading...</span> : <span>{evaluation_status == 4 ? "Evaluated " : "Not Evaluated"}</span>}
            </div>
          </div>
        </button>
      )

    }
  }
}
export default ButtonComponent;