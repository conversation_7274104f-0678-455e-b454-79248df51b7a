"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import Nothing from "@/components/ui/Nothing";
import { InformationCircleIcon } from "@heroicons/react/24/outline";
import ButtonComponent from "./ButtonComponent";
import { useCount } from "@/context/searchStore";

export default function ManualQuestionTable({ UserData }) {
  const [isRemoveModal, setIsRemoveModal] = useState(false);
  const [showDeletedUserModal, setShowDeletedUserModal] = useState(false);
  const [selectedAssessmentId, setSelectedAssessmentId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const totalPages = Math.ceil(UserData?.length / itemsPerPage);
  const { searchValue, searchNumber, searchCurrentValue } = useCount();
  const [filterValue, setfilterValue] = useState<[]>()

  function filterAssessments(searchString, assessments) {
    console.log("Current assessments incoming:", assessments);
    console.log("Search string:", searchString);

    const filteredAssessments = assessments.filter(assessment =>
      assessment.user_full_name.toLowerCase().includes(searchString.toLowerCase())
    );

    console.log("Filtered assessments:", filteredAssessments);
    return filteredAssessments;
  }

  let temp2: boolean = false;
  let temp3: string;
  useEffect(() => {

    temp2 = useCount.getState().searchValue
    temp3 = useCount.getState().searchCurrentValue
    console.log("search numberss:- ", temp2)
    console.log("search numberss page:- ", temp3)
    if (temp2 == true) {
      if (currentPage != 1) {
        setCurrentPage(1)
      }
      console.log("current currentItems incoming", currentItems)
      setfilterValue(filterAssessments(temp3, UserData))
    }
  }, [searchNumber])

  console.log("function:- ", UserData)

  let finalData = (filterValue && useCount.getState().searchValue) ? filterValue : UserData

  const handleDeleteClick = (assessmentId: any) => {
    setSelectedAssessmentId(assessmentId); // Store the ID for deletion
    setIsRemoveModal(true); // Open the modal for confirmation
  };

  function formatTime(totalTime) {
    const hours = Math.floor(totalTime / 3600);
    const minutes = Math.floor((totalTime % 3600) / 60);
    const seconds = totalTime % 60;

    if (hours > 0) {
      return `${hours} hrs ${minutes} mins ${seconds} secs`;
    } else if (minutes > 0) {
      return `${minutes} mins ${seconds} secs`;
    } else {
      return `${seconds} secs`;
    }
  }

  const handleConfirmDelete = () => {
    if (selectedAssessmentId !== null) {
      onDelete(selectedAssessmentId); // Now, actually call onDelete
    }
    setIsRemoveModal(false); // Close the modal
    setSelectedAssessmentId(null); // Reset the selected ID
  };

  const handleCloseDeletedUserModal = () => {
    setShowDeletedUserModal(false); // Close the DeletedUserModal
  };

  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = UserData?.slice(indexOfFirstItem, indexOfLastItem) || [];

  return (
    <div className="flex flex-col bg-white gap-2 justify-start items-start w-full rounded-md h-full  border shadow-md">
      {/* Scrollable table containe r for medium to large screens */}
      <div className="overflow-auto   w-full h-full rounded-md">
        <table className="min-w-full h-fit">
          <thead className="bg-secondary text-white text-sm uppercase sticky top-0 ">
            <tr className="w-full">
              <th className="p-2 text-center">User Id</th>
              <th className="p-2 text-center">User Assessment Attempt Id</th>
              <th className="p-2 text-center">user Name</th>

              <th className="p-2 text-center">Attempt</th>

              <th className="p-2 text-center">Total Time</th>
              <th className=" flex justify-center gap-1 p-2  text-center w-full">
                Status
                <span>
                  <InformationCircleIcon
                    className="w-5 h-5"
                    title="More info"
                  />
                </span>
              </th>
            </tr>
          </thead>
          <tbody className="h-full">
            {Array.isArray(finalData) && finalData.length > 0 ? (
              finalData.map((item) => (
                <tr key={item.user_id} className="   border-b h-10 h-fit">
                  <td className="  p-2  px-3 text-center  ">{item.user_id}</td>
                  <td className=" p-2  text-center ">
                    {item.user_assessment_attempt_id}
                  </td>
                  <td className="  p-2  text-center  ">
                    {item.user_full_name}
                  </td>

                  <td className="  p-2  text-center ">{item.attempt_number}</td>
                  {/* <td className="  p-2  text-center ">{(item.total_time_allowed/60).toFixed(2)}</td> */}
                  <td className="p-2 text-center">
                    {formatTime(item.attempt_total_time)}
                  </td>
                  <td className="p-2 text-center">
                    <div className="flex justify-center w-full">
                      <Link
                        href={{
                          pathname: "/admin/evaluatelist/questionlist",
                          query: {
                            userAssessmentAttemptId:
                              item.user_assessment_attempt_id,
                            userId: item.user_id
                          },
                        }}
                      >
                        <ButtonComponent evaluation_status={item.attempt_status} />
                      </Link>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="text-center p-4">
                  <Nothing
                    title="No Assessmet Available"
                    para="There are currently no assessmet to display.
                        Please check back later."
                  />
                </td>
              </tr>
            )}
          </tbody>
          {/* Empty rows to maintain the table's appearance for fewer items */}
          {currentItems.length < itemsPerPage &&
            [...Array(itemsPerPage - currentItems.length)].map((_, index) => (
              <tr key={`empty-${index}`} className=" align-text-top h-10">
                <td colSpan={7}></td>
              </tr>
            ))}
        </table>
      </div>
      {/* Pagination controls */}
      <div className="flex justify-between items-end w-full">
        <button
          onClick={handlePreviousPage}
          disabled={currentPage <= 1}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Previous
        </button>
        <button
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
        >
          Next
        </button>
      </div>
    </div>
  );
}
