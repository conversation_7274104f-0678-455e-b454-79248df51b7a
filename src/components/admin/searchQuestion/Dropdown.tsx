import React, { useEffect, useRef, useState } from "react";

interface DropdownProps {
  options: { label: string; content: string[] }[];
  set: React.Dispatch<React.SetStateAction<string>>;
  currentOption: any
  width: any
}

const Dropdown: React.FC<DropdownProps> = ({ options, set, currentOption, width }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const handleOptionSelect = (option: string) => {
    setSelectedOption(option);
    set(option);
    setIsOpen(false);
  };

  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const tempValue = options
  console.log("tempValue",tempValue)

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      <div className="w-full">
        {options.map((option, index) => (
          <div key={option.label}>
            <button
              type="button"
              onClick={() => setIsOpen(!isOpen)}
              className={`flex  justify-center gap-2 p-3 bg-white text-black rounded-md text-[10px] md:text-[12px] lg:text-[15px]   w-[${width}] ${selectedOption === option.label ? 'bg-blue-100' : ''
                }`}
              id="options-menu"
              aria-haspopup="true"
              aria-expanded="true"
            >
              {currentOption ? currentOption : option.label}
            </button>
          </div>
        ))}
      </div>

      {isOpen && (
        <div className="ml-2 origin-top-right absolute right-50 mt-1 w-fit md:w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50 overflow-auto min-h-fit max-h-80">
          <div
            className="py-1"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="options-menu"
          >
            {options.map((option, index) => (
              <div key={option.label}>
                {option.content.map((dropdownOption) => (
                  <label
                    key={dropdownOption}
                    className={`flex gap-2 inline-flex justify-between w-full px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring focus:border-blue-300 ${
                      selectedOption === dropdownOption ? 'bg-blue-100' : ''
                    }`}
                  >
                    <p>{dropdownOption}</p>
                    <div>
                      <input
                        type="radio"
                        name={`radioGroup-${index}`}
                        onChange={() => handleOptionSelect(dropdownOption)}
                        checked={selectedOption === dropdownOption}
                        className="form-radio h-5 w-5 text-blue-500"
                      />
                    </div>
                  </label>
                ))}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dropdown;