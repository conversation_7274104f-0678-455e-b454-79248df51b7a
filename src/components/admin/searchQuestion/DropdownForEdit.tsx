import React, { useEffect, useRef, useState } from "react";

interface DropdownProps {
  options?: { label: string; content: string[] }[];
  set?: React.Dispatch<React.SetStateAction<string>>;
  currentOption?: any

}

const DropdownForEdit: React.FC<DropdownProps> = ({ options, set, currentOption }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const handleOptionSelect = (option: string) => {
    setSelectedOption(option);
    set(option);
    setIsOpen(false);
  };

  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  if (true) {
    return (
      <div className="relative inline-block text-left w-full" ref={dropdownRef}>
        
        <div className="w-full ">
          {options.map((option, index) => (
            <div className="w-full " key={option.label}>

              <button
                type="button"
                onClick={() => setIsOpen(!isOpen)}
                className={`flex    border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500  w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 `}
                id="options-menu"
                aria-haspopup="true"
                aria-expanded="true"
              >
                {currentOption ? currentOption : option.label}
              </button>
            </div>
          ))}
        </div>
          
        {(isOpen&&set) && (
          <div className="ml-2 origin-top-right absolute right-50 mt-1 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
            <div
              className="py-1"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="options-menu"
            >
              {options.map((option, index) => (
                <div key={option.label}>
                  {option.content.map((dropdownOption) => (
                    <label
                      key={dropdownOption}
                      className={`inline-flex justify-between w-full px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none  `}
                    >
                      <p>{dropdownOption}</p>
                      <div>
                        <input
                          type="radio"
                          name={`radioGroup-${index}`}
                          onChange={() => handleOptionSelect(dropdownOption)}
                          checked={selectedOption === dropdownOption}
                          className="form-radio h-5 w-5 text-blue-500"
                        />
                      </div>
                    </label>
                  ))}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  } else {
    return (
      <div className="relative inline-block text-left w-full" ref={dropdownRef}>
        <div className="w-full">

          {/* <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className={`flex    border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500  w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 `}
          id="options-menu"
          aria-haspopup="true"
          aria-expanded="true"
        >
          {currentOption}llllllll
    </button> */}
        </div>
      </div>
    )

  }

};

export default DropdownForEdit;