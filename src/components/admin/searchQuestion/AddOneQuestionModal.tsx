"use client";
import React, { useState, useEffect, use, useRef } from "react";
// import { Edit_Questions } from "@/types/LMSTypes";
import { PencilIcon } from "@heroicons/react/24/outline";
import { XMarkIcon } from "@heroicons/react/24/outline";

import { useQueryClient } from "@tanstack/react-query";
import DropdownForEdit from "./DropdownForEdit";
import { usePostOneQuestion } from "@/hook/questions/usePostOneQuestion";
import { One_Questions } from "@/types/LMSTypes";
import { useGetAllFilteredQuestion } from "@/hook/questions/useGetAllFilteredQuestion";


type Edit_Questions = {
  question: string;
  question_type: string;
  question_difficulty: any; // Adjust the type according to your needs
  option1: string;
  option2: string;
  option3: string;
  option4: string;
  question_source: string; // Allow null for potential absence of source
  picture: any; // Adjust the type according to how you handle pictures
  answer_option: string; // Adjust the type according to your needs
  answer_text: string;
  marks: number; // Assuming it's a number
  answer_explanation: string;
  topics_string: string;
  default_text: string;
  // Add more properties as needed
};

export default function AddOneQuestionModal({
  onClose,
  onCrossClose,

}) {
  const queryClient = useQueryClient();
  const [selectedQuestionTypeOption, setSelectedQuestionTypeOption] = useState("");
  const [selectedQuestionDifficultyOption, setSelectedQuestionDifficultyOption] = useState(0)
  const [selectedQuestionCategory, setSelectedQuestionCategory] = useState("")
  const [answerOption, setAnswerOption] = useState(0)
  const [question, setQuestion] = useState("");
  const [questionSource, setQuestionSource] = useState("");
  const [answerText, setAnswerText] = useState("");
  const [marks, setMarks] = useState(0)
  const [answerExplanation, setAnswerExplanation] = useState("");
  const [topicsString, setTopicsString] = useState("");
  const [defaultText, setDefaultText] = useState("");
  const [imagedata, setImagedata] = useState("");
  const [option1, setOption1] = useState("Option 1 ");
  const [option2, setOption2] = useState("Option 2 ");
  const [option3, setOption3] = useState("Option 3 ");
  const [option4, setOption4] = useState("Option 4 ");
  const [options, setOptions] = useState([option1, option2, option3, option4]);
  const [editingStates, setEditingStates] = useState(options.map(() => true));
  const [editingValue, setEditingValue] = useState(null);
  const [editingIndex, setEditingIndex] = useState(null);
  const [error, setError] = useState("");
  const [questionError, setQuestionError] = useState("");
  const [questionTypeError, setQuestionTypeError] = useState("");
  const [questionDifficultyError, setQuestionDifficultyError] = useState("");
  const [questionSourceError, setQuestionSourceError] = useState("");
  const [answerOptionError, setAnswerOptionError] = useState("")
  const [answerTextError, setAnswerTextError] = useState("");
  const [marksError, setMarksError] = useState("");
  const [answerExplanationError, setAnswerExplanationError] = useState("");
  const [topicsStringError, setTopicsStringError] = useState("");
  const [defaultTextError, setDefaultTextError] = useState("");
  const [file, setFile] = useState();

  const inputFileRef = useRef(null);

  const formData: One_Questions[] = [{
    question: question,
    question_type: selectedQuestionTypeOption,
    question_difficulty: selectedQuestionDifficultyOption,
    question_category: selectedQuestionCategory,
    option1: option1,
    option2: option2,
    option3: option3,
    option4: option4,
    picture: "",
    topics_string: topicsString,
    default_text: defaultText,
    question_source: questionSource,
    answer_option: answerOption,
    answer_text: answerText,
    marks: marks,
    answer_explanation: answerExplanation
  }];

  const addOneQuestionData = usePostOneQuestion(formData);

  const handleSubmit = async (e) => {
    e.preventDefault(); // Prevent the default form submission
    // if (!validateFields()) {
    //   console.error("Validation failed.");
    //   return;
    // }

    if (addOneQuestionData) {
      try {
        await addOneQuestionData.mutate();
        onClose()

      } catch (error) {
        console.error("Error while submitting:", error);
      }
    } else {
      console.log("Please fill in all fields before submitting.");
    }
  }

  let isValid = true;

  const validateFields = () => {
    // Validate each input field
    if (!question) {
      setQuestionError("Question field is required");
      isValid = false;
    } else {
      setQuestionError("");
    }

    if (!selectedQuestionTypeOption) {
      setQuestionTypeError("Question Type field is required");
      isValid = false;
    } else {
      setQuestionTypeError("");
    }

    if (!selectedQuestionDifficultyOption) {
      setQuestionDifficultyError("Question Difficulty field is required");
      isValid = false;
    } else {
      setQuestionDifficultyError("");
    }

    if (!questionSource) {
      setQuestionSourceError("Question Source field is required");
      isValid = false;
    } else {
      setQuestionSourceError("");
    }

    if (!answerOption) {
      setAnswerOptionError("Select Correct Answer field is required");
      isValid = false;
    } else {
      setAnswerOptionError("");
    }

    if (!answerText) {
      setAnswerTextError("Answer text field is required");
      isValid = false;
    } else {
      setAnswerTextError("");
    }

    if (!marks || isNaN(marks) || marks <= 0) {
      setMarksError("Marks must be a positive number");
      isValid = false;
    } else {
      setMarksError("");
    }

    if (!answerExplanation) {
      setAnswerExplanationError("Answer Explanation field is required");
      isValid = false;
    } else {
      setAnswerExplanationError("");
    }

    if (!topicsString) {
      setTopicsStringError("Topic String field is required");
      isValid = false;
    } else {
      setTopicsStringError("");
    }

    if (!defaultText) {
      setDefaultTextError("Default Text field is required");
      isValid = false;
    } else {
      setDefaultTextError("");
    }
    // Add validations for other fields as needed

    return isValid;
  };


  const optionsQuestionDifficulty = [
    {
      label: "Question Difficulty",
      content: ["1", "2", "3", "4", "5"],
    },
  ];

  const optionsQuestionType = [
    {
      label: "Question Type",
      content: ["T/F", "MCQ", "CODING", "SUBJECTIVE"],
    },
  ];

  const questionCategory = [
    {
      label: "Question Category",
      content: ["Recall", "Application", "Communication"],
    },
  ];

  const trueFalse = ["True", "False"]


  const optionsAnswerOption = [
    {
      label: "Answer Option",
      content: ["1", "2", "3", "4"],
    },
  ];

  const optionsAnswerOptionforTF = [
    {
      label: "Answer Option",
      content: ["1", "2"],
    },
  ];


  const startEditing = (index1, value1) => {

    setEditingStates(options.map(() => true)); //reset all value to true
    const newEditingStates = [...editingStates];
    newEditingStates[index1] = false;

    setEditingValue(value1.option); //setting the value came from
    setEditingIndex(index1);

    setEditingStates(newEditingStates);
  };

  const saveEditedValues = () => {
    //map the values
    for (let j = 0; j < options.length; j++) {
      switch (j) {

        case 0:
          setOption1(options[0]);
          break;
        case 1:
          setOption2(options[1]);
          break;
        case 3:
          setOption3(options[2]);
          break;
        case 4:
          setOption4(options[3]);
          break;
      }
    }
  };

  const rtEditing = (index, value) => {
    //real time editing

    //edit the value inside the option
    console.log("rt Index: ", index);
    console.log("rt value: ", value);

    for (let i = 0; i < options.length; i++) {
      if (i == index) {
        const tempValue = [...options];
        tempValue[index] = value;
        setOptions(tempValue);
      }
    }
  };

  const imageUpload = (e) => {
    const formData = new FormData();
    formData.append("file", e.target.files[0]);
    setFile(URL.createObjectURL(e.target.files[0]));
    setImagedata(formData);

    console.log("File Data: ", formData);

  };

  const removeImage = () => {
    setFile(null);
    // Reset the file input value
    if (inputFileRef.current) {
      inputFileRef.current.value = "";
    }
  };

  useEffect(() => {
    if (selectedQuestionTypeOption == "T/F") {
      setOption1("True");
      setOption2("False");
      console.log("!Done!")
    }
  }, [selectedQuestionTypeOption])


  useEffect(() => {
    setOptions([option1, option2, option3, option4]);
  }, [option1, option2, option3, option4]);

  //new update

  const addOption = () => {
    const nonNullCount = options.filter((item) => item !== null).length;
    console.log("nonNullValues: ", nonNullCount);
    if (nonNullCount < 4 && options.length == 4) {
      // Find the index of the first occurrence of null
      const nullIndex = options.indexOf(null);

      console.log("Inside loop: 1");
      const tempArray = [...options]; //copy of the array
      tempArray[nullIndex] = "option";
      setOptions(tempArray);

      // setOption2("option");
      // setOptions(prevOptions => [...prevOptions, "option"]);
      //-----
    }
  };

  useEffect(() => {
    console.log("Updated Options:", options);
  }, [options]);

  const removeOption = (indexVal) => {
    console.log("remove index value: ", indexVal);
    const nonNullCount = options.filter((item) => item !== null).length;
    setEditingValue(null);
    if (nonNullCount > 1) {
      const newOptions = options.map((item, i) =>
        i === indexVal ? null : item
      );
      setOptions(newOptions);
    }
  };


  if (selectedQuestionTypeOption == "MCQ" || selectedQuestionTypeOption == "") {
    return (
      <form onSubmit={handleSubmit} >
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 ">
          <div className=" p-4   shadow-md h-5/6 w-[600px]  bg-white flex rounded-lg ">
            <div className=" flex flex-col  w-full h-full  gap-2 ">
              <h1 className="text-2xl font-bold  ">Add question details</h1>

              <div className="flex flex-col gap-2 w-full  overflow-y-scroll">

                <div className="flex flex-col w-full gap-3">
                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="first_name"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question:{" "}
                      </label>
                      <input
                        type="text"
                        id="first_name"
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5"
                        placeholder="Enter the question"

                        onChange={(e) => setQuestion(e.target.value)} // Use onChange to update the state
                      />
                      {questionError && (
                        <p className="text-red-500 text-xs">{questionError}</p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="question_type"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Type:{" "}
                      </label>

                      <DropdownForEdit
                        options={optionsQuestionType}
                        set={setSelectedQuestionTypeOption}
                        currentOption={selectedQuestionTypeOption}
                      />
                      {questionTypeError && <p className="text-red-500 text-xs">{questionTypeError}</p>}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="question_difficulty"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Difficulty:{" "}
                      </label>

                      <div className="text-left w-full">
                        <DropdownForEdit
                          options={optionsQuestionDifficulty}
                          set={setSelectedQuestionDifficultyOption}
                          currentOption={selectedQuestionDifficultyOption}
                        />
                        {questionDifficultyError && <p className="text-red-500 text-xs">{questionDifficultyError}</p>}
                      </div>
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="Questionsrc"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Source:{" "}
                      </label>
                      <input
                        type="text"
                        id="Questionsrc"
                        value={questionSource}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Question Source"

                        onChange={(e) => setQuestionSource(e.target.value)}
                      />
                      {questionSourceError && (
                        <p className="text-red-500 text-xs">
                          {questionSourceError}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="website"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Answer Option:{" "}
                      </label>

                      <div className="text-left w-full">
                        <DropdownForEdit
                          options={optionsAnswerOption}
                          set={setAnswerOption}
                          currentOption={answerOption}
                        />
                        {answerOptionError && <p className="text-red-500 text-xs">{answerOptionError}</p>}
                      </div>
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="visitors"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Answer Text:{" "}
                      </label>
                      <input
                        type="text"
                        id="visitors"
                        value={answerText}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Answer Text"

                        onChange={(e) => setAnswerText(e.target.value)}
                      />
                      {answerTextError && (
                        <p className="text-red-500 text-xs">{answerTextError}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="marks"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Marks:{" "}
                      </label>
                      <input
                        type="number"
                        id="marks"
                        value={marks}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Marks"

                        onChange={(e) => setMarks(e.target.value)}
                      />
                      {marksError && (
                        <p className="text-red-500 text-xs">{marksError}</p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="answer_explanation"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Answer Explaination:{" "}
                      </label>
                      <input
                        type="text"
                        id="answer_explanation"
                        value={answerExplanation}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Answer Explaination"

                        onChange={(e) => setAnswerExplanation(e.target.value)}
                      />
                      {answerExplanationError && (
                        <p className="text-red-500 text-xs">
                          {answerExplanationError}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="topics_string"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Topic String:{" "}
                      </label>
                      <input
                        type="text"
                        id="topics_string"
                        value={topicsString}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Topic String"

                        onChange={(e) => setTopicsString(e.target.value)}
                      />
                      {topicsStringError && (
                        <p className="text-red-500 text-xs">
                          {topicsStringError}
                        </p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="default_text"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Default Text:{" "}
                      </label>
                      <input
                        type="text"
                        id="default_text"
                        value={defaultText}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Default Text"

                        onChange={(e) => setDefaultText(e.target.value)}
                      />
                      {defaultTextError && (
                        <p className="text-red-500 text-xs">{defaultTextError}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="Questionsrc"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Category:{" "}
                      </label>
                      <div className="text-left w-64">
                        <DropdownForEdit
                          options={questionCategory}
                          set={setSelectedQuestionCategory}
                          currentOption={selectedQuestionCategory}
                        />
                        {answerOptionError && <p className="text-red-500 text-xs">{answerOptionError}</p>}
                      </div>
                      {questionSourceError && (
                        <p className="text-red-500 text-xs">
                          {questionSourceError}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-2">
                  <h2 className="text-lg font-semibold">Options</h2>

                  <div className="w-full h-full flex flex-col justify-center gap-3  ">
                    <div className="w-full h-full flex flex-col justify-center gap-1">
                      {options.map(
                        (option, index) =>
                          option !== null && (
                            <div
                              key={index}
                              className="w-full  p-1 justify-between border bg-secondary rounded-lg flex items-center gap-1"
                            >
                              <span className="w-full text-white text-sm rounded-lg p-1">
                                {option}
                              </span>
                              <div className="flex">
                                <button
                                  type="button"
                                  className="m-1 w-6 h-6"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    startEditing(index, { option });
                                  }}
                                >
                                  <PencilIcon
                                    color="#FFFFFF"
                                    height={"14px"}
                                    width={"14px"}
                                  />
                                </button>
                                <button
                                  type="button"
                                  className="m-1 w-6 h-6"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeOption(index);
                                  }}
                                >
                                  <XMarkIcon
                                    color="#FFFFFF"
                                    height={"14px"}
                                    width={"14px"}
                                  />
                                </button>
                              </div>
                            </div>
                          )
                      )}
                    </div>
                    <div className="w-full">
                      {editingValue != null ? (
                        <div className="flex justify-center w-full gap-2">
                          <input
                            className="border border-secondary rounded-lg   w-full "
                            type="text"
                            onChange={(e) => {
                              rtEditing(editingIndex, e.target.value);
                              setEditingValue(e.target.value);
                            }}
                          />
                          <button
                            type="button" // Add this line
                            className=" bg-secondary hover:bg-hoverColor w-full  text-white py-2 px-4 rounded-lg"
                            onClick={(e) => {
                              e.stopPropagation();
                              setEditingValue(null);
                              saveEditedValues();
                            }}
                          >
                            Save edit
                          </button>
                        </div>
                      ) : (
                        <button
                          type="button" // Add this line
                          className="sm:w-[150px] md:w-[255px] lg:w-48 m-4 bg-secondary hover:bg-hoverColor transition duration-150  text-white font-bold py-2 px-4 rounded-lg"
                          onClick={() => addOption()}
                        >
                          Add options
                        </button>
                      )}
                    </div>
                  </div>
                </div>
                {/* 
                <div className="gap-2 py-3 flex flex-col justify-center w-full h-full ">
                  <div className="flex justify-between w-full h-full">
                    <div className="flex-none">
                      <input
                        ref={inputFileRef}
                        className=""
                        type="file"
                        onChange={imageUpload}
                      />
                    </div>
  
                    <div>
                      {file && (
                        <button className="flex-1 ml-4" onClick={removeImage}>
                          <XMarkIcon height={"26px"} width={"26px"} />
                        </button>
                      )}
                    </div>
                  </div>
  
                  <div className="flex justify-center">
                    <img src={file} alt="upload" />
                  </div>
                </div> */}
              </div>

              <div className="w-full  flex justify-center">
                <button
                  className="w-full md:w-32 md:ml-2 lg:w-48 mr-2 bg-secondary hover:bg-hoverColor transition duration-150 text-white font-bold py-2 px-4 rounded"
                  type="submit"
                >
                  Submit
                </button>
              </div>
            </div>

            <button
              className="flex h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                onCrossClose()
              }}
            >
              <XMarkIcon height={"25px"} width={"25px"} />
            </button>
          </div>
        </div>
      </form>
    );
  } else if (selectedQuestionTypeOption == "T/F") {

    return (
      <form onSubmit={handleSubmit} >
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 ">
          <div className=" p-4   shadow-md h-5/6 w-[600px]  bg-white flex rounded-lg ">
            <div className=" flex flex-col  w-full h-full  gap-2 ">
              <h1 className="text-2xl font-bold  ">Add question details</h1>

              <div className="flex flex-col gap-2 w-full h-5/6 overflow-y-scroll">

                <div className="flex flex-col w-full gap-3">
                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="first_name"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question:{" "}
                      </label>
                      <input
                        type="text"
                        id="first_name"
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5"
                        placeholder="Enter the question"

                        onChange={(e) => setQuestion(e.target.value)} // Use onChange to update the state
                      />
                      {questionError && (
                        <p className="text-red-500 text-xs">{questionError}</p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="last_name"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Type:{" "}
                      </label>

                      <DropdownForEdit
                        options={optionsQuestionType}
                        set={setSelectedQuestionTypeOption}
                        currentOption={selectedQuestionTypeOption}
                      />
                      {questionTypeError && <p className="text-red-500 text-xs">{questionTypeError}</p>}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="company"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Difficulty:{" "}
                      </label>

                      <div className="text-left w-full">
                        <DropdownForEdit
                          options={optionsQuestionDifficulty}
                          set={setSelectedQuestionDifficultyOption}
                          currentOption={selectedQuestionDifficultyOption}
                        />
                        {questionDifficultyError && <p className="text-red-500 text-xs">{questionDifficultyError}</p>}
                      </div>
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="Questionsrc"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Source:{" "}
                      </label>
                      <input
                        type="text"
                        id="Questionsrc"
                        value={questionSource}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Question Source"

                        onChange={(e) => setQuestionSource(e.target.value)}
                      />
                      {questionSourceError && (
                        <p className="text-red-500 text-xs">
                          {questionSourceError}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="website"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Answer Option:{" "}
                      </label>

                      <div className="text-left w-full">
                        <DropdownForEdit
                          options={optionsAnswerOptionforTF}
                          set={setAnswerOption}
                          currentOption={answerOption}
                        />
                        {answerOptionError && <p className="text-red-500 text-xs">{answerOptionError}</p>}
                      </div>
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="visitors"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Answer Text:{" "}
                      </label>
                      <input
                        type="text"
                        id="visitors"
                        value={answerText}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Answer Text"

                        onChange={(e) => setAnswerText(e.target.value)}
                      />
                      {answerTextError && (
                        <p className="text-red-500 text-xs">{answerTextError}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="marks"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Marks:{" "}
                      </label>
                      <input
                        type="number"
                        id="marks"
                        value={marks}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Marks"

                        onChange={(e) => setMarks(e.target.value)}
                      />
                      {marksError && (
                        <p className="text-red-500 text-xs">{marksError}</p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="answer_explanation"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Answer Explaination:{" "}
                      </label>
                      <input
                        type="text"
                        id="answer_explanation"
                        value={answerExplanation}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Answer Explaination"

                        onChange={(e) => setAnswerExplanation(e.target.value)}
                      />
                      {answerExplanationError && (
                        <p className="text-red-500 text-xs">
                          {answerExplanationError}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="topics_string"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Topic String:{" "}
                      </label>
                      <input
                        type="text"
                        id="topics_string"
                        value={topicsString}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Topic String"

                        onChange={(e) => setTopicsString(e.target.value)}
                      />
                      {topicsStringError && (
                        <p className="text-red-500 text-xs">
                          {topicsStringError}
                        </p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="default_text"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Default Text:{" "}
                      </label>
                      <input
                        type="text"
                        id="default_text"
                        value={defaultText}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Default Text"

                        onChange={(e) => setDefaultText(e.target.value)}
                      />
                      {defaultTextError && (
                        <p className="text-red-500 text-xs">{defaultTextError}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="Questionsrc"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Category:{" "}
                      </label>
                      <div className="text-left w-64">
                        <DropdownForEdit
                          options={questionCategory}
                          set={setSelectedQuestionCategory}
                          currentOption={selectedQuestionCategory}
                        />
                        {answerOptionError && <p className="text-red-500 text-xs">{answerOptionError}</p>}
                      </div>
                      {questionSourceError && (
                        <p className="text-red-500 text-xs">
                          {questionSourceError}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-2">
                  <h2 className="text-lg font-semibold">Options</h2>

                  <div className="w-full h-full flex flex-col justify-center gap-3  ">
                    <div className="w-full h-full flex flex-row justify-center gap-1">
                      <div
                        className="w-full p-1 justify-between border bg-secondary rounded-lg flex items-center gap-1"
                      >
                        <span className="w-full text-white text-sm rounded-lg p-1">
                          True
                        </span>
                      </div>
                      <div
                        className="w-full p-1 justify-between border bg-secondary rounded-lg flex items-center gap-1"
                      >
                        <span className="w-full text-white text-sm rounded-lg p-1">
                          False

                        </span>
                      </div>
                    </div>

                  </div>
                </div>
                {/* 
                <div className="gap-2 py-3 flex flex-col justify-center w-full h-full ">
                  <div className="flex justify-between w-full h-full">
                    <div className="flex-none">
                      <input
                        ref={inputFileRef}
                        className=""
                        type="file"
                        onChange={imageUpload}
                      />
                    </div>
  
                    <div>
                      {file && (
                        <button className="flex-1 ml-4" onClick={removeImage}>
                          <XMarkIcon height={"26px"} width={"26px"} />
                        </button>
                      )}
                    </div>
                  </div>
  
                  <div className="flex justify-center">
                    <img src={file} alt="upload" />
                  </div>
                </div> */}
              </div>

              <div className="w-full  flex justify-center">
                <button
                  className="w-full md:w-32 md:ml-2 lg:w-48 mr-2 bg-secondary hover:bg-hoverColor transition duration-150 text-white font-bold py-2 px-4 rounded"
                  type="submit"
                >
                  Submit
                </button>
              </div>
            </div>

            <button
              className="flex h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                onCrossClose()
              }}
            >
              <XMarkIcon height={"25px"} width={"25px"} />
            </button>
          </div>
        </div>
      </form>
    );
  }
  else if (selectedQuestionTypeOption == "CODING") {
    return (
      <form onSubmit={handleSubmit} >
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 ">
          <div className=" p-4 shadow-md h-5/6 w-[600px]  bg-white flex rounded-lg ">
            <div className=" flex flex-col w-full h-full  gap-2 ">
              <h1 className="text-2xl font-bold  ">Add question details</h1>

              <div className="flex flex-col gap-2 w-full h-5/6 overflow-y-scroll">

                <div className="flex flex-col w-full gap-3">
                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="first_name"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question:{" "}
                      </label>
                      <input
                        type="text"
                        id="first_name"
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5"
                        placeholder="Enter the question"

                        onChange={(e) => setQuestion(e.target.value)} // Use onChange to update the state
                      />
                      {questionError && (
                        <p className="text-red-500 text-xs">{questionError}</p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="last_name"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Type:{" "}
                      </label>

                      <DropdownForEdit
                        options={optionsQuestionType}
                        set={setSelectedQuestionTypeOption}
                        currentOption={selectedQuestionTypeOption}
                      />
                      {questionTypeError && <p className="text-red-500 text-xs">{questionTypeError}</p>}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="company"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Difficulty:{" "}
                      </label>

                      <div className="text-left w-full">
                        <DropdownForEdit
                          options={optionsQuestionDifficulty}
                          set={setSelectedQuestionDifficultyOption}
                          currentOption={selectedQuestionDifficultyOption}
                        />
                        {questionDifficultyError && <p className="text-red-500 text-xs">{questionDifficultyError}</p>}
                      </div>
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="Questionsrc"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Source:{" "}
                      </label>
                      <input
                        type="text"
                        id="Questionsrc"
                        value={questionSource}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Question Source"

                        onChange={(e) => setQuestionSource(e.target.value)}
                      />
                      {questionSourceError && (
                        <p className="text-red-500 text-xs">
                          {questionSourceError}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="marks"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Marks:{" "}
                      </label>
                      <input
                        type="number"
                        id="marks"
                        value={marks}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Marks"

                        onChange={(e) => setMarks(e.target.value)}
                      />
                      {marksError && (
                        <p className="text-red-500 text-xs">{marksError}</p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="answer_explanation"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Answer Explaination:{" "}
                      </label>
                      <input
                        type="text"
                        id="answer_explanation"
                        value={answerExplanation}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Answer Explaination"

                        onChange={(e) => setAnswerExplanation(e.target.value)}
                      />
                      {answerExplanationError && (
                        <p className="text-red-500 text-xs">
                          {answerExplanationError}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="topics_string"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Topic String:{" "}
                      </label>
                      <input
                        type="text"
                        id="topics_string"
                        value={topicsString}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Topic String"

                        onChange={(e) => setTopicsString(e.target.value)}
                      />
                      {topicsStringError && (
                        <p className="text-red-500 text-xs">
                          {topicsStringError}
                        </p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="default_text"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Default Text:{" "}
                      </label>
                      <input
                        type="text"
                        id="default_text"
                        value={defaultText}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Default Text"

                        onChange={(e) => setDefaultText(e.target.value)}
                      />
                      {defaultTextError && (
                        <p className="text-red-500 text-xs">{defaultTextError}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="Questionsrc"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Category:{" "}
                      </label>
                      <div className="text-left w-64">
                        <DropdownForEdit
                          options={questionCategory}
                          set={setSelectedQuestionCategory}
                          currentOption={selectedQuestionCategory}
                        />
                        {answerOptionError && <p className="text-red-500 text-xs">{answerOptionError}</p>}
                      </div>
                      {questionSourceError && (
                        <p className="text-red-500 text-xs">
                          {questionSourceError}
                        </p>
                      )}
                    </div>
                  </div>
                </div>


              </div>

              <div className="w-full  flex justify-center">
                <button
                  className="w-full md:w-32 md:ml-2 lg:w-48 mr-2 bg-secondary hover:bg-hoverColor transition duration-150 text-white font-bold py-2 px-4 rounded"
                  type="submit"
                >
                  Submit
                </button>
              </div>
            </div>

            <button
              className="flex h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                onCrossClose()
              }}
            >
              <XMarkIcon height={"25px"} width={"25px"} />
            </button>
          </div>
        </div>
      </form>
    );
  }
  else if (selectedQuestionTypeOption == "SUBJECTIVE") {
    return (
      <form onSubmit={handleSubmit} >
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 ">
          <div className=" p-4 shadow-md h-5/6 w-[600px]  bg-white flex rounded-lg ">
            <div className=" flex flex-col w-full h-full  gap-2 ">
              <h1 className="text-2xl font-bold  ">Add question details</h1>

              <div className="flex flex-col gap-2 w-full h-5/6 overflow-y-scroll">

                <div className="flex flex-col w-full gap-3">
                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="first_name"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question:{" "}
                      </label>
                      <input
                        type="text"
                        id="first_name"
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5"
                        placeholder="Enter the question"

                        onChange={(e) => setQuestion(e.target.value)} // Use onChange to update the state
                      />
                      {questionError && (
                        <p className="text-red-500 text-xs">{questionError}</p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="last_name"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Type:{" "}
                      </label>

                      <DropdownForEdit
                        options={optionsQuestionType}
                        set={setSelectedQuestionTypeOption}
                        currentOption={selectedQuestionTypeOption}
                      />
                      {questionTypeError && <p className="text-red-500 text-xs">{questionTypeError}</p>}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="company"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Difficulty:{" "}
                      </label>

                      <div className="text-left w-full">
                        <DropdownForEdit
                          options={optionsQuestionDifficulty}
                          set={setSelectedQuestionDifficultyOption}
                          currentOption={selectedQuestionDifficultyOption}
                        />
                        {questionDifficultyError && <p className="text-red-500 text-xs">{questionDifficultyError}</p>}
                      </div>
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="Questionsrc"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Source:{" "}
                      </label>
                      <input
                        type="text"
                        id="Questionsrc"
                        value={questionSource}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Question Source"

                        onChange={(e) => setQuestionSource(e.target.value)}
                      />
                      {questionSourceError && (
                        <p className="text-red-500 text-xs">
                          {questionSourceError}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="marks"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Marks:{" "}
                      </label>
                      <input
                        type="number"
                        id="marks"
                        value={marks}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Marks"

                        onChange={(e) => setMarks(e.target.value)}
                      />
                      {marksError && (
                        <p className="text-red-500 text-xs">{marksError}</p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="answer_explanation"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Answer Explaination:{" "}
                      </label>
                      <input
                        type="text"
                        id="answer_explanation"
                        value={answerExplanation}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Answer Explaination"

                        onChange={(e) => setAnswerExplanation(e.target.value)}
                      />
                      {answerExplanationError && (
                        <p className="text-red-500 text-xs">
                          {answerExplanationError}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="topics_string"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Topic String:{" "}
                      </label>
                      <input
                        type="text"
                        id="topics_string"
                        value={topicsString}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Topic String"

                        onChange={(e) => setTopicsString(e.target.value)}
                      />
                      {topicsStringError && (
                        <p className="text-red-500 text-xs">
                          {topicsStringError}
                        </p>
                      )}
                    </div>

                    <div className="w-full ">
                      <label
                        htmlFor="default_text"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Default Text:{" "}
                      </label>
                      <input
                        type="text"
                        id="default_text"
                        value={defaultText}
                        className=" border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                        placeholder="Default Text"

                        onChange={(e) => setDefaultText(e.target.value)}
                      />
                      {defaultTextError && (
                        <p className="text-red-500 text-xs">{defaultTextError}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex w-full gap-5">
                    <div className="w-full ">
                      <label
                        htmlFor="Questionsrc"
                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                      >
                        Question Category:{" "}
                      </label>
                      <div className="text-left w-64">
                        <DropdownForEdit
                          options={questionCategory}
                          set={setSelectedQuestionCategory}
                          currentOption={selectedQuestionCategory}
                        />
                        {answerOptionError && <p className="text-red-500 text-xs">{answerOptionError}</p>}
                      </div>
                      {questionSourceError && (
                        <p className="text-red-500 text-xs">
                          {questionSourceError}
                        </p>
                      )}
                    </div>
                  </div>
                </div>


              </div>

              <div className="w-full  flex justify-center">
                <button
                  className="w-full md:w-32 md:ml-2 lg:w-48 mr-2 bg-secondary hover:bg-hoverColor transition duration-150 text-white font-bold py-2 px-4 rounded"
                  type="submit"
                >
                  Submit
                </button>
              </div>
            </div>

            <button
              className="flex h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                onCrossClose()
              }}
            >
              <XMarkIcon height={"25px"} width={"25px"} />
            </button>
          </div>
        </div>
      </form>
    );
  } else {
    return (<>
      Wrong Type
    </>)
  }

}
