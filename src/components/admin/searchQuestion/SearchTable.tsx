"use client";
import React, { useState, useEffect, useRef } from "react";

import { useDeleteQuestionFromTable } from "@/hook/admin/question/useDeleteQuestionFromTable";

import { PencilSquareIcon, TrashIcon } from "@heroicons/react/24/outline";
import Nothing from "@/components/ui/Nothing";
import DeleteModal from "../userGroup/DeleteModal";
import DeletedUserModal from "../userGroup/DeletedUserModal";
import EditQuestionModal from "./EditQuestionModal";
import SubmitModal from "@/components/ui/SubmitModal";
import { useCount } from "@/context/searchStore";


export default function SearchTable({
  questionData,
}) {

  const [showDeletedUserModal, setShowDeletedUserModal] = useState(false);
  const [isRemoveModal, setIsRemoveModal] = useState(false);
  const [selectedContentId, setSelectedContentId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [indexNumber, setIndexNumber] = useState(Number);
  const { searchValue, searchNumber } = useCount();
  
  let temp2:boolean = false 

useEffect(() => {
  temp2 = useCount.getState().searchValue
  console.log("search numberss:- ", temp2)
  console.log("search numberss page:- ", currentPage)
  console.log("indexNumber222",indexNumber)
  console.log("questionData",questionData)
  if(temp2==true && currentPage != 1){
    setCurrentPage(1)
  }
}, [searchNumber])

  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const itemsPerPage = 6;

  const { mutate: deleteQuestion } = useDeleteQuestionFromTable();

  if (!questionData) {
    return <div>Loading...</div>;
  }

  const handleDelete = (questionId) => {
    if (!questionId) {
      console.error("Question ID is undefined.");
      return;
    }
    setSelectedContentId(questionId); // Remember the ID of the question to delete
    setIsRemoveModal(true); // Show the modal for confirmation
  };

  const data = questionData.map((item) => ({
    questionNo: item.question_id,
    question: item.question,
    questionDifficulty: item.question_difficulty,
    questionSource: item.question_source,
    questionTopics: item.topics_string,
  }));
  const totalPages = Math.ceil(data.length / itemsPerPage);


  const handleCloseDeletedUserModal = () => {
    setShowDeletedUserModal(false); // Close the DeletedUserModal
  };

  const handleConfirmDelete = () => {
    if (selectedContentId !== null) {
      deleteQuestion({ question_id: selectedContentId });
      setIsRemoveModal(false); // Close the modal after deletion
      // Optionally reset selectedContentId or perform other cleanup here
    }
  }
  const handleNextPage = () => {
    setCurrentPage((prevCurrentPage) =>
      Math.min(prevCurrentPage + 1, totalPages)
    );
  };

  const handlePreviousPage = () => {
    setCurrentPage((prevCurrentPage) => Math.max(prevCurrentPage - 1, 1));
  };


  const onCloseEditQestionModal = () => {
    setShowModal(false); // Close AddContentModal
    setShowSubmitModal(true);
  };

  const onCloseSubmitModal = () => {
    setShowSubmitModal(false);
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = data.slice(indexOfFirstItem, indexOfLastItem);

  // useEffect(() => {
  //   console.log("search numberss:- ", searchValue)
  // }, [questionData])




  return (
    <div className="flex flex-col bg-white gap-2 justify-start items-start w-full rounded-md h-full  border shadow-md">
    {/* Scrollable table containe r for medium to large screens */}
    <div className="overflow-auto w-full h-full  rounded-md">
        <table className="min-w-full h-full">
          <thead className="bg-secondary text-white text-sm uppercase sticky top-0  ">
          <tr className="w-full">
              <th className="p-2 text-left">Question No</th>
              <th className="p-2 text-left">Question</th>
              <th className="p-2 text-left">Difficulty Level</th>
              <th className="p-2 text-left">Question Source</th>
              <th className="p-2 text-left">Topics</th>
              <th className="p-2 text-center">Edit</th>
              <th className="p-2 text-center">Action</th>
            </tr>
          </thead>
          <tbody className="h-full">
            {Array.isArray(currentItems) && currentItems.length > 0 ? (
              currentItems.map((item, index) => (
                <tr key={item.questionNo} className="border-b h-10">
                  <td className="p-2 px-3 text-left">{item.questionNo}</td>
                  <td className="p-2 text-left">{item.question}</td>
                  <td className="p-2 text-left">{item.questionDifficulty}</td>
                  <td className="p-2 text-left">{item.questionSource}</td>
                  <td className="p-2 text-left">{item.questionTopics}</td>
                  <td className="p-2 text-center">
                    <button
                      onClick={() => {
                        setShowModal(true);
                        setIndexNumber(item.questionNo);
                      }}
                    >
                      <PencilSquareIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-secondary" />
                    </button>
                  </td>
                  <td className="p-2 text-center" >
                    <button onClick={() => handleDelete(item.questionNo)}>
                      <TrashIcon className="md:w-[25px] md:h-[25px] w-[20px] h-[20px] text-red-500" />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr >
                <td colSpan={7} className="text-center p-4">
                  <Nothing
                    title="No Question Available"
                    para="There are currently no questions to display. Please check back later."
                  />
                </td>
              </tr>
            )}
            {/* Empty rows to maintain the table's appearance for fewer items */}
            {currentItems.length < itemsPerPage &&
              [...Array(itemsPerPage - currentItems.length)].map((_, index) => (
                <tr key={`empty-${index}`} className=" h-14">
                  <td colSpan={6}></td>
                </tr>
              ))
            }
          </tbody>
        </table>
      </div>

      {/* Pagination controls */}
      {  
        <div className="flex justify-between items-end w-full">
          <button
            onClick={handlePreviousPage}
            disabled={currentPage <= 1}
            className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
          >
            Previous
          </button>
          <button
            onClick={handleNextPage}
            disabled={currentPage >= totalPages}
            className="flex gap-2 py-2 px-4 m-2 disabled:bg-gray-200 disabled:text-gray-500 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]"
          >
            Next
          </button>
        </div>
      }


      {showModal && (<EditQuestionModal
        questionDetails={questionData.find(item => item.question_id === indexNumber)}
        setShowModal={setShowModal}
        onClose={onCloseEditQestionModal}

      />)}

      {showSubmitModal && (
        <SubmitModal
          modalName="Question Upload Success"
          onClose={onCloseSubmitModal}
        />
      )}
      {isRemoveModal && (
        <DeleteModal
          deleteName="Question"
          onClose={() => setIsRemoveModal(false)}
          onOpen={handleConfirmDelete}
        />
      )}
      {showDeletedUserModal && (
        <DeletedUserModal
          deleteName="Question"
          onClose={handleCloseDeletedUserModal}
        />
      )}
    </div>
  );
}
