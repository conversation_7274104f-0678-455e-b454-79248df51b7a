apiVersion: apps/v1
kind: Deployment
metadata:
  name: lms-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lms-frontend
  template:
    metadata:
      labels:
        app: lms-frontend
    spec:
      nodeSelector:
        "kubernetes.io/os": linux
      containers:
      - name: lms-frontend
        image: skillingairegistry.azurecr.io/lms-frontend:latest
        ports:
        - containerPort: 3000
          protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: lms-frontend
spec:
  ports:
  - port: 80
    targetPort: 3000
  selector:
    app: lms-frontend
  type: LoadBalancer
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lms-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lms-backend
  template:
    metadata:
      labels:
        app: lms-backend
    spec:
      nodeSelector:
        "kubernetes.io/os": linux
      containers:
      - name: lms-backend
        image: skillingairegistry.azurecr.io/lms-backend
        ports:
        - containerPort: 80
          protocol: TCP
        env:
        - name: ENV
          value: /code/configs/local.env
        - name: CONFIG_FILE
          value: /code/configs/lmsapi.ini
        volumeMounts:
        - name: lms-backend-storage
          persistentVolumeClaim: 
            claimName: backend-pv-claim
          mountPath: /data/shared/question_images
        - name: lms-backend-db
          persistentVolumeClaim: 
            claimName: backend-pv-claim
          mountPath: /data
      volumes:
      - name: lms-backend-storage
        hostPath:
          path: /shared/question_images
          type: DirectoryOrCreate
      - name: lms-backend-db
        hostPath:
          path: /
          type: DirectoryOrCreate
---
apiVersion: v1
kind: Service
metadata:
  name: lms-backend
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 8080
    targetPort: 80
  selector:
    app: lms-backend

