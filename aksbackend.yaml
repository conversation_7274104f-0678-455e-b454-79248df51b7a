apiVersion: v1
kind: Pod
metadata:
  name: lms-backend
spec:
  containers:
  - name: lms-backend
    image: skillingairegistry.azurecr.io/lms-backend
    ports:
    - containerPort: 8080
      protocol: TCP
    env:
    - name: ENV
      value: /code/configs/local.env
    - name: CONFIG_FILE
      value: /code/configs/lmsapi.ini
    volumeMounts:
#    - name: lms-backend-storage
#      persistentVolumeClaim: 
#        claimName: backend-pv-claim
#      mountPath: /data/shared/question_images
    - name: lms-backend-db
      persistentVolumeClaim: 
        claimName: backend-pv-claim
      mountPath: /data
  volumes:
#  - name: lms-backend-storage
#    hostPath:
#      path: /data/shared/question_images
#      type: DirectoryOrCreate
  - name: lms-backend-db
    hostPath:
      path: /
      type: DirectoryOrCreate
#      path: /test_app_sql.db
#      type: FileOrCreate