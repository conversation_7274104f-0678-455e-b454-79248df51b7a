# Tailwind CSS Styling Guide for talentlens Project

This guide provides a comprehensive framework for aligning the talentlens project with the LMS project's current design patterns using Tailwind CSS.

## Table of Contents

1. [Color Palette](#1-color-palette)
2. [Typography](#2-typography)
3. [Layout Components](#3-layout-components)
4. [Navigation Components](#4-navigation-components)
5. [Form Elements](#5-form-elements)
6. [Modal Components](#6-modal-components)
7. [Interactive Elements](#7-interactive-elements)
8. [Responsive Design Patterns](#8-responsive-design-patterns)
9. [Animation and Transitions](#9-animation-and-transitions)
10. [Accessibility Considerations](#10-accessibility-considerations)

## 1. Color Palette

The LMS project uses a modern color system that combines both custom theme colors and Tailwind's default color palette. While there are theme colors defined in tailwind.config.js, the newer components often use Tailwind's default color scale for more flexibility:

```javascript
// tailwind.config.js - Theme colors are defined but not exclusively used
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: "#F2F3F5",
        secondary: "#1E2B3A",
        hoverColor: "#3F5479",
        textColor: "#1A2B3B",
        textPrimary: "#1E2B3A",
        textSecondary: "#407BBF",
      },
    },
  },
}
```

### Current Color Usage Patterns:

1. **Background Colors**:
   - White backgrounds for cards and content areas: `bg-white`
   - Light gray for page backgrounds: `bg-gray-50`
   - Blue accents for active states: `bg-blue-50`, `bg-blue-600`
   - Status colors: `bg-green-100`, `bg-amber-100`, `bg-red-500`, `bg-purple-500`

2. **Text Colors**:
   - Primary text: `text-gray-900`, `text-gray-800`
   - Secondary text: `text-gray-700`, `text-gray-600`
   - Muted text: `text-gray-500`
   - Accent text: `text-blue-600`, `text-blue-700`
   - Status text: `text-green-800`, `text-amber-800`

3. **Border Colors**:
   - Default borders: `border-gray-200`, `border-gray-300`
   - Accent borders: `border-blue-500`
   - Status borders: `border-green-500`, `border-red-500`

### Example Usage:

```jsx
// Modern button with Tailwind's color system
<button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors duration-200">
  Submit
</button>

// Card with modern styling
<div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
  <h2 className="text-gray-900 text-xl font-bold">Card Title</h2>
  <p className="text-gray-600 mt-2">Card content with <span className="text-blue-600">highlighted text</span>.</p>
</div>

// Status indicator
<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
  Active
</span>
```

## 2. Typography

The LMS project uses a modern typography system with consistent patterns:

### Text Sizes and Weights:
- **Page Headings**: `text-xl sm:text-2xl font-bold text-gray-900`
- **Section Headings**: `text-lg font-semibold text-gray-800`
- **Card Titles**: `text-lg font-medium text-gray-800`
- **Body Text**: `text-sm text-gray-600` or `text-gray-700`
- **Small Text/Captions**: `text-xs text-gray-500`
- **Responsive Text**: `text-[10px] md:text-[12px] lg:text-[15px]`

### Font Weights:
- **Bold**: `font-bold` for headings and emphasis
- **Semibold**: `font-semibold` for subheadings
- **Medium**: `font-medium` for card titles and buttons
- **Normal**: Default weight for body text

### Line Height:
- **Relaxed**: `leading-relaxed` for body text
- **Tight**: `leading-tight` for headings
- **Normal**: Default for most text

### Example Usage:

```jsx
// Page heading
<h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-4">Dashboard Overview</h1>

// Section heading
<h2 className="text-lg font-semibold text-gray-800 mb-3">Recent Activity</h2>

// Card title
<h3 className="text-lg font-medium text-gray-800 mb-2">Card Title</h3>

// Body text
<p className="text-sm text-gray-600 leading-relaxed">
  This is the main content of the page with normal body text styling.
</p>

// Small text / caption
<span className="text-xs text-gray-500">Last updated: 2 days ago</span>

// Badge text
<span className="text-xs font-medium text-green-800">Active</span>

// Button text
<button className="text-sm font-medium">Submit</button>

// Responsive text
<p className="text-[10px] md:text-[12px] lg:text-[15px]">Responsive text example</p>
```

## 3. Layout Components

### Cards

Cards are a fundamental component in the LMS project with several modern styling variations:

```jsx
// Basic card
<div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 p-4">
  <h3 className="text-lg font-medium text-gray-800 mb-2">Card Title</h3>
  <p className="text-sm text-gray-600">Card content goes here.</p>
</div>

// Card with border
<div className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-200">
  <h3 className="text-lg font-medium text-gray-800 mb-2">Card Title</h3>
  <p className="text-sm text-gray-600">Card content goes here.</p>
</div>

// Card with colored top bar (modern style)
<div className="bg-white rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200 flex flex-col h-full">
  <div className="h-2 w-full bg-blue-500 rounded-t-xl"></div>
  <div className="p-4">
    <h3 className="text-lg font-medium text-gray-800 mb-2">Card Title</h3>
    <p className="text-sm text-gray-600">Card content goes here.</p>
  </div>
</div>

// Card with colored header (full-width style)
<div className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-200">
  <div className="bg-blue-100 h-32 w-full relative">
    <div className="absolute inset-0 flex items-center justify-center">
      <svg className="h-16 w-16 text-blue-500" /* SVG content */ />
    </div>
  </div>
  <div className="p-4">
    <h3 className="text-lg font-medium text-gray-800 mb-2">Card Title</h3>
    <p className="text-sm text-gray-600">Card content goes here.</p>
  </div>
</div>

// Selected card state
<div className="bg-white rounded-lg border border-blue-400 shadow-sm ring-2 ring-blue-200 p-4">
  <h3 className="text-lg font-medium text-gray-800 mb-2">Selected Card</h3>
  <p className="text-sm text-gray-600">This card is in a selected state.</p>
</div>

// Card with badge
<div className="bg-white rounded-lg shadow-md p-4 relative">
  <span className="absolute top-3 left-3 bg-green-100 rounded-full px-2 py-0.5 text-xs font-medium text-green-800">
    New
  </span>
  <h3 className="text-lg font-medium text-gray-800 mb-2">Card with Badge</h3>
  <p className="text-sm text-gray-600">Card content goes here.</p>
</div>
```

### Containers

The LMS project uses consistent container patterns:

```jsx
// Page container
<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
  {/* Page content */}
</div>

// Main content container
<main className="w-full flex h-full bg-white rounded-xl flex-col p-4">
  {/* Main content */}
</main>

// Section container
<section className="bg-white rounded-lg shadow-sm p-4 sm:p-6 mb-6">
  <h2 className="text-lg font-semibold text-gray-800 mb-4">Section Title</h2>
  {/* Section content */}
</section>

// Grid layout
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
  {/* Grid items */}
</div>

// Responsive grid with dynamic columns
<div className={`grid ${getGridColumns()} gap-4`}>
  {/* Grid items with dynamic column count */}
</div>

// Flex container
<div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
  {/* Flex items */}
</div>
```

## 4. Navigation Components

### Sidebar

The sidebar in the LMS project has a modern, collapsible design:

```jsx
// Collapsible sidebar container
<div className={`h-screen border-r border-gray-200 bg-gray-100 ${shouldShowFullMenu ? 'w-64' : 'w-20'} flex flex-col transition-all duration-300 overflow-hidden relative z-30`}>
  {/* Logo section */}
  <div className="p-4 border-b border-gray-200">
    <div className={`flex ${shouldShowFullMenu ? 'justify-between' : 'justify-center'} items-center`}>
      {shouldShowFullMenu ? (
        <img src="/logo.png" alt="Logo" className="h-8 w-auto" />
      ) : (
        <div className="flex-shrink-0 text-blue-600 font-bold text-lg">S.ai</div>
      )}
      <button
        onClick={toggleSidebar}
        className="p-1 rounded-full hover:bg-gray-200 text-gray-500"
      >
        {isDesktop ? (
          isExpanded ? (
            <ChevronLeftIcon className="h-5 w-5" />
          ) : (
            <ChevronRightIcon className="h-5 w-5" />
          )
        ) : (
          <XMarkIcon className="h-5 w-5" />
        )}
      </button>
    </div>
  </div>

  {/* Navigation section */}
  <div className="flex-1 overflow-y-auto py-4">
    <nav className="space-y-1 px-2">
      {/* Navigation items */}
    </nav>
  </div>
</div>
```

### Navigation Links

Navigation links follow a consistent pattern with responsive behavior:

```jsx
// Active and inactive navigation links with responsive behavior
<Link
  href="/dashboard"
  className={`
    group flex items-center ${shouldShowFullMenu ? 'px-4' : 'justify-center'} py-3 text-sm font-medium rounded-md
    bg-blue-50 text-blue-700
  `}
  title={!shouldShowFullMenu ? 'Dashboard' : ''}
>
  <HomeIcon
    className={`
      ${shouldShowFullMenu ? 'mr-3' : ''} h-5 w-5 flex-shrink-0
      text-blue-600
    `}
    aria-hidden="true"
  />
  {shouldShowFullMenu && <span>Dashboard</span>}
</Link>

<Link
  href="/reports"
  className={`
    group flex items-center ${shouldShowFullMenu ? 'px-4' : 'justify-center'} py-3 text-sm font-medium rounded-md
    text-gray-700 hover:bg-gray-200
  `}
  title={!shouldShowFullMenu ? 'Reports' : ''}
>
  <ChartBarIcon
    className={`
      ${shouldShowFullMenu ? 'mr-3' : ''} h-5 w-5 flex-shrink-0
      text-gray-500 group-hover:text-gray-600
    `}
    aria-hidden="true"
  />
  {shouldShowFullMenu && <span>Reports</span>}
</Link>

// Dropdown navigation item
<Disclosure>
  {({ open }) => (
    <>
      <Disclosure.Button className="w-full flex items-center px-4 py-3 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-200 focus:outline-none">
        <FolderIcon className="h-5 w-5 mr-3 text-gray-500" />
        <span className="flex-1 text-left">Projects</span>
        <ChevronUpIcon
          className={`${open ? 'transform rotate-180' : ''} w-5 h-5 text-gray-500 transition-transform duration-200`}
        />
      </Disclosure.Button>
      <Transition
        show={open}
        enter="transition duration-100 ease-out"
        enterFrom="transform scale-95 opacity-0"
        enterTo="transform scale-100 opacity-100"
        leave="transition duration-75 ease-out"
        leaveFrom="transform scale-100 opacity-100"
        leaveTo="transform scale-95 opacity-0"
      >
        <Disclosure.Panel className="pl-12 pr-4 py-1 space-y-1">
          <Link
            href="/projects/active"
            className="block py-2 text-sm text-gray-600 hover:text-blue-600"
          >
            Active Projects
          </Link>
          <Link
            href="/projects/archived"
            className="block py-2 text-sm text-gray-600 hover:text-blue-600"
          >
            Archived Projects
          </Link>
        </Disclosure.Panel>
      </Transition>
    </>
  )}
</Disclosure>
```

### Mobile Navigation

The LMS project includes responsive navigation for mobile devices:

```jsx
{/* Mobile header with menu button */}
<div className="lg:hidden fixed top-0 left-0 right-0 z-40 flex items-center justify-between bg-white p-4 shadow-sm">
  <button
    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
    className="text-gray-700 hover:text-blue-600"
  >
    {isMobileMenuOpen ?
      <XMarkIcon className="h-6 w-6" /> :
      <Bars3Icon className="h-6 w-6" />
    }
  </button>
  <h1 className="text-lg font-semibold">Skilling.ai</h1>
  <div className="w-6"></div> {/* Spacer for alignment */}
</div>

{/* Mobile menu overlay */}
{isMobileMenuOpen && (
  <div
    className="lg:hidden fixed inset-0 z-30 bg-black bg-opacity-50"
    onClick={() => setIsMobileMenuOpen(false)}
  />
)}

{/* Mobile sidebar */}
<div
  className={`
    lg:hidden fixed inset-y-0 left-0 z-40 w-64 bg-white transform transition-transform duration-300 ease-in-out
    ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
  `}
>
  {/* Mobile sidebar content */}
</div>
```

## 5. Form Elements

### Page Background Colors

The LMS project consistently uses light background colors for pages and content areas:

```jsx
// Main page background - light gray
<div className="flex h-screen bg-gray-50 overflow-hidden">
  {/* Page content */}
</div>

// Content area background - white
<main className="w-full flex h-full bg-white rounded-xl flex-col p-4">
  {/* Content */}
</main>

// Section background - white with shadow
<section className="bg-white rounded-lg shadow-sm p-4">
  {/* Section content */}
</section>
```

### Buttons

The LMS project uses several modern button styles with consistent patterns:

```jsx
// Primary button (blue)
<button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200">
  Submit
</button>

// Primary button (with textColor theme)
<button className="flex gap-2 py-2 px-4 bg-textColor text-white rounded-md text-[10px] md:text-[12px] lg:text-[15px]">
  <BtnIcon className="h-[15px] w-[15px] md:h-[20px] md:w-[20px] text-white" />
  Button Text
</button>

// Secondary button (outline with theme color)
<button className="self-center w-fit border-2 border-textSecondary text-textSecondary font-semibold p-2 rounded text-[0.6rem] md:text-sm hover:bg-textSecondary hover:text-white transition-all duration-300 whitespace-nowrap">
  Cancel
</button>

// Outline button (gray)
<button className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all">
  View Details
</button>

// Icon button
<button className="p-1 rounded-full hover:bg-gray-200 text-gray-500 transition-colors">
  <PencilIcon className="h-5 w-5" />
</button>

// Text button with icon (inline style)
<button className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800">
  <ClipboardDocumentCheckIcon className="h-4 w-4 mr-1" />
  View Skills
</button>

// Link-style button
<button className="text-blue-600 hover:text-blue-800 hover:underline text-sm font-medium">
  Learn More
</button>

// Disabled button
<button className="px-4 py-2 rounded-md font-medium bg-gray-300 text-gray-500 cursor-not-allowed" disabled>
  Processing...
</button>

// Loading button
<button className="px-4 py-2 rounded-md font-medium bg-blue-600 text-white flex items-center justify-center">
  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
  Processing...
</button>

// Success button
<button className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-all">
  Confirm
</button>

// Danger button
<button className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-all">
  Delete
</button>

// Full-width button
<button className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-center">
  Sign In
</button>

// Button with responsive text
<Link
  className="w-full px-2 py-2 bg-textColor text-white rounded-md text-center text-[10px] md:text-[15px] lg:text-[18px] flex justify-center"
  href={"/dashboard/leaderboard/myprogress"}>
  My Progress
</Link>
```

### Input Fields

The LMS project uses modern input styling with consistent focus states:

```jsx
// Basic text input
<input
  type="text"
  className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
  placeholder="Enter your name"
/>

// Modern input with hover effect
<motion.div
  className="p-[2px] rounded-lg transition duration-300 group/input"
  onMouseMove={handleMouseMove}
  onMouseEnter={() => setVisible(true)}
  onMouseLeave={() => setVisible(false)}
>
  <input
    type="text"
    className="flex h-10 w-full border-none bg-gray-50 text-black shadow-input rounded-md px-3 py-2 text-sm placeholder:text-neutral-400 focus-visible:outline-none focus-visible:ring-[2px] focus-visible:ring-neutral-400 group-hover/input:shadow-none transition duration-400"
    placeholder="Enter your name"
  />
</motion.div>

// Input with label
<div className="mb-4">
  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
    Email Address
  </label>
  <input
    id="email"
    type="email"
    className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
    placeholder="<EMAIL>"
  />
</div>

// Input with floating label
<div className="relative">
  <input
    type="text"
    id="project_name"
    className="shadow-lg h-[60px] w-full rounded-md px-3 py-2 text-sm"
    placeholder=""
  />
  <label
    htmlFor="project_name"
    className="absolute text-sm text-gray-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1"
  >
    Project Name
  </label>
</div>

// Input with error
<div className="mb-4">
  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
    Password
  </label>
  <input
    id="password"
    type="password"
    className="flex h-10 w-full border border-red-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500"
    aria-invalid="true"
  />
  <p className="mt-1 text-xs text-red-600">Password must be at least 8 characters</p>
</div>

// Input with icon
<div className="relative">
  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
    <SearchIcon className="h-5 w-5 text-gray-400" />
  </div>
  <input
    type="text"
    className="pl-10 h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
    placeholder="Search..."
  />
</div>

// Textarea
<textarea
  className="flex min-h-[100px] w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
  placeholder="Write your message here..."
></textarea>

// Date picker input
<div className="flex flex-col mb-2">
  <label className="text-slate-500" htmlFor="startDate">Start Date</label>
  <DateTimePicker
    className="text-slate-500 mb-10 p-2 mt-1 outline-0 focus:border-blue-500 rounded-md"
    onChange={(date) => setStartDateValue(date)}
    value={startDateValue}
  />
</div>
```

### Select Dropdowns

The LMS project uses consistent dropdown styling:

```jsx
// Basic select
<select className="h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
  <option value="" disabled selected>Select an option</option>
  <option value="option1">Option 1</option>
  <option value="option2">Option 2</option>
  <option value="option3">Option 3</option>
</select>

// Select with label
<div className="mb-4">
  <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
    Country
  </label>
  <select
    id="country"
    className="h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
  >
    <option value="" disabled selected>Select a country</option>
    <option value="us">United States</option>
    <option value="ca">Canada</option>
    <option value="uk">United Kingdom</option>
  </select>
</div>

// Custom dropdown with Headless UI
<Listbox value={selected} onChange={setSelected}>
  <div className="relative mt-1">
    <Listbox.Button className="relative w-full h-10 border border-gray-300 bg-white rounded-md px-3 py-2 text-left text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
      <span className="block truncate">{selected.name}</span>
      <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
        <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
      </span>
    </Listbox.Button>
    <Transition
      as={Fragment}
      leave="transition ease-in duration-100"
      leaveFrom="opacity-100"
      leaveTo="opacity-0"
    >
      <Listbox.Options className="absolute w-full py-1 mt-1 overflow-auto bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none text-sm z-10">
        {options.map((option, optionIdx) => (
          <Listbox.Option
            key={optionIdx}
            className={({ active }) =>
              `${active ? 'text-blue-900 bg-blue-100' : 'text-gray-900'}
              cursor-default select-none relative py-2 pl-10 pr-4`
            }
            value={option}
          >
            {({ selected, active }) => (
              <>
                <span className={`${selected ? 'font-medium' : 'font-normal'} block truncate`}>
                  {option.name}
                </span>
                {selected ? (
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600">
                    <CheckIcon className="h-5 w-5" aria-hidden="true" />
                  </span>
                ) : null}
              </>
            )}
          </Listbox.Option>
        ))}
      </Listbox.Options>
    </Transition>
  </div>
</Listbox>
```

### Checkboxes and Radio Buttons

The LMS project uses consistent checkbox and radio button styling:

```jsx
// Basic checkbox
<div className="flex items-center">
  <input
    id="remember-me"
    type="checkbox"
    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
  />
  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
    Remember me
  </label>
</div>

// Checkbox group
<fieldset className="space-y-2">
  <legend className="text-sm font-medium text-gray-700">Notifications</legend>
  <div className="space-y-2">
    <div className="flex items-start">
      <div className="flex items-center h-5">
        <input
          id="comments"
          type="checkbox"
          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
      </div>
      <div className="ml-3 text-sm">
        <label htmlFor="comments" className="font-medium text-gray-700">Comments</label>
        <p className="text-gray-500">Get notified when someone comments on your post.</p>
      </div>
    </div>
    <div className="flex items-start">
      <div className="flex items-center h-5">
        <input
          id="offers"
          type="checkbox"
          className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
      </div>
      <div className="ml-3 text-sm">
        <label htmlFor="offers" className="font-medium text-gray-700">Offers</label>
        <p className="text-gray-500">Get notified when a new offer is posted.</p>
      </div>
    </div>
  </div>
</fieldset>

// Radio buttons
<fieldset className="space-y-2">
  <legend className="text-sm font-medium text-gray-700">Plan</legend>
  <div className="space-y-2">
    <div className="flex items-center">
      <input
        id="plan-free"
        name="plan"
        type="radio"
        className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
      />
      <label htmlFor="plan-free" className="ml-2 block text-sm text-gray-700">
        Free
      </label>
    </div>
    <div className="flex items-center">
      <input
        id="plan-premium"
        name="plan"
        type="radio"
        className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
      />
      <label htmlFor="plan-premium" className="ml-2 block text-sm text-gray-700">
        Premium
      </label>
    </div>
  </div>
</fieldset>

// Toggle switch
<div className="flex items-center">
  <button
    type="button"
    className={`${enabled ? 'bg-blue-600' : 'bg-gray-200'} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
    role="switch"
    aria-checked={enabled}
    onClick={() => setEnabled(!enabled)}
  >
    <span className="sr-only">Use setting</span>
    <span
      className={`${enabled ? 'translate-x-5' : 'translate-x-0'} pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
    />
  </button>
  <span className="ml-3 text-sm font-medium text-gray-700">
    {enabled ? 'Enabled' : 'Disabled'}
  </span>
</div>
```

### Form Layout

The LMS project uses consistent form layouts:

```jsx
// Basic form layout
<form className="space-y-6">
  <div className="space-y-4">
    {/* Form fields */}
    <div>
      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
        Full Name
      </label>
      <input
        id="name"
        type="text"
        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>

    <div>
      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
        Email Address
      </label>
      <input
        id="email"
        type="email"
        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
  </div>

  <div className="flex items-center justify-end space-x-4">
    <button
      type="button"
      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      Cancel
    </button>
    <button
      type="submit"
      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      Save
    </button>
  </div>
</form>

// Two-column form layout
<form className="space-y-6">
  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <label htmlFor="first-name" className="block text-sm font-medium text-gray-700 mb-1">
        First Name
      </label>
      <input
        id="first-name"
        type="text"
        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>

    <div>
      <label htmlFor="last-name" className="block text-sm font-medium text-gray-700 mb-1">
        Last Name
      </label>
      <input
        id="last-name"
        type="text"
        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>

    <div className="md:col-span-2">
      <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
        Address
      </label>
      <input
        id="address"
        type="text"
        className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
  </div>

  <div className="flex items-center justify-end space-x-4">
    <button
      type="button"
      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      Cancel
    </button>
    <button
      type="submit"
      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      Save
    </button>
  </div>
</form>
```

## 6. Modal Components

The LMS project uses a consistent modal pattern:

```jsx
// Basic modal
<div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm">
  <div className="w-full max-w-md bg-white rounded-lg shadow-xl transform transition-all duration-300 ease-in-out relative max-h-[80vh]">
    <button
      className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors duration-200"
      aria-label="Close modal"
    >
      <XIcon className="h-5 w-5" />
    </button>
    <div className="p-6 overflow-y-auto max-h-[80vh]">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Modal Title</h3>
      <p className="text-sm text-gray-500 mb-4">
        Modal content goes here. This can include text, forms, or any other components.
      </p>
      <div className="mt-6 flex justify-end space-x-3">
        <button
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Confirm
        </button>
      </div>
    </div>
  </div>
</div>

// Confirmation modal
<div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm">
  <div className="w-full max-w-sm bg-white rounded-lg shadow-xl transform transition-all duration-300 ease-in-out relative">
    <div className="p-6">
      <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full bg-red-100">
        <ExclamationIcon className="h-6 w-6 text-red-600" />
      </div>
      <h3 className="text-lg font-medium text-center text-gray-900 mb-2">Delete Item</h3>
      <p className="text-sm text-center text-gray-500 mb-6">
        Are you sure you want to delete this item? This action cannot be undone.
      </p>
      <div className="flex justify-center space-x-3">
        <button
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          Delete
        </button>
      </div>
    </div>
  </div>
</div>

// Form modal
<div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm">
  <div className="w-full max-w-lg bg-white rounded-lg shadow-xl transform transition-all duration-300 ease-in-out relative max-h-[80vh]">
    <button
      className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors duration-200"
      aria-label="Close modal"
    >
      <XIcon className="h-5 w-5" />
    </button>
    <div className="p-6 overflow-y-auto max-h-[80vh]">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Add New User</h3>
      <form className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Full Name
          </label>
          <input
            id="name"
            type="text"
            className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email Address
          </label>
          <input
            id="email"
            type="email"
            className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
            Role
          </label>
          <select
            id="role"
            className="h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="" disabled selected>Select a role</option>
            <option value="user">User</option>
            <option value="admin">Admin</option>
            <option value="editor">Editor</option>
          </select>
        </div>
        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Add User
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
```

## 7. Interactive Elements

### Tabs

The LMS project uses a consistent tab pattern:

```jsx
// Basic tabs
<div className="border-b border-gray-200">
  <div className="flex space-x-2">
    <button
      className="py-2 px-4 text-sm font-medium rounded-t-md bg-white text-blue-600 border-t border-l border-r border-gray-200"
    >
      Overview
    </button>
    <button
      className="py-2 px-4 text-sm font-medium rounded-t-md bg-gray-100 text-gray-500 hover:bg-gray-200"
    >
      Analytics
    </button>
    <button
      className="py-2 px-4 text-sm font-medium rounded-t-md bg-gray-100 text-gray-500 hover:bg-gray-200"
    >
      Settings
    </button>
  </div>
</div>
<div className="p-4 bg-white rounded-b-lg border-l border-r border-b border-gray-200">
  <p className="text-sm text-gray-600">
    Tab content goes here. This is the overview tab.
  </p>
</div>

// Tabs with icons
<div className="border-b border-gray-200">
  <div className="flex space-x-2">
    <button
      className="py-2 px-4 text-sm font-medium rounded-t-md bg-white text-blue-600 border-t border-l border-r border-gray-200 flex items-center"
    >
      <HomeIcon className="h-4 w-4 mr-2" />
      Overview
    </button>
    <button
      className="py-2 px-4 text-sm font-medium rounded-t-md bg-gray-100 text-gray-500 hover:bg-gray-200 flex items-center"
    >
      <ChartBarIcon className="h-4 w-4 mr-2" />
      Analytics
    </button>
    <button
      className="py-2 px-4 text-sm font-medium rounded-t-md bg-gray-100 text-gray-500 hover:bg-gray-200 flex items-center"
    >
      <CogIcon className="h-4 w-4 mr-2" />
      Settings
    </button>
  </div>
</div>
<div className="p-4 bg-white rounded-b-lg border-l border-r border-b border-gray-200">
  <p className="text-sm text-gray-600">
    Tab content goes here. This is the overview tab.
  </p>
</div>
```

### Accordions

The LMS project uses Headless UI's Disclosure component for accordions:

```jsx
// Basic accordion
<Disclosure>
  {({ open }) => (
    <>
      <Disclosure.Button className="flex justify-between w-full px-4 py-3 text-sm font-medium text-left bg-gray-50 rounded-md hover:bg-gray-100 focus:outline-none focus-visible:ring">
        <span>What is your refund policy?</span>
        <ChevronUpIcon
          className={`${open ? 'transform rotate-180' : ''} w-5 h-5 text-gray-500`}
        />
      </Disclosure.Button>
      <Transition
        show={open}
        enter="transition duration-100 ease-out"
        enterFrom="transform scale-95 opacity-0"
        enterTo="transform scale-100 opacity-100"
        leave="transition duration-75 ease-out"
        leaveFrom="transform scale-100 opacity-100"
        leaveTo="transform scale-95 opacity-0"
      >
        <Disclosure.Panel className="px-4 pt-4 pb-2 text-sm text-gray-500">
          If you're unhappy with your purchase for any reason, email us within 90 days and we'll refund you in full, no questions asked.
        </Disclosure.Panel>
      </Transition>
    </>
  )}
</Disclosure>

// Accordion group
<div className="space-y-2">
  <Disclosure as="div" className="border border-gray-200 rounded-md overflow-hidden">
    {({ open }) => (
      <>
        <Disclosure.Button className="flex justify-between w-full px-4 py-3 text-sm font-medium text-left bg-white hover:bg-gray-50 focus:outline-none focus-visible:ring">
          <span>Section 1</span>
          <ChevronUpIcon
            className={`${open ? 'transform rotate-180' : ''} w-5 h-5 text-gray-500`}
          />
        </Disclosure.Button>
        <Disclosure.Panel className="px-4 pt-4 pb-2 bg-gray-50 text-sm text-gray-500">
          Content for section 1 goes here.
        </Disclosure.Panel>
      </>
    )}
  </Disclosure>

  <Disclosure as="div" className="border border-gray-200 rounded-md overflow-hidden">
    {({ open }) => (
      <>
        <Disclosure.Button className="flex justify-between w-full px-4 py-3 text-sm font-medium text-left bg-white hover:bg-gray-50 focus:outline-none focus-visible:ring">
          <span>Section 2</span>
          <ChevronUpIcon
            className={`${open ? 'transform rotate-180' : ''} w-5 h-5 text-gray-500`}
          />
        </Disclosure.Button>
        <Disclosure.Panel className="px-4 pt-4 pb-2 bg-gray-50 text-sm text-gray-500">
          Content for section 2 goes here.
        </Disclosure.Panel>
      </>
    )}
  </Disclosure>
</div>
```

### Tooltips

The LMS project uses tooltips for additional information:

```jsx
// Basic tooltip
<div className="relative inline-block">
  <button
    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
    aria-describedby="tooltip"
  >
    Hover me
  </button>
  <div
    id="tooltip"
    role="tooltip"
    className="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-md shadow-sm opacity-0 tooltip dark:bg-gray-700"
  >
    Tooltip content
    <div className="tooltip-arrow" data-popper-arrow></div>
  </div>
</div>

// Tooltip with Headless UI
<Popover className="relative">
  {({ open }) => (
    <>
      <Popover.Button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <span>More info</span>
        <InformationCircleIcon className="ml-2 h-5 w-5" />
      </Popover.Button>
      <Transition
        show={open}
        as={Fragment}
        enter="transition ease-out duration-200"
        enterFrom="opacity-0 translate-y-1"
        enterTo="opacity-100 translate-y-0"
        leave="transition ease-in duration-150"
        leaveFrom="opacity-100 translate-y-0"
        leaveTo="opacity-0 translate-y-1"
      >
        <Popover.Panel className="absolute z-10 w-64 px-4 mt-3 transform -translate-x-1/2 left-1/2 sm:px-0">
          <div className="rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden">
            <div className="relative bg-white p-4">
              <p className="text-sm text-gray-500">
                This is additional information that appears in a tooltip.
              </p>
            </div>
          </div>
        </Popover.Panel>
      </Transition>
    </>
  )}
</Popover>
```

## 8. Responsive Design Patterns

The LMS project uses consistent responsive patterns:

```jsx
// Responsive container
<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  {/* Container content */}
</div>

// Responsive grid
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
  {/* Grid items */}
</div>

// Responsive flex layout
<div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
  {/* Flex items */}
</div>

// Responsive spacing
<div className="p-4 sm:p-6 lg:p-8">
  {/* Content with responsive padding */}
</div>

// Responsive text
<h1 className="text-xl sm:text-2xl lg:text-3xl font-bold">
  Responsive Heading
</h1>

// Responsive visibility
<div className="hidden md:block">
  {/* Only visible on medium screens and up */}
</div>
<div className="block md:hidden">
  {/* Only visible on small screens */}
</div>
```

### Implementation Guidelines:
- Use mobile-first approach with responsive modifiers
- Apply consistent breakpoints: `sm`, `md`, `lg`, `xl`
- Use responsive spacing: `p-4 sm:p-6 lg:p-8`
- Implement responsive layouts with Flexbox and Grid
- Use responsive text sizing: `text-sm md:text-base lg:text-lg`
- Implement responsive visibility with `hidden md:block` or `block md:hidden`

## 9. Animation and Transitions

The LMS project uses consistent animation patterns:

```jsx
// Basic transition
<button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
  Hover me
</button>

// Transform on hover
<div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300">
  Card with hover effect
</div>

// Fade in/out transition
<div className={`${isVisible ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}>
  Content that fades in/out
</div>

// Slide-in transition
<div className={`transform ${isOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out`}>
  Sidebar that slides in
</div>

// Rotate transition
<button className="p-2 rounded-full hover:bg-gray-100 transition-all duration-200">
  <ChevronDownIcon className={`h-5 w-5 ${isOpen ? 'transform rotate-180' : ''} transition-transform duration-200`} />
</button>
```

### Implementation Guidelines:
- Use `transition-all` with appropriate duration: `duration-200`, `duration-300`
- Apply `transform` with `scale`, `rotate`, or `translate` for movement
- Use `hover:` and `focus:` states with transitions
- Implement `ease-in-out` for smooth animations
- Use Framer Motion for more complex animations

## 10. Accessibility Considerations

The LMS project includes accessibility features:

```jsx
// Accessible button
<button
  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
  aria-label="Save changes"
>
  Save
</button>

// Skip to content link
<a
  href="#main-content"
  className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 px-4 py-2 bg-blue-600 text-white rounded-md"
>
  Skip to content
</a>

// Accessible form field
<div>
  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1" id="email-label">
    Email Address
  </label>
  <input
    id="email"
    type="email"
    aria-labelledby="email-label"
    aria-required="true"
    className="flex h-10 w-full border border-gray-300 bg-white rounded-md px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
  />
</div>

// Screen reader only text
<button className="p-2 rounded-full bg-gray-100">
  <SearchIcon className="h-5 w-5" />
  <span className="sr-only">Search</span>
</button>
```

### Implementation Guidelines:
- Use semantic HTML elements
- Include proper ARIA attributes
- Ensure sufficient color contrast
- Implement keyboard navigation
- Add focus states for interactive elements
- Use appropriate text sizes and spacing
- Include screen reader text with `sr-only` class

## Conclusion

This styling guide provides a comprehensive framework for aligning the talentlens project with the LMS project's modern design patterns. By following these guidelines, you'll create a consistent user experience across both projects while leveraging Tailwind CSS's utility-first approach.

### Key Takeaways:

1. **Modern Color System**:
   - Use Tailwind's default color scale for flexibility
   - Maintain light backgrounds (white and gray-50) for content areas
   - Apply blue accents for interactive elements
   - Use semantic colors for status indicators

2. **Typography Patterns**:
   - Use responsive text sizing with mobile-first approach
   - Maintain consistent font weights for different text elements
   - Apply appropriate text colors based on hierarchy

3. **Component Styling**:
   - Use consistent rounded corners (rounded-md, rounded-lg)
   - Apply subtle shadows and hover effects
   - Implement consistent spacing and padding
   - Use transitions for interactive elements

4. **Responsive Design**:
   - Design for mobile-first with responsive breakpoints
   - Use flexible layouts with Grid and Flexbox
   - Implement responsive spacing and typography
   - Consider collapsible navigation for smaller screens

5. **Accessibility**:
   - Use semantic HTML elements
   - Include proper ARIA attributes
   - Ensure sufficient color contrast
   - Implement keyboard navigation
   - Add focus states for interactive elements

### Implementation Strategy:

1. Start by updating the base styles and color system
2. Implement core UI components (buttons, inputs, cards)
3. Build layout components (navigation, containers)
4. Add interactive elements (modals, tabs, accordions)
5. Apply responsive design patterns
6. Test for accessibility compliance

By following this guide, you'll ensure that the talentlens project maintains visual and functional consistency with the modern styling patterns used in the LMS project.