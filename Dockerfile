# Use node:20-alpine as the base image
FROM node:20-alpine3.19 AS base

# Install dependencies only when needed
FROM base AS deps
RUN echo "https://dl-cdn.alpinelinux.org/alpine/v3.21/main" > /etc/apk/repositories && \
    echo "https://dl-cdn.alpinelinux.org/alpine/v3.21/community" >> /etc/apk/repositories && \
    apk update && \
    apk add --no-cache libc6-compat git python3 make g++ pkgconfig cairo-dev pango-dev gdk-pixbuf-dev

WORKDIR /app

# Set Git HTTP postBuffer size to handle large repositories
RUN git config --global http.postBuffer 1048576000  # 1GB buffer

# Increase Yarn network timeout
RUN yarn config set network-timeout 1200000  # 20 minutes

# Install Yarn with cache configuration
RUN yarn config set cache-folder /tmp/yarn-cache

# Clean Yarn cache to ensure no leftover files
RUN yarn cache clean

# Install dependencies based on the preferred package manager (Yarn)
COPY package.json yarn.lock* package-lock.json* ./
RUN \
  if [ -f yarn.lock ]; then yarn install --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Disable telemetry during build
ENV NEXT_TELEMETRY_DISABLED=1

RUN yarn build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

RUN mkdir .next
# Copy necessary files from builder stage
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=deps /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Set permissions
RUN chown -R nextjs:nodejs ./.next

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Ensure to use yarn next start in CMD
CMD ["yarn", "next", "start"]
