/** @type {import('tailwindcss').Config} */
module.exports = {
	darkMode: 'class',
	content: [
	  './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
	  './src/components/**/*.{js,ts,jsx,tsx,mdx}',
	  './src/app/**/*.{js,ts,jsx,tsx,mdx}',
	  "./src/**/*.{js,jsx,ts,tsx}",
	  "./node_modules/react-tailwindcss-datepicker/dist/index.esm.js",
	],
	theme: {
	  extend: {
		colors: {
		  primary: "#F2F3F5",
		  secondary: "#1E2B3A",
		  hoverColor: "#3F5479",
		  textColor: "#1A2B3B",
		  textPrimary: "#1E2B3A",
		  textSecondary: "#407BBF",
		},
		backgroundImage: {
		  'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
		  'gradient-conic':
			'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
		},
	  },
	},
  }